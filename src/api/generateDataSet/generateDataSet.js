import request from '@/utils/request'

// 查询数据集管理列表
export function getDataSetList(query) {
  return request({
    url: '/test/temporary/list',
    method: 'get',
    params: query
  })
}

// 查询数据集详细
export function getdataSet(query) {
  return request({
    url: '/test/temporaryDes/list',
    method: 'get',
    params: query
  })
}
// 新增数据集
export function addDataSet(data) {
  return request({
    url: '/test/temporary',
    method: 'post',
    data: data
  })
}

// 删除数据集
export function delDataSet(ids) {
  return request({
    url: '/test/temporary/' + ids,
    method: 'delete'
  })
}
export function getRulesName() {
  return request({
    url: '/test/rules/find',
    method: 'get',
  })
}

export function exportData(dataId) {
  return request({
    url: '/test/temporaryDes/export/'+dataId,
    method: 'get'
  })
}
