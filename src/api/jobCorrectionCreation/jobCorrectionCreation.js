import request from '@/utils/request'

export function getHomeworkList(query) {
  return request({
    url: '/test/homework/list',
    method: 'get',
    params: query
  })
}

export function createHomeWork(data) {
  return request({
    url: '/test/homework/createHomeWork',
    method: 'post',
    timeout: 180000,
    data: data
  })
}

export function saveHomeWork(data) {
  return request({
    url: '/test/homework/saveHomeWork',
    method: 'post',
    data: data
  })
}
export function putHomeWork(data) {
  return request({
    url: '/test/homework',
    method: 'put',
    data: data
  })
}

export function publishHomeWork(data) {
  return request({
    url: '/test/homework/publish',
    method: 'post',
    data: data
  })
}

export function getHomeWork(id) {
  return request({
    url: '/test/homework/' + id,
    method: 'get'
  })
}



export function delHomework(ids) {
  return request({
    url: '/test/homework/' + ids,
    method: 'delete'
  })
}
// 查询院系信息
export function getUniversity() {
  return request({
    url: '/test/university/getAll',
    method: 'get',
  })
}
// 查询院系信息
export function getUniversityll() {
  return request({
    url: '/test/university/getUniversity',
    method: 'get',
  })
}

// 作业批改
export function getCorrectList(query) {
  return request({
    url: '/test/student/listCorrect',
    method: 'get',
    params: query
  })
}

// 延长截止时间
export function addCutOffTime(data) {
  return request({
    url: '/test/student/addCutOffTime',
    method: 'put',
    data: data
  })
}
export function getStudentHwDetali(data) {
  return request({
    url: '/test/detail/all',
    method: 'post',
    data: data
  })
}
export function comment(data) {
  return request({
    url: '/test/student',
    method: 'put',
    data: data
  })
}

export function getClassList(id) {
  return request({
    url: '/test/student/listClass/' + id,
    method: 'get',
  })
}


export function getLesson() {
  return request({
    url: '/test/courseManagement/courseNameList',
    method: 'get',
  })
}

export function selectClaByHId(data) {
  return request({
    url: '/test/homework/selectClaByHId',
    method: 'get',
    params: data
  })
}
