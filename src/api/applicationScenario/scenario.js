import request from '@/utils/request'


export function getImage(query) {
  return request({
    url: '/intelligent/recording/getImage',
    method: 'post',
    timeout: 180000,
    params: query
  })
}

// 查询应用场景列表
export function listScenario(query) {
  return request({
    url: '/test/scenario/list',
    method: 'get',
    params: query
  })
}

// 查询应用场景详细
export function getScenario(id) {
  return request({
    url: '/test/scenario/' + id,
    method: 'get'
  })
}
export function getScenarioImage(id) {
  return request({
    url: '/test/scenario/image/' + id,
    method: 'get',
    responseType: 'blob' // 设置返回类型为 blob
  })
}

// 新增应用场景
export function addScenario(data) {
  return request({
    url: '/test/scenario',
    method: 'post',
    data: data
  })
}

// 修改应用场景
export function updateScenario(data) {
  return request({
    url: '/test/scenario',
    method: 'put',
    data: data
  })
}

export function getMajor(query) {
  return request({
    url: '/test/scenario/major',
    method: 'get'
  })
}

// 删除应用场景
export function delScenario(id) {
  return request({
    url: '/test/scenario/' + id,
    method: 'delete'
  })
}


// 查询学生应用场景列表
export function listScenarioS(query) {
  return request({
    url: '/test/scenarioS/list',
    method: 'get',
    params: query
  })
}

// 查询学生应用场景详细
export function getScenarioS(id) {
  return request({
    url: '/test/scenarioS/' + id,
    method: 'get'
  })
}

export function getScenarioBgS(id) {
  return request({
    url: '/test/scenarioS/bg/' + id,
    method: 'get',
    responseType: 'blob' // 设置返回类型为 blob
  })
}

// 新增学生应用场景
export function addScenarioS(data) {
  return request({
    url: '/test/scenarioS',
    method: 'post',
    data: data
  })
}

// 修改学生应用场景
export function updateScenarioS(data) {
  return request({
    url: '/test/scenarioS',
    method: 'put',
    data: data
  })
}

export function addOrUpdateScenarioS(data) {
  return request({
    url: '/test/scenarioS/addOrUpdate',
    method: 'post',
    data: data
  })
}

export function deleteImgByPath(data) {
  return request({
    url: '/test/scenarioS/deleteImgByPath',
    method: 'post',
    data: data
  })
}


export function saveExecl(data) {
  return request({
    url: '/test/execlStuTea/save',
    method: 'post',
    timeout: 360000,
    data: data
  })
}


export function delFalg() {
  return request({
    url: '/test/execlStuTea/delFalg',
    method: 'get',
  })
}
// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: '/system/dict/data/type/' + dictType,
    method: 'get'
  })
}


// // 删除学生应用场景
// export function delScenario(id) {
//   return request({
//     url: '/test/scenario/' + id,
//     method: 'delete'
//   })
// }


