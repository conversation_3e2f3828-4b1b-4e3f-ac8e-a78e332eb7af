import request from '@/utils/request'

// 查询对话记录列表
export function getDialogueList(query) {
  return request({
    url: '/intelligent/recording/list',
    method: 'get',
    timeout: 180000,
    params: query
  })
}
//查询prompt模板列表
export function getPromptList() {
  return request({
    url: '/test/template/all',
    method: 'get',
  })
}

// 查询对话记录详细
export function getDialogue(id) {
  return request({
    url: '/intelligent/recording/' + id,
    method: 'get',
    timeout: 180000
  })
}
// 新增对话记录
export function addDialogue(data) {
  return request({
    url: '/intelligent/recording',
    method: 'post',
    timeout: 180000,
    data: data
  })
}

// 修改对话记录
export function updateDialogue(data) {
  return request({
    url: '/intelligent/recording',
    method: 'put',
    timeout: 180000,
    data: data
  })
}
// 修改对话记录点赞点踩
export function updateDialoguelikeStomp(data) {
  return request({
    url: '/intelligent/details/likeStomp',
    method: 'post',
    timeout: 180000,
    data: data
  })
}


// 删除对话记录
export function delDialogue(id) {
  return request({
    url: '/intelligent/recording/' + id,
    method: 'delete'
  })
}

// 保存讲义
export function preserveFullTextContent(data) {
  return request({
    url: '/intelligent/recording/preserve',
    method: 'post',
    data: data
  })
}

export function getAuthUrl(query) {
  return request({
    url: '/intelligent/xfSpark/getAuthUrl',
    method: 'get',
    params: query
  })
}
export function getBaiDuToken() {
  return request({
    url: '/baidu/BaiDuSpeech/getBaDuApiToken',
    method: 'post',
  })
}

export function getId() {
  return request({
    url: '/intelligent/recording/getId',
    method: 'post',
  })
}

// 智慧学堂列表
export function getCourseList(query) {
  return request({
    url: '/plat/studyrecord/list',
    method: 'get',
    params: query
  })
}

// 智慧学堂列表2
export function getCourseList2(query) {
  return request({
    url: '/plat/studyrecord/list2',
    method: 'get',
    params: query
  })
}

// 智慧学堂列表3
export function getCourseList3(query) {
  return request({
    url: '/plat/studyrecord/list3',
    method: 'get',
    params: query
  })
}

// ppt详情
export function getpptInfo(data) {
  return request({
    url: '/plat/studyrecord/selectPptDigital',
    method: 'post',
    data: data
  })
}
// ppt演讲稿
export function getDigitalHumanSpeech(data) {
  return request({
    url: '/plat/studyrecord/getDigitalHumanSpeech3',
    method: 'post',
    data: data
  })
}
// 更新学习进度
export function updateProgress(data) {
  return request({
    url: '/plat/studyrecord/addOrUpdate',
    method: 'post',
    data: data
  })
}

//
export function updateDiogloe(data) {
  return request({
    url: '/plat/studyrecord/edit',
    method: 'post',
    data: data
  })
}



export function getppt(data) {
  return request({
    url: '/plat/studyrecord/fileDownload',
    method: 'get',
    responseType: 'blob',
    params: data,
    timeout: 1000 * 60 * 3
  })
}

getpoints

// 获取图片相对路径
export function getRelativePath(data) {
  return request({
    url: '/plat/studyrecord/getDigitalHuman3',
    method: 'post',
    data: data
  })
}

export function getPlatlist() {
  return request({
    url: '/plat/figure/getLastVersionPlatList',
    method: 'post',

  })
}

export function savePlat(data) {
  return request({
    url: '/plat/userFigure',
    method: 'post',
    data: data
  })
}

// 获取数字人初始形象设置
export function getInitialPlatById(data) {
  return request({
    url: '/plat/figure/getInitialPlatById',
    method: 'post',
    data: data
  })
}

// 获取演讲稿中的题目
export function getProblem(data) {
  return request({
    url: '/plat/problem/getproblem',
    method: 'post',
    data: data
  })
}

// 校验答案正确
export function checkAnwer(data) {
  return request({
    url: '/plat/problemsub/checkAnwer',
    method: 'post',
    data: data
  })
}

// 点赞/点踩
export function likeOrStomp(data) {
  return request({
    url: '/intelligent/details/likeStomp',
    method: 'post',
    data: data
  })
}

// 语音识别
export function baiduSpeechToText(data) {
  return request({
    url: '/baidu/BaiDuSpeech/BaiDuASR',
    method: 'post',
    data: data
  })
}

// 停止生成
export function stopGeneration(id) {
  return request({
    url: '/intelligent/recording/stop/' + id,
    method: 'get'
  })
}

export function evaluation(data) {
  return request({
    url: '/plat/studyrecord/evaluation',
    method: 'post',
    timeout:1000000,
    data: data
  })
}
export function submit(data) {
  return request({
    url: '/plat/studyrecord/submit',
    method: 'post',
    timeout:1000000,
    data: data
  })
}

export function getpoints(query) {
  return request({
    url: '/plat/studyrecord/points',
    method: 'get',
    params: query
  })
}

export function relatedIssues(query) {
  return request({
    url: '/intelligent/recording/related',
    method: 'get',
    timeout:30000,
    params: query
  })
}

export function queryImages(id) {
  return request({
    url: 'plat/motionDetail/queryImages',
    method: 'post',
    timeout: 300000,
    params:  { figureID: id }
  })
}

// 获取用户ppt缩略图
export function getImageThumbnail(id) {
  return request({
    url: 'plat/studyrecord/getImageThumbnail',
    method: 'get',
    timeout: 300000,
    params: {presentationId : id}
  })
}

export function checkID(id) {
  return request({
    url: '/plat/figure/checkID',
    method: 'post',
    params: {id : id}
  })
}
