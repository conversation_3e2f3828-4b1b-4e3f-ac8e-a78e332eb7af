import request from '@/utils/request'

// 查询场景管理列表
export function getManagementList(query) {
  return request({
    url: '/intelligent/management/list',
    method: 'get',
    params: query
  })
}

// 查询场景管理详细
export function getManagement(id) {
  return request({
    url: '/intelligent/management/' + id,
    method: 'get',
  })
}
// 新增场景管理
export function addManagement(data) {
  return request({
    url: '/intelligent/management',
    method: 'post',
    data: data
  })
}

// 修改场景管理
export function updateManagement(data) {
  return request({
    url: '/intelligent/management',
    method: 'put',
    data: data
  })
}

// 删除场景管理
export function delManagement(ids) {
  return request({
    url: '/intelligent/management/' + ids,
    method: 'delete'
  })
}
