import request from '@/utils/request'

// 查询PPT模板（解压后的模板管理）详细列表
export function listPptTemplateThemeDetail(query) {
  return request({
    url: '/ppt/pptTemplateManagementDetail/list',
    method: 'get',
    params: query
  })
}

// 查询PPT模板（解压后的模板管理）详细详细
export function getPptTemplateThemeDetail(id) {
  return request({
    url: '/ppt/pptTemplateManagementDetail/' + id,
    method: 'get'
  })
}

// 新增PPT模板（解压后的模板管理）详细
export function addPptTemplateThemeDetail(data) {
  return request({
    url: '/ppt/pptTemplateManagementDetail',
    method: 'post',
    data: data
  })
}

// 修改PPT模板（解压后的模板管理）详细
export function updatePptTemplateThemeDetail(data) {
  return request({
    url: '/ppt/pptTemplateManagementDetail',
    method: 'put',
    data: data
  })
}

// 删除PPT模板（解压后的模板管理）详细
export function delPptTemplateThemeDetail(id) {
  return request({
    url: '/ppt/pptTemplateManagementDetail/' + id,
    method: 'delete'
  })
}
