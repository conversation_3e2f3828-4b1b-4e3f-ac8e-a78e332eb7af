import request from '@/utils/request'

export function getTrainingList(query) {
  return request({
    url: '/zhi/training/list',
    method: 'get',
    params: query
  })
}

export function getTraining(id) {
  return request({
    url: '/zhi/training/' + id,
    method: 'get'
  })
}
export function addTraining(data) {
  return request({
    url: '/zhi/training',
    method: 'post',
    data: data
  })
}

export function delTraining(ids) {
  return request({
    url: '/zhi/training/' + ids,
    method: 'delete'
  })
}

export function getDataSetPublished(menuRouting) {
  return request({
    url: '/zhi/dataSet/published',
    method: 'post',
    data: menuRouting
  })
}
