import request from '@/utils/request'

export function getMessageList(query) {
  return request({
    url: '/test/message1/list',
    method: 'get',
    params: query
  })
}
export function getMessage(id) {
  return request({
    url: '/test/reply1/all/' + id,
    method: 'get'
  })
}
export function addMessage(data) {
  return request({
    url: '/test/message1',
    method: 'post',
    data: data
  })
}
export function addReply(data) {
  return request({
    url: '/test/reply1',
    method: 'post',
    data: data
  })
}
export function delMessage(ids) {
  return request({
    url: '/test/message1/' + ids,
    method: 'delete'
  })
}

export function delReply(ids) {
  return request({
    url: '/test/reply1/removeReply/' + ids,
    method: 'delete'
  })
}

