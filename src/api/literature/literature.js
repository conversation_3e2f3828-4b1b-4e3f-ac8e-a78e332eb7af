import request from '@/utils/request'

export function getCount(data) {
  return request({
    url: '/test/quantity/getCount',
    method: 'post',
    data: data
  })
}

export function literatruecount(data) {
  return request({
    url: '/test/literatruecount/all',
    method: 'post',
    data: data
  })
}

export function getThesis(query) {
  return request({
    url: '/test/literatruedata/clickselect',
    method: 'get',
    params: query
  })
}