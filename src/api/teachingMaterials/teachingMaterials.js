import request from '@/utils/request'

// 查询列表
export function getPresentationList(query) {
  return request({
    url: '/test/presentation/getPresentationList',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getPresentation(id) {
  return request({
    url: '/test/presentation/' + id,
    method: 'get',
  })
}
// 新增
export function addPresentation(data) {
  return request({
    url: '/test/presentation/add',
    method: 'post',
    data: data
  })
}
// 修改
export function updatePresentation(data) {
  return request({
    url: '/test/presentation/update',
    method: 'post',
    data: data
  })
}

// 删除
export function delPresentation(ids) {
  return request({
    url: '/test/presentation/delete/' + ids,
    method: 'delete'
  })
}

export function getUniversity() {
  return request({
    url: '/test/university/getAll',
    method: 'get',
  })
}
//获取讲演稿
export function getTxt(data) {
  return request({
    url: '/test/presentation/txt2',
    method: 'get',
    params: data
  })
}

//更新讲演稿
export function updateTxt(data) {
  return request({
    url: '/test/presentation/submit',
    method: 'post',
    data: data
  })
}
// 获取专业
export function getMajorList() {
  return request({
    url: '/test/presentation/majorlist',
    method: 'get'
  })
}

// 获取学院
export function getCollegeList() {
  return request({
    url: '/test/collegeInfo/all',
    method: 'get'
  })
}


// 课程列表
export function getCourseList(query) {
  return request({
    url: '/test/presentation/getPresentationListGroupByCourse',
    method: 'get',
    params: query
  })
}

// 下载ppt
export function downloadPresentation(id) {
  return request({
    url: '/test/presentation/downloadPresentation/' + id,
    method: 'get',
    responseType: 'blob', // 确保返回的是blob数据
  })
}


// 下载讲演稿
export function downloadSpeech(id) {
  return request({
    url: '/test/presentation/downloadSpeech/' + id,
    method: 'get',
    responseType: 'blob', // 确保返回的是blob数据
  })
}

// 审核
export function checkCourse(data) {
  return request({
    url: '/test/examine/add',
    method: 'post',
    data: data
  })
}

//获取对应的教材
export function getKnowledge(data) {
  return request({
    url: '/test/presentation/knowledge',
    method: 'get',
    params: data
  })
}
