import axios from 'axios';

const API_BASE_URL = 'https://open.docmee.cn/api';
const TOKEN = 'ak_6KD3vSv36T333E519t';

// 自定义ajax实现
const customRequest = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000
});

// 请求重试配置
const retryConfig = {
  retries: 1,
  retryDelay: 1000,
  retryCondition: (error) => {
    return axios.isAxiosError(error) && error.response?.status >= 500;
  }
};

// 请求拦截器
customRequest.interceptors.request.use(
  config => {
    // 添加token
    config.headers.token = TOKEN;

    // 如果是 multipart/form-data，不设置 Content-Type，让浏览器自动设置
    if (!config.headers['Content-Type'] && !config.data?.constructor?.name === 'FormData') {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
customRequest.interceptors.response.use(
  response => {
    // 如果是二进制数据，直接返回
    if (response.config.responseType === 'blob') {
      return response.data;
    }

    // 返回完整的响应数据，而不是只返回data
    return response.data;
  },
  error => {
    if (error.response?.status === 401) {
      // Token过期处理
      console.error('Token已过期，请重新获取');
    }
    return Promise.reject(error);
  }
);

// 带重试的请求方法
const requestWithRetry = async (config) => {
  let lastError = null;
  for (let i = 0; i < retryConfig.retries; i++) {
    try {
      return await customRequest(config);
    } catch (error) {
      lastError = error;
      if (!retryConfig.retryCondition(error)) {
        throw error;
      }
      if (i < retryConfig.retries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryConfig.retryDelay * (i + 1)));
      }
    }
  }
  throw lastError;
};

// API请求封装
export default {
  /**
   * 获取生成选项
   * @returns {Promise<Object>} 返回支持的语言、场景、受众等选项
   */
  getOptions() {
    return requestWithRetry({
      url: '/ppt/v2/options',
      method: 'get'
    });
  },

  /**
   * 创建任务
   * @param {any} data 任务参数
   * @param {number} data.type 类型：1.智能生成 2.上传文件 3.思维导图 4.word转换 5.网页链接 6.文本内容 7.Markdown
   * @param {string} [data.content] 内容
   * @param {File[]} [data.files] 文件列表
   * @returns {Promise<any>} 返回任务ID
   */
  createTask(data) {
    return requestWithRetry({
      url: '/ppt/v2/createTask',
      method: 'post',
      data: data
    });
  },

  /**
   * 生成大纲内容
   * @param {Object} params 生成参数
   * @param {string} params.id 任务ID
   * @param {boolean} [params.stream=true] 是否流式返回
   * @param {string} [params.length='medium'] 篇幅长度：short/medium/long
   * @param {string} [params.scene] 演示场景
   * @param {string} [params.audience] 受众
   * @param {string} [params.lang] 语言
   * @param {string} [params.prompt] 用户要求（小于50字）
   * @returns {Promise<Object>} 返回生成的内容
   */
  generateContent(params) {
    return requestWithRetry({
      url: '/ppt/v2/generateContent',
      method: 'post',
      data: {
        id: params.id,
        stream: params.stream !== false,
        length: params.length || 'medium',
        scene: params.scene,
        audience: params.audience,
        lang: params.lang,
        prompt: params.prompt
      }
    });
  },

  /**
   * 修改大纲内容
   * @param {Object} params 修改参数
   * @param {string} params.id 任务ID
   * @param {boolean} [params.stream=true] 是否流式返回
   * @param {string} params.markdown 大纲内容markdown
   * @param {string} params.question 用户修改建议
   * @returns {Promise<Object>} 返回修改后的内容
   */
  updateContent(params) {
    return requestWithRetry({
      url: '/ppt/v2/updateContent',
      method: 'post',
      data: {
        id: params.id,
        stream: params.stream !== false,
        markdown: params.markdown,
        question: params.question
      }
    });
  },

  /**
   * 生成PPT
   * @param {Object} params 生成参数
   * @param {string} params.id 任务ID
   * @param {string} [params.templateId] 模板ID
   * @param {string} params.markdown 大纲内容markdown
   * @returns {Promise<Object>} 返回生成的PPT信息
   */
  generatePptx(params) {
    return requestWithRetry({
      url: '/ppt/v2/generatePptx',
      method: 'post',
      data: {
        id: params.id,
        templateId: params.templateId,
        markdown: params.markdown
      }
    });
  },

  // 直接生成PPT
  directGeneratePptx(data) {
    return requestWithRetry({
      url: '/ppt/directGeneratePptx',
      method: 'post',
      data
    });
  },

  // Word转PPT
  word2pptx(formData, onUploadProgress) {
    return requestWithRetry({
      url: '/ppt/v1/word2pptx',
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: formData,
      onUploadProgress
    });
  },

  // 获取模板列表
  getTemplates(data) {
    return requestWithRetry({
      url: '/ppt/templates',
      method: 'post',
      data,
      params: {
        lang: 'zh-CN'
      }
    });
  },

  // 获取随机模板
  getRandomTemplates(data) {
    return requestWithRetry({
      url: '/ppt/randomTemplates',
      method: 'post',
      data
    });
  },

  // 获取PPT列表
  getPptList(data) {
    return requestWithRetry({
      url: '/ppt/listAllPptx',
      method: 'post',
      data,
      params: {
        lang: 'zh-CN'
      }
    });
  },

  // 下载PPT
  downloadPpt(data) {
    return requestWithRetry({
      url: '/ppt/downloadPptx',
      method: 'post',
      data,
      responseType: 'blob'
    });
  },

  // 下载带动画的PPT
  downloadPptWithAnimation(id, type = 1) {
    return requestWithRetry({
      url: `/ppt/downloadWithAnimation`,
      method: 'get',
      params: { id, type },
      responseType: 'blob'
    });
  },

  // 更换PPT模板
  updatePptTemplate(data) {
    return requestWithRetry({
      url: '/ppt/updatePptTemplate',
      method: 'post',
      data
    });
  },

  // 更新PPT属性
  updatePptAttr(data) {
    return requestWithRetry({
      url: '/ppt/updatePptxAttr',
      method: 'post',
      data
    });
  },

  // 设置PPT Logo
  setPptLogo(formData) {
    return requestWithRetry({
      url: '/ppt/setPptLogo',
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: formData
    });
  },

  // 保存PPT
  savePpt(data) {
    return requestWithRetry({
      url: '/ppt/savePptx',
      method: 'post',
      data
    });
  },

  // 上传模板
  uploadTemplate(formData) {
    return requestWithRetry({
      url: '/ppt/uploadTemplate',
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: formData
    });
  },

  // 删除模板
  deleteTemplate(data) {
    return requestWithRetry({
      url: '/ppt/delTemplateId',
      method: 'post',
      data
    });
  },

  // 设置公共模板
  setPublicTemplate(data) {
    return requestWithRetry({
      url: '/ppt/updateUserTemplate',
      method: 'post',
      data
    });
  },

  // 删除PPT
  deletePpt(data) {
    return requestWithRetry({
      url: '/ppt/deletePptx',
      method: 'post',
      data
    });
  },

  // 获取用户信息
  getUserInfo() {
    return requestWithRetry({
      url: '/user/apiInfo',
      method: 'get'
    });
  },

  // 获取积分使用记录
  getUsageRecords(data) {
    return requestWithRetry({
      url: '/record/listPage',
      method: 'post',
      data
    });
  },

  // 获取按小时统计的积分使用
  getHourlyStats(data) {
    return requestWithRetry({
      url: '/record/statisticHours',
      method: 'post',
      data
    });
  },

  // 获取按天统计的积分使用
  getDailyStats(data) {
    return requestWithRetry({
      url: '/record/statisticDays',
      method: 'post',
      data
    });
  },

  // 上传文件
  uploadFile(formData, onUploadProgress) {
    return requestWithRetry({
      url: '/file/upload',
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: formData,
      onUploadProgress
    });
  },

  // 流式生成内容
  async generateContentStream(data, onData, onComplete) {
    const response = await fetch(`${API_BASE_URL}/ppt/v2/generateContent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'token': TOKEN
      },
      body: JSON.stringify({ ...data, stream: true })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });

      // 处理接收到的数据
      const lines = buffer.split('\n');
      buffer = lines.pop(); // 保留最后一个不完整的行

      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            onData(data);
          } catch (e) {
            console.error('解析数据失败:', e);
          }
        }
      }
    }

    // 处理最后的数据
    if (buffer.trim()) {
      try {
        const data = JSON.parse(buffer);
        onData(data);
      } catch (e) {
        console.error('解析最后的数据失败:', e);
      }
    }

    onComplete();
  },
};

