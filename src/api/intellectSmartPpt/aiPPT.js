import request from '@/utils/request'

// API Key 常量定义
const API_KEY_HEADER = "api-key";
export function createPPT(data) {
  return request({
    url: '/test/smartPPT/createSmartPpt',
    method: 'post',
    timeout: 180000,
    data: data
  })
}


export function createApiToken(data,headers) {
  return request({
    url: '/ppt/createPpt/createApiToken',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


/**
 * 创建任务
 * @param {any} data 任务参数
 * @param headers
 * @param {number} data.type 类型：1.智能生成 2.上传文件 3.思维导图 4.word转换 5.网页链接 6.文本内容 7.Markdown
 * @param {string} [data.content] 内容
 * @param {File[]} [data.files] 文件列表
 * @returns {Promise<any>} 返回任务ID
 */
export function createTask(data,headers) {
  return request({
    url: '/ppt/createPpt/createTask',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


export function generateContent(data,headers) {
  return fetch(`${process.env.VUE_APP_BASE_API}/ppt/createPpt/generateContent`, {
    method: 'POST',
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey,
        'Content-Type': 'application/json'
      }
    }),
    body: JSON.stringify(data)
  });
}
export function aiPptGeneratePptx(data,headers) {
  return request({
    url: '/ppt/createPpt/generatePptx',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}
