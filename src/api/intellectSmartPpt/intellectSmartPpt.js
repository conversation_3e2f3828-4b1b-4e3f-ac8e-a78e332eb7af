import request from '@/utils/request'
import fetchRequest from "@/utils/fetchRquest";

// API Key 常量定义
const API_KEY_HEADER = "api-key";
export function createPPT(data) {
  return request({
    url: '/test/smartPPT/createSmartPpt',
    method: 'post',
    timeout: 180000,
    data: data
  })
}

export function downLoadPPt(data) {
  return request({
    responseType: 'blob',
    url: '/ppt/createPpt/downLoadPPt',
    method: 'post',
    timeout: 180000,
    data: data,
  })
}

export function downLoad(data) {
  return request({
    responseType: 'blob',
    url: '/file/file/customDownLoad',
    method: 'post',
    timeout: 180000,
    data: data,
  })
}

export function createAndDownLoadPPt(data) {
  return request({
    responseType: 'blob',
    url: '/ppt/createPpt/createPptOutput',
    method: 'post',
    timeout: 180000,
    data: data,
  })
}

export function createPPtToGetPath(data, headers) {
  return request({
    url: '/ppt/createPpt/createSmartPpt',
    method: 'post',
    timeout: 180000,
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getFontList() {
  return request({
    url: '/ppt/createPpt/getSystemFontList',
    method: 'GET',
    timeout: 60000,
  })
}

export function enterPPTPage(data) {
  return request({
    url: '/ppt/createPpt/enterPPTPage',
    method: 'post',
    data: data,
  })
}
export function getUidDetail(data,headers) {
  return request({
    url: '/ppt/createPpt/getUidDetail',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


export function createApiToken(data,headers) {
  return request({
    url: '/ppt/createPpt/createApiToken',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function createTask(data,headers) {
  return request({
    url: '/ppt/createPpt/createTask',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getAiPptTemplates(data,headers) {
  return request({
    url: '/ppt/createPpt/templates',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getAiPptRandomTemplates(data,headers) {
  return request({
    url: '/ppt/createPpt/randomTemplates',
    method: 'post',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function aiPptGeneratePptx(data,headers) {
  return request({
    url: '/ppt/createPpt/generatePptx',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


export function aiPptListPptx(data,headers) {
  return request({
    url: '/ppt/createPpt/listPptx',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}
export function aiPptDelete(data,headers) {
  return request({
    url: '/ppt/createPpt/delete',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}
export function aiPptDelTemplateId(data,headers) {
  return request({
    url: '/ppt/createPpt/delTemplateId',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function aiPptUploadTemplate(data,headers) {
  return request({
    url: '/ppt/createPpt/uploadTemplate',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getSceneOptions(data,headers) {
  return request({
    url: '/ppt/createPpt/getSceneOptions',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


export function getOutlineLang(data,headers) {
  return request({
    url: '/ppt/createPpt/getOutlineLang',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getChatModel(data,headers) {
  return request({
    url: '/ppt/createPpt/getChatModel',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function templatesNameUpdateApi(data,headers) {
  return request({
    url: '/ppt/createPpt/templatesNameUpdate',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}
export function updatePptxAttrApi(data,headers) {
  return request({
    url: '/ppt/createPpt/updatePptxAttr',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}


export function getText2image(data,headers) {
  return request({
    url: '/ppt/createPpt/text2image',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getImgStyleOptions(data,headers) {
  return request({
    url: '/ppt/createPpt/imgStyleOptions',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function getCasEnter() {
  return request({
    url: '/ppt/createPpt/getCasEnter',
    method: 'POST',
  })
}

export function getPptEditData(data,headers) {
  return request({
    url: '/ppt/createPpt/getPptEditData',
    method: 'POST',
    data: data,
    ...(headers?.apiKey && {
      headers: {
        [API_KEY_HEADER]: headers.apiKey
      }
    })
  })
}

export function downloadTemplate(data,headers) {
	return request({
		url: '/ppt/createPpt/downloadTemplate',
		method: 'POST',
		data: data,
		...(headers?.apiKey && {
			headers: {
				[API_KEY_HEADER]: headers.apiKey
			}
		})
	})
}

export function generateContentApi(data, headers, abortSignal = null) {
	return fetchRequest({
		url: '/ppt/createPpt/generateContent',
		method: 'post',
		data: data,
		signal: abortSignal, // 添加 AbortSignal 支持
		...(headers?.apiKey && {
			headers: {
				[API_KEY_HEADER]: headers.apiKey
			}
		})
	})
}

// 取消生成请求的API接口
export function cancelGenerationApi(data, headers) {
	return fetchRequest({
		url: '/ppt/createPpt/cancelGeneration',
		method: 'post',
		data: data,
		...(headers?.apiKey && {
			headers: {
				[API_KEY_HEADER]: headers.apiKey
			}
		})
	})
}

