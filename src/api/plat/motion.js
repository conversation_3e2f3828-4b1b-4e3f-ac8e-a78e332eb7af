import request from '@/utils/request'

// 查询动作信息列表
export function listMotion(query) {
  return request({
    url: '/plat/motion/list',
    method: 'get',
    timeout: 180000,
    params: query
  })
}

// 查询动作信息详细
export function getMotion(id) {
  return request({
    url: '/plat/motion/' + id,
    method: 'get',
    timeout: 180000
  })
}

// 新增动作信息
export function addMotion(data) {
  return request({
    url: '/plat/motion',
    method: 'post',
    timeout: 180000,
    data: data
  })
}

// 修改动作信息
export function updateMotion(data) {
  return request({
    url: '/plat/motion',
    method: 'put',
    timeout: 180000,
    data: data
  })
}

// 删除动作信息
export function delMotion(id) {
  return request({
    url: '/plat/motion/' + id,
    method: 'delete',
    timeout: 180000
  })
}

// 预览动作信息
export function preview(motionId) {
  return request({
    url: '/plat/motionDetail/getImgUrlList/' + motionId,
    method: 'get',
    timeout: 180000
  })
}
