import request from '@/utils/request'

// 查询数字人形象列表
export function listFigure(query) {
  return request({
    url: '/plat/figure/list',
    method: 'get',
    timeout: 180000,
    params: query
  })
}

// 查询数字人形象详细
export function getFigure(id) {
  return request({
    url: '/plat/figure/' + id,
    method: 'get',
    timeout: 180000
  })
}

// 新增数字人形象
export function addFigure(data) {
  return request({
    url: '/plat/figure',
    method: 'post',
    timeout: 180000,
    data: data
  })
}

// 修改数字人形象
export function updateFigure(data) {
  return request({
    url: '/plat/figure',
    method: 'put',
    timeout: 180000,
    data: data
  })
}

// 删除数字人形象
export function delFigure(id) {
  return request({
    url: '/plat/figure/' + id,
    method: 'delete',
    timeout: 180000
  })
}

// 上传形象
export function uploadFile(data) {
  return request({
    url: '/plat/figure/upload',
    method: 'post',
    timeout: 300000,
    data: data
  })
}

// 预览动作信息
export function preview(data) {
  return request({
    url: '/plat/figure/preview1/',
    method: 'post',
    timeout: 300000,
    data: data
  })
}
