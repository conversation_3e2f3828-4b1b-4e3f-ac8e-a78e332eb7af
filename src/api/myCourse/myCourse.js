import request from '@/utils/request'


// 学生查看课程
export function getCourseList(query) {
  return request({
    url: '/test/courseManagement/xskcList',
    method: 'get',
    params: query
  })
}

// 查询通知列表
export function getMessageList(query) {
  return request({
    url: '/test/courseNotifications/list2',
    method: 'get',
    params: query
  })
}

// 查询讨论列表
export function getDiscussList(query) {
  return request({
    url: '/test/courseTopics/list2',
    method: 'get',
    params: query
  })
}


export function getDiscuss(id) {
  return request({
    url: '/test/courseTopics/' + id,
    method: 'get',
  })
}

export function getDiscussLMessageList(query) {
  return request({
    url: '/test/replies/list',
    method: 'get',
    params: query
  })
}
export function getMessage(id) {
  return request({
    url: '/test/topReply/all/' + id,
    method: 'get'
  })
}
export function addMessage(data) {
  return request({
    url: '/test/replies',
    method: 'post',
    data: data
  })
}
export function addReply(data) {
  return request({
    url: '/test/topReply',
    method: 'post',
    data: data
  })
}
export function delMessage(ids) {
  return request({
    url: '/test/replies/' + ids,
    method: 'delete'
  })
}

export function delReply(ids) {
  return request({
    url: '/test/topReply/removeReply/' + ids,
    method: 'delete'
  })
}


// 点赞点踩
export function likeStomp(data) {
  return request({
    url: '/test/kikeStep',
    method: 'post',
    data: data
  })
}

export function delLikeStep(id) {
  return request({
    url: '/test/kikeStep/' + id,
    method: 'delete'
  })
}