import request from '@/utils/request'

export function getMessageList(query) {
  return request({
    url: '/test/message/list',
    method: 'get',
    params: query
  })
}
export function getMessage(id) {
  return request({
    url: '/test/reply/all/' + id,
    method: 'get'
  })
}
export function addMessage(data) {
  return request({
    url: '/test/message',
    method: 'post',
    data: data
  })
}
export function addReply(data) {
  return request({
    url: '/test/reply',
    method: 'post',
    data: data
  })
}
export function delMessage(ids) {
  return request({
    url: '/test/message/' + ids,
    method: 'delete'
  })
}

export function delReply(ids) {
  return request({
    url: '/test/reply/removeReply/' + ids,
    method: 'delete'
  })
}

