import request from '@/utils/request'

// 查询对话记录列表
export function getStudyList(query) {
  return request({
    url: '/intelligent/study/list',
    method: 'get',
    params: query
  })
}
// 查询对话记录详细
export function getStudy(id) {
  return request({
    url: '/intelligent/study/' + id,
    method: 'get'
  })
}
// 新增对话记录
export function addStudy(data) {
  return request({
    url: '/intelligent/study',
    method: 'post',
    timeout: 30000000,
    data: data
  })
}

// 修改对话记录
export function updateStudy(data) {
  return request({
    url: '/intelligent/study',
    method: 'put',
    timeout: 30000000,
    data: data
  })
}

// 删除对话记录
export function delStudy(id) {
  return request({
    url: '/intelligent/study/' + id,
    method: 'delete'
  })
}

export function getBaiDuToken() {
  return request({
    url: '/baidu/BaiDuSpeech/getBaDuApiToken',
    method: 'post',
  })
}

// 点赞/点踩
export function likeOrStomp(data) {
  return request({
    url: '/intelligent/etail/likeStomp',
    method: 'post',
    data: data
  })
}
