import request from '@/utils/request'

export function getPromotionClass(query) {
  return request({
    url: '/test/promotion/list',
    method: 'post',
    params: query
  })
}

export function addOrUpdateStudentStudyRecord(data) {
  return request({
    url: '/test/promotion/addOrUpdateStudentStudyRecord',
    method: 'post',
    data: data
  })
}

export function getPlatUserFigureByUserId() {
  return request({
    url: '/plat/userFigure/getPlatUserFigureByUserId',
    method: 'post',
  })
}