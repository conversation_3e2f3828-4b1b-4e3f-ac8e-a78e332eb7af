import request from '@/utils/request'

// 查轮播图列表
export function getRotationChartList(query) {
  return request({
    url: '/test/circularbannerconfig/list',
    method: 'get',
    params: query
  })
}

// 新增轮播图
export function addRotationChart(data) {
  return request({
    url: '/test/circularbannerconfig',
    method: 'post',
    data: data
  })
}
// 修改轮播图
export function updateRotationChart(data) {
  return request({
    url: '/test/circularbannerconfig',
    method: 'put',
    data: data
  })
}
// 删除轮播图
export function delRotationChart(ids) {
  return request({
    url: '/test/circularbannerconfig/' + ids,
    method: 'delete'
  })
}


// 获取全部轮播图
export function getAllRotationChart() {
  return request({
    url: '/test/circularbannerconfig/listAll',
    method: 'get',
  })
}

// 获取学校名
export function getSchoolName() {
  return request({
    url: '/test/circularbannerconfig/selUniverName',
    method: 'get',
  })
}