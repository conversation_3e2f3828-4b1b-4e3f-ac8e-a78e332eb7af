import request from '@/utils/request'

/**
 * 执行代码接口
 * @param {Object} data - 请求数据
 * @param {string} data.language - 编程语言，默认python
 * @param {string} data.code - 要执行的代码
 * @returns {Promise} 执行结果
 */
export function executeCode(data) {
  return request({
    url: '/code/execute', // 请根据您的实际接口地址调整，比如 '/api/code/execute'
    method: 'post',
    timeout: 30000, // 30秒超时
    data: data
  })
}

/**
 * 获取代码执行状态
 * @param {string} executionId - 执行ID
 * @returns {Promise} 执行状态
 */
export function getExecutionStatus(executionId) {
  return request({
    url: `/code/execution/${executionId}/status`,
    method: 'get'
  })
}
