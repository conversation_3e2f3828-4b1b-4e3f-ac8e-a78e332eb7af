import request from "@/utils/request";

export function getDataSetRulesList(queryParams) {
  return request({
    url: "/test/rules/list",
    method: "get",
    params: queryParams,
  });
}
export function getDataSetRulesById(id) {
  return request({
    url: "/test/rules/" + id,
    method: "get",
  });
}
export function getDataSetRules(dataSetRuleContentQueryParams) {
  return request({
    url: "/test/content/list",
    method: "get",
    params: dataSetRuleContentQueryParams,
  });
}
export function updateDataSetRules(dataSet) {
  return request({
    url: "/test/rules",
    method: "put",
    data: dataSet,
  });
}
export function addDataSetRules(dataSet) {
  return request({
    url: "/test/rules",
    method: "post",
    data: dataSet,
  });
}
export function delDataSetRules(ids) {
  return request({
    url: "/test/rules/" + ids,
    method: "delete",
  });
}

export function addDataSetRulesContent(dataContent) {
  return request({
    url: "/dataSet/content",
    method: "post",
    data: dataContent,
  });
}
export function getDataSetRulesContentById(id) {
  return request({
    url: "/dataSet/content/" + id,
    method: "get",
  });
}
export function updateDataSetRulesContent(dataContent) {
  return request({
    url: "/dataSet/content",
    method: "put",
    data: dataContent,
  });
}

export function delDataSetRulesContent(ids) {
  return request({
    url: "/dataSet/content/" + ids,
    method: "delete",
  });
}
