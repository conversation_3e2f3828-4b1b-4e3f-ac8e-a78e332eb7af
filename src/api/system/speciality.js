import request from '@/utils/request'

// 查询知识库专业配置列表
export function listSpeciality(query) {
  return request({
    url: '/test/speciality/list',
    method: 'get',
    params: query
  })
}

// 查询知识库专业配置详细
export function getSpeciality(id) {
  return request({
    url: '/test/speciality/' + id,
    method: 'get'
  })
}

// 新增知识库专业配置
export function addSpeciality(data) {
  return request({
    url: '/test/speciality',
    method: 'post',
    data: data
  })
}

// 修改知识库专业配置
export function updateSpeciality(data) {
  return request({
    url: '/test/speciality',
    method: 'put',
    data: data
  })
}

// 删除知识库专业配置
export function delSpeciality(id) {
  return request({
    url: '/test/speciality/' + id,
    method: 'delete'
  })
}
