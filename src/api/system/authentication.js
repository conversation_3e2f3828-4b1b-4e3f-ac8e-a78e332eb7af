
import request from '@/utils/request'

// 查询列表
export function getAuthenticationList(query) {
  return request({
    url: '/system/record/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getAuthenticationInfo() {
  return request({
    url: '/system/user/userDetails',
    method: 'get'
  })
}

//信息认证提交
export function saveAuthentication(data) {
  return request({
    url: '/system/user/updateAuth',
    method: 'put',
    data: data
  })
}


// 通过、驳回
export function audit(data) {
  return request({
    url: '/system/record',
    method: 'put',
    data: data
  })
}
// 查询院系信息
export function getUniversity(roleId) {
  return request({
    url: '/test/university/all/' + roleId,
    method: 'get',
  })
}

// 身份认证-发送验证码
export function send(data) {
  return request({
    url: '/system/user/sendrz',
    method: 'post',
    data: data
  })
}


//延期
export function postponeStatus(data) {
  return request({
    url: '/system/user/postponeStatus',
    method: 'put',
    data: data
  })
}


// 查询学生院系信息
export function getCollByStuId(query) {
  return request({
    url: '/test/execlStuTea/getCollByStuId',
    method: 'get',
    params: query

  })
}

// 查询老师院系信息
export function getCollByTeaId(query) {
  return request({
    url: '/test/execlStuTea/getCollByTeaId',
    method: 'get',
    params: query

  })
}


export function externalCertification(data) {
  return request({
    url: '/test/externalCertification/add',
    method: 'post',
    data: data
  })
}

export function checkStuOrTea(data) {
  return request({
    url: '/test/externalCertification/checkStuOrTea',
    method: 'post',
    data: data
  })
}
