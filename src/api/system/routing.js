import request from '@/utils/request'

// 查询知识库路由配置列表
export function listRouting(query) {
  return request({
    url: '/test/routing/list',
    method: 'get',
    params: query
  })
}

// 查询知识库路由配置详细
export function getRouting(id) {
  return request({
    url: '/test/routing/' + id,
    method: 'get'
  })
}

// 新增知识库路由配置
export function addRouting(data) {
  return request({
    url: '/test/routing',
    method: 'post',
    data: data
  })
}

// 修改知识库路由配置
export function updateRouting(data) {
  return request({
    url: '/test/routing',
    method: 'put',
    data: data
  })
}

// 删除知识库路由配置
export function delRouting(id) {
  return request({
    url: '/test/routing/' + id,
    method: 'delete'
  })
}
