import request from '@/utils/request'

// 保存用户语音角色
export function saveUserVoiceRole(query) {
    return request({
      url: '/test/voiceRole/save',
      method: 'post',
      params: query
    })
}

// 获取用户语音角色
export function getUserVoiceRole() {
  return request({
    url: '/test/voiceRole/getUserVoiceRole',
    method: 'get',

  })
}

// 银瑞语音合成后台接口
export function YRTTS(data) {
  return request({
    url: '/baidu/BaiDuSpeech/YRTTS',
    method: 'post',
    data: data
  })
}

// 豆包语音合成接口
export function DBTTS(data) {
  return request({
    url: '/baidu/BaiDuSpeech/DBTTS',
    method: 'post',
    data: data
  })
}

// 获取系统语音合成选择引擎
export function getSystemTTSChoose() {
  return request({
    url: '/test/voiceRole/getSystemTTSChoose',
    method: 'post',
  })
}
