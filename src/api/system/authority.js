import request from '@/utils/request'

// 查询鉴权信息列表
export function listInfor(query) {
  return request({
    url: '/test/infor/list',
    method: 'get',
    params: query
  })
}

// 查询鉴权信息详细
export function getInfor(id) {
  return request({
    url: '/test/infor/' + id,
    method: 'get'
  })
}

// 新增鉴权信息
export function addInfor(data) {
  return request({
    url: '/test/infor',
    method: 'post',
    data: data
  })
}

// 修改鉴权信息
export function updateInfor(data) {
  return request({
    url: '/test/infor',
    method: 'put',
    data: data
  })
}

// 删除鉴权信息
export function delInfor(id) {
  return request({
    url: '/test/infor/' + id,
    method: 'delete'
  })
}
