import request from '@/utils/request'

// 查询课程联盟列表
export function listUnionCourses(query) {
  return request({
    url: '/zhi/unionCourses/list2',
    method: 'get',
    params: query
  })
}

// 新增课程联盟
export function addUnionCourses(data) {
  return request({
    url: '/zhi/unionCourses',
    method: 'post',
    data: data
  })
}

// 修改课程联盟
export function updateUnionCourses(data) {
  return request({
    url: '/zhi/unionCourses',
    method: 'put',
    data: data
  })
}

// 删除课程联盟
export function delUnionCourses(id) {
  return request({
    url: '/zhi/unionCourses/' + id,
    method: 'delete'
  })
}

// 获取所有学校列表
export function getUniversityAll() {
  return request({
    url: '/test/university/getUniversityAll2',
    method: 'get'
  })
}

// 课程联盟图谱
export function getMapping(data) {
  return request({
    url: '/zhi/unionCourses/unionCourseMapping',
    method: 'post',
    data: data
  })
}

// 查询点击的教师信息
export function getTeacherInfo(query) {
  return request({
    url: '/zhi/unionCourses/unionCourseTeacher',
    method: 'get',
    params: query
  })
}

// 查询点击的教材信息
export function getBookInfo(query) {
  return request({
    url: '/zhi/unionCourses/unionCourseBook',
    method: 'get',
    params: query
  })
}

// 查询全部教材列表
export function getTextbookInfoList(query) {
  return request({
    url: '/zhi/unionCourses/unionCourseBookList',
    method: 'get',
    params: query
  })
}

// 查询全部教师列表
export function getTeacherInfoList(query) {
  return request({
    url: '/zhi/unionCourses/unionCourseTeacherList',
    method: 'get',
    params: query
  })
}
