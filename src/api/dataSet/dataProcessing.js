import request from '@/utils/request'

// 查询数据集处理列表
export function getDataProcessList(query) {
  return request({
    url: '/zhi/task/list',
    method: 'get',
    params: query
  })
}

// 查询数据集处理详细
export function getDataProcessInfo(id) {
  return request({
    url: '/zhi/task/' + id,
    method: 'get'
  })
}
// 新增数据集处理
export function addDataProcess(data) {
  return request({
    url: '/zhi/task',
    method: 'post',
    data: data
  })
}

// 删除数据集处理行
export function delDataProcess(ids) {
  return request({
    url: '/zhi/task/' + ids,
    method: 'delete'
  })
}

// 查询全部数据集
export function getAllDataSet(menuRouting) {
  return request({
    url: '/zhi/dataSet/all',
    method: 'post',
    data: menuRouting
  })
}
