import request from '@/utils/request'

// 查询数据集文件列表
export function listInfo(query) {
  return request({
    url: '/ragflow/info/list',
    method: 'get',
    params: query
  })
}

// 查询数据集文件详细
export function getInfo(id) {
  return request({
    url: '/ragflow/info/' + id,
    method: 'get'
  })
}

// 新增数据集文件
export function addInfo(data) {
  return request({
    url: '/ragflow/info',
    method: 'post',
    data: data
  })
}

// 修改数据集文件
export function updateInfo(data) {
  return request({
    url: '/ragflow/info',
    method: 'put',
    data: data
  })
}

// 删除数据集文件
export function delInfo(id) {
  return request({
    url: '/ragflow/info/' + id,
    method: 'delete'
  })
}
