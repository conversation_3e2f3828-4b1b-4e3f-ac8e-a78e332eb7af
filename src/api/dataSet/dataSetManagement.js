import request from '@/utils/request'

// 查询数据集管理列表
export function getDataSetList(query) {
  return request({
    url: '/zhi/dataSet/list',
    method: 'get',
    params: query
  })
}

// 查询数据集详细
export function getdataSet(query) {
  return request({
    url: '/zhi/information/list',
    method: 'get',
    params: query
  })
}
// 新增数据集
export function addDataSet(data) {
  return request({
    url: '/zhi/dataSet',
    method: 'post',
    data: data
  })
}

// 删除数据集
export function delDataSet(ids) {
  return request({
    url: '/zhi/dataSet/' + ids,
    method: 'delete'
  })
}
// 发布
export function release(data) {
  return request({
    url: '/zhi/dataSet/release',
    method: 'post',
    data: data
  })
}


//poductionToDataSet
export function poductionToDataSet(data) {
  return request({
    url: '/zhi/dataSet/poductionToDataSet',
    method: 'post',
    data: data
  })
}

// 查询当前登陆人是否是任意一课程的负责人
export function cheackChargeFlag() {
  return request({
    url: '/zhi/dataSet/cheackChargeFlag',
    method: 'post',
  })
}

// 查询当前登陆人是负责人的课程
export function cheackChargeClass(data) {
  return request({
    url: '/zhi/dataSet/cheackChargeClass',
    method: 'post',
    data: data
  })
}