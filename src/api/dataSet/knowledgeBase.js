import request from '@/utils/request'

// 知识库列表
export function getBaseList(query) {
  return request({
    url: '/zhi/base/list',
    method: 'get',
    params: query
  })
}


// why的api------------------------------------------------------------------------


// 新增应用场景知识库文件
export function addFile(data) {
  return request({
    url: '/zhi/file',
    method: 'post',
    data: data,
    //  headers: {
    //   'Content-Type': 'application/json;charset=UTF-8' 
    // }
  })
}


export function listFile(id, query) {
  return request({
    url: '/zhi/file/list/' + id, 
    method: 'get',
    params: query                
})
}



// 删除应用场景知识库文件
export function delKbFile(id) {
  return request({
    url: '/zhi/file/' + id,
    method: 'delete'
  })
}


// 新增提示提
export function addPrompt(data) {
  return request({
    url: '/zhi/prompt',
    method: 'post',
    data: data
  })
}

// 修改提示词
export function updatePrompt(data) {
  return request({
    url: '/zhi/prompt',
    method: 'put',
    data: data
  })
}





// ----------------------------------------------------------------------------------
// 教务处创建课程
export function addCourse(data) {
  return request({
    url: '/zhi/major',
    method: 'post',
    timeout: 300000,
    data: data
  })
}


// 3.根据文件id删除文件
export function delFile(ids) {
  return request({
    url: '/teacher/' + ids,
    method: 'delete'
  })
}

// why的api------------------------------------------------------------------------





// 专业课程列表
export function getdCourseList(query) {
  return request({
    url: '/zhi/teacher/knowledge/list',
    method: 'get',
    params: query
  })
}





// 教材列表
export function getKbfile(query) {
  return request({
    url: '/zhi/kbfile/knowledge/list',
    method: 'get',
    params: query
  })
}

// 课题组列表
export function getResearchGroupList(query) {
  return request({
    url: '/zhi/teacher/list',
    method: 'get',
    params: query
  })
}
// 添加课题组老师
export function addTeacher(data) {
  return request({
    url: '/zhi/teacher',
    method: 'post',
    data: data
  })
}

// 修改课题组老师
export function updateTeacher(data) {
  return request({
    url: '/zhi/teacher',
    method: 'put',
    data: data
  })
}

// 删除课题组老师
export function delTeacher(ids) {
  return request({
    url: '/zhi/teacher/' + ids,
    method: 'delete'
  })
}

// 文件解析
export function analyze(id) {
  return request({
    url: '/zhi/analysis/initiate/' + id,
    method: 'get'
  })
}

// 解析结果列表
export function getAnalysisList(query) {
  return request({
    url: '/zhi/analysis/list',
    method: 'get',
    params: query
  })
}
export function addAnalysis(data) {
  return request({
    url: '/zhi/analysis',
    method: 'post',
    data: data
  })
}
// 解析结果修改
export function updateAnalysis(data) {
  return request({
    url: '/zhi/analysis',
    method: 'put',
    data: data
  })
}

// 审核解析结果
export function checkAnalysis(data) {
  return request({
    url: '/zhi/analysis/submit',
    method: 'post',
    timeout: 300000,
    data: data
  })
}

// 删除解析结果
export function delAnalysisr(ids) {
  return request({
    url: '/zhi/analysis/' + ids,
    method: 'delete'
  })
}


// 知识图谱
export function getKnowledgeMapping(data) {
  return request({
    url: '/zhi/major/tbKnowledgeMapping',
    method: 'post',
    data: data
  })
}


// 知识图谱
export function getKb(data) {
  return request({
    url: '/test/textbookdata/tbKnowledgeMapping',
    method: 'post',
    data: data
  })
}

// 查询院系信息
export function getUniversity() {
  return request({
    url: '/test/university/getAll',
    method: 'get',
  })
}

// 解析结果列表
export function getKnowledgeInfoList(query) {
  return request({
    url: '/zhi/knowledgeInformation/list',
    method: 'get',
    params: query
  })
}


// 课程图谱
export function getcourseMapping(data) {
  return request({
    url: '/zhi/major/courseAtlas',
    method: 'post',
    data: data
  })
}

// 查询当前课程是否存在，存在是否为联盟课
export function getAllianceCourse(data) {
  return request({
    url: '/zhi/major/getAllianceCourse',
    method: 'post',
    data: data
  })
}

// 根据老师姓名查询老师信息
export function getTeacherinfo(data) {
  return request({
    url: '/system/teacherinfo/name',
    method: 'post',
    data: data
  })
}

// 提交发起审核
// export function submitAudit(data) {
//   return request({
//     url: '/zhi/kbfile/updataStatus',
//     method: 'post',
//     data: data
//   })
//
// }
//提交到解析结果
export function submitAudit(data) {
  return request({
    url: '/zhi/analysis/submit',
    method: 'post',
    data: data
  })

}
// 新知识图谱
export function getKnowledgeMappingNew(data) {
  return request({
    url: '/test/textbookdata/tbKnowledgeMappingNew',
    method: 'post',
    data: data
  })
}



export function initiateParsing(data) {
  return request({
    url: '/zhi/kbfile/initiate',
    method: 'post',
    data: data
  })

}


// 删除数据集处理行
export function delKnowledgeData(id) {
  return request({
    url: '/zhi/kbfile/' + id,
    method: 'delete'
  })
}


// 思维导图
export function getMindMapping() {
  return request({
    url: '/test/textbookdata/thinking',
    method: 'post',
  })
}

// 思维导图
export function getMindMappingById(id) {
  return request({
    url: '/test/textbookdata/keyword/' + id,
    method: 'get',
  })
}

// 统计图
export function getSatisticalChart() {
  return request({
    url: '/test/textbookdata/statistics',
    method: 'post',
  })
}
// 课程、教材、知识点 数量统计
export function getCount() {
  return request({
    url: '/zhi/teacher/count',
    method: 'post',
  })
}

// 知识点数量变化折线
export function getNumber() {
  return request({
    url: '/zhi/number/getNumber',
    method: 'get',
  })
}

