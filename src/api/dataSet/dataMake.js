import request from '@/utils/request'

// 查询数据集处理列表
export function getDataMakeList(query) {
  return request({
    url: '/zhi/poduction/list',
    method: 'get',
    params: query
  })
}

// 查询数据集处理详细
export function getDataMake(query) {
  return request({
    url: '/zhi/generate/list',
    method: 'get',
    params: query
  })
}
// 新增数据集处理
export function addDataMake(data) {
  return request({
    url: '/zhi/poduction',
    method: 'post',
    data: data
  })
}

// 删除数据集处理行
export function delDataMake(ids) {
  return request({
    url: '/zhi/poduction/' + ids,
    method: 'delete'
  })
}
// 删除数据集处理行
export function editDataMake(data) {
  return request({
    url: '/zhi/generate/' ,
    method: 'put',
    data: data
  })
}
// 删除数据集处理行
export function delDataGenerate(ids) {
  return request({
    url: '/zhi/generate/' + ids,
    method: 'delete'
  })
}
//---------------------
// 查询知识库信息列表
export function listSchool(query) {
  return request({
    url: '/zhi/rulesAndDiscipline/listS',
    method: 'get',
    params: query
  })
}

export function ruleOrAdd(params) {
  return request({
    url: '/zhi/rulesAndDiscipline/add',
    method: 'post',
    params: params
  })
}

export function initDocument(params) {
  return request({
    url: '/zhi/rulesAndDiscipline/initDocument',
    method: 'post',
    params: params
  })
}


// 查询知识库文件列表
export function listSchoolfile(query) {
  return request({
    url: '/zhi/schoolfile/list',
    method: 'get',
    params: query
  })
}

// 查询知识库文件详细
export function getSchoolfile(id) {
  return request({
    url: '/zhi/schoolfile/' + id,
    method: 'get'
  })
}

// 新增知识库文件
export function addSchoolfile(data) {
  return request({
    url: '/zhi/schoolfile',
    method: 'post',
    data: data
  })
}

// 修改知识库文件
export function updateSchoolfile(data) {
  return request({
    url: '/zhi/schoolfile',
    method: 'put',
    data: data
  })
}

// 删除知识库文件
export function delSchoolfile(id) {
  return request({
    url: '/zhi/schoolfile/' + id,
    method: 'delete'
  })
}
