import request from '@/utils/request'

// 查询数据集列表
export function listDatasets(query) {
  return request({
    url: '/ragflow/datasets/list',
    method: 'get',
    params: query
  })
}

// 查询数据集详细
export function getDatasets(datasetId) {
  return request({
    url: '/ragflow/datasets/' + datasetId,
    method: 'get'
  })
}

// 新增数据集
export function addDatasets(data) {
  return request({
    url: '/ragflow/datasets',
    method: 'post',
    data: data
  })
}

// 修改数据集
export function updateDatasets(data) {
  return request({
    url: '/ragflow/datasets',
    method: 'put',
    data: data
  })
}

// 删除数据集
export function delDatasets(datasetId) {
  return request({
    url: '/ragflow/datasets/' + datasetId,
    method: 'delete'
  })
}
