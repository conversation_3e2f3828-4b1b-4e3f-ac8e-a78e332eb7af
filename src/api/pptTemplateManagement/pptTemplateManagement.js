import request from '@/utils/request'

// 查询ppt模板管理列表
export function listPptTemplateManagement(query) {
  return request({
    url: '/ppt/pptTemplateManagement/list',
    method: 'get',
    params: query
  })
}

// 查询ppt模板管理详细
export function getPptTemplateManagement(id) {
  return request({
    url: '/ppt/pptTemplateManagement/' + id,
    method: 'get'
  })
}

// 新增ppt模板管理
export function addPptTemplateManagement(data) {
  return request({
    url: '/ppt/pptTemplateManagement',
    method: 'post',
    data: data
  })
}

// 修改ppt模板管理
export function updatePptTemplateManagement(data) {
  return request({
    url: '/ppt/pptTemplateManagement',
    method: 'put',
    data: data
  })
}

// 删除ppt模板管理
export function delPptTemplateManagement(id) {
  return request({
    url: '/ppt/pptTemplateManagement/' + id,
    method: 'delete'
  })
}

export function buildMapDataList(query, headers) {
  return request({
    url: '/ppt/pptTemplateManagement/buildMapDataList',
    method: 'get',
    params: query,
    ...(headers?.apiKey && {
      headers: {
        'api-key': headers.apiKey
      }
    })
  })
}



