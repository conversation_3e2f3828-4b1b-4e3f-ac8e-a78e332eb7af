import request from "@/utils/request";

export function getPromptTemplateList(queryParams) {
  return request({
    url: "/test/template/list",
    method: "get",
    params: queryParams,
  });
}

export function getPromptTemplate(id) {
  return request({
    url: "/test/template/" + id,
    method: "get",
  });
}
export function addPromptTemplate(datas) {
    return request({
      url: '/test/template',
      method: 'post',
      data: datas
    })
  }
  export function updatePromptTemplate(datas) {
    return request({
      url: "/test/template",
      method: "put",
      data: datas,
    });
  }
  export function delPromptTemplate(ids) {
    return request({
      url: "/test/template/" + ids,
      method: "delete",
    });
  }


// export function getBusinessType() {
//   return request({
//     url: "/test/type/user",
//     method: "get",
//   });
// }

