import request from '@/utils/request'

// 查询课程管理列表
export function getCourseManagementList(query) {
  return request({
    url: '/test/courseManagement/list',
    method: 'get',
    params: query
  })
}

// 查询课程详细
export function getCourseInfo(query) {
  return request({
    url: '/test/courseManagement/listDetails',
    method: 'get',
    params: query
  })
}
// 新增课程管理
export function addCourseName(data) {
  return request({
    url: '/test/courseManagement/addCourseName',
    method: 'post',
    data: data
  })
}

// 新增课程
export function addCourseManagement(data) {
  return request({
    url: '/test/courseManagement',
    method: 'post',
    data: data
  })
}
// 新增课程
export function deleteManagement(data) {
  return request({
    url: '/test/courseManagement/deleteCourseName',
    method: 'delete',
    data: data
  })
}

// 查询当前登陆人参与的课程
export function cheackChargeClass(data) {
  return request({
    url: '/zhi/dataSet/cheackChargeClass',
    method: 'post',
    data: data
  })
}
// 删除课程
export function deleteCourses(query) {
  return request({
    url: 'test/courseManagement/del',
    method: 'delete',
    params: query
  })
}

// 删除班级
export function deleteCourse(query) {
  return request({
    url: 'test/courseManagement',
    method: 'delete',
    params: query
  })
}

export function getDictData(dictCode) {
  return request({
    url: '/system/dict/data/type/' + dictCode,
    method: 'GET',
  })
}

// 查询教案、资料列表
export function getMaterialList(query) {
  return request({
    url: '/test/courseMaterials/list2',
    method: 'get',
    params: query
  })
}

// 新增教案、资料
export function addTeacherPlan(data) {
  return request({
    url: '/test/courseMaterials',
    method: 'post',
    data: data
  })
}

// 删除教案、资料
export function delMaterials(ids) {
  return request({
    url: '/test/courseMaterials/' + ids,
    method: 'delete'
  })
}

// 查询通知列表
export function getMessageList(query) {
  return request({
    url: '/test/courseNotifications/list2',
    method: 'get',
    params: query
  })
}

// 新增通知
export function addMessage(data) {
  return request({
    url: '/test/courseNotifications',
    method: 'post',
    data: data
  })
}

// 删除通知
export function delMessage(ids) {
  return request({
    url: '/test/courseNotifications/' + ids,
    method: 'delete'
  })
}

// 查询讨论列表
export function getDiscussList(query) {
  return request({
    url: '/test/courseTopics/list2',
    method: 'get',
    params: query
  })
}

// 新增讨论
export function addDiscuss(data) {
  return request({
    url: '/test/courseTopics',
    method: 'post',
    data: data
  })
}

// 删除讨论
export function delDiscuss(ids) {
  return request({
    url: '/test/courseTopics/' + ids,
    method: 'delete'
  })
}

// 单门学生课程统计
export function getStudentCourseStatistics(query) {
  return request({
    url: '/test/classStudentCourseReport/getSingleCourseAnalysis',
    method: 'get',
    params: query,
    timeout: 300000,
  })
}

// 班级课程分析统计
export function getClassCourseAnalysis(query) {
  return request({
    url: '/test/classStudentCourseReport/getClassCourseAnalysis',
    method: 'get',
    params: query,
    timeout: 300000,
  })
}

// 查询课程视频列表
export function getVideoList(query) {
  return request({
    url: '/test/video/list',
    method: 'get',
    params: query
  })
}

// 新增课程视频
export function addVideo(data) {
  return request({
    url: '/test/video',
    method: 'post',
    data: data
  })
}

// 删除课程视频
export function delVideo(ids) {
  return request({
    url: '/test/video/' + ids,
    method: 'delete'
  })
}

// 查询课程案例列表
export function getCaseList(query) {
  return request({
    url: '/test/case/list',
    method: 'get',
    params: query
  })
}

// 新增课程案例
export function addCase(data) {
  return request({
    url: '/test/case',
    method: 'post',
    data: data
  })
}

// 删除课程案例
export function delCase(ids) {
  return request({
    url: '/test/case/' + ids,
    method: 'delete'
  })
}

// 查询课程习题列表
export function getExercisesList(query) {
  return request({
    url: '/test/exercises/list',
    method: 'get',
    params: query
  })
}

// 新增课程习题
export function addExercises(data) {
  return request({
    url: '/test/exercises',
    method: 'post',
    data: data
  })
}

// 删除课程习题
export function delExercises(ids) {
  return request({
    url: '/test/exercises/' + ids,
    method: 'delete'
  })
}

// 查询课程习题列表
export function getStudentExercisesList(query) {
  return request({
    url: '/test/exercises/student/list',
    method: 'get',
    params: query
  })
}

// 查询课程案例列表
export function getStudentCaseList(query) {
  return request({
    url: '/test/case/student/list',
    method: 'get',
    params: query
  })
}

// 查询课程视频列表
export function getStudentVideoList(query) {
  return request({
    url: '/test/video/student/list',
    method: 'get',
    params: query
  })
}


// 查询签到列表
export function getSignInList(query) {
  return request({
    url: '/test/courseClass/list',
    method: 'get',
    params: query
  })
}

// 发起签到
export function addSignIn(data) {
  return request({
    url: '/test/courseClass/initiateAttendance',
    method: 'post',
    data: data
  })
}

// 班级签到情况
export function getSignInHisInfo(query) {
  return request({
    url: '/test/studentsign/list',
    method: 'get',
    params: query
  })
}

// 课程统计
export function courseStatistics(data) {
  return request({
    url: '/test/courseManagement/courseStatistics',
    method: 'post',
    data: data
  })
}
