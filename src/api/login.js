import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: { username, password, code, uuid }
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    timeout:300000,
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 修改密码-发送验证码
export function send(data) {
  return request({
    url: '/system/user/send',
    method: 'post',
    data: data
  })
}

// 修改密码
export function revise(data) {
  return request({
    url: '/system/user/revise',
    method: 'post',
    data: data
  })
}

export function externalAccess(query) {
  return request({
    url: '/auth/externalAccess',
    method: 'get',
    params: query
  })
}


export function casLogin(query) {
  return request({
    url: '/auth/casLogin',
    method: 'get',
    params: query
  })
}


export function getTokenByUserCode(data) {
  return request({
    url: '/auth/getTokenByUserCode',
    method: 'post',
    data: data
  })
}


// CAS登出
export function casLogout(query) {
  return request({
    url: '/auth/casLogout',
    method: 'get',
    params: query
  })
}

