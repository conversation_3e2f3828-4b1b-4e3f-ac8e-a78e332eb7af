import request from '@/utils/request'

// 查询key_secret管理列表
export function listManage(query) {
  return request({
    url: '/system/keySecretManage/list',
    method: 'get',
    params: query
  })
}

// 查询key_secret管理详细
export function getManage(id) {
  return request({
    url: '/system/keySecretManage/' + id,
    method: 'get'
  })
}

// 新增key_secret管理
export function addManage(data) {
  return request({
    url: '/system/keySecretManage',
    method: 'post',
    data: data
  })
}

// 修改key_secret管理
export function updateManage(data) {
  return request({
    url: '/system/keySecretManage',
    method: 'put',
    data: data
  })
}

// 删除key_secret管理
export function delManage(id) {
  return request({
    url: '/system/keySecretManage/' + id,
    method: 'delete'
  })
}

// 更新 key_secret 缓存管理
export function setCacheApiKey() {
  return request({
    url: '/system/keySecretManage/setCacheApiKey',
    method: 'get',
  })
}

// 验证 api_key
export function checkApiKey(query) {
  return request({
    url: '/system/keySecretManage/checkApiKey',
    method: 'get',
    params: query,
    headers: {
      'api_key': 'application/json'
    }
  })
}
// 生成 api_key api_secret
export function createApiKeySecretApi() {
  return request({
    url: '/system/keySecretManage/createApiKeySecret',
    method: 'get',
  })
}
