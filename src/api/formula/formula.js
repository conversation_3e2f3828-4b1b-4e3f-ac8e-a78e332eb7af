import request from '@/utils/request'

// 查询公式图片列表
export function listFormula(query) {
  return request({
    url: '/intelligent/formula/list',
    method: 'get',
    params: query
  })
}

// 查询公式图片详细
export function getFormula(id) {
  return request({
    url: '/intelligent/formula/' + id,
    method: 'get'
  })
}

// 新增公式图片
export function addFormula(data) {
  return request({
    url: '/intelligent/formula/add',
    method: 'post',
    data: data
  })
}

// 修改公式图片
export function updateFormula(data) {
  return request({
    url: '/intelligent/formula',
    method: 'put',
    data: data
  })
}

// 删除公式图片
export function delFormula(id) {
  return request({
    url: '/intelligent/formula/' + id,
    method: 'delete'
  })
}

export function upload(id) {
  return request({
    url: '/intelligent/formula/uploadPath',
    method: 'post'
  })
}

export function getImageByPath(data) {
  return request({
    url: '/file/file/previewPath',
    method: 'get',
    params: data,
    responseType: 'blob', // 确保返回的是blob数据
  })
}

export function checkHaveAdd() {
  return request({
    url: '/intelligent/formula/checkHaveAdd',
    method: 'get',
  })
}
