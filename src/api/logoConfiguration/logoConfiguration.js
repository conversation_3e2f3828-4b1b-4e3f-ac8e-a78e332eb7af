import request from '@/utils/request'

// logo列表
export function getLogoList(query) {
  return request({
    url: '/test/logosconfig/list',
    method: 'get',
    params: query
  })
}

// 新增logo
export function addLogo(data) {
  return request({
    url: '/test/logosconfig',
    method: 'post',
    data: data
  })
}
// 修改logo
export function updateLogo(data) {
  return request({
    url: '/test/logosconfig',
    method: 'put',
    data: data
  })
}
// 删除logo
export function delLogo(ids) {
  return request({
    url: '/test/logosconfig/' + ids,
    method: 'delete'
  })
}

// 获取某个logo
export function getLogos(logoPosition) {
  return request({
    url: '/test/logosconfig/listAll/' + logoPosition,
    method: 'get',
  })
}

// 获取学校名
export function getSchoolName() {
  return request({
    url: '/test/circularbannerconfig/selUniverName',
    method: 'get',
  })
}