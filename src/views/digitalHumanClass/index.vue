<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程" prop="lessonName">
        <el-input v-model="queryParams.lessonName" placeholder="请输入课程名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="数据库编号"  prop="id" v-if="false"></el-table-column>
      <el-table-column label="课程名称" prop="lessonName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="课程书籍" prop="textbookName" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button :disabled="scope.row.commitStatus == 1" size="mini" type="text" icon="el-icon-position"
                     @click="handleClass()">去课堂</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getLesson" />
  </div>
</template>
<script>
import { getLesson } from "@/api/digitalHumanClass/lessonData.js"

export default {
  name: "digitalHumanClass",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [{ lessonName: "123" }],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lessonName: undefined,
      },
    };
  },
  created() {
    this.getLesson();
  },
  activated() {

  },
  methods: {
    getLesson(){
      // console.log(this.queryParams)
      getLesson(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // console.log(this.queryParams.lessonName)
      this.queryParams.pageNum = 1;
      this.getLesson();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleClass() {
      this.$router.push({
        path: "/digitalHumanClass/courseList",
      });
    },
  },
};
</script>
