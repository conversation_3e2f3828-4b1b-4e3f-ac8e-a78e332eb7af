<template>
  <div class="app-container ck-container">
    <div class="imgbox">
      <img class="img" :src="require(`@/assets/szr/${item.img}`)" alt="" v-for="(item, index) in imglist" :key="index" v-show="v==index?true:false">
    </div>
    <div class="right-container">
      <div class="answer-box" id="answer-box" ref="scrollContainer">
        <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
                justify="center">
          <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
          <div v-if="item.issue == 'assistant'" style="width:100%;">
            <div
              style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
              <img v-if="item.imageUrl" :src="item.imageUrl" alt="Dialogue Image"
                style="max-width: 100%; height: auto;">
              <div style="width: 100%">
                <el-button style="float: right;margin: 5px;" size="mini" round @click="stopAudio">停止播放</el-button>
                <el-button style="float: right;margin: 5px;" size="mini" round
                  @click="playAudio(item.content)">开始播放</el-button>
              </div>
            </div>
          </div>
          <div v-if="item.issue == 'user'" style="width:100%;">
            <div
              style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
            </div>
          </div>
          <!-- <el-input v-model=" item.content" type="textarea" autosize readonly /> -->
          <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;" />

        </el-row>

        <!-- <div>
          <el-button style="margin: 5px;" size="mini" round :icon="playFlag?'el-icon-bell':'el-icon-close-notification'"
            @click="handlePlay">
            {{isplaying ?  '停止': '播放'}}
          </el-button>
        </div> -->
      </div>
      <div class="ask-box">
        <!-- <el-input v-model="content" type="textarea" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" id="askBox"  :autosize="{ minRows: 6, maxRows: 6}" maxlength="1500"
        @keydown.native="handleKeyCode($event)" show-word-limit style="top:30px;" />
        <el-button type="primary" id="askButton" class="ask-button" size="mini" round icon="el-icon-s-promotion" @click="ask" />
        <el-button class="talk-button" size="mini" round
        :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'" @click="translation">
          {{translationFlag ? '停止' : '语音'}}
        </el-button> -->
        <div
          style="border:1px solid #a4a4a4;padding: 10px 10px;background-color: #fff;border-radius: 12px ;">
          <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'" placement="top">
            <el-button style="margin: 5px;" size="mini" round
              :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'" @click="translation">
              {{translationFlag ? '停止' : '语音'}}
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="light" :content="playFlag ?  '点击停止播放': '点击播放'" placement="top">
            <el-button style="margin: 5px;" size="mini" round @click="autoPlay">
              <svg-icon :icon-class="playFlag ?  'voice': 'voiceClose'" />
              {{playFlag ?  ' 停播': ' 播放'}}
            </el-button>
          </el-tooltip>
          <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
            show-word-limit @keydown.native="handleKeyCode($event)" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
        </div>
        <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"
          @click="sendQuestion" :loading="loadSendBtn" />
      </div>
    </div>
    <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio" @ended="continuePlay"></audio>
  </div>



</template>

<script>
  import asks from "@/assets/logo/asks.png";
  import user from "@/assets/logo/user.png";
  import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
  import IatRecorder from '@/assets/js/IatRecorder.js'
  import Knowengine from '@/assets/js/knowengine.js'
  import {
    getDialogueList,
    getPromptList,
    addDialogue,
    updateDialogue,
    delDialogue,
    getDialogue,
    getBaiDuToken,
  } from "@/api/explorationCenter/experience.js";
  import {
    getUserVoiceRole,
  } from "@/api/system/voiceRole.js";
  import axios from "axios";
  import qs from "qs";
  const ttsRecorder = new TtsRecorder();
  const iatRecorder = new IatRecorder('en_us', 'mandarin', '5f27b6a9');//
  const knowengine = new Knowengine();
  import VueSocketIO from "vue-socket.io";
  import SocketIO from 'socket.io-client';
  import store from '@/store'
  import { socket } from "@/socket";
  import Cookies from "js-cookie";
  export default {
    name: "Operlog",
    mounted() {

    },
    data() {
      return {
        v:0,
        pptIndex: 2,
        motionlist:[
          {dir:"nomotion/",max_index:16,interval_time:50},
          {dir:"righthand/",max_index:29,interval_time:100},
          {dir:"finger/",max_index:50},
          {dir:"openhands/",max_index:46},
        ],
        currentMotionIndex: 0,
        curMotionMaxIndex:0,
        imglist:[
        ],
        // 非多个禁用
        multiple: true,
        //是否正在播放语音
        isplaying: false,
        intervalId_img: null,
        //动作播放速度
        interval_time: 0,
        //无动作说话循环次数
        nomotion_order:0,
        //无动作说话最大循环次数
        nomotion_max_order:6,
        //当前动作是否播放完成
        curMotionIsDone: false,
        asks: asks,
        user: user,
        invocation: "model",
        hisList: [],
        menuRouting: "",
        dialogueResult: {content:'正在回答，请稍等···'},
        dialogueList: [],
        dialogueNum: true,
        loadSendBtn: false,
        content: "",
        id: "",
        routePath: this.$route.path,
        playFlag : false,
        translationFlag: false,
        per: "1",
        token : "",
        audioArr : [],
        segments : [],
        currentIndex : 0,
        isPlayAudio : false,
        isPlayFinish : false,
        isStopFinsh : true,
        createSocketIO: null,
        createSocketEmitter: null,
        socket: null,
        socketStatus: false,
        speed: 5,
        pitch: 5,
        volum: 5
      };
    },
    created() {
      this.initMotion();
      this.intervalId_img = setInterval(() => {
        if(this.isplaying) {
          if(this.v >= this.curMotionMaxIndex){
            //切换动作
            this.changeMotion();
            this.v=0
          }else{
            this.v++
          }
        }

      }, this.interval_time);

      //获取当前用户对话ID
      //this.id = this.getDialogueId();

      this.handleReview();//根据ID获取对话记录

      this.getVoiceRole();

      knowengine.start();

    },
    beforeDestroy() {
      //关闭页面前停止
      this.clearAudio();

      // 清除定时器
      if (this.intervalId_img) {
        clearInterval(this.intervalId_img);
      }

      //订阅事件记得要取消
      // if (this.createSocketIO) {
      //   this.createSocketEmitter.removeListener('msgContent', this)
      //   this.createSocketIO.close()
      // }

      // 组件销毁前断开连接
      // if (this.socket.connected) {
      //       this.socket.disconnect();
      //     }

      knowengine.close();

    },
    computed :{
      computedAudioSrc() {
        return this.audioArr[this.currentIndex];
      },
    },
    watch: {
      computedAudioSrc(newSrc) {
        if (newSrc && this.isPlayAudio && this.playFlag) {
          this.$refs.audio.addEventListener(
            'loadeddata',
            () => {
              this.$refs.audio.play();
              this.isplaying = true;
            },
            { once: true }
          );
        }
      },
      dialogueList: {
        deep: true,
        handler() {
          this.scrollToBottom();
        }
      }
    },
    methods: {
      async addDialogue2(data) {
        const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/addLiu_Direct', {
          method: 'POST',
          headers: {
            Authorization: "Bearer " + getToken(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        let num1 = 1;
        let imageUrls = [];
        // 使用ReadableStream处理流式数据
        const reader = response.body.getReader();
        let done = false;
        try {
          this.dialogueList[this.dialogueList.length - 1].content = "";
          let s = "";
          while (!done) {
            const { value, done: isDone } = await reader.read();
            if (isDone) {
              done = true;
              this.loadSendBtn = false;
              this.dialogueNum = false;
              this.getDialogueList();
              if (this.playFlag && !this.txtToImage) {
                this.playAudio(s);
              }
            } else {
              // 将接收到的数据转换为字符串
              const str = new TextDecoder('utf-8').decode(value);
              s += str;

              const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
              let match;
              while ((match = imageRegex.exec(str)) !== null) {
                const imagePath = match[2] || match[1]; // 获取匹配的图片路径
                imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
              }

              // 更新对话内容，排除图片路径
              const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
                return ''; // 替换匹配的部分为空字符串
              });

              // 移除多余的符号
              const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
              this.$nextTick(() => {
                this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              });
            }

          }
        } finally {
          reader.releaseLock(); // 确保在循环结束时释放锁
        }
      },
      getImgUrl(pptPath) {
        let baseUrl = window.location.origin
        var imgData;
        if (pptPath.includes('/ruoyi/')) {
          // 替换路径中的 ruoyi/ 之前的部分
          const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

          if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
            imgData = 'http://127.0.0.1:9215' + finalPath
            // console.log('Final baseUrl:', baseUrl)
          } else {
            imgData = baseUrl + finalPath
          }
        }
        return imgData;

      },

      async updateDialogue2(data) {
        const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/updateLiu', {
          method: 'POST',
          headers: {
            Authorization: "Bearer " + getToken(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        let num1 = 1;
        let imageUrls = [];
        // 使用ReadableStream处理流式数据
        const reader = response.body.getReader();
        let done = false;
        try {
          this.dialogueList[this.dialogueList.length - 1].content = "";
          let s = "";
          while (!done) {
            const { value, done: isDone } = await reader.read();
            if (isDone) {
              done = true;
              this.loadSendBtn = false;
              this.dialogueNum = false;
              if (this.playFlag && !this.txtToImage) {
                this.playAudio(s);
              }
              this.handleReview({ id: this.id })
            } else {
              // 将接收到的数据转换为字符串
              const str = new TextDecoder('utf-8').decode(value);
              s += str;

              const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
              let match;
              while ((match = imageRegex.exec(str)) !== null) {
                const imagePath = match[2] || match[1]; // 获取匹配的图片路径
                imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
              }

              // 更新对话内容，排除图片路径
              const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
                return ''; // 替换匹配的部分为空字符串
              });

              // 移除多余的符号
              const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
              this.$nextTick(() => {
                this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              });
            }
          }
        } finally {
          reader.releaseLock(); // 确保在循环结束时释放锁
        }
      },
      scrollToBottom() {
        this.$nextTick(() => {
          const scrollContainer = this.$refs.scrollContainer;
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        });
      },
      //初始化动作
      initMotion() {
        // console.log("aaaaaaaaaaaaaaaaaaaaaaaa")
        let max_index = this.motionlist[this.currentMotionIndex].max_index;
        let imglist = [];
        for(var i = 0; i <= max_index; i++) {
          imglist.push({id: i,img:this.motionlist[this.currentMotionIndex].dir+ i +".jpg"});
        }
        this.imglist = imglist;
        this.curMotionMaxIndex = max_index;
        this.interval_time = this.motionlist[this.currentMotionIndex].interval_time;
      },
      //切换动作
      changeMotion() {

        //设置要切换的动作ID
        let motionIndex = 0;
        //如果是无动作说话，且循环次数小于5次，依然播放该动作；否则切换为下一个动作
        if(this.currentMotionIndex == 0 && this.nomotion_order < this.nomotion_max_order) {
          motionIndex = 0;
        } else {
          motionIndex = this.currentMotionIndex == 1 ? 0 : this.currentMotionIndex + 1;
          this.nomotion_order = 0;
        }
        //当前动作的最大帧数
        let max_index = this.motionlist[motionIndex].max_index;
        //更新动作帧
        this.imglist = [{"id":0,img:"nomotion/0.png"}];
        for(var i = 0; i <= max_index; i++) {
          this.imglist.push({id: i,img:this.motionlist[motionIndex].dir+ i +".jpg"});
        }
        this.imglist.shift();
        //this.imglist = imglist;
        //this.v = 0;
        //更新当前动作ID
        this.currentMotionIndex = motionIndex;
        //更新当前动作的最大帧数
        this.curMotionMaxIndex = max_index;
        //更新动作播放速度
        this.interval_time = this.motionlist[this.currentMotionIndex].interval_time;
        //当前动作播放状态
        this.curMotionIsDone = false;

        //清除定时器
        if (this.intervalId_img) {
          clearInterval(this.intervalId_img);
        }
        //设置定时器
        this.intervalId_img = setInterval(() => {
          //如果语音正在播放
          if(this.isplaying ) {
            if(this.v >= this.curMotionMaxIndex){
              //如果是无动作说话，且循环次数小于4次，则继续循环，循环次数+1
              if(this.currentMotionIndex == 0 && this.nomotion_order < this.nomotion_max_order) {
                this.nomotion_order ++;
              }

              this.curMotionIsDone = true;
              this.changeMotion();
              this.v = 0;
            }else{
              this.v++;
            }
          } else if(!this.curMotionIsDone) { //如果语音播放完成但动作还未播放完成，则动作继续播放至结束
            if(this.v < this.curMotionMaxIndex){
              this.v++;
            } else {
              this.curMotionIsDone = true;
              this.v = 0;
            }
          }

        }, this.interval_time);

      },

      // 获取当前用户发言人id
      getVoiceRole(){
        getUserVoiceRole().then(res => {
          if(res.data != null){
            this.per = res.data.voiceRoleId;
            this.speed = res.data.voiceSpeed;
            this.pitch = res.data.voicePitch;
            this.volume = res.data.voiceVolume;
            this.playFlag = res.data.userPlayflag;
          }
        })
      },
      // 自动播放
      autoPlay() {
        if(this.playFlag) {
          this.playFlag = !this.playFlag;
          this.isPlayAudio = false
          this.$refs.audio.pause();
          this.clearAudio();
          this.isplaying = false
        } else {
          this.playFlag = !this.playFlag;
          this.isPlayAudio = true
        }
      },
      async stopAudio(){
        if(this.isStopFinsh){
          this.isStopFinsh = false;
          this.$refs.audio.pause();
          this.isplaying = false;
          this.isPlayAudio = false;
          this.isPlayFinish = true;
          this.clearAudio();
        }else{
          setTimeout(() => {
            this.isStopFinsh = true;
          }, 500);
        }
      },
      async playAudio(content) {
        if(this.isPlayAudio){
          return;
        }
        this.clearAudio()
        this.isPlayAudio = true
        const res = await getBaiDuToken();
        this.token = res.token;
        this.isPlayFinish = false
        this.textToAudio2(content, this.token)
        this.currentIndex = 0
        await this.$refs.audio.play()
        this.isplaying = true;
        this.isStopFinsh = true
      },
      //每段音频结束后调用此函数播放下一段
      continuePlay() {
        this.currentIndex++;
        if (this.currentIndex < this.audioArr.length) {
          setTimeout(() => {
            this.$refs.audio.load();
            this.$refs.audio.play().catch((error) => {
              console.error('Failed to play:', error);
            });
          }, 100);
        } else {
          this.isplaying = false;
        }
      },

      translation() {
        if (this.translationFlag) {
          iatRecorder.stop();
          this.translationFlag = false;
        } else {
          iatRecorder.start()
          this.translationFlag = true;
          iatRecorder.onTextChange = (text) => {
            let inputText = text;
            this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
            console.log(this.content);
          };
        }
      },
      sendQuestion() {
        if(!this.content || this.content == '') {
          this.$message.error('请先输入您的问题');
          return false;
        }
        this.loadSendBtn = true;
        this.dialogueList.push({ content: this.content, issue: "user" });
        this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
        // var parent = document.getElementById('answer-box');
        // // 滚动到最下方
        // parent.scrollTop = parent.scrollHeight - parent.clientHeight;

        this.token = getBaiDuToken().then(res => {
          this.token = res.token;
        }).catch(error => {
          console.error('Failed to get token:', error);
        });

        knowengine.webSocketSend(this.content);
        this.content = '';

        let answer = '';
        let len = 1;
        knowengine.onTextChange = (text) => {
          //console.log("step="+knowengine.step);
          if(text) {
            let inputText = text;
            //inputText = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
            //console.log(inputText);
            this.dialogueList[this.dialogueList.length-1].content = inputText;

            if(this.playFlag) {
              let step = knowengine.step;
              let finish = knowengine.finish
              answer = answer + step;
              if(len == 4) {
                //console.log(answer)
                this.textToAudio2(answer, this.token)
                len = 1;
                answer = '';
              } else {
                len++;
                if(finish && finish == 'yes' && answer) {
                  //console.log("-=-=-=-=-=-=")
                  this.textToAudio2(answer, this.token)
                }
              }
            }
          }

        };
        //如果最后不够4段，会不播放，在这里判断，如果answer有值，播放
        // if(this.playFlag && answer) {
        //   this.textToAudio2(answer, this.token)
        // }

        this.loadSendBtn = false;

      },
      async getId() {
        await getId()
          .then((res) => {
            if (res.code === 200) {
              this.id = res.data;
            }
          })
          .catch((err) => {
            this.loadSendBtn = false;
          });
      },
      async ask() {
        if (!this.content || this.content == '') {
          this.$message.error('请先输入您的问题');
          return false;
        }
        this.loadSendBtn = true;
        this.dialogueList.push({content: this.content, issue: "user"});
        this.dialogueList.push({content: '正在生成，请稍等···', issue: "assistant"});

        if (this.dialogueNum) {
          await this.getId();
          const param = {
            invocation: this.invocation,
            //promptId: this.promptId,
            content: this.content,
            id: this.id,
            language: Cookies.get("voiceType"),
            menuRouting: this.routePath + "/",
          };

          this.token = getBaiDuToken().then(res => {
            this.token = res.token;
          }).catch(error => {
            console.error('Failed to get token:', error);
          });
          this.content = ''
          await this.addDialogue2(param).catch(error => {
            console.error('添加对话时出错:', error);
          });
        } else {
          const param = {
            invocation: this.invocation,
            //promptId: this.promptId,
            content: this.content,
            id: this.id,
            language: Cookies.get("voiceType"),
            menuRouting: this.routePath + "/",
          };

          this.content = ''
          this.updateDialogue2(param)
            .catch(error => console.error('Error:', error));
        }

      },
      handleKeyCode(event) {
        if (event.keyCode == 13 && event.ctrlKey) {
          this.content += "\n";
        } else if (event.keyCode == 13) {
          event.preventDefault();
          this.sendQuestion()
        }
      },
      getDialogueList() {
        const param = {
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          invocation: this.invocation,
        };
        getDialogueList(param).then((res) => {
          this.hisList = res.data;
        });
      },
      handleReview() {
        this.dialogueList = [
            {
                "createBy": null,
                "createTime": null,
                "updateBy": null,
                "updateTime": null,
                "remark": null,
                "id": 556,
                "dialogueId": ****************,
                "orderIn": 2,
                "issue": "assistant",
                "content": "您好，欢迎来到知识探索的殿堂！我是AI·CAI大模型精心打造的数字讲师，专注于公共管理学领域的深度解析。无论您是对公共管理的基本概念感到好奇，还是希望深入了解政策制定、公共服务、组织管理等核心议题，亦或是对当前公共管理领域的热点话题与未来趋势抱有浓厚兴趣，都欢迎您向我提问。我将运用我的专业知识与广泛资源，竭诚为您解答，共同探索公共管理学的奥秘与魅力。",
                "prompt": null,
                "processing": null
            }
        ]
        // getDialogue(this.id).then((res) => {
        //   this.dialogueList = res.data.dialogueDetailsList;
        //   for (let i = 0; i < this.dialogueList.length; i++) {
        //     if (this.dialogueList[i].processing != null) {
        //       const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`;
        //       this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrl: imageData });
        //     }
        //   }
        //   this.dialogueNum = false;
        // });
      },
      // 拆分文本
      splitTextByPunctuation(text, maxLength) {
        // 定义标点符号和次级标点符号的正则表达式
        const punctuation = /[。！？；]/;
        const secondaryPunctuation = /[ ，]/;

        let result = [];

        while (text.length > 0) {
          // 匹配文本中的主要标点符号
          let match = punctuation.exec(text);

          // 如果找到标点符号
          if (match) {
            let segment = text.slice(0, match.index + 1);

            // 如果当前片段长度不超过最大长度
            if (segment.length <= maxLength) {
              if (segment.trim().length > 0) {
                result.push(segment.trim());
              }
              text = text.slice(match.index + 1);
            } else {
              // 如果当前片段长度超过最大长度
              while (segment.length > maxLength) {
                // 查找次级标点符号
                let secondaryMatch = secondaryPunctuation.exec(segment);

                if (secondaryMatch && secondaryMatch.index < maxLength) {
                  let subSegment = segment.slice(0, secondaryMatch.index + 1);
                  if (subSegment.trim().length > 0) {
                    result.push(subSegment.trim());
                  }
                  segment = segment.slice(secondaryMatch.index + 1);
                } else {
                  let toAdd = segment.slice(0, maxLength).trim();
                  if (toAdd.length > 0) {
                    result.push(toAdd);
                  }
                  segment = segment.slice(maxLength);
                }
              }

              // 处理最后剩下的部分
              if (segment.trim().length > 0) {
                result.push(segment.trim());
              }
              text = text.slice(match.index + 1);
            }
          } else {
            // 如果没有找到标点符号，直接按最大长度分割文本
            while (text.length > maxLength) {
              let part = text.slice(0, maxLength).trim();
              if (part.length > 0) {
                result.push(part);
              }
              text = text.slice(maxLength);
            }
            // 处理最后剩下的部分
            if (text.trim().length > 0) {
              result.push(text.trim());
            }
            text = "";
          }
        }

        return result;
      },
      // 文本转语音
      async textToAudio (text,token) {
        // if(this.isPlayFinish){
        //   break;
        // }
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          responseType: "blob",
        });
        console.log(res.data)
        this.audioArr.push(URL.createObjectURL(res.data));

      },
      async textToAudio2 (text,token) {
        this.segments = this.splitTextByPunctuation(text,55)
        for(const text of this.segments){

          const option = {
            tex: text,
            tok: token,
            cuid: `${Math.floor(Math.random() * 1000000)}`,
            ctp: "1",
            lan: "zh",
            per: this.per,
            spd: this.speed,
            pit: this.pitch,
            vol: this.volume
          };

          const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            responseType: "blob",
          });
          // console.log(res.data)
          if(this.isPlayFinish){
            // console.log("结束00赋值")
            this.clearAudio()
            return;
          }
          this.audioArr.push(URL.createObjectURL(res.data));
            //this.audioSrc = URL.createObjectURL(res.data);
        }
        // this.isPlayFinish = true
      },
      clearAudio() {
        this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
        this.currentIndex = 0
        this.audioArr = []
      },
    }
};
</script>
<style lang="scss" scoped>
  .ck-container {
    margin: 0 auto;
    width: 100%;
    height: calc(100vh - 84px);
    background-size: 100% 100%;
    display: flex;
    .imgbox {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .img {
        height: 100%;
      }
    }
    .right-container {
      background: #dbe9f740;
      flex: 1;
      border-radius: 10px;
      margin-left: 20px;
      .answer-box {
        height: 72%;
        overflow-y: auto;
        border-bottom: 1px solid #e8e8e8;
        padding: 10px;
        .answer {
          width: 100%;
          height: 150px;
          overflow: auto;
        }
      }
      .ask-box {
        height: 28%;
        overflow-y: auto;
        padding: 8px;
        border-radius: 5px;
        .ask-button {
          position: absolute;
          bottom: 8px;
          right: 10px;
          margin: auto;
        }
        .talk-button {
          position: absolute;
          bottom: 8px;
          right: 60px;
          margin: auto;
        }
      }
    }


  }
</style>

