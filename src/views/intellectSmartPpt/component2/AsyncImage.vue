<!-- AsyncImage.vue -->
<template>
  <div class="imgBox" :class="{ 'is-disabled': isDownloading }">
    <div class="demo-image__lazy elimg" v-loading="loading">
      <div>
        <el-link
          type="primary"
          class="el-icon-bottom"
          @click="scrollToBottom"
          @dblclick="handleDoubleClick"
        >
          向下滚动
        </el-link>
        <el-link
          type="primary"
          class="el-icon-top"
          @click="scrollToTop"
          @dblclick="handleDoubleClick"
        >
          向上滚动
        </el-link>
      </div>

      <div class="image-container">
        <el-image
          v-for="(url, index) in imgSinglePreviewList"
          :key="index"
          :src="url"
          lazy
          @click="openFullscreen(url)"
          class="image-item"
        ></el-image>
      </div>

      <div>
        <!-- 全屏显示的对话框 -->
        <el-dialog
          :visible.sync="fullscreenVisible"
          :close-on-click-modal="true"
          :modal="false"
          width="90%"
          style="padding: 0 50px 0 50px"
        >
          <img :src="fullscreenImage" class="fullscreen-image" alt="全屏显示的图片"/>
          <span slot="footer" class="dialog-footer">
        </span>
        </el-dialog>
      </div>
    </div>

  </div>
</template>

<script>
import JSZip from 'jszip'

export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    imgSinglePreviewList: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: 'img'
    }
  },
  data() {
    return {
      isDownloading: false,
      fullscreenVisible: false, // 控制全屏显示的状态
      fullscreenImage: '', // 存储全屏显示的图片
      isScrolling: false, // 标志位，控制滚动状态
      loading: true
    }
  },
  mounted() {
    // 添加双击事件监听
    this.$el.addEventListener('dblclick', this.handleDoubleClick)
    this.clodeLoading()
  },

  beforeDestroy() {
    // 移除事件监听
    this.$el.removeEventListener('dblclick', this.handleDoubleClick)
  },
  methods: {
    clodeLoading() {
      if (this.imgSinglePreviewList.length >= 1) {
        setTimeout(() => {
          this.loading = false
        }, 1000)
      } else {
        setTimeout(() => {
          this.loading = false
        }, 10000)
      }
    },
    // updateDownloadNotification(percent, message, notification = null) {
    //   if (!notification) {
    //     return this.$notify({
    //       title: '图片合成中',
    //       message: `已合成 ${percent}%`,
    //       duration: 0,
    //       type: 'info'
    //     })
    //   } else {
    //     notification.close()
    //     return this.$notify({
    //       title: '图片合成中',
    //       message: message || `已合成 ${percent}%`,
    //       duration: 0,
    //       type: 'info'
    //     })
    //   }
    // },
    updateDownloadNotification(percent, message, notification = null) {
      if (!notification) {
        return this.$notify({
          title: '图片合成中',
          message: `已合成 ${percent}%`,
          duration: 0,
          type: 'info'
        })
      } else {
        // 更新现有通知
        notification.message = message || `已合成 ${percent}%`
        return notification
      }
    },

    async downloadImage() {
      if (this.isDownloading) return
      this.isDownloading = true

      let notification = this.updateDownloadNotification(0, '图片加载中...')

      try {
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')

        const gap = 30 // 图片之间的间距
        let totalHeight = 0
        let maxWidth = 0

        // 1. 加载并计算所有图片的尺寸
        const loadImages = async() => {
          const loadedImages = await Promise.all(this.imgSinglePreviewList.map((imageUrl, index) => {
            return new Promise((resolve) => {
              const img = new Image()
              img.crossOrigin = 'anonymous' // 解决 CORS 问题
              img.src = imageUrl // 使用HTTP/HTTPS地址
              img.onload = () => {
                const percent = (((index + 1) / this.imgSinglePreviewList.length) * 100).toFixed(2)
                notification = this.updateDownloadNotification(percent, null, notification)
                resolve(img)
              }
              img.onerror = () => {
                console.error(`图片加载失败：第 ${index + 1} 张图片 (${imageUrl})`) // 记录错误
                resolve(null) // 处理加载失败
              }
            })
          }))
          return loadedImages
        }

        // 2. 加载所有图片
        const loadedImages = await loadImages()

        // 3. 计算总高度和最大宽度
        loadedImages.forEach(img => {
          if (img) {
            totalHeight += img.height + gap // 累加总高度
            if (img.width > maxWidth) maxWidth = img.width // 更新最大宽度
          }
        })

        // 4. 更新 canvas 大小
        canvas.width = maxWidth
        canvas.height = totalHeight

        // 5. 绘制所有图片
        let yOffset = 0 // 当前绘制的起始位置
        for (const img of loadedImages) {
          if (img) {
            context.drawImage(img, 0, yOffset, img.width, img.height)
            yOffset += img.height + gap // 每张图片下移
          }
        }

        // 6. 尝试生成 Blob
        const blob = await new Promise((resolve, reject) => {
          canvas.toBlob((blob) => {
            if (blob) {
              resolve(blob)
            } else {
              reject(new Error('Blob 生成失败'))
            }
          }, 'image/jpeg', 0.8)
        })

        if (!blob) {
          throw new Error('合成的图片数据为空')
        }

        // 下载合成的图片
        setTimeout(() => {
          const elink = document.createElement('a')
          elink.href = URL.createObjectURL(blob)
          elink.download = this.name + '.jpeg'

          document.body.appendChild(elink)

          elink.click()
          document.body.removeChild(elink)
        }, 800)

        // 下载成功通知
        if (notification != null) {
          notification.title = '图片合成完成'
          notification.message = '成功合成并下载'
          notification.type = 'success'
          setTimeout(() => {
            this.$notify.closeAll()
            this.isDownloading = false
          }, 4000)
        }

      } catch (error) {
        console.error('下载失败:', error) // 打印错误信息

        // 图片过大合成下载失败，打包所有原始图片并下载
        const zip = new JSZip()
        const imgPromises = this.imgSinglePreviewList.map((imageUrl, index) => {
          return fetch(imageUrl)
            .then(response => {
              if (!response.ok) {
                throw new Error(`下载失败：第 ${index + 1} 张图片 (${imageUrl})`)
              }
              return response.blob()
            })
            .then(blob => {
              zip.file(`image_${index + 1}.jpeg`, blob)
            })
        })

        await Promise.all(imgPromises)

        // 生成 ZIP 文件并下载
        const content = await zip.generateAsync({ type: 'blob' })
        const zipLink = document.createElement('a')
        zipLink.href = URL.createObjectURL(content)
        zipLink.download = 'images.zip'

        setTimeout(() => {
          document.body.appendChild(zipLink)
          zipLink.click()
          document.body.removeChild(zipLink)
        }, 800)

        if (notification != null) {
          notification.title = '合成失败'
          notification.message = '图片过大，已将图片打包下载。'
          notification.type = 'warning'
        }
        setTimeout(() => {
          this.$notify.closeAll()
          this.isDownloading = false
        }, 4000)
      }
    },

    handleDoubleClick() {
      if (this.isScrolling) {
        this.isScrolling = false // 停止滚动
      }
    },
    scrollToBottom() {
      const container = this.$el.querySelector('.image-container')
      const lastImage = this.$el.querySelector('.image-item:last-child')

      if (lastImage && container) {
        if (this.isScrolling) {
          // 如果正在滚动，则停止滚动
          return
        }
        this.isScrolling = true // 开始滚动

        // 获取目标位置和当前滚动位置
        const targetPosition = lastImage.offsetTop + container.scrollTop // 使用 offsetTop
        const startPosition = container.scrollTop
        const distance = targetPosition - startPosition

        const duration = this.imgSinglePreviewList.length / 4 * 1000 // 持续时间

        let startTime = null

        const easeInOutQuad = (t) => {
          return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
        }

        const animateScroll = (currentTime) => {
          if (!startTime) startTime = currentTime
          const timeElapsed = currentTime - startTime
          const t = Math.min(timeElapsed / duration, 1) // 计算时间的比例

          // 应用缓动函数
          const easingValue = easeInOutQuad(t)
          const scrollTo = startPosition + distance * easingValue

          container.scrollTop = scrollTo

          if (t < 1) {
            if (!this.isScrolling) return // 如果已停止，直接返回
            requestAnimationFrame(animateScroll)
          } else {
            this.isScrolling = false // 滚动结束
          }
        }

        requestAnimationFrame(animateScroll)
      } else {
        console.log('未找到最后一张图片或容器')
      }
    },
    scrollToTop() {
      const container = this.$el.querySelector('.image-container')
      const firstImage = this.$el.querySelector('.image-item:first-child') // 获取第一张图片

      if (firstImage && container) {
        if (this.isScrolling) {
          // 如果正在滚动，则停止滚动
          return
        }
        this.isScrolling = true // 开始滚动

        // 获取目标位置和当前滚动位置
        const targetPosition = firstImage.offsetTop // 目标位置为第一张图片的 offsetTop
        const startPosition = container.scrollTop // 当前滚动位置
        const distance = targetPosition - startPosition // 计算距离
        const duration = this.imgSinglePreviewList.length / 4 / 2 * 1000 // 持续时间

        let startTime = null

        const easeInOutQuad = (t) => {
          return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t // 缓动函数
        }

        const animateScroll = (currentTime) => {
          if (!startTime) startTime = currentTime
          const timeElapsed = currentTime - startTime
          const t = Math.min(timeElapsed / duration, 1) // 计算时间的比例

          // 应用缓动函数
          const easingValue = easeInOutQuad(t)
          const scrollTo = startPosition + distance * easingValue

          container.scrollTop = scrollTo // 设置滚动位置

          if (t < 1) {
            if (!this.isScrolling) return // 如果已停止，直接返回
            requestAnimationFrame(animateScroll)
          } else {
            this.isScrolling = false // 滚动结束
          }
        }

        requestAnimationFrame(animateScroll) // 启动动画
      } else {
        console.log('未找到第一张图片或容器')
      }
    },
    openFullscreen(imageUrl) {
      this.fullscreenImage = imageUrl // 设置要显示的图片
      this.fullscreenVisible = true // 打开全屏对话框
    }

  }
}
</script>

<style scoped>
.fontTip {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  transition: all 0.3s ease;
}

.is-disabled .fontTip {
  cursor: not-allowed; /* 更改光标样式，表示禁用状态 */
  pointer-events: none; /* 禁用点击事件 */
  opacity: 0.5; /* 设置透明度以显著标识禁用状态 */
}


.fontTip:hover {
  color: #00daa7;
  font-size: 13px;
  font-weight: bold;
}

.imgBox {
  width: 80%;
  position: relative;
  display: block;
  margin: 0 auto;
  text-align: center;
  transition: all 1s ease;
}

.imgBox:hover {
  width: 90%;
  border: 3px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.elimg {
  position: relative;
  display: inline-block;
}

.imgBorder {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-item {
  margin: 3px; /* 上、右、下、左的间隔设置 */
  cursor: pointer; /* 提示用户可点击 */
  max-width: 100%; /* 防止图片超出容器 */
  max-height: 100%; /* 防止图片超出容器 */
}

.image-container {
  height: 65vh;
  overflow-y: auto; /* 允许垂直滚动 */
}

.el-icon-top {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  transition: all 0.3s ease;
  font-weight: bold;
  opacity: 0.2;
}

.el-icon-top:hover {
  color: #00daa7;
  font-size: 13px;
  font-weight: bold;
  opacity: 1;
}

.el-icon-bottom {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  font-weight: bold;
  transition: all 0.3s ease;
  opacity: 0.2;
}

.el-icon-bottom:hover {
  color: #00daa7;
  font-size: 13px;
  font-weight: bold;
  opacity: 1;
}

.imgBox {
  width: 88%;
  position: relative;
  display: block;
  margin: 0 auto;
  text-align: center;
  transition: all 1s ease;
}

.fullscreen-image {
  height: 80vh; /* 全屏显示时高度设置为 95% 的视口高度 */
  width: auto; /* 自动宽度以保持比例 */
  max-width: 100%; /* 防止图片超出容器 */
  display: block; /* 确保图片为块级元素 */
  margin: auto; /* 居中显示 */
}
</style>
