<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="PPT要求" prop="query">
        <template>
          <el-radio v-model="requestType" label="text">输入文本</el-radio>
          <el-radio v-model="requestType" label="file">上传文件</el-radio>
        </template>
        <div v-if="requestType==='text'">
          <el-input type="textarea" v-model.trim="form.query" placeholder="请按照格式输入生成PPT文本" maxlength="5500"
                    show-word-limit
                    @input="changeMessage()"
                    :autosize="{ minRows: 4, maxRows: 12}"/>
        </div>
        <div v-if="requestType==='file'">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers"
                     multiple :limit="1" :on-success="handleUploadSuccess" :on-remove="handleRemove"
                     accept=".txt,.doc,.docx">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持txt、doc、docx格式文件上传，仅能上传一个文件</div>
          </el-upload>
        </div>
        <div class="text-tip">
          <el-link @click="handelShowTextFormate">
            <i :class="{'text-tip-active':this.showTextTip}">查看输入格式</i>
            <i class="el-icon-view el-icon--right"></i>
          </el-link>
          <formatTip></formatTip>
        </div>
        <div v-show="showTextTip" class="text-tip-box">
          <el-row>
            <el-col :span="11">
              <div class="grid-content bg-purple">
                <div class="grid-content bg-purple">
                  <el-card class="box-card" shadow="hover">
                    <!-- 一键复制按钮 -->
                    <el-button size="mini" @click="copyToClipboard(textTip)">一键复制【格式】</el-button>
                    <div
                      class="text item textRulesItem"
                      v-for="(item, index) in textTip"
                      :key="index"
                      @mouseover="setHighlightedIndices(index)"
                      @mouseleave="clearHighlightedIndices"
                      :class="{ 'highlighted': highlightedRulesIndex === index }"
                    >
                      {{ item }}
                    </div>
                  </el-card>
                </div>
              </div>
            </el-col>
            <el-col :span="13">
              <div class="grid-content bg-purple-light" style="width: 130%;">
                <el-card class="box-card" shadow="hover">
                  <!-- 一键复制按钮 -->
                  <el-button size="mini" @click="copyToClipboard(textTipExample)">一键复制【用例】</el-button>
                  <div
                    class="text item textExamplesItem"
                    v-for="(item, index) in textTipExample"
                    :key="index"
                    @mouseover="setHighlightedRulesIndexByExample(index)"
                    @mouseleave="clearHighlightedIndices"
                    :class="{ 'highlighted': highlightedExampleIndices.includes(index)  }"
                  >
                    {{ item }}
                  </div>
                </el-card>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form-item>

      <div>
        <el-row>
          <el-col style="flex: 0 0 200px;">
            <el-form-item label="PPT生成主题" prop="selectedThemeOptionData">
              <el-select class="ck-input"
                         v-model="form.templateManagementId"
                         placeholder="请选择PPT生成主题" value-key="id">
                <el-option
                  v-for="item in themeOptions"
                  :key="item.id"
                  :label="item.tLabel"
                  :value="item.id"
                  filterable>
                  <el-row>
                    <el-col :span="12">
                      <span style="float: left">{{ item.tLabel }}</span>
                    </el-col>
                    <el-col :span="12">
                      <span style="float: right; color: #8492a6; font-size: 14px">{{ item.tValue }}</span>
                    </el-col>
                  </el-row>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-row>
          <el-col style="flex: 0 0 200px;">
            <el-form-item label="PPT作者名" prop="author">
              <el-input class="ck-input"
                        v-model="form.author"
                        placeholder="请输入PPT作者名"
                        maxlength="20"
                        show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-row>
          <el-col :span="5">
            <el-form-item label="下载" prop="conirmDownloadPpt">
              <el-switch
                v-model="conirmDownload"
                active-text="是"
                inactive-text="否"
                active-value="true"
                inactive-value="false"
                :disabled=disabledConirmDownload
                @change="handleConirmDownloadChange">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="5" style="flex: 0 0 200px;">
            <el-form-item label="预览" prop="showLongImg" v-if="form.showLongImg==='false'">
              <div>
                <el-switch
                  v-model="form.showLongImg"
                  active-text="是"
                  inactive-text="否"
                  active-value="true"
                  inactive-value="false"
                  @change="handleShowLongImgChange">
                </el-switch>
              </div>
            </el-form-item>
            <el-form-item label="预览" prop="showLongImg" v-if="form.showLongImg==='true'">
              <div>
                <el-dropdown @command="handleCommand" size="small" trigger="hover"
                             :show-timeout="elDropdownShowTimeoOut">
                  <el-switch
                    v-model="form.showLongImg"
                    active-text="是"
                    inactive-text="否"
                    active-value="true"
                    inactive-value="false"
                    @change="handleShowLongImgChange">
                  </el-switch>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="previeQquality[0].highImageQuality"
                      :class="{ 'highlightedSelectLongImg': form.selectedQuality === previeQquality[0].highImageQuality }">
                      高清预览（慢）
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="previeQquality[1].highImageQuality" divided
                      :class="{ 'highlightedSelectLongImg': form.selectedQuality === previeQquality[1].highImageQuality }">
                      清晰预览（快）
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="5" style="flex: 0 0 200px;">
            <el-form-item label="添加logo" prop="addLogo">
              <el-switch
                v-model="form.addLogo"
                active-text="是"
                inactive-text="否"
                active-value="true"
                inactive-value="false"
                @change="handleAddLogoChange"
              >
              </el-switch>

            </el-form-item>
          </el-col>
          <el-col :span="9" style="flex: 0 0 200px;">
            <el-form-item label="位置" prop="logoPosition" v-show="form.addLogo === 'true'">
              <el-select class="ck-input" v-model="form.logoPosition" placeholder="请选择log位置标签"
                         style="width: 100%;">
                <el-option v-for="dict in dict.type.logo_position" :key="dict.value" :label="dict.label"
                           :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!--      <div>-->
      <!--        <el-row>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;">-->
      <!--            <el-form-item label="添加logo" prop="addLogo">-->
      <!--              <el-switch-->
      <!--                v-model="form.addLogo"-->
      <!--                active-text="是"-->
      <!--                inactive-text="否"-->
      <!--                active-value="true"-->
      <!--                inactive-value="false"-->
      <!--                @change="handleAddLogoChange">-->
      <!--              </el-switch>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;">-->
      <!--            <el-form-item label="位置" prop="logoPosition" v-if="form.addLogo === 'true'">-->
      <!--              <el-select class="ck-input" v-model="form.logoPosition" placeholder="请选择log位置标签"-->
      <!--                         style="width: 100%;">-->
      <!--                <el-option v-for="dict in dict.type.logo_position" :key="dict.value" :label="dict.label"-->
      <!--                           :value="dict.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->

      <!--          <el-col :span="8" style="flex: 0 0 200px;">-->
      <!--            <el-form-item label="缩放" prop="logoPosition" v-if="form.addLogo === 'true'">-->
      <!--              <el-select class="ck-input" v-model="form.logoZoom" placeholder="请选择log缩放"-->
      <!--                         style="width: 110%;">-->
      <!--                <el-option v-for="dict in dict.type.logo_zoom" :key="dict.value" :label="dict.label"-->
      <!--                           :value="dict.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </div>-->
      <!--      <div>-->
      <!--        <el-row>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;">-->
      <!--            <el-form-item label="是否预览" prop="showLongImg">-->
      <!--              <el-switch-->
      <!--                v-model="form.showLongImg"-->
      <!--                active-text="是"-->
      <!--                inactive-text="否"-->
      <!--                active-value="true"-->
      <!--                inactive-value="false">-->
      <!--              </el-switch>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;" v-if="form.showLongImg==='true'">-->
      <!--            <el-form-item label="列数" prop="longImgRows">-->
      <!--              <el-input-number v-model="form.longImgRows" controls-position="right" style="width: 100%;"-->
      <!--                               :min="1" :max="3">-->
      <!--              </el-input-number>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;" v-if="form.showLongImg==='true'">-->
      <!--            <el-form-item label="间距" prop="spacingXY">-->
      <!--              <el-select class="ck-input" v-model="form.spacingXY" placeholder="间距"-->
      <!--                         style="width: 110%;">-->
      <!--                <el-option v-for="dict in dict.type.spacing_x_y" :key="dict.value" :label="dict.label"-->
      <!--                           :value="dict.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </div>-->

      <!--      <div>-->
      <!--        <el-row>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;">-->
      <!--            <el-form-item label="调整字体" prop="showFontAdjest">-->
      <!--              <el-switch-->
      <!--                v-model="showFontAdjest"-->
      <!--                active-value="true"-->
      <!--                inactive-value="false"-->
      <!--                active-text="是"-->
      <!--                inactive-text="否"-->
      <!--                @change="changeFontAdjest">-->
      <!--              </el-switch>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="8" style="flex: 0 0 200px;" v-if="showFontAdjest==='true'">-->
      <!--            <el-form-item label="字体选择" prop="useFont">-->
      <!--              <el-select class="ck-input" v-model="form.useFont" placeholder="字体选择"-->
      <!--                         style="width: 100%;">-->
      <!--                <el-option v-for="item in fontAvaliableList"-->
      <!--                           :key="item.value"-->
      <!--                           :label="item.label"-->
      <!--                           :value="item.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </div>-->
    </el-form>
    <div class="buildPptProgressDivBox" v-show="btnLoad">
      <buildPptProgress :from="form" ref="buildPptProgress"
                        @show-text-tip="showTextTip=!showTextTip"></buildPptProgress>
    </div>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmit" :loading="btnLoad" :disabled=disabledConirmDownload>确定
      </el-button>
    </el-row>

    <!--    <div class="imgBox">-->
    <!--      <div class="demo-image__lazy elimg" v-if="shoLongImgBox">-->
    <!--        &lt;!&ndash; 悬浮在图片右上角的下载按钮 &ndash;&gt;-->
    <!--        <el-link-->
    <!--          type="primary"-->
    <!--          @click="downloadImage"-->
    <!--          class="fontTip">-->
    <!--          图片下载-->
    <!--        </el-link>-->
    <!--        <el-image-->
    <!--          :src="longImg"-->
    <!--          :preview-src-list="[longImg]"-->
    <!--          :preview-teleported="true"-->
    <!--          class="imgBorder"-->
    <!--        ></el-image>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="imgBox">
      <!--      <div class="demo-image__lazy elimg" v-if="shoLongImgBox">-->
      <!--        <div class="demo-image__lazy elimg" v-show="shoLongImgBox && longImg.length>10">-->
      <!--          &lt;!&ndash; 悬浮在图片右上角的下载按钮 &ndash;&gt;-->
      <!--          <el-link-->
      <!--            type="primary"-->
      <!--            class="fontTip"-->
      <!--            @click="downloadImage">-->
      <!--            图片下载-->
      <!--          </el-link>-->
      <!--          <el-image-->
      <!--            :src="longImg"-->
      <!--            :preview-src-list="[longImg]"-->
      <!--            :preview-teleported="true"-->
      <!--            class="imgBorder"-->
      <!--          ></el-image>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
    <div class="shoLongImgBoxDiv" v-if="shoLongImgBox">
      <AsyncImage :imgSinglePreviewList="imgSinglePreviewList" :name="pptName"></AsyncImage>
    </div>

    <div v-if="dialogVisibleForAsyncImage">
      <el-dialog
        title="预览"
        :visible.sync="dialogVisibleForAsyncImage"
        width="70%"
        :close-on-click-modal="false"
      >
        <AsyncImage :imgSinglePreviewList="imgSinglePreviewList" :name="pptName"
                    ref="AsyncImage"></AsyncImage>
        <div slot="footer" class="dialog-footer">
          <div class="footerTipFont">
            预览不代表PPT质量
          </div>
          <el-button type="success" class="el-icon-document"
                     :disabled=disabledConirmDownload
                     @click="handleDownloadPpt">
            PPT下载
          </el-button>
          <el-button type="primary" class="el-icon-picture"
                     @click="handleDialogVisibleForAsyncImageDownload" :disabled="disabledDwnLoadAsyncImage">
            图片下载
          </el-button>
        </div>
      </el-dialog>
    </div>


    <el-backtop target=".app-container" class="elBackTop"></el-backtop>
  </div>


</template>
<script>
import { getToken } from '@/utils/auth'
import {
  createPPtToGetPath,
  downLoadPPt,
  downLoad, getFontList, enterPPTPage
} from '@/api/intellectSmartPpt/intellectSmartPpt.js'
import formatTip from '/src/views/intellectSmartPpt/component/formatTip.vue'
import buildPptProgress from '/src/views/intellectSmartPpt/component/buildPptProgress.vue'
import { getLogos } from '@/api/logoConfiguration/logoConfiguration'
import { buildMapDataList } from '@/api/pptTemplateManagement/pptTemplateManagement'
// 异步加载组件
const AsyncImage = () => import('/src/views/intellectSmartPpt/component/AsyncImage.vue')

export default {
  name: 'IntellectPPT',
  // dicts: ['custom_theme', 'logo_position', 'logo_zoom', 'spacing_x_y', 'create_model', 'is_card_note'],
  dicts: ['logo_position'],
  components: { formatTip, buildPptProgress, AsyncImage },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/file/upload', // 上传的图片服务器地址
      uploadData: { modeltype: 'smartPPT' },
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      form: {
        query: '',
        isCardNote: 'false',
        theme: '',
        templateManagementId: '',
        addLogo: 'true',
        logoPosition: 'topRight',
        logoZoom: '100',
        showLongImg: 'false',
        longImgRows: '1',
        author: '',
        useFont: '',
        spacingXY: '10and40',
        selectedQuality: '',
        logoPath: ''
      },
      btnLoad: false,
      disabledDwnLoadAsyncImage: false,
      rules: {
        templateManagementId: [
          { required: true, message: '请选择PPT主题', trigger: ['blur', 'change', 'input'] }
        ]
      },
      requestType: 'text',
      fileId: '',
      queryData: {
        filePath: ''
      },
      dialogVisibleForAsyncImage: false,
      longImg: '',
      imgSinglePreviewList: [],
      imgSinglePreviewListBlobUrl: [],
      pptUrl: null,
      showFontAdjest: '',
      fontAvaliableList: [],
      showTextTip: false,
      conirmDownload: 'true',
      shoLongImgBox: false,
      disabledConirmDownload: false,
      pptName: '',
      previeQquality: [
        { highImageQuality: 'true' },
        { highImageQuality: 'false' }
      ],
      elDropdownShowTimeoOut: parseInt(100),
      highlightedRulesIndex: null,
      highlightedExampleIndices: [],

      textTip2: [
        '文本的第一行是名字',
        '一级标题：中文数字加顿号“、”加大标题内容表示',
        '二级级标题：中文数字加中文右括号“）',
        '三标题：使用阿拉伯数字加英文“.”加标题以句号“。”结尾，后面跟着正文',
        '如果有总结，总结在小标题上面',
        '各个标题之间必须换行'
      ],
      textTip: [
        '文本的第一行是名字',
        '一级标题：中文数字（一） + （中/英）【， 。 、】 + 标题内容表示',
        '二级级标题：(中/英)【(】 + 中文数字 一 + （中/英）【)】 + 标题内容',
        '三标题：使用阿拉伯数字(1) + 【.】 + 标题 +（中/英）【。 :】 + 正文',
        '如有总结，单独一行',
        '各个标题之间必须换行'
      ],
      textTipExample: [
        '城市管理讲义',
        '一，导论',
        '1. 城市管理概述。城市管理涉及城市组织、运行和管理的原理和规律。',
        '二。城市管理学的基础理论',
        '(一)城市管理学定义',
        '1. 公共管理理论。包括公共政策、公共行政等，是城市管理学的理论基础。',
        '2. 公共管理事件：包括xxx、xxx等',
        '(二)城市管理的核心内容',
        '管理活动的对应性，城市管理活动严格依赖管理对象的特征。',
        '三、结语',
        '《城市管理学：公共视角》一书，系统阐述了城市管理理论、方法与实践，对于学习城市管理理论、提升城市管理实践能力具有重要意义。'
      ],
      themeOptions: []
    }
  },
  watch: {},
  methods: {
    callChildMethod() {
      // 通过 ref 调用子组件的方法
      this.$refs.buildPptProgress.handleReconnect()
      this.$refs.buildPptProgress.clearStep()
    },
    saveData() {
      const dataToSave = {
        form: this.form,
        fileId: this.fileId,
        queryData: this.queryData
      }
      localStorage.setItem('intellectSmartPpt_index_pageData', JSON.stringify(dataToSave))
    },
    loadData() {
      const savedData = localStorage.getItem('intellectSmartPpt_index_pageData')
      if (savedData) {
        const data = JSON.parse(savedData)
        this.form = data.form
        console.log('恢复数据')
        console.log(this.form)
        this.fileId = data.fileId
        this.queryData = data.queryData
      }
    },
    handleBeforeUnload(event) {
      if (event) {
        // 判断是否是用户手动刷新
        // 可以通过设置一个标志位或直接清除缓存
        localStorage.removeItem('intellectSmartPpt_index_pageData')
      }
    },
    changeMessage() {
      this.$forceUpdate()
    },
    /** 查询logo */
    getLogoPath() {
      // 获取当前页面的 base URL（协议+主机+端口）
      let baseUrl = window.location.origin
      if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
        baseUrl = 'http://127.0.0.1:80'
      }
      getLogos(2).then(res => {
        this.form.logoPath = `${baseUrl}${res[0]}`
        console.log(res)
        console.log(this.form.logoPath)
        // this.form.logoPath =  "http://***************:9204/ss/ppt/logo.png";
      })
    },
    handleSubmit() {
      console.log(this.requestType)
      console.log(this.form.query)
      if (this.requestType === 'text' && (this.form.query === undefined || this.form.query === null || this.form.query === ''
      )) {
        this.$notify({
          title: '警告',
          message: '请确输入文本内容',
          type: 'warning',
          position: 'top-right'

        })
        return
      }
      if (this.requestType === 'file' && (this.fileId === null || this.fileId === '' || this.fileId === undefined)) {
        this.$notify({
          title: '警告',
          message: '请先上传文本文件',
          type: 'warning',
          position: 'top-right'
        })
        return
      }
      if (this.requestType === 'text') {
        this.form.requestType = 'text'
      } else {
        this.form.requestType = 'file'
        this.form.fileId = this.fileId
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.callChildMethod()
          this.btnLoad = true

          setTimeout(() => {
            createPPtToGetPath(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('生成完成')
                this.queryData.filePath = res.pptPath
                this.pptUrl = res.pptUrl
                this.pptName = res.pptName
                this.imgSinglePreviewList = res.imgSinglePreviewUrlList
                // // 处理 Base64 图片转换为 Blob 或 URL
                // this.imgSinglePreviewList = res.imgSinglePreviewList.map(base64Image => {
                //   // 创建 Blob
                //   const byteCharacters = atob(base64Image); // 解码 Base64
                //   const byteNumbers = new Array(byteCharacters.length);
                //   for (let i = 0; i < byteCharacters.length; i++) {
                //     byteNumbers[i] = byteCharacters.charCodeAt(i);
                //   }
                //   const byteArray = new Uint8Array(byteNumbers);
                //   const blob = new Blob([byteArray], { type: 'image/jpeg' });
                //
                //   // 创建 URL
                //   return URL.createObjectURL(blob);
                // });
                if (this.conirmDownload === 'true') {
                  // 下载
                  // this.handleDownLoad()
                  this.handleDownLoad2(this.pptUrl, this.pptName)
                }
                if (this.form.showLongImg === 'true') {
                  // 生成预览图
                  // this.getImgPreview(res.longImg);
                  this.getImgPreview2()
                }
                setTimeout(() => {
                  this.btnLoad = false
                }, 2000)

              } else {
                this.$message.error('返回异常：' + res.msg)
                // 下载失败时直接关闭加载状态
                this.btnLoad = false
              }
            }).catch(() => {
              this.btnLoad = false  // 请求失败时直接关闭加载状态
            })
          }, 100)
        }
      })
    },
    handleConirmDownloadChange(val) {
      if (val === 'true' && this.pptUrl?.length > 10) {
        this.handleDownLoad2(this.pptUrl, this.pptName)
        // this.handleDownLoad()
        // this.handleDownLoad2()
      }
      if ('false' === val && 'false' === this.form.showLongImg) {
        this.$message({
          message: '请确保选择【预览】或下【载选】项中的至少一个',
          type: 'warning'
        })
        setTimeout(() => {
          this.conirmDownload = 'true'
        }, 50)
      }
    },
    handleShowLongImgChange(val) {
      if ('false' === val) {
        this.form.selectedQuality = null
        this.shoLongImgBox = false
      }
      if ('true' === val) {
        if (this.imgSinglePreviewList.length > 0) {
          this.shoLongImgBox = true
          this.getImgPreview2()
        }

      }
      if ('false' === val && 'false' === this.conirmDownload) {
        this.$message({
          message: '请确保选择【预览】或【下载】选项中的至少一个',
          type: 'warning'
        })
        setTimeout(() => {
          this.form.showLongImg = 'true'
        }, 50)
      }
    },
    handleDownLoad() {
      try {
        // console.log("this.queryData.filePath", this.queryData.filePath);
        downLoadPPt(this.queryData).then(res => {
          // console.log(res); // 控制台输出：Blob {size: 30208, type: 'application/octet-stream'}

          // 从请求头 获取到文件名
          // const f = res.headers['filename'];
          // let tmpFileName = decodeURIComponent(f);
          // console.log("tmpFileName", tmpFileName);

          if (res) {
            const elink = document.createElement('a')
            elink.download = this.pptName + '.pptx'
            elink.style.display = 'none'
            const blob = new Blob([res.data], { type: 'application/octet-stream' })
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            document.body.removeChild(elink)
            // 延迟后关闭加载状态
            setTimeout(() => {
              this.btnLoad = false
            }, 1000)
          } else {
            this.$message.error('下载过程中出现错误，请重新尝试')
            this.btnLoad = false  // 下载失败时关闭加载状态
          }
        })
      } catch (err) {
        console.log(err)
        this.$message.error('下载过程中出现错误，请重新尝试')
        this.btnLoad = false  // 下载失败时关闭加载状态
      } finally {
        this.btnLoad = false
      }
    },
    getImgPreview(filePath) {
      const data = {
        'filePath': filePath
      }
      try {
        downLoad(data).then(res => {
          const blob = new Blob([res], { type: 'application/octet-stream' })
          const imageUrl = URL.createObjectURL(blob)
          this.longImg = imageUrl
          this.shoLongImgBox = 'true'
          // 直接在新窗口中显示图片
          this.openImageInNewWindow()
          // 延迟  后关闭加载状态
          setTimeout(() => {
            this.btnLoad = false
          }, 100)
        })
      } catch (err) {
        this.btnLoad = false
      } finally {
        this.btnLoad = false
      }
    },
    getImgPreview2() {
      try {
        // 直接在新窗口中显示图片
        // this.openImageInNewWindow()
        this.dialogVisibleForAsyncImage = true

        setTimeout(() => {
          this.shoLongImgBox = 'true'
        }, 500)

      } catch (err) {
        this.$message.error('显示预览图图出现错误，请重新尝试')
      }
    },
    openSuccessNotify() {
      this.$notify({
        title: 'PPT下载',
        message: '正在下载中',
        type: 'success',
        duration: 20000
      })
    },
    updateDownloadNotification(percent, message, notification = null) {
      if (!notification) {
        // 如果没有现有通知，创建一个新通知
        return this.$notify({
          title: '下载中',
          message: `已下载 ${percent}%`,
          duration: 0,  // 设置为0，防止自动关闭
          type: 'info'
        })
      } else {
        // 更新现有通知
        notification.message = message || `已下载 ${percent}%`
        return notification
      }
    },

    async handleDownLoad2(pptUrl, pptName) {
      this.disabledConirmDownload = true

      let notification = null // 用于存储通知实例
      try {
        const response = await fetch(pptUrl)
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const contentLength = +response.headers.get('Content-Length')

        let receivedLength = 0 // 当前接收到的字节长度
        const chunks = [] // 用来保存接收到的数据块

        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          chunks.push(value)
          receivedLength += value.length

          // 你可以在这里更新下载进度显示
          // console.log(`Received ${((receivedLength / contentLength) * 100).toFixed(2)}%`);
          // 计算下载百分比
          const percent = ((receivedLength / contentLength) * 100).toFixed(2)
          // 实时更新通知内容
          notification = this.updateDownloadNotification(percent, null, notification)
        }

        // 将接收到的数据块合并为一个 Blob
        const blob = new Blob(chunks)
        const url = window.URL.createObjectURL(blob)

        // 创建一个隐藏的链接元素进行下载
        setTimeout(() => {
          const elink = document.createElement('a')
          elink.href = url
          elink.download = pptName ? pptName + '.pptx' : 'example'
          document.body.appendChild(elink)
          elink.click()
          document.body.removeChild(elink)
          // 释放 URL 对象
          window.URL.revokeObjectURL(url)
        }, 800)

        // 下载完成后更新通知内容并关闭通知
        if (notification != null) {
          notification.title = 'PPT下载完成'
          notification.message = '文件已成功下载'
          notification.type = 'success'
          setTimeout(() => {
            this.$notify.closeAll()
            this.disabledConirmDownload = false
          }, 3000)
        }

      } catch (error) {
        console.error('Download error:', error)

        // 出现错误时更新通知内容
        if (notification != null) {
          notification.title = '下载失败'
          notification.message = '文件下载时出现错误，请稍后再试。'
          notification.type = 'error'
        }

        setTimeout(() => {
          this.$notify.closeAll()
          this.disabledConirmDownload = false
        }, 4000)
      }
    },
    // async handleDownLoad2(pptUrl, pptName) {
    //   this.disabledConirmDownload = true;
    //   this.openSuccessNotify();
    //
    //   try {
    //     // 使用 fetch 下载文件
    //     const response = await fetch(pptUrl);
    //     if (!response.ok) {
    //       throw new Error('Network response was not ok');
    //     }
    //
    //     const blob = await response.blob();
    //     const url = window.URL.createObjectURL(blob);
    //
    //     // 创建一个隐藏的链接元素进行下载
    //     const elink = document.createElement('a');
    //     elink.href = url;
    //     elink.download = pptName ? pptName + ".pptx" : "example";
    //     document.body.appendChild(elink);
    //     elink.click();
    //     document.body.removeChild(elink);
    //
    //     // 释放 URL 对象
    //     window.URL.revokeObjectURL(url);
    //
    //     // 下载完成后关闭提示并解除按钮禁用
    //     setTimeout(() => {
    //       this.$notify.closeAll();
    //     }, 3000);
    //     setTimeout(() => {
    //       this.btnLoad = false;
    //       this.disabledConirmDownload = false;
    //     }, 1500);
    //
    //   } catch (error) {
    //     console.error('Download error:', error);
    //
    //     // 出现错误时显示错误提示
    //     this.$notify({
    //       title: '下载失败',
    //       message: '文件下载时出现错误，请稍后再试。',
    //       type: 'error',
    //       duration: 5000
    //     });
    //
    //     // 关闭提示并解除按钮禁用
    //     setTimeout(() => {
    //       this.$notify.closeAll();
    //       this.btnLoad = false;
    //       this.disabledConirmDownload = false;
    //     }, 1500)
    //   }
    // },
    // handleDownLoad2(pptUrl, pptName) {
    //   this.openSuccessNotify()
    //   try {
    //     // 创建一个隐藏的链接元素
    //     const elink = document.createElement('a');
    //     elink.href = pptUrl;
    //     elink.download = pptName + ".pptx";
    //
    //     // 确保添加和移除元素的正确顺序
    //     document.body.appendChild(elink);
    //     elink.click();
    //     document.body.removeChild(elink);
    //
    //     // 关闭加载状态
    //     this.btnLoad = false;
    //   } catch (error) {
    //     console.error('Download error:', error);
    //     // 处理错误情况
    //     this.btnLoad = false;
    //   }
    // },
    // handleDownLoad2(pptUrl, pptName) {
    //   try {
    //     // 创建一个 Blob 对象
    //     fetch(pptUrl)
    //       .then(response => response.blob())
    //       .then(blob => {
    //         // 使用 Blob 对象创建 URL
    //         const url = window.URL.createObjectURL(blob);
    //
    //         // 设置链接的 href 属性为 Blob URL
    //         elink.href = url;
    //
    //         // 设置下载的文件名
    //         elink.download = pptName + ".pptx";  // 确保文件扩展名与实际文件类型匹配
    //
    //         // 将链接添加到 DOM 中
    //         document.body.appendChild(elink);
    //
    //         // 触发点击事件以开始下载
    //         elink.click();
    //
    //         // 下载完成后从 DOM 中移除链接
    //         document.body.removeChild(elink);
    //
    //         // 释放 Blob URL
    //         window.URL.revokeObjectURL(url);
    //
    //         // 延迟关闭加载状态
    //         setTimeout(() => {
    //           this.btnLoad = false;
    //         }, 1500);
    //       })
    //       .catch(err => {
    //         console.log(err);
    //         this.$message.error('下载过程中出现错误，请重新尝试');
    //         this.btnLoad = false;  // 下载失败时关闭加载状态
    //       });
    //   } catch (err) {
    //     console.log(err);
    //     this.$message.error('下载过程中出现错误，请重新尝试');
    //     this.btnLoad = false;  // 下载失败时关闭加载状态
    //   }
    // },
    extractFileName(pptUrl) {
      // 通过正则表达式匹配最后一个 '/' 和第一个 '_' 之间的内容
      const match = pptUrl.match(/\/([^\/]+?)_[^\/]*\.pptx$/)
      if (match && match[1]) {
        return match[1] + '.pptx'
      } else {
        return pptUrl
      }
    },
    changeFontAdjest(val) {
      if (val === 'true' && this.fontAvaliableList.length === 0) {
        this.gethandleFontList()
      }
      if (val === 'true') {
        this.form.useFont = '宋体'
      }
      if (val === 'false') {
        this.form.useFont = ''
      }

    },
    async openImageInNewWindow() {
      try {
        const newWindow = await new Promise((resolve, reject) => {
          const wnd = window.open('', '_blank')
          if (wnd) {
            resolve(wnd)
          } else {
            reject(new Error('新窗口未能打开。可能是浏览器阻止了弹出窗口。'))
          }
        })

        if (newWindow) {
          newWindow.document.write(`
        <html>
          <head>
            <style>
              .img {
                width: 50%;
                height: auto;
                display: flex;
                margin: auto;
                border-radius: 10px;
                overflow: hidden;
                transition: all 1s ease;
              }
              .img:hover {
                width: 52%;
                border: 5px solid #ddd;
                border-radius: 10px;
                overflow: hidden;
              }
            </style>
          </head>
          <body>
            <div class="container">
          v-for="(url, index) in ${this.imgSinglePreviewList}"
          :key="index"
          :src="'data:image/jpeg;base64,' + url"
          class="image-item"
        ></el-image>
            </div>
          </body>
        </html>
      `)
          newWindow.document.close()
          await new Promise((resolve) => setTimeout(resolve, 1000))
          newWindow.focus()
        }
      } catch (error) {
        console.error(error.message)
        alert(error.message)
        this.shoLongImgBox = true
      }
    },
    gethandleFontList() {
      getFontList().then(res => {
        this.fontAvaliableList = res.data
        // console.log(res.data)
      })
    },
    copyToClipboard(textArray) {
      // 将数组转换为字符串，每个元素用换行符分隔
      const text = textArray.join('\n')

      // 创建一个隐藏的 textarea 元素
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)

      // 选择并复制文本
      textarea.select()
      document.execCommand('copy')

      // 移除 textarea 元素
      document.body.removeChild(textarea)

      // 可选: 提示用户复制成功
      this.$message.success('内容已复制到剪贴板')
    },

    downloadImage() {
      // 创建一个隐藏的链接元素
      const elink = document.createElement('a')
      // 创建一个 Blob 对象
      fetch(this.longImg)
        .then(response => response.blob())
        .then(blob => {
          // 使用 Blob 对象创建 URL
          const url = window.URL.createObjectURL(blob)
          // 设置链接的 href 属性为 Blob URL
          elink.href = url
          // 设置下载的文件名
          elink.download = this.pptName + '.png'  // 确保文件扩展名与实际文件类型匹配
          // 将链接添加到 DOM 中
          document.body.appendChild(elink)
          // 触发点击事件以开始下载
          elink.click()
          // 下载完成后从 DOM 中移除链接
          document.body.removeChild(elink)
          // 释放 Blob URL
          window.URL.revokeObjectURL(url)

        })
    },
    handelShowTextFormate() {
      this.showTextTip = !this.showTextTip
    },
    handleUploadSuccess(res, file) {
      file.id = res.data.id
      this.fileId = res.data.id
    },
    handleRemove(file, fileList) {
      this.fileId = ''
    },
    handleAddLogoChange(val) {
      if (val === 'false') {
        this.form.logoPosition = null
        this.form.logoZoom = null
      }
      if (val === 'true') {
        this.form.logoPosition = 'topRight'
        this.form.logoZoom = '100'
      }
    },
    setHighlightedIndices(index) {
      this.highlightedRulesIndex = index
      this.highlightedExampleIndices = this.getExampleIndicesForRule(index)
    },
    setHighlightedRulesIndexByExample(index) {
      this.highlightedRulesIndex = this.getRuleIndexForExample(index)
      this.highlightedExampleIndices = this.getExampleIndicesForRule(this.highlightedRulesIndex)
    },
    clearHighlightedIndices() {
      this.highlightedRulesIndex = null
      this.highlightedExampleIndices = []
    },
    getExampleIndicesForRule(index) {
      switch (index) {
        case 0:
          return [0] // 规则索引0对应案例索引0
        case 1:
          return [1, 3, 9]
        case 2:
          return [4, 7]
        case 3:
          return [2, 5, 6]
        case 4:
          return [8, 10]
        case 5:
          return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        default:
          return []
      }
    },
    getRuleIndexForExample(index) {
      switch (index) {
        case 0:
          return 0 // 案例索引0对应规则索引0
        case 1:
          return 1
        case 2:
          return 1
        case 3:
          return 3
        case 4:
          return 2
        case 5:
          return 3
        case 6:
          return 3
        case 7:
          return 2
        case 8:
          return 4
        case 9:
          return 3
        case 10:
          return 3
        case 11:
          return 4
        default:
          return null
      }
    },
    handleCommand(command) {
      this.form.selectedQuality = command
      if (command === 'true') {
        this.$message({
          message: '生成高清预览图',
          type: 'success'
        })
      } else if (command === 'false') {
        this.$message({
          message: '生成清晰预览图',
          type: 'success'
        })
      }
    },
    handleDialogVisibleForAsyncImageDownload() {
      this.disabledDwnLoadAsyncImage = true
      this.$refs.AsyncImage.downloadImage()
      setTimeout(() => {
        this.disabledDwnLoadAsyncImage = false
      }, 3000)
    },
    handleDownloadPpt() {
      if (this.pptUrl && this.pptName) {
        this.handleDownLoad2(this.pptUrl, this.pptName)
      }

    },

    /** 获取模板列表 */
    getThemeOptions() {
      buildMapDataList().then(res => {
        // 赋值给 themeOptions
        this.themeOptions = [...res.data]
        // 将默认选项设置为当前选中的值
        this.form.templateManagementId = this.themeOptions[0].id
      })
    },
    changeOptions(data) {
      console.log(data)
    }
  },
  created() {
    this.getThemeOptions()
    this.loadData() // 页面加载时恢复数据
    this.form.query = this.$route.query && this.$route.query.content
    // 监听浏览器的刷新或关闭操作
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    this.getLogoPath()
  },
  mounted() {
  },

  beforeDestroy() {
    this.saveData() // 页面卸载时保存数据
    window.removeEventListener('beforeunload', this.handleBeforeUnload) // 移除事件监听器
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  overflow-y: auto;
  max-height: 100vh;

}

.ck-form {
  width: 80%;
  margin: auto;
}

.ck-input {
  width: 50%;
}

.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.fontTip {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  transition: all 0.3s ease;
}

.fontTip:hover {
  color: #00daa7;
  font-size: 13px;
  font-weight: bold;
}

.text-tip-active {
  color: #04bc87;
  font-size: 15px;
  font-style: italic;
}


.elBackTop {
  transition: all 0.3s ease;
}

.elBackTop:hover {
  color: #0ef6b0;
  transform: scale(1.3);
}

.box-card {

}

.buildPptProgressDivBox {
  width: 90%;
}

.textRulesItem {
  font-size: 14px;
}

.textExamplesItem {
  font-size: 12px;
}

.highlighted {
  color: #068769;
  transform: scale(1.05);
  transition: all 0.3s ease;
  font-weight: bold;
  font-style: italic;
}

.highlighted:hover {
  display: flex;
  font-weight: bold;
  font-style: italic;
  justify-content: left;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.highlightedSelectLongImg {
  background-color: #d9ebff; /* 高亮背景颜色 */
}

.imgBox {
  width: 70%;
  position: relative;
  display: block;
  margin: 0 auto;
  text-align: center;
  transition: all 1s ease;
}

.imgBox:hover {
  width: 72%;
  border: 3px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.elimg {
  position: relative;
  display: inline-block;
}

.imgBorder {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.shoLongImgBoxDiv {
  margin: 30px 5px 50px 5px;
}

.footerTipFont {
  height: 20px;
  font-size: 10px;
  padding: 5px; /* 添加内边距 */
  color: #666666;
  position: relative;
  right: 10px;
  bottom: 30px;
  margin-bottom: -20px;
  z-index: 10;
}
</style>

