<template>
  <div class="app-container">
    <!-- API key缺失时显示的全屏提示 -->
    <div v-if="!hasValidApiKey" class="api-key-missing">
      <!-- 背景装饰元素 -->
      <div class="background-decoration">
      </div>
      <div class="message-container">
        <div class="icon-wrapper">
          <i class="el-icon-warning-outline warning-icon"></i>
        </div>
        <h2>访问受限</h2>
        <div class="divider"></div>
        <p class="main-message">请提供有效的API密钥以访问此功能</p>
        <div class="additional-info">
          <p><i class="el-icon-info"></i> 您需要：</p>
          <ul>
            <li><i class="el-icon-check"></i> 确保URL中包含正确的API密钥参数</li>
            <li><i class="el-icon-check"></i> 检查API密钥格式是否正确</li>
            <li><i class="el-icon-check"></i> 验证API密钥是否有效</li>
          </ul>
        </div>
        <div class="contact-support">
          <i class="el-icon-service"></i>
          <span>如需帮助，请联系系统管理员</span>
        </div>
      </div>
    </div>
    <!-- 仅在API key存在时显示表单内容 -->
    <div v-else>

      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
        <!-- 添加步骤条 -->
        <el-steps :active="activeStep" finish-status="success" align-center class="steps-nav">
          <el-step title="内容生成">
            <template slot="icon">
              <i class="el-icon-edit-outline"></i>
            </template>
          </el-step>
          <el-step title="模板设置">
            <template slot="icon">
              <i class="el-icon-picture-outline"></i>
            </template>
          </el-step>
          <el-step title="生成PPT">
            <template slot="icon">
              <i class="el-icon-document-add"></i>
            </template>
          </el-step>
        </el-steps>


        <!-- 步骤1: 内容输入区域 -->
        <div v-show="activeStep === 0" class="step-container">
          <div class="step-header">
            <h2>创建您的PPT内容</h2>
            <p class="step-description">选择合适的方式输入您的PPT内容</p>
          </div>

          <el-form-item label="PPT要求" prop="query">
            <div class="input-type-selector">
              <el-radio-group v-model="requestType" size="large">
                <el-radio label="topic">
                  <i class="el-icon-magic-stick"></i>
                  一句话生成
                </el-radio>
                <el-radio label="text">
                  <i class="el-icon-edit"></i>
                  输入文本
                </el-radio>
                <el-radio label="file">
                  <i class="el-icon-upload2"></i>
                  上传文件
                </el-radio>
              </el-radio-group>
            </div>

            <!-- 聊天对话界面（完全替换） -->
            <div v-if="messages.length > 0" class="chat-container">
              <div class="chat-messages">
                <!-- 遍历渲染所有消息 -->
                <div
                  v-for="(message, index) in messages"
                  :key="index"
                  :class="[
                      'message-item',
                      message.role === 'user' ? 'message-user' : 'message-assistant',
                    ]"
                >
                  <!-- 消息内容区域 -->
                  <div class="message-bubble">
                    <!-- 角色图标 -->
                    <div class="message-icon">
                      <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-chat-dot-square'"></i>
                    </div>

                    <!-- 消息内容 -->
                    <template v-if="isEditing && editingIndex === index">
                      <div class="edit-actions">
                        <el-input
                          type="textarea"
                          v-model="editContent"
                          :autosize="{ minRows: 6, maxRows: 25}"
                          class="edit-textarea"
                        ></el-input>
                        <div class="edit-buttons">
                          <el-button size="small" type="primary" @click="saveEditMessage">保存</el-button>
                          <el-button size="small" @click="cancelEditMessage">取消</el-button>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="message-content">{{ message.content }}</div>

                      <!-- 仅为最后一条模型消息添加编辑按钮 -->
                      <div
                        v-if="message.role === 'assistant' && index === getLastAssistantIndex()"
                        class="message-actions"
                      >
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-edit"
                          @click="startEditMessage(index)"
                        >编辑
                        </el-button>

                        <!-- 准备就绪标记，当内容生成完成时显示 -->
                        <span v-if="isGenerateComplete" class="ready-indicator">
                            <i class="el-icon-check"></i>
                            <span>内容已生成</span>
                          </span>
                      </div>
                    </template>
                  </div>
                </div>

                <!-- 流式响应时的加载效果 -->
                <div v-if="isGenerating" class="message-item message-assistant">
                  <div class="message-bubble">
                    <div class="message-icon">
                      <i class="el-icon-chat-dot-square"></i>
                    </div>
                    <div class="message-content">{{ streamContent }}</div>
                    <div class="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 一句话生成输入框 -->
            <div v-if="requestType === 'topic'" class="content-wrapper content-wrapper-top">
              <el-input
                v-model="topicPrompt"
                placeholder="请输入一句话描述PPT内容（最多20字）"
                @keyup.enter.native="handleTopicGenerate"
                maxlength="20"
                show-word-limit
              >
                <el-button
                  slot="append"
                  icon="el-icon-magic-stick"
                  @click="handleTopicGenerate"
                  :loading="isGenerating"
                >生成
                </el-button>
              </el-input>

              <!--              &lt;!&ndash; 流式响应内容展示区域 &ndash;&gt;-->
              <!--              <div v-if="streamContent && messages.length === 0" class="stream-content">-->
              <!--                <el-input-->
              <!--                  type="textarea"-->
              <!--                  ref="streamTextarea"-->
              <!--                  v-model="streamContent"-->
              <!--                  :autosize="{ minRows: 6, maxRows: 10}"-->
              <!--                  placeholder="模型生成的内容"-->
              <!--                  :disabled="isGenerating"-->
              <!--                ></el-input>-->
              <!--              </div>-->


            </div>

            <!-- 原有的文本输入和文件上传选项 -->
            <div v-if="requestType==='text'" class="content-wrapper">
              <el-input type="textarea" v-model="textContent" placeholder="请按照格式输入生成PPT文本"
                        maxlength="5500"
                        show-word-limit
                        :autosize="{ minRows: 4, maxRows: 12}"
                        class="content-textarea"/>

              <div class="format-helper">
                <div class="format-controls">
                  <el-link @click="handelShowTextFormate" class="format-toggle-link">
                    <i class="el-icon-info-circle"></i>
                    <span>{{ showTextTip ? '隐藏输入格式说明' : '查看输入格式说明' }}</span>
                  </el-link>
                  <formatTip class="format-tip-component"></formatTip>
                </div>

                <transition name="fade">
                  <div v-show="showTextTip" class="format-instructions">
                    <div class="format-instruction-header">
                      <h3>输入文本规则</h3>
                      <p>请按照以下格式输入内容，以生成结构良好的PPT</p>
                    </div>

                    <div class="format-instruction-container">
                      <div class="format-left-panel">
                        <div class="format-card">
                          <div class="format-card-header">
                            <h3>格式规范</h3>
                            <el-button size="mini" type="primary" plain icon="el-icon-document-copy"
                                       @click="copyToClipboard(textTip)">复制格式
                            </el-button>
                          </div>
                          <div class="format-card-body">
                            <div
                              class="format-rule-item"
                              v-for="(item, index) in textTip"
                              :key="index"
                              @mouseover="setHighlightedIndices(index)"
                              @mouseleave="clearHighlightedIndices"
                              :class="{ 'highlighted': highlightedRulesIndex === index }"
                            >
                              <i class="el-icon-success format-rule-icon"></i>
                              {{ item }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="format-right-panel">
                        <div class="format-card example-card">
                          <div class="format-card-header">
                            <h3>示例参考</h3>
                            <el-button size="mini" type="success" plain icon="el-icon-document-copy"
                                       @click="copyToClipboard(textTipExample)">复制示例
                            </el-button>
                          </div>
                          <div class="format-card-body">
                            <div
                              class="format-example-item"
                              v-for="(item, index) in textTipExample"
                              :key="index"
                              @mouseover="setHighlightedRulesIndexByExample(index)"
                              @mouseleave="clearHighlightedIndices"
                              :class="{ 'highlighted': highlightedExampleIndices.includes(index) }"
                            >
                              {{ item }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
            <div v-if="requestType==='file'" class="content-wrapper file-upload-container">
              <el-upload
                class="upload-demo"
                drag
                :action="uploadUrl"
                :data="uploadData"
                :headers="headers"
                multiple
                :limit="1"
                :on-success="handleUploadSuccess"
                :on-remove="handleRemove"
                accept=".txt,.doc,.docx"
                :file-list="fileList"
              >
                <div class="upload-content">
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip">支持txt、doc、docx格式文件上传，仅能上传一个文件</div>
                </div>
              </el-upload>

              <!-- 文件上传后的提示 -->
              <div v-if="fileId" class="upload-success-info">
                <i class="el-icon-document-checked"></i>
                <span>文件已上传成功!</span>
                <el-button type="text" @click="handleRemove(null, [])" class="remove-file-btn">
                  <i class="el-icon-delete"></i> 删除文件
                </el-button>
              </div>
            </div>
          </el-form-item>
        </div>

        <!-- 步骤2: 模板设置 -->
        <div v-show="activeStep === 1" class="step-container">
          <div class="step-header">
            <h2>选择PPT模板风格</h2>
            <p class="step-description">挑选一个适合您内容的专业模板</p>
          </div>

          <div class="theme-selector-container">
            <el-form-item label="PPT生成主题" prop="selectedThemeOptionData" class="theme-form-item">
              <el-select
                v-model="form.templateManagementId"
                @change="handleThemeChange"
                placeholder="请选择PPT生成主题"
                value-key="id"
                class="theme-select">
                <el-option
                  v-for="item in themeOptions"
                  :key="item.id"
                  :label="item.tLabel"
                  :value="item.id"
                  filterable>
                  <div class="theme-option">
                    <span class="theme-label">{{ item.tLabel }}</span>
                    <span class="theme-value">
                      {{ item.tValue }}
                      <i v-if="item.tValue !== 'auto' && (!item.previewList || item.previewList.length === 0)"
                         class="el-icon-loading"></i>
                    </span>
                  </div>
                </el-option>
              </el-select>

              <!--              <div class="theme-preview-hint" v-if="form.templateManagementId">-->
              <!--                <el-button type="text" @click="showThemePreview">-->
              <!--                  <i class="el-icon-view"></i> 预览模板效果-->
              <!--                </el-button>-->
              <!--              </div>-->
            </el-form-item>
          </div>

          <div class="author-input-container">
            <el-form-item label="PPT作者名" prop="author">
              <el-input
                v-model="form.author"
                placeholder="请输入PPT作者名（将显示在幻灯片中）"
                maxlength="20"
                show-word-limit
                class="author-input"
                prefix-icon="el-icon-user"/>
            </el-form-item>
          </div>

          <div class="options-container">
            <el-row :gutter="20">
              <el-col :md="8" :sm="12">
                <div class="option-card">
                  <div class="option-header">
                    <i class="el-icon-download"></i>
                    <span>自动下载</span>
                  </div>
                  <div class="option-content">
                    <el-form-item label="完成后下载" prop="conirmDownloadPpt">
                      <el-switch
                        v-model="conirmDownload"
                        active-text="是"
                        inactive-text="否"
                        active-value="true"
                        inactive-value="false"
                        :disabled=disabledConirmDownload
                        @change="handleConirmDownloadChange">
                      </el-switch>
                    </el-form-item>
                  </div>
                </div>
              </el-col>
              <el-col :md="8" :sm="12">
                <div class="option-card">
                  <div class="option-header">
                    <i class="el-icon-picture"></i>
                    <span>预览设置</span>
                  </div>
                  <div class="option-content">
                    <el-form-item label="生成预览" prop="showLongImg" v-if="form.showLongImg==='false'">
                      <el-switch
                        v-model="form.showLongImg"
                        active-text="是"
                        inactive-text="否"
                        active-value="true"
                        inactive-value="false"
                        @change="handleShowLongImgChange">
                      </el-switch>
                    </el-form-item>
                    <el-form-item label="生成预览" prop="showLongImg" v-if="form.showLongImg==='true'">
                      <el-dropdown @command="handleCommand" size="small" trigger="hover"
                                   :show-timeout="elDropdownShowTimeoOut">
                        <el-switch
                          v-model="form.showLongImg"
                          active-text="是"
                          inactive-text="否"
                          active-value="true"
                          inactive-value="false"
                          @change="handleShowLongImgChange">
                        </el-switch>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item
                            :command="previeQquality[0].highImageQuality"
                            :class="{ 'highlightedSelectLongImg': form.selectedQuality === previeQquality[0].highImageQuality }">
                            高清预览（慢）
                          </el-dropdown-item>
                          <el-dropdown-item
                            :command="previeQquality[1].highImageQuality" divided
                            :class="{ 'highlightedSelectLongImg': form.selectedQuality === previeQquality[1].highImageQuality }">
                            清晰预览（快）
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </el-form-item>
                  </div>
                </div>
              </el-col>

              <!--              <el-col :md="8" :sm="12">-->
              <!--                <div class="option-card">-->
              <!--                  <div class="option-header">-->
              <!--                    <i class="el-icon-download"></i>-->
              <!--                    <span>Logo上传</span>-->
              <!--                  </div>-->
              <!--                  <div class="option-content">-->
              <!--                    <div class="logo-container">-->
              <!--                      <div class="upload-tip">-->
              <!--                        <span><i class="el-icon-info"></i> 支持 JPG、PNG 格式，≤2MB</span>-->
              <!--                      </div>-->
              <!--                      <el-upload-->
              <!--                        class="logo-uploader"-->
              <!--                        :action="uploadUrl"-->
              <!--                        :headers="headers"-->
              <!--                        :show-file-list="false"-->
              <!--                        :on-success="handleLogoSuccess"-->
              <!--                        :before-upload="beforeLogoUpload"-->
              <!--                        :data="{ modeltype: 'logo' }"-->
              <!--                        accept=".jpg,.jpeg,.png"-->
              <!--                      >-->
              <!--                        <img v-if="form.logoPath" :src="form.logoPath" class="logo-image">-->
              <!--                        <i v-else class="el-icon-plus logo-uploader-icon"></i>-->
              <!--                      </el-upload>-->
              <!--                      <div v-if="form.logoPath" class="logo-actions">-->
              <!--                        <el-button type="text" @click="removeLogo" class="remove-logo-btn">-->
              <!--                          <i class="el-icon-delete"></i>-->
              <!--                        </el-button>-->
              <!--                      </div>-->
              <!--                    </div>-->
              <!--                  </div>-->
              <!--                </div>-->
              <!--              </el-col>-->

              <!--              <el-col :md="8" :sm="12" v-if="form.addLogo === 'true'">-->
              <!--                <div class="option-card">-->
              <!--                  <div class="option-header">-->
              <!--                    <i class="el-icon-picture-outline-round"></i>-->
              <!--                    <span>Logo设置</span>-->
              <!--                  </div>-->
              <!--                  <div class="option-content">-->
              <!--                    <el-form-item label="Logo位置" prop="logoPosition" class="text-center">-->
              <!--                      <el-select v-model="form.logoPosition" placeholder="请选择位置" size="small"-->
              <!--                                 style="width: 140px;">-->
              <!--                        <el-option v-for="item in logoPositionList" :key="item.value" :label="item.label"-->
              <!--                                   :value="item.value">-->
              <!--                        </el-option>-->
              <!--                      </el-select>-->
              <!--                    </el-form-item>-->
              <!--                  </div>-->
              <!--                </div>-->
              <!--              </el-col>-->
            </el-row>
          </div>
        </div>

        <!-- 步骤3: PPT生成 -->
        <div v-show="activeStep === 2" class="step-container">
          <div class="step-header">
            <h2>生成您的PPT</h2>
            <p class="step-description">点击"生成PPT"按钮开始创建</p>
          </div>

          <div class="generation-container">
            <div class="buildPptProgressDivBox" v-show="btnLoad">
              <buildPptProgress :from="form" ref="buildPptProgress"
                                @show-text-tip="showTextTip=!showTextTip"></buildPptProgress>
            </div>

            <div class="generation-result" v-if="!btnLoad && isGenerateSuccess">
              <div class="result-header">
                <i class="el-icon-success result-icon"></i>
                <h3>PPT生成成功</h3>
              </div>

              <div class="action-buttons">
                <el-button type="primary" icon="el-icon-download"
                           :disabled="disabledConirmDownload"
                           @click="handleDownloadPpt">
                  下载PPT文件
                </el-button>
                <el-button type="info" icon="el-icon-picture"
                           v-if="form.showLongImg==='true' && imgSinglePreviewList && imgSinglePreviewList.length > 0"
                           @click="showGeneratedPPTPreview">
                  查看PPT预览
                </el-button>
              </div>
            </div>

            <div class="empty-state" v-if="!btnLoad && !isGenerateSuccess">
              <i class="el-icon-document"></i>
              <p>点击下方"生成PPT"按钮开始创建</p>
            </div>
          </div>

          <!-- 统一的预览对话框 -->
          <el-dialog
            :visible.sync="showPreviewDialog"
            :fullscreen="false"
            width="90%"
            custom-class="theme-preview-dialog"
            :modal="true"
            :append-to-body="true"
            :close-on-click-modal="true"
            :show-close="false"
            :destroy-on-close="true"
            @open="handlePreviewOpen"
            @close="handlePreviewClose"
          >
            <template slot="title">
              <div class="preview-dialog-header">
                <span>{{ isTemplatePreview ? '模板预览' : 'PPT预览' }}</span>
                <el-button type="text" @click="handlePreviewClose" class="close-btn">
                  <i class="el-icon-close"></i>
                </el-button>
              </div>
            </template>
            <div class="template-preview-container">
              <div class="preview-disclaimer">
                <i class="el-icon-info"></i>
                <span>预览图仅供参考，实际PPT效果可能有所不同</span>
              </div>
              <!-- 轮播图组件 -->
              <el-carousel
                ref="carousel"
                :autoplay="false"
                trigger="click"
                :initial-index="currentIndex"
                @change="handleCarouselChange"
                height="60vh"
                indicator-position="none"
              >
                <el-carousel-item v-for="(url, index) in currentPreviewList" :key="index">
                  <div class="carousel-item-container">
                    <div class="image-wrapper">
                      <img
                        :src="url"
                        :style="{ transform: `scale(${currentScale})` }"
                        class="preview-image"
                        @error="handleImageError($event)"
                      />
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>

              <!-- 控制按钮 -->
              <div class="preview-controls">
                <el-button-group>
                  <el-button
                    type="primary"
                    icon="el-icon-zoom-out"
                    circle
                    @click="handleZoom(false)"
                  ></el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-zoom-in"
                    circle
                    @click="handleZoom(true)"
                  ></el-button>
                </el-button-group>
                <div class="preview-counter">
                  {{ currentIndex + 1 }} / {{ currentPreviewList.length }}
                </div>
              </div>

              <!-- 缩略图导航 -->
              <div class="preview-thumbnails-container">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-arrow-left"
                  class="thumbnail-nav-btn thumbnail-nav-prev"
                  @click="handleThumbnailScroll('prev')"
                  v-show="canScrollLeft && !isThumbnailsCentered"
                ></el-button>

                <div
                  class="preview-thumbnails"
                  ref="thumbnailsContainer"
                  :class="{ 'centered': isThumbnailsCentered }"
                >
                  <div
                    v-for="(url, index) in currentPreviewList"
                    :key="index"
                    class="thumbnail-item"
                    :class="{ active: currentIndex === index }"
                    @click="$refs.carousel.setActiveItem(index)"
                  >
                    <el-image
                      :src="url"
                      fit="cover"
                    ></el-image>
                  </div>
                </div>
              </div>
            </div>
            <div slot="footer" v-if="!isTemplatePreview" class="dialog-footer">
              <div class="preview-actions">
                <el-button type="success" icon="el-icon-document"
                           :disabled="disabledConirmDownload"
                           @click="handleDownloadPpt">
                  下载PPT文件
                </el-button>
              </div>
            </div>
          </el-dialog>

          <el-backtop target=".app-container" class="elBackTop"></el-backtop>
        </div>

        <!-- 统一的步骤控制按钮 -->
        <el-row class="step-btn-box" id="stepBtnBoxId">
          <el-button type="primary" @click="goPrevStep" :disabled="activeStep === 0">上一步</el-button>
          <template v-if="activeStep !== 2">
            <el-button type="primary" @click="goNextStep">下一步</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="handleSubmit" :loading="btnLoad" :disabled="disabledConirmDownload">
              生成PPT
            </el-button>
          </template>
        </el-row>
      </el-form>


    </div>
  </div>
</template>

<script>
import {
  createPPtToGetPath,
  downLoad,
  downLoadPPt,
  getFontList,
  getUidDetail
} from '@/api/intellectSmartPpt/intellectSmartPpt.js'
import formatTip from '/src/views/intellectSmartPpt/component2/formatTip.vue'
import buildPptProgress from '/src/views/intellectSmartPpt/component2/buildPptProgress.vue'
import {buildMapDataList} from '@/api/pptTemplateManagement/pptTemplateManagement'
import {checkApiKey} from "@/api/keySecretManage/manage";
import Cookies from "js-cookie";
import {Base64} from 'js-base64';
// 异步加载组件
const AsyncImage = () => import('/src/views/intellectSmartPpt/component2/AsyncImage.vue')

export default {
  name: 'CreatePPT',
  // dicts: ['custom_theme', 'logo_position', 'logo_zoom', 'spacing_x_y', 'create_model', 'is_card_note'],
  // dicts: ['logo_position'],
  components: {formatTip, buildPptProgress, AsyncImage},
  data() {
    return {
      // API密钥相关
      /** API密钥验证状态 */
      hasValidApiKey: false,
      /** API密钥值 */
      apiKey: '',
      /** 内容 uid */
      uid: '',
      // 上传相关配置
      /** 文件上传地址 */
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/file/upload',
      /** 上传请求附加数据 */
      uploadData: {modeltype: 'smartPPT'},
      /** 上传请求头 */
      headers: {
        "api-key": this.apiKey
      },
      textContent: '',
      topicContent: '',
      /** 表单数据对象 */
      form: {
        /** PPT内容 */
        query: '',
        /** 是否为卡片笔记 */
        isCardNote: 'false',
        /** PPT主题 */
        theme: '',
        /** 模板管理ID */
        templateManagementId: '',
        /** 是否添加logo */
        addLogo: 'false',
        /** logo位置 */
        logoPosition: 'topRight',
        /** logo缩放比例 */
        logoZoom: '100',
        /** 是否显示长图 */
        showLongImg: 'false',
        /** 长图行数 */
        longImgRows: '1',
        /** 作者名 */
        author: '',
        /** 使用的字体 */
        useFont: '',
        /** 间距设置 */
        spacingXY: '10and40',
        /** 选择的质量 */
        selectedQuality: '',
        /** logo路径 */
        logoPath: ''
      },

      // 步骤和状态控制
      /** 当前活动步骤 */
      activeStep: 0,
      /** 按钮加载状态 */
      btnLoad: false,
      /** 图片下载按钮禁用状态 */
      disabledDwnLoadAsyncImage: false,
      /** 表单验证规则 */
      rules: {
        templateManagementId: [
          {required: true, message: '请选择PPT主题', trigger: ['blur', 'change', 'input']}
        ]
      },

      // 一句话生成相关
      /** 请求类型：text/file/topic */
      requestType: 'text',
      /** 一句话生成的输入内容 */
      topicPrompt: '',
      /** 流式响应的内容 */
      streamContent: '',
      /** 是否正在生成 */
      isGenerating: false,
      /** 生成是否完成 */
      isGenerateComplete: false,
      /** SSE事件源 */
      eventSource: null,

      // 聊天界面相关数据
      /** 聊天消息数组 */
      messages: [],
      /** 是否处于编辑状态 */
      isEditing: false,
      /** 当前编辑的消息索引 */
      editingIndex: -1,
      /** 编辑框的内容 */
      editContent: '',

      // 文件相关
      /** 上传文件ID */
      fileId: '',
      /** 查询数据对象 */
      queryData: {
        filePath: ''
      },

      // UI控制
      /** 异步图片对话框显示状态 */
      dialogVisibleForAsyncImage: false,
      /** 长图URL */
      longImg: '',
      /** 单张预览图片列表 */
      imgSinglePreviewList: [],
      /** 预览图片Blob URL列表 */
      imgSinglePreviewListBlobUrl: [],
      /** PPT下载URL */
      pptUrl: null,
      /** 字体调整显示状态 */
      showFontAdjest: '',
      /** 可用字体列表 */
      fontAvaliableList: [],
      /** 文本提示显示状态 */
      showTextTip: false,
      /** 确认下载状态 */
      conirmDownload: 'true',
      /** 长图显示状态 */
      shoLongImgBox: false,
      /** 确认下载按钮禁用状态 */
      disabledConirmDownload: false,
      /** PPT文件名 */
      pptName: '',

      // 预览质量选项
      /** 预览质量选项 */
      previeQquality: [
        {highImageQuality: 'true'},
        {highImageQuality: 'false'}
      ],
      /** 下拉菜单显示延时 */
      elDropdownShowTimeoOut: parseInt(100),
      /** 高亮规则索引 */
      highlightedRulesIndex: null,
      /** 高亮示例索引数组 */
      highlightedExampleIndices: [],

      // Logo位置选项
      /** Logo位置选项列表 */
      logoPositionList: [
        {
          label: '左上角',
          value: 'topLeft'
        },
        {
          label: '右上角',
          value: 'topRight'
        },
        {
          label: '左下角',
          value: 'bottomLeft'
        },
        {
          label: '右下角',
          value: 'bottomRight'
        }
      ],

      // 文本提示和示例
      /** 文本格式提示 */
      textTip: [
        "文本的第一行是PPT内容主题",
        "一级标题：中文数字 + ','+ 标题内容",
        "二级级标题：'(' + 中文数字 +  ' )' + 标题内容",
        "三标题：使用阿拉伯数字 + '.' + 标题 + ':' + 正文 + '.'",
        "如有总结，延续一级标题,单独一行",
        "各个标题之间必须换行"
      ],
      /** 文本示例 */
      textTipExample: [
        '城市管理讲义',
        '一、导论',
        '1.城市管理概述。城市管理涉及城市组织、运行和管理的原理和规律。',
        '二、城市管理学的基础理论',
        '(一)城市管理学定义',
        '1.公共管理理论：包括公共政策、公共行政等，是城市管理学的理论基础。',
        '2.公共管理事件：包括xxx、xxx等。',
        '(二)城市管理的核心内容',
        '管理活动的对应性，城市管理活动严格依赖管理对象的特征。',
        '三、结语',
        '《城市管理学：公共视角》一书，系统阐述了城市管理理论、方法与实践，对于学习城市管理理论、提升城市管理实践能力具有重要意义。'
      ],
      /** 主题选项 */
      themeOptions: [],
      /** 预览列表 */
      previewList: [],
      /** 预览对话框显示状态 */
      showPreviewListDialog: false,

      /** 文件列表 */
      fileList: [],

      /** 当前缩放比例 */
      currentScale: 1,
      /** 当前选中的缩略图索引 */
      currentIndex: 0,
      canScrollLeft: false,
      canScrollRight: false,

      /** 是否为模板预览 */
      isTemplatePreview: true,
      /** 当前预览列表 */
      currentPreviewList: [],
      /** 预览对话框显示状态 */
      showPreviewDialog: false,

      /** 生成完成状态 */
      isGenerateSuccess: false,

      /** 缩略图居中状态 */
      isThumbnailsCentered: false,
    };
  },
  /**
   * 计算当前步骤是否为第一步
   * @returns {boolean} 是否为第一步
   */
  computed: {
    isFirstStep() {
      return this.activeStep === 0
    },

    /**
     * 计算当前步骤是否为最后一步
     * @returns {boolean} 是否为最后一步
     */
    isLastStep() {
      return this.activeStep === this.steps.length - 1
    },

    /**
     * 计算是否显示下一步按钮
     * @returns {boolean} 是否显示下一步按钮
     */
    showNextButton() {
      return !this.isLastStep && !this.isGenerating
    },

    /**
     * 计算是否显示生成按钮
     * @returns {boolean} 是否显示生成按钮
     */
    showGenerateButton() {
      return this.isLastStep && !this.isGenerating
    },

    /**
     * 计算是否可以进行下一步
     * @returns {boolean} 是否可以进行下一步
     */
    canProceedNext() {
      if (this.activeStep === 0) {
        return this.form.query.trim() !== '' || this.fileId !== ''
      }
      return true
    }
  },
  watch: {
    // 监听预览列表变化
    currentPreviewList: {
      handler() {
        this.$nextTick(() => {
          this.checkThumbnailsOverflow();
        });
      },
      immediate: true
    }
  },
  /**
   * 组件创建时的生命周期钩子
   * 初始化页面数据和事件监听
   */
  created() {
    // 初始化状态
    this.requestType = 'text';  // 默认使用文本输入模式
    this.textContent = '';
    this.topicPrompt = '';
    this.messages = [];
    this.streamContent = '';
    this.isGenerating = false;
    this.isGenerateComplete = false;
    this.fileId = '';
    this.fileList = [];

    // 获取其他必要的初始数据
    this.getLogoPath();
    this.initPageData();

    this.getUrlParam()
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    window.addEventListener('keydown', this.handleKeyDown);
  },
  mounted() {
    // 监听缩略图容器的滚动事件
    this.$nextTick(() => {
      const container = this.$refs.thumbnailsContainer;
      if (container) {
        container.addEventListener('scroll', this.updateScrollButtons);
      }

      // 监听窗口大小变化
      window.addEventListener('resize', this.checkThumbnailsOverflow);
    });
  },

  /**
   * 组件销毁时的生命周期钩子
   * 清理事件监听和数据
   */
  destroyed() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
    localStorage.removeItem('createPPTData')
    window.removeEventListener('keydown', this.handleKeyDown);

    // 清理滚动相关事件监听
    const container = this.$refs.thumbnailsContainer;
    if (container) {
      container.removeEventListener('scroll', this.updateScrollButtons);
    }
    window.removeEventListener('resize', this.checkThumbnailsOverflow);
  },
  methods: {
    /**
     * 根据指定name获取url参数
     */
    handleUrlParam(name) {
      const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
      const r = window.location.search.slice(1).match(reg);
      return r ? decodeURIComponent(r[2]) : null
    },

    // // 校验初始参数信息
    // async getUrlParam() {
    //   const expires = new Date();
    //   expires.setTime(expires.getTime() + 2 * 60 * 60 * 1000); // 当前时间 + 2小时
    //
    //
    //   // 1. 获取 api-key-encrypt 和uid
    //   const cacheAPiKeyEncrypt= Cookies.get("create-ppt-api-key-encrypt");
    //   const urlUid= this.handleUrlParam('uid');
    //
    //
    //
    //   // 2. 验证uid是否存在
    //   if (!this.uid) {
    //     this.$notify.error({
    //       title: '错误',
    //       message: 'uid不存在',
    //       duration: 5000,
    //     });
    //     return;
    //   }
    //   try {
    //     // 3. 校验uid获取相关数据（等待异步结果）
    //     const res = await getUidDetail({uid: this.uid});
    //     if (res.code !== 200 || res.data.error_msg) {
    //       this.hasValidApiKey = false;
    //       this.$notify.error({
    //         title: '错误',
    //         message: res.data?.error_msg || '密钥验证失败',
    //         duration: 8000,
    //       });
    //       return;
    //     }
    //
    //     // 4. 校验通过后执行后续逻辑
    //     this.apiKey = Base64.decode(res.data.apiKey);
    //     this.requestType = res.data.type;
    //     if ('topic' === res.data.type) {
    //       this.topicPrompt = res.data.content
    //     }else if ('text' === res.data.type) {
    //       this.textContent = res.data.content
    //     }
    //     console.log("res",res);
    //
    //     // 4. 校验通过后执行后续逻辑
    //     this.hasValidApiKey = true;
    //     Cookies.set("create-ppt-api-key-encrypt", res.data.apiKey, {expires});
    //
    //
    //     this.headers = {
    //       "api-key": this.apiKey,
    //     };
    //
    //     // 保留除apiKey外的所有URL参数
    //     // 清理URL中的apiKey参数并跳转
    //     const newPath = this.cleanUrlAndNavigate("uid");
    //     await this.$router.push(newPath);
    //
    //     // 其他初始
    //     this.getThemeOptions();
    //     this.initPageData();
    //     window.addEventListener('beforeunload', this.handleBeforeUnload);
    //
    //   } catch (error) {
    //     // 网络请求异常处理
    //     this.hasValidApiKey = false;
    //     this.$notify.error({
    //       title: '错误',
    //       message: 'API密钥验证服务不可用',
    //       duration: 5000,
    //     });
    //     console.error('API校验请求失败:', error);
    //   }

    // // 确定使用哪个uid（优先使用URL参数）
    // this.apiKey = urlApiKey || cacheApiKey;
    //
    // // 如果URL中有apiKey且与缓存不同，更新缓存
    // if (urlApiKey && urlApiKey !== cacheApiKey) {
    //   Cookies.set("create-ppt-api-key", urlApiKey, {expires});
    // }
    //
    //
    // // 2. 验证apiKey是否存在
    // if (!this.apiKey) {
    //   this.$notify.error({
    //     title: '错误',
    //     message: 'apiKey不存在',
    //     duration: 5000,
    //   });
    //   return;
    // }
    //
    //
    // // 3. 获取其他URL参数 type
    // this.requestType = this.handleUrlParam('type') || 'text'
    // // 获取获取其他URL参数 content
    // this.topicPrompt = this.handleUrlParam('content')
    // // 存在content
    // this.form.query = this.topicPrompt;


    // try {
    //   // 4. 校验apiKey（等待异步结果）
    //   const res = await checkApiKey({apiKey: this.apiKey});
    //   if (res.code !== 200 || res.data.error) {
    //     this.hasValidApiKey = false;
    //     this.$notify.error({
    //       title: '错误',
    //       message: res.data?.error || 'API密钥验证失败',
    //       duration: 8000,
    //     });
    //     return;
    //   }
    //
    //   // 5. 校验通过后执行后续逻辑
    //   this.hasValidApiKey = true;
    //   Cookies.set("create-ppt-api-key", this.apiKey, {expires});
    //
    //   this.headers = {
    //     "api-key": this.apiKey,
    //   };
    //
    //   // 保留除apiKey外的所有URL参数
    //   // 清理URL中的apiKey参数并跳转
    //   const newPath = this.cleanUrlAndNavigate("apiKey");
    //   await this.$router.push(newPath);
    //
    //   // 其他初始
    //   this.getThemeOptions();
    //   this.initPageData();
    //
    //   window.addEventListener('beforeunload', this.handleBeforeUnload);
    //
    // } catch (error) {
    //   // 网络请求异常处理
    //   this.hasValidApiKey = false;
    //   this.$notify.error({
    //     title: '错误',
    //     message: 'API密钥验证服务不可用',
    //     duration: 5000,
    //   });
    //   console.error('API校验请求失败:', error);
    // }
    // },
    // 校验初始参数信息
    async getUrlParam() {
      // 1. 获取 api-key 和uid
      const apikey = Cookies.get("create-ppt-api-key");
      const urlUid = this.handleUrlParam('uid');

      // 2. 根据不同情况处理
      // 2.1 两者都不存在
      if (!urlUid && !apikey) {
        this.$notify.error({
          title: '错误',
          message: '缺少必要的认证参数',
          duration: 8000,
        });
        this.hasValidApiKey = false;
        return;
      }

      // 2.2 只存在 apikey
      if (!urlUid && apikey) {
        try {
          this.apiKey = apikey;
          this.hasValidApiKey = true;
          this.headers = {
            "api-key": this.apiKey,
          };
          // 初始化其他数据
          await this.initializeData();
          return;
        } catch (error) {
          this.handleError('缓存的API密钥无效');
          return;
        }
      }

      // 2.3 存在 uid（包括只有uid和两者都有的情况）
      try {
        const res = await getUidDetail({uid: urlUid});
        if (res.code !== 200 || res.data.error_msg) {
          this.handleError(res.data?.error_msg || '密钥验证失败');
          return;
        }

        // uid 校验通过，设置相关数据
        this.apiKey = res.data.apiKey;
        this.requestType = res.data.type;
        if ('topic' === res.data.type) {
          this.topicPrompt = res.data.content;
        } else if ('text' === res.data.type) {
          this.textContent = res.data.content;
        }

        this.hasValidApiKey = true;
        Cookies.set("create-ppt-api-key", res.data.apiKey);
        this.headers = {
          "api-key": this.apiKey,
        };

        // 清理URL中的uid参数并跳转
        const newPath = this.cleanUrlAndNavigate("uid");
        await this.$router.push(newPath);

        // 初始化其他数据
        await this.initializeData();

      } catch (error) {
        this.handleError('API密钥验证服务不可用');
        console.error('API校验请求失败:', error);
      }
    },

    // 处理错误的辅助方法
    handleError(message) {
      this.hasValidApiKey = false;
      this.$notify.error({
        title: '错误',
        message: message,
        duration: 5000,
      });
    },

    // 初始化其他数据的辅助方法
    async initializeData() {
      this.getThemeOptions();
      this.initPageData();
      window.addEventListener('beforeunload', this.handleBeforeUnload);
    },

    // 清理url 相关参数地址
    cleanUrlAndNavigate(item) {
      const currentUrl = new URL(window.location.href);
      const params = new URLSearchParams(currentUrl.search);
      params.delete(item);
      return '/createPPT' + (params.toString() ? `?${params.toString()}` : '');
    },

    /**
     * 调用子组件方法重新连接和清除步骤
     */
    callChildMethod() {
      this.$refs.buildPptProgress.handleReconnect()
      this.$refs.buildPptProgress.clearStep()
    },

    /**
     * 初始化页面数据
     * 从本地存储加载数据或设置默认值
     */
    initPageData() {
      const savedData = localStorage.getItem('createPPTData')
      if (savedData) {
        const data = JSON.parse(savedData)
        this.form = data.form || this.form
        this.fileId = data.fileId || ''
        this.activeStep = data.activeStep || 0
      }
    },

    /**
     * 保存页面数据到本地存储
     */
    saveData() {
      const data = {
        form: this.form,
        fileId: this.fileId,
        activeStep: this.activeStep
      }
      localStorage.setItem('createPPTData', JSON.stringify(data))
    },

    /**
     * 页面关闭前的处理函数
     * @param {Event} e - 浏览器的beforeunload事件对象
     */
    handleBeforeUnload(e) {
      this.saveData()
      e.preventDefault()
      e.returnValue = ''
    },


    /**
     * 重置表单数据
     */
    resetForm() {
      this.form = {
        query: '',
        theme: '',
        font: '',
        logoPosition: '',
        logoZoom: 1,
        spacingXY: '',
        createModel: '',
        isCardNote: false
      }
      this.fileId = ''
      this.activeStep = 0
      this.isGenerateSuccess = false
      this.imgSinglePreviewList = []
      localStorage.removeItem('createPPTData')
    },

    /**
     * 获取Logo路径
     */
    getLogoPath() {
      // 获取当前页面的 base URL（协议+主机+端口）
      let baseUrl = window.location.origin
      if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
        baseUrl = 'http://127.0.0.1:80'
      }
      // getLogos(2).then(res => {
      //   this.form.logoPath = `${baseUrl}${res[0]}`
      //   console.log(res)
      //   console.log(this.form.logoPath)
      //   // this.form.logoPath =  "http://***************:9204/ss/ppt/logo.png";
      // })
    },

    /**
     * 处理表单提交
     */
    handleSubmit() {
      console.log(this.requestType)
      // 设置requestType
      if (this.requestType === 'text' || this.requestType === 'topic') {
        this.form.requestType = 'text'
      } else {
        this.form.requestType = 'file'
        this.form.fileId = this.fileId
      }
      // 设置内容
      if (this.requestType === 'text') {
        // 文本输入模式
        this.form.query = this.textContent;
      }

      if (this.requestType === 'text' && (this.form.query === undefined || this.form.query === null || this.form.query === ''
      )) {
        this.$notify({
          title: '警告',
          message: '请确输入文本内容',
          type: 'warning',
          position: 'top-right'
        })
        return
      }
      if (this.requestType === 'file' && (this.fileId === null || this.fileId === '' || this.fileId === undefined)) {
        this.$notify({
          title: '警告',
          message: '请先上传文本文件',
          type: 'warning',
          position: 'top-right'
        })
        return
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.callChildMethod()
          this.btnLoad = true
          this.imgSinglePreviewList = [] // 重置预览列表
          this.isGenerateSuccess = false // 重置生成状态
          this.activeStep = 2 // 更新步骤条状态为最后一步

          setTimeout(() => {
            createPPtToGetPath(this.form, {apiKey: this.apiKey}).then(res => {
              if (res.code === 200) {
                this.$message.success('生成完成')
                this.queryData.filePath = res.pptPath
                this.pptUrl = res.pptUrl
                this.pptName = res.pptName
                this.imgSinglePreviewList = res.imgSinglePreviewUrlList || []
                this.isGenerateSuccess = true // 设置生成成功状态

                if (this.conirmDownload === 'true') {
                  this.handleDownLoad2(this.pptUrl, this.pptName)
                }
                if (this.form.showLongImg === 'true') {
                  this.getImgPreview2()
                }
                setTimeout(() => {
                  this.btnLoad = false
                }, 2000)
              } else {
                this.$message.error('返回异常：' + res.msg)
                this.btnLoad = false
                this.isGenerateSuccess = false
              }
            }).catch(() => {
              this.btnLoad = false
              this.imgSinglePreviewList = []
              this.isGenerateSuccess = false
            })
          }, 100)
        }
      })
    },
    handleConirmDownloadChange(val) {
      if (val === 'true' && this.pptUrl?.length > 10) {
        this.handleDownLoad2(this.pptUrl, this.pptName)
        // this.handleDownLoad()
        // this.handleDownLoad2()
      }
      // if ('false' === val && 'false' === this.form.showLongImg) {
      //   this.$message({
      //     message: '请确保选择【预览】或下【载选】项中的至少一个',
      //     type: 'warning'
      //   })
      //   setTimeout(() => {
      //     this.conirmDownload = 'true'
      //   }, 50)
      // }
    },
    handleShowLongImgChange(val) {
      if ('false' === val) {
        this.form.selectedQuality = null
        this.shoLongImgBox = false
      }
      if ('true' === val) {
        if (this.imgSinglePreviewList.length > 0) {
          this.shoLongImgBox = true
          this.getImgPreview2()
        }

      }
      if ('false' === val && 'false' === this.conirmDownload) {
        this.$message({
          message: '请确保选择【预览】或【下载】选项中的至少一个',
          type: 'warning'
        })
        setTimeout(() => {
          this.form.showLongImg = 'true'
        }, 50)
      }
    },

    updateDownloadNotification(percent, message, notification = null) {
      if (!notification) {
        // 如果没有现有通知，创建一个新通知
        return this.$notify({
          title: '下载中',
          message: `已下载 ${percent}%`,
          duration: 0,  // 设置为0，防止自动关闭
          type: 'info'
        })
      } else {
        // 更新现有通知
        notification.message = message || `已下载 ${percent}%`
        return notification
      }
    },

    async handleDownLoad2(pptUrl, pptName) {
      this.disabledConirmDownload = true

      let notification = null // 用于存储通知实例
      try {
        const response = await fetch(pptUrl)
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const contentLength = +response.headers.get('Content-Length')

        let receivedLength = 0 // 当前接收到的字节长度
        const chunks = [] // 用来保存接收到的数据块

        while (true) {
          const {done, value} = await reader.read()
          if (done) break
          chunks.push(value)
          receivedLength += value.length

          // 你可以在这里更新下载进度显示
          // console.log(`Received ${((receivedLength / contentLength) * 100).toFixed(2)}%`);
          // 计算下载百分比
          const percent = ((receivedLength / contentLength) * 100).toFixed(2)
          // 实时更新通知内容
          notification = this.updateDownloadNotification(percent, null, notification)
        }

        // 将接收到的数据块合并为一个 Blob
        const blob = new Blob(chunks)
        const url = window.URL.createObjectURL(blob)

        // 创建一个隐藏的链接元素进行下载
        setTimeout(() => {
          const elink = document.createElement('a')
          elink.href = url
          elink.download = pptName ? pptName + '.pptx' : 'example'
          document.body.appendChild(elink)
          elink.click()
          document.body.removeChild(elink)
          // 释放 URL 对象
          window.URL.revokeObjectURL(url)
        }, 800)

        // 下载完成后更新通知内容并关闭通知
        if (notification != null) {
          notification.title = 'PPT下载完成'
          notification.message = '文件已成功下载'
          notification.type = 'success'
          setTimeout(() => {
            this.$notify.closeAll()
            this.disabledConirmDownload = false
          }, 3000)
        }

      } catch (error) {
        console.error('Download error:', error)

        // 出现错误时更新通知内容
        if (notification != null) {
          notification.title = '下载失败'
          notification.message = '文件下载时出现错误，请稍后再试。'
          notification.type = 'error'
        }

        setTimeout(() => {
          this.$notify.closeAll()
          this.disabledConirmDownload = false
        }, 4000)
      }
    },

    async openImageInNewWindow() {
      try {
        const newWindow = await new Promise((resolve, reject) => {
          const wnd = window.open('', '_blank')
          if (wnd) {
            resolve(wnd)
          } else {
            reject(new Error('新窗口未能打开。可能是浏览器阻止了弹出窗口。'))
          }
        })

        if (newWindow) {
          newWindow.document.write(`
        <html>
          <head>
            <style>
              .img {
                width: 50%;
                height: auto;
                display: flex;
                margin: auto;
                border-radius: 10px;
                overflow: hidden;
                transition: all 1s ease;
              }
              .img:hover {
                width: 52%;
                border: 5px solid #ddd;
                border-radius: 10px;
                overflow: hidden;
              }
            </style>
          </head>
          <body>
            <div class="container">
          v-for="(url, index) in ${this.imgSinglePreviewList}"
          :key="index"
          :src="'data:image/jpeg;base64,' + url"
          class="image-item"
        ></el-image>
            </div>
          </body>
        </html>
      `)
          newWindow.document.close()
          await new Promise((resolve) => setTimeout(resolve, 1000))
          newWindow.focus()
        }
      } catch (error) {
        console.error(error.message)
        alert(error.message)
        this.shoLongImgBox = true
      }
    },

    gethandleFontList() {
      getFontList().then(res => {
        this.fontAvaliableList = res.data
        // console.log(res.data)
      })
    },
    copyToClipboard(textArray) {
      // 将数组转换为字符串，每个元素用换行符分隔
      const text = textArray.join('\n')

      // 创建一个隐藏的 textarea 元素
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)

      // 选择并复制文本
      textarea.select()
      document.execCommand('copy')

      // 移除 textarea 元素
      document.body.removeChild(textarea)

      // 可选: 提示用户复制成功
      this.$message.success('内容已复制到剪贴板')
    },


    handelShowTextFormate() {
      this.showTextTip = !this.showTextTip
    },
    handleUploadSuccess(response, file) {
      console.log(response)
      if (response.code === 200) {
        this.fileId = response.data.id
        this.fileList = [{name: file.name, url: ''}]
        this.$message.success('上传成功')
      } else {
        this.$notify.error({
          title: '错误' + response.code,
          message: response.msg
        });
      }
    },
    handleRemove(file, fileList) {
      this.fileId = ''
      this.fileList = []
    },
    handleAddLogoChange(val) {
      if (val === 'false') {
        this.form.logoPosition = null
        this.form.logoZoom = null
      }
      if (val === 'true') {
        this.form.logoPosition = 'topRight'
        this.form.logoZoom = '100'
      }
    },
    setHighlightedIndices(index) {
      this.highlightedRulesIndex = index
      this.highlightedExampleIndices = this.getExampleIndicesForRule(index)
    },
    setHighlightedRulesIndexByExample(index) {
      this.highlightedRulesIndex = this.getRuleIndexForExample(index)
      this.highlightedExampleIndices = this.getExampleIndicesForRule(this.highlightedRulesIndex)
    },
    clearHighlightedIndices() {
      this.highlightedRulesIndex = null
      this.highlightedExampleIndices = []
    },
    getExampleIndicesForRule(index) {
      switch (index) {
        case 0:
          return [0] // 规则索引0对应案例索引0
        case 1:
          return [1, 3, 9]
        case 2:
          return [4, 7]
        case 3:
          return [2, 5, 6]
        case 4:
          return [8, 10]
        case 5:
          return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        default:
          return []
      }
    },
    getRuleIndexForExample(index) {
      switch (index) {
        case 0:
          return 0 // 案例索引0对应规则索引0
        case 1:
          return 1
        case 2:
          return 1
        case 3:
          return 3
        case 4:
          return 2
        case 5:
          return 3
        case 6:
          return 3
        case 7:
          return 2
        case 8:
          return 4
        case 9:
          return 3
        case 10:
          return 3
        case 11:
          return 4
        default:
          return null
      }
    },
    handleCommand(command) {
      this.form.selectedQuality = command
      if (command === 'true') {
        this.$message({
          message: '生成高清预览图',
          type: 'success'
        })
      } else if (command === 'false') {
        this.$message({
          message: '生成清晰预览图',
          type: 'success'
        })
      }
    },
    handleDialogVisibleForAsyncImageDownload() {
      this.disabledDwnLoadAsyncImage = true
      this.$refs.AsyncImage.downloadImage()
      setTimeout(() => {
        this.disabledDwnLoadAsyncImage = false
      }, 3000)
    },
    handleDownloadPpt() {
      if (this.pptUrl && this.pptName) {
        this.handleDownLoad2(this.pptUrl, this.pptName)
      }

    },

    /** 获取模板列表 */
    getThemeOptions() {
      buildMapDataList(null, {apiKey: this.apiKey}).then(res => {
        this.themeOptions = [...res.data];
        this.form.templateManagementId = this.themeOptions[0]?.id;
      });
    },

    /**
     * 处理主题变更
     * @param {string} themeId - 主题ID
     */
    handleThemeChange(themeId) {
      console.log("处理主题模板预览效果")
      const selectedTheme = this.themeOptions.find(item => item.id === themeId);

      // 只保存选择的主题，不显示预览
      if (selectedTheme) {
        // 保存预览列表，但不显示预览对话框
        this.previewList = selectedTheme.previewList || [];
      }
    },

    /**
     * 显示主题预览
     */
    showThemePreview() {
      const selectedTheme = this.themeOptions.find(item => item.id === this.form.templateManagementId);

      if (!selectedTheme) {
        this.$notify({
          title: '提示',
          message: '未找到选中的模板主题',
          type: 'warning',
          position: 'top-right'
        });
        return;
      }

      if (selectedTheme.tValue === 'auto') {
        this.$notify({
          title: '提示',
          message: '随机主题无预览图片',
          type: 'info',
          position: 'top-right'
        });
        return;
      }

      if (!selectedTheme.previewList || selectedTheme.previewList.length === 0) {
        this.$notify({
          title: '提示',
          message: '该主题暂无预览图片',
          type: 'info',
          position: 'top-right'
        });
        return;
      }

      this.isTemplatePreview = true;
      this.currentPreviewList = selectedTheme.previewList;
      this.showPreviewDialog = true;
    },

    /**
     * 显示生成的PPT预览
     */
    showGeneratedPPTPreview() {
      if (!this.imgSinglePreviewList || this.imgSinglePreviewList.length === 0) {
        this.$notify({
          title: '提示',
          message: '暂无预览图片',
          type: 'warning',
          position: 'top-right'
        });
        return;
      }

      this.isTemplatePreview = false;
      this.currentPreviewList = this.imgSinglePreviewList;
      this.showPreviewDialog = true;
    },

    /**
     * 关闭预览对话框
     */
    handlePreviewClose() {
      this.showPreviewDialog = false;
      this.currentIndex = 0;
      this.currentScale = 1;
    },

    // 修改getImgPreview2方法
    getImgPreview2() {
      try {
        this.showGeneratedPPTPreview();
      } catch (err) {
        this.$message.error('显示预览图出现错误，请重新尝试');
      }
    },

    /**
     * 处理图片缩放
     */
    handleZoom(isZoomIn) {
      const scale = isZoomIn ? 1.2 : 0.8;
      this.currentScale = Math.max(0.5, Math.min(2, this.currentScale * scale));
    },

    /**
     * 处理键盘事件
     */
    handleKeyDown(e) {
      if (!this.showPreviewListDialog) return;

      switch (e.key) {
        case 'ArrowLeft':
          this.$refs.carousel.prev();
          break;
        case 'ArrowRight':
          this.$refs.carousel.next();
          break;
        case 'Escape':
          this.handleThemeClose();
          break;
      }
    },

    /**
     * 处理一句话生成请求
     * @returns {Promise<void>}
     */
    async handleTopicGenerate() {
      if (!this.topicPrompt.trim()) {
        this.$message.warning('请输入内容描述');
        return;
      }

      if (this.topicPrompt.length > 20) {
        this.$message.warning('内容描述不能超过20个字符');
        this.topicPrompt = this.topicPrompt.substring(0, 30);
        return;
      }

      // 添加用户消息到消息数组
      this.messages.push({
        role: 'user',
        content: this.topicPrompt,
        timestamp: new Date().getTime()
      });

      this.isGenerating = true;
      this.streamContent = '';
      this.isGenerateComplete = false;

      try {
        const response = await fetch(process.env.VUE_APP_BASE_API + '/ppt/createPpt/chatModelFlux', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'api-key': this.apiKey
          },
          body: JSON.stringify({
            content: this.topicPrompt,
            type: 'topic'
          })
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let currentAssistantMessage = '';

        while (true) {
          const {done, value} = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, {stream: true});
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim().startsWith('data:')) {
              try {
                const jsonStr = line.replace('data:', '').trim();
                const data = JSON.parse(jsonStr);
                if (data.role === 'assistant' && data.resType === 'answer') {
                  currentAssistantMessage += data.content;
                  this.streamContent = currentAssistantMessage;
                  this.scrollToBottom();
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e);
              }
            }
          }
        }

        if (buffer.trim()) {
          try {
            const jsonStr = buffer.replace('data:', '').trim();
            const data = JSON.parse(jsonStr);
            if (data.role === 'content') {
              currentAssistantMessage += data.content;
              this.streamContent = currentAssistantMessage;
              this.scrollToBottom();
            }
          } catch (e) {
            console.error('Error parsing final SSE data:', e);
          }
        }

        // 添加模型回复到消息数组
        if (currentAssistantMessage) {
          this.messages.push({
            role: 'assistant',
            content: currentAssistantMessage,
            timestamp: new Date().getTime()
          });
        }

        // 设置一句话生成内容为最新的助手回复内容
        this.topicContent = currentAssistantMessage;

        // 清空输入框
        this.topicPrompt = '';
      } catch (error) {
        this.$message.error('生成失败：' + error.message);
      } finally {
        this.isGenerating = false;
        this.isGenerateComplete = true;

        // 检查是否成功生成了内容，如果成功则自动进入下一步
        if (this.messages.length > 0 && this.getLastAssistantIndex() !== -1) {
          const lastAssistantMsg = this.messages[this.getLastAssistantIndex()];
          if (lastAssistantMsg && lastAssistantMsg.content.trim()) {
            // 显示成功提示
            this.$notify({
              title: '内容生成成功',
              message: '将自动进入模板设置步骤',
              type: 'success',
              duration: 2000
            });

            // 延迟1.5秒后自动进入下一步，给用户一点时间查看生成的内容
            setTimeout(() => {
              this.goNextStep();
            }, 1500);
          }
        }
      }
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const chatContainer = document.querySelector('.chat-messages');
        if (chatContainer) {
          chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        document.getElementById("stepBtnBoxId").scrollIntoView({
          behavior: 'smooth', //顺滑的滚动
          block: 'center', //容器上下的中间
          inline: 'start', //容器左右的左边
        });
      });
    },

    /**
     * 获取最后一条助手消息的索引
     */
    getLastAssistantIndex() {
      // 从后往前查找第一个助手消息
      for (let i = this.messages.length - 1; i >= 0; i--) {
        if (this.messages[i].role === 'assistant') {
          return i;
        }
      }
      return -1;
    },

    /**
     * 进入下一步
     */
    goNextStep() {
      if (this.activeStep < 2) {
        if (this.activeStep === 0) {
          // 检查是否有内容
          if (this.requestType === 'topic') {
            // 从消息历史中获取最新的助手回复
            const lastAssistantIndex = this.getLastAssistantIndex();
            if (lastAssistantIndex === -1) {
              this.$message.warning('请先生成内容');
              return;
            }

            // 使用最后一条助手消息作为PPT内容
            this.form.query = this.messages[lastAssistantIndex].content;
          } else if (this.requestType === 'text') {
            if (!this.textContent || !this.textContent.trim()) {
              this.$message.warning('请输入内容');
              return;
            }
          } else if (this.requestType === 'file') {
            if (!this.fileId) {
              this.$message.warning('请上传文件');
              return;
            }
          }
        }
        this.activeStep++;
      }
    },

    /**
     * 返回上一步
     */
    goPrevStep() {
      if (this.activeStep > 0) {
        this.activeStep--;
      }
    },

    // 添加切换请求类型的处理方法
    handleRequestTypeChange() {
      // 保存当前内容
      if (this.requestType === 'text') {
        // 切换到文本输入模式
        this.textContent = '';  // 清空文本内容
        this.form.query = this.textContent;
        this.messages = [];     // 清空消息历史
      } else if (this.requestType === 'topic') {
        // 切换到一句话生成模式
        this.topicPrompt = '';  // 清空主题输入
        this.form.query = '';   // 清空表单查询
        this.messages = [];     // 清空消息历史
        this.streamContent = ''; // 清空流式内容
        this.isGenerating = false; // 重置生成状态
      } else {
        // 切换到文件上传模式
        this.form.query = '';   // 清空表单查询
        this.fileId = '';       // 清空文件ID
        this.fileList = [];     // 清空文件列表
      }

      // 重置通用状态
      this.isGenerateComplete = false;
      this.isGenerating = false;
      this.btnLoad = false;
    },

    /**
     * 处理预览对话框打开
     */
    handlePreviewOpen() {
      console.log('Preview dialog opened');
      console.log('Current preview list:', this.previewList);
      this.currentIndex = 0;
      this.currentScale = 1;

      // 等待DOM更新后初始化滚动按钮状态和滚动位置
      this.$nextTick(() => {
        // 检查缩略图是否需要滚动
        this.checkThumbnailsOverflow();
        this.scrollThumbnailToCenter(0);
      });
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      console.error('Image load error:', e);
      const target = e.target;
      if (target) {
        // 创建错误显示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'image-error';

        const icon = document.createElement('i');
        icon.className = 'el-icon-picture-outline';

        const text = document.createElement('p');
        text.textContent = '图片加载失败';

        errorDiv.appendChild(icon);
        errorDiv.appendChild(text);

        // 替换图片
        target.parentNode.replaceChild(errorDiv, target);
      }
    },

    /**
     * 处理轮播图变化
     * @param {number} index - 当前活动项索引
     */
    handleCarouselChange(index) {
      this.currentIndex = index;
      this.$nextTick(() => {
        this.scrollThumbnailToCenter(index);
      });
    },

    /**
     * 滚动缩略图到中心位置
     * @param {number} index - 缩略图索引
     */
    scrollThumbnailToCenter(index) {
      const container = this.$refs.thumbnailsContainer;
      if (!container) return;

      const thumbnails = container.querySelectorAll('.thumbnail-item');
      if (!thumbnails || !thumbnails[index]) return;

      const thumbnail = thumbnails[index];
      const containerWidth = container.offsetWidth;
      const thumbnailWidth = thumbnail.offsetWidth;

      // 计算缩略图应该滚动到的位置（使其居中）
      const scrollLeft = thumbnail.offsetLeft - (containerWidth / 2) + (thumbnailWidth / 2);

      // 平滑滚动到计算的位置
      container.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: 'smooth'
      });

    },

    handleLogoSuccess(response, file) {
      if (response.code === 200) {
        this.form.logoPath = URL.createObjectURL(file.raw);
        this.$message.success('Logo上传成功');
      } else {
        this.$message.error(response.msg || 'Logo上传失败');
      }
    },

    beforeLogoUpload(file) {
      const isValidFormat = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isValidFormat) {
        this.$message.error('Logo只能是JPG/PNG/GIF格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('Logo大小不能超过2MB!');
        return false;
      }
      return true;
    },

    removeLogo() {
      this.$confirm('确定要删除当前Logo吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.logoPath = '';
        this.$message.success('Logo已删除');
      }).catch(() => {
      });
    },

    startEditMessage(index) {
      this.isEditing = true;
      this.editingIndex = index;
      this.editContent = this.messages[index].content;
    },

    saveEditMessage() {
      // 更新消息内容
      this.messages[this.editingIndex].content = this.editContent;

      // 如果是助手消息，且是最后一条助手消息，则同步到form.query
      if (this.messages[this.editingIndex].role === 'assistant' &&
        this.editingIndex === this.getLastAssistantIndex()) {
        this.form.query = this.editContent;
      }

      // 重置编辑状态
      this.isEditing = false;
      this.editingIndex = -1;
      this.editContent = '';
    },

    cancelEditMessage() {
      this.isEditing = false;
      this.editingIndex = -1;
      this.editContent = '';
    },

    /**
     * 更新滚动按钮状态
     */
    updateScrollButtons() {
      const container = this.$refs.thumbnailsContainer;
      if (!container) return;

      this.canScrollLeft = container.scrollLeft > 0;
      this.canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth;
      this.isThumbnailsCentered = container.scrollLeft === 0 && container.scrollWidth === container.clientWidth;
    },

    /**
     * 检查缩略图是否需要滚动
     */
    checkThumbnailsOverflow() {
      const container = this.$refs.thumbnailsContainer;
      if (!container) return;

      // 检查是否所有缩略图都可以在容器中完全显示
      const thumbnailsWidth = container.scrollWidth;
      const containerWidth = container.clientWidth;

      // 如果内容宽度小于或等于容器宽度，则应该居中显示
      this.isThumbnailsCentered = thumbnailsWidth <= containerWidth;

      // 更新滚动按钮状态
      this.updateScrollButtons();
    },

    /**
     * 处理缩略图滚动
     * @param {string} direction - 滚动方向
     */
    handleThumbnailScroll(direction) {
      const container = this.$refs.thumbnailsContainer;
      if (!container) return;

      // 如果居中显示，则不需要滚动
      if (this.isThumbnailsCentered) return;

      const scrollAmount = direction === 'next' ? 200 : -200;
      container.scrollTo({
        left: container.scrollLeft + scrollAmount,
        behavior: 'smooth'
      });
    },
  }
}
</script>
<style lang="scss" scoped>
// 定义响应式断点
$screen-xs: 480px;
$screen-sm: 768px;
$screen-md: 992px;
$screen-lg: 1200px;
$screen-xl: 1600px;

.app-container {
  overflow-y: auto;
  max-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  min-height: 100vh;
  padding: 20px;

  @media (max-width: $screen-sm) {
    padding: 10px;
  }
}

.ck-form {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  @media (max-width: $screen-sm) {
    width: 100%;
    padding: 20px 15px;
    border-radius: 8px;
  }
}

.steps-nav {
  margin: 0 auto 20px;
  max-width: 800px;
  padding: 20px 0;

  @media (max-width: $screen-sm) {
    padding: 10px 0;

    ::v-deep .el-step__title {
      font-size: 14px;
    }

    ::v-deep .el-step__icon {
      width: 30px;
      height: 30px;
    }
  }

  @media (max-width: $screen-xs) {
    ::v-deep .el-step__title {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80px;
    }
  }
}

.one-click-input {
  margin-bottom: 30px;

  .el-input {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }
}

.stream-content {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);

  @media (max-width: $screen-sm) {
    padding: 15px;
    border-radius: 8px;
  }

  .el-input.el-textarea {
    width: 100%;
  }
}

.upload-demo {
  .el-upload__tip {
    font-size: 14px;
    color: #909399;
    margin-top: 12px;
    text-align: center;

    @media (max-width: $screen-sm) {
      font-size: 12px;
    }
  }
}

.upload-content {
  text-align: center;
  padding: 20px;

  @media (max-width: $screen-sm) {
    padding: 15px 10px;

    .el-icon-upload {
      font-size: 32px;
    }

    .el-upload__text {
      font-size: 14px;
    }
  }
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: $screen-sm) {
    flex-wrap: wrap;
    gap: 5px;
    padding: 8px;
  }
}

.text-tip {
  margin: 20px 0;
  text-align: right;

  .el-link {
    font-size: 14px;
    color: #409EFF;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.text-tip-box {
  margin-top: 30px;

  .el-card {
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
  }
}

.step-btn-box {
  text-align: center;
  margin-top: 40px;
  padding: 20px 0;

  @media (max-width: $screen-sm) {
    margin-top: 20px;
    padding: 15px 0;
  }

  .el-button {
    min-width: 120px;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    margin: 0 10px;
    transition: all 0.3s ease;

    @media (max-width: $screen-sm) {
      min-width: 100px;
      height: 40px;
      font-size: 14px;
      margin: 0 5px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);

      @media (max-width: $screen-sm) {
        transform: translateY(-1px);
      }
    }
  }
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.fontTip {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  transition: all 0.3s ease;
}

.fontTip:hover {
  color: #00daa7;
  font-size: 13px;
  font-weight: bold;
}

.text-tip-active {
  color: #04bc87;
  font-size: 15px;
  font-style: italic;
}


.elBackTop {
  transition: all 0.3s ease;
}

.elBackTop:hover {
  color: #0ef6b0;
  transform: scale(1.3);
}

.box-card {

}

.buildPptProgressDivBox {
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.textRulesItem {
  font-size: 14px;
}

.textExamplesItem {
  font-size: 12px;
}

.highlighted {
  color: #068769;
  transform: scale(1.05);
  transition: all 0.3s ease;
  font-weight: bold;
  font-style: italic;
}

.highlighted:hover {
  display: flex;
  font-weight: bold;
  font-style: italic;
  justify-content: left;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.highlightedSelectLongImg {
  background-color: #d9ebff; /* 高亮背景颜色 */
}

.imgBox {
  width: 70%;
  position: relative;
  display: block;
  margin: 0 auto;
  text-align: center;
  transition: all 1s ease;
}

.imgBox:hover {
  width: 72%;
  border: 3px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.elimg {
  position: relative;
  display: inline-block;
}

.imgBorder {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.shoLongImgBoxDiv {
  margin: 30px 5px 50px 5px;
}

.footerTipFont {
  height: 20px;
  font-size: 10px;
  padding: 5px; /* 添加内边距 */
  color: #666666;
  position: relative;
  right: 10px;
  bottom: 30px;
  margin-bottom: -20px;
  z-index: 10;
}

.api-key-missing {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  z-index: 1000;
  overflow: hidden;
}

.message-container {
  width: 600px;
  text-align: center;
  padding: 50px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.5s ease-in-out;
  position: relative;
  z-index: 1;
}

.icon-wrapper {
  background: rgba(230, 162, 60, 0.1);
  width: 96px;
  height: 96px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.warning-icon {
  font-size: 48px;
  color: #E6A23C;
}

.divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #ebeef5, transparent);
  margin: 24px 0;
}

.message-container h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 28px;
  font-weight: 600;
}

.main-message {
  color: #606266;
  font-size: 18px;
  margin: 0 0 24px;
}

.additional-info {
  background: #f8f9fb;
  padding: 20px;
  border-radius: 8px;
  text-align: left;
  margin: 24px 0;

  p {
    color: #409EFF;
    font-size: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      color: #606266;
      margin: 8px 0;
      display: flex;
      align-items: center;

      i {
        color: #67C23A;
        margin-right: 8px;
      }
    }
  }
}

.contact-support {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  margin-top: 24px;

  i {
    margin-right: 8px;
    font-size: 20px;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.one-click-input {
  margin-bottom: 20px;

  .el-input {
    width: 100%;
  }
}

.stream-content {
  margin-top: 20px;

  .content-box {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

.step-container {
  animation: fadeIn 0.5s ease-out;
}

.step-header {
  text-align: center;
  margin-bottom: 40px;

  @media (max-width: $screen-sm) {
    margin-bottom: 25px;
  }

  h2 {
    font-size: 28px;
    color: #303133;
    margin-bottom: 12px;
    font-weight: 600;

    @media (max-width: $screen-sm) {
      font-size: 22px;
      margin-bottom: 8px;
    }
  }

  .step-description {
    font-size: 16px;
    color: #606266;
    margin: 0;

    @media (max-width: $screen-sm) {
      font-size: 14px;
    }
  }
}

.input-type-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;

  .el-radio-group {
    display: flex;
    gap: 20px;

    @media (max-width: $screen-md) {
      gap: 10px;
    }

    @media (max-width: $screen-sm) {
      flex-direction: column;
      width: 100%;
      align-items: center;
      gap: 15px;
    }

    .el-radio {
      height: 80px;
      width: 200px;
      border: 2px solid #ebeef5;
      border-radius: 12px;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      @media (max-width: $screen-md) {
        width: 180px;
        height: 70px;
      }

      @media (max-width: $screen-sm) {
        width: 100%;
        max-width: 300px;
        height: 60px;
      }

      &:hover {
        transform: translateY(-2px);
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.05);
      }

      &.is-checked {
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.1);

        .el-radio__label {
          color: #409EFF;
        }
      }

      .el-radio__input {
        display: none;
      }

      .el-radio__label {
        font-size: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;

        @media (max-width: $screen-sm) {
          flex-direction: row;
          gap: 10px;
          font-size: 14px;
        }

        i {
          font-size: 24px;
          margin-bottom: 8px;

          @media (max-width: $screen-sm) {
            margin-bottom: 0;
            font-size: 20px;
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-textarea {
  width: 100%;
  margin-bottom: 10px;
}

.generate-input-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.format-helper {
  margin-top: 12px;
  padding: 0 10px;
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.format-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  padding: 10px;
  background: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.format-toggle-link {
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-2px);
  }

  i {
    color: #409EFF;
    font-size: 16px;
  }

  span {
    font-size: 14px;
  }
}

.format-tip-component {
  flex: 1;
}

.format-instructions {
  margin-top: 20px;
  background: #f9fafc;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.upload-demo {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

.upload-content {
  text-align: center;
  padding: 20px;
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.theme-selector-container {
  max-width: 800px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.theme-form-item {
  @media (max-width: $screen-sm) {
    ::v-deep .el-form-item__label {
      float: none;
      text-align: left;
      display: block;
      margin-bottom: 10px;
    }

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.theme-select {
  width: 100%;
}

.theme-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .theme-label {
    font-size: 15px;
    color: #303133;

    @media (max-width: $screen-sm) {
      font-size: 14px;
    }
  }

  .theme-value {
    color: #909399;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;

    @media (max-width: $screen-sm) {
      font-size: 12px;
    }
  }
}

.theme-preview-hint {
  text-align: center;
  margin-top: 15px;

  @media (max-width: $screen-sm) {
    margin-top: 10px;
  }
}

.author-input-container {
  max-width: 600px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }

  @media (max-width: $screen-sm) {
    ::v-deep .el-form-item__label {
      float: none;
      text-align: left;
      display: block;
      margin-bottom: 10px;
    }

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .author-input {
    width: 100%;
  }
}

.options-container {
  margin-bottom: 30px;

  @media (max-width: $screen-sm) {
    // 在小屏幕上调整el-row的gutter
    ::v-deep .el-row {
      margin-left: -5px !important;
      margin-right: -5px !important;
    }

    ::v-deep .el-col {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
}

.option-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 150px;
  margin-bottom: 20px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: $screen-sm) {
    min-height: 120px;
    margin-bottom: 10px;

    &:hover {
      transform: translateY(-3px);
    }
  }

  .option-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    i {
      margin-right: 8px;
      font-size: 18px;
      color: #409EFF;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    @media (max-width: $screen-sm) {
      padding: 12px 15px;

      i {
        font-size: 16px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  .option-content {
    padding: 20px;
    display: flex;
    justify-items: center;


    @media (max-width: $screen-sm) {
      padding: 15px;
    }

    // Logo设置盒子特殊样式
    .el-form-item[prop="logoPosition"] {
      display: flex;
      flex-direction: column;
      align-items: center;

      .el-form-item__label {
        text-align: center;
        padding: 0 0 10px;
      }

      .el-form-item__content {
        margin-left: 0 !important;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.generation-container {
  max-width: 800px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.generation-result {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeInUp 0.8s ease-out;

  @media (max-width: $screen-sm) {
    padding: 20px;
    border-radius: 10px;
  }

  .result-header {
    margin-bottom: 30px;

    @media (max-width: $screen-sm) {
      margin-bottom: 20px;
    }

    .result-icon {
      font-size: 60px;
      color: #67C23A;
      margin-bottom: 20px;

      @media (max-width: $screen-sm) {
        font-size: 40px;
        margin-bottom: 15px;
      }
    }

    h3 {
      font-size: 24px;
      color: #303133;
      margin: 0;

      @media (max-width: $screen-sm) {
        font-size: 20px;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: $screen-sm) {
      flex-direction: column;
      gap: 10px;
      align-items: center;
    }

    .el-button {
      min-width: 160px;
      height: 50px;
      border-radius: 25px;
      font-size: 16px;

      @media (max-width: $screen-sm) {
        min-width: 200px;
        height: 44px;
        font-size: 14px;
      }
    }
  }
}

.empty-state {
  background: #f5f7fa;
  border-radius: 16px;
  padding: 50px;
  text-align: center;
  border: 2px dashed #dcdfe6;

  @media (max-width: $screen-sm) {
    padding: 30px;
    border-radius: 10px;
  }

  i {
    font-size: 60px;
    color: #909399;
    margin-bottom: 20px;

    @media (max-width: $screen-sm) {
      font-size: 40px;
      margin-bottom: 15px;
    }
  }

  p {
    font-size: 18px;
    color: #606266;
    margin: 0;

    @media (max-width: $screen-sm) {
      font-size: 16px;
    }
  }
}

.preview-dialog {
}

.preview-container {
  max-width: 100%;
  overflow: hidden;
}

.preview-disclaimer {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  i {
    color: #E6A23C;
    margin-right: 8px;
    font-size: 16px;
  }

  span {
    font-size: 14px;
    color: #606266;
  }
}

.preview-actions {
  display: flex;
  justify-content: center;
  gap: 20px;

  .el-button {
    min-width: 140px;
  }
}

.theme-preview-dialog {
  @media (max-width: $screen-sm) {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin-top: 3vh !important;
    }
  }

  .el-dialog {
    margin-top: 5vh !important;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }

  .el-dialog__header {
    background: #f5f7fa;
    padding: 15px 20px;
    margin: 0;
    border-bottom: 1px solid #ebeef5;

    @media (max-width: $screen-sm) {
      padding: 10px 15px;
    }

    .preview-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #303133;

      .close-btn {
        color: #909399;
        font-size: 20px;

        &:hover {
          color: #f56c6c;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    background: #ffffff;
    position: relative;
  }
}

.template-preview-container {
  height: 100%;
  background: #f9f9f9;
  position: relative;
  display: flex;
  flex-direction: column;
}

.carousel-item-container {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.image-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.preview-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.preview-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 2;

  .el-button-group {
    display: flex;

    .el-button {
      padding: 5px;
      font-size: 12px;
      opacity: 0.8;
      transition: all 0.2s ease;

      &:hover {
        opacity: 1;
      }
    }
  }

  .preview-counter {
    color: #606266;
    font-size: 12px;
    min-width: 40px;
    text-align: center;
    opacity: 0.9;
  }
}

.preview-thumbnails-container {
  margin-top: auto;
  height: 90px;
  background: #f5f5f5;
  border-top: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  position: relative;

  .thumbnail-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    transition: all 0.3s ease;

    &.thumbnail-nav-prev {
      left: 10px;
    }

    &.thumbnail-nav-next {
      right: 10px;
    }
  }
}

.preview-thumbnails {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0 50px;
  gap: 8px;
  overflow-x: auto;
  scroll-behavior: smooth;
  justify-content: flex-start; /* 默认左对齐 */

  /* 当子元素数量较少时（可以完全显示）则居中显示 */
  &.centered {
    justify-content: center;
    padding: 0 10px; /* 居中时减少内边距 */
  }

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  .thumbnail-item {
    flex: 0 0 auto;
    width: 80px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.active {
      border-color: #409EFF;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .el-image {
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-wrapper {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.content-wrapper-top {

  margin-top: 20px;
}

.content-textarea {
  width: 100%;
  margin-bottom: 10px;
}

.format-helper {
  margin-top: 12px;
  padding: 0 10px;
  width: 100%;
}

.format-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  padding: 10px;
  background: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.format-toggle-link {
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-2px);
  }

  i {
    color: #409EFF;
    font-size: 16px;
  }

  span {
    font-size: 14px;
  }
}

.format-tip-component {
  flex: 1;
}

.format-instructions {
  margin-top: 20px;
  background: #f9fafc;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.file-upload-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-demo {
  width: 100%;
  display: flex;
  justify-content: center;
}

.upload-content {
  text-align: center;
  padding: 20px;
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;

  i {
    color: #67C23A;
    font-size: 20px;
  }

  span {
    color: #67C23A;
    font-weight: 500;
    margin-right: 15px;
  }

  .remove-file-btn {
    color: #909399;

    &:hover {
      color: #f56c6c;
    }

    i {
      color: inherit;
      font-size: 14px;
      margin-right: 5px;
    }
  }
}

// 保留其他格式说明的样式
.format-instruction-header {
  text-align: center;
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    color: #303133;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #606266;
    margin: 0;
  }
}

.format-instruction-container {
  display: flex;
  gap: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.format-left-panel, .format-right-panel {
  flex: 1;
}

.format-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 300px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &.example-card {
    background: #f0f9ff;
  }
}

.format-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.format-card-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.format-rule-item, .format-example-item {
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;

  &.highlighted {
    color: #409EFF;
    transform: translateX(10px);
    font-weight: 500;
  }
}

.format-rule-item {
  padding-left: 28px;

  .format-rule-icon {
    position: absolute;
    left: 0;
    top: 9px;
    color: #67C23A;
  }
}

.format-example-item {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  padding: 4px 8px;
  border-radius: 4px;

  &.highlighted {
    background: rgba(64, 158, 255, 0.1);
  }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.preview-counter {
  font-size: 14px;
  color: #606266;
}

.thumbnail-item {
  width: 80px;
  height: 60px;
  object-fit: cover;
  margin-right: 10px;
  cursor: pointer;
  border: 2px solid transparent;

  &.active {
    border-color: #409EFF;
  }
}

.keyboard-tips {
  font-size: 12px;
  color: #909399;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;

  i {
    font-size: 40px;
    margin-bottom: 10px;
  }
}

// 为移动端添加辅助工具类
@media (max-width: $screen-sm) {
  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-mt-10 {
    margin-top: 10px !important;
  }

  .mobile-mb-10 {
    margin-bottom: 10px !important;
  }

  .mobile-p-10 {
    padding: 10px !important;
  }

  .mobile-hidden {
    display: none !important;
  }
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.upload-tip {
  text-align: center;
  margin-bottom: 8px;
  width: 100%;

  span {
    color: #909399;
    font-size: 11px;
    display: inline-flex;
    align-items: center;
    gap: 4px;

    i {
      color: #909399;
      font-size: 12px;
    }
  }
}

.logo-uploader {
  text-align: center;
  width: 100%;
  margin-bottom: 8px;

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409EFF;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  transition: color 0.3s ease;
}

.logo-actions {
  text-align: center;
  margin-top: 12px;

  .remove-logo-btn {
    color: #f56c6c;
    padding: 5px 15px;
    transition: all 0.3s ease;

    &:hover {
      color: #ff4949;
      transform: translateY(-1px);
    }

    i {
      margin-right: 4px;
    }
  }
}

.chat-container {
  margin-top: 20px;
  padding: 20px;
  background: #f9fafc;
  border-radius: 12px;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  @media (max-width: $screen-sm) {
    padding: 15px;
    border-radius: 8px;
  }

  .chat-messages {
    max-height: 1200px;
    overflow-y: auto;
    padding: 10px;
    will-change: transform; /* 提示浏览器这个元素会有变换，创建新的渲染层 */
    transform: translateZ(0); /* 触发GPU加速 */
    backface-visibility: hidden; /* 减少重绘 */

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.02);
      border-radius: 3px;
    }
  }

  // 优化消息项渲染性能
  .message-item {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    max-width: 85%;
    transition: opacity 0.3s ease;
    will-change: transform, opacity; /* 提示浏览器这些属性会改变 */
    contain: layout; /* 告诉浏览器这个元素内部的布局不会影响外部 */

    &.message-user {
      align-self: flex-end;
      margin-left: auto;

      .message-bubble {
        background: #ecf5ff;
        border: 1px solid #d9ecff;
        border-radius: 16px 4px 16px 16px;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);

        .message-icon {
          background: #409EFF;
          order: 1;
          margin-left: 8px;
          margin-right: 0;
        }
      }
    }

    &.message-assistant {
      align-self: flex-start;
      margin-right: auto;
      will-change: transform;

      .message-bubble {
        background: #f0f9eb;
        border: 1px solid #e1f3d8;
        border-radius: 4px 16px 16px 16px;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);

        .message-icon {
          background: #67C23A;
          order: -1;
          margin-right: 8px;
          margin-left: 0;
        }
      }
    }

    .message-bubble {
      padding: 12px 16px;
      position: relative;
      display: flex;
      align-items: flex-start;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      will-change: transform, box-shadow;
      contain: layout;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
    }

    .message-icon {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        color: white;
        font-size: 16px;
      }
    }
  }

  // 优化消息内容的布局性能
  .message-content {
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    padding: 0 4px;
    flex: 1;
    contain: content; /* 内容隔离，减少重绘范围 */
  }

  // 优化加载动画效果，使用transform而非opacity动画
  @keyframes pulse {
    0%, 100% {
      transform: scale(0.95);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .typing-indicator span {
    width: 8px;
    height: 8px;
    background: #67C23A;
    border-radius: 50%;
    display: inline-block;
    animation: pulse 1.4s linear infinite;
    will-change: transform;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }

  // 优化淡入动画
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.edit-actions {
  position: relative;
  width: 100%;
  padding-bottom: 40px;
  margin: 10px 0;
}

.edit-textarea {
  width: 100%;
  margin-bottom: 8px;
}


.edit-buttons {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 8px 0;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.edit-buttons .el-button {
  padding: 6px 12px;
  font-size: 12px;
  min-width: 60px;
}

.message-actions {
  position: absolute;
  right: 8px;
  bottom: -24px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 2px 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  opacity: 0;
  display: flex;
  align-items: center;

  .el-button {
    padding: 4px 8px;

    i {
      margin-right: 2px;
    }
  }

  .ready-indicator {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: #67C23A;
    font-size: 12px;
    animation: pulse 1.5s infinite;

    i {
      margin-right: 4px;
    }
  }
}

.message-item:hover .message-actions {
  opacity: 1;
  transform: translateY(-2px);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  height: 20px;
}
</style>


