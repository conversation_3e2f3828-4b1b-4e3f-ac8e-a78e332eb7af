<template>
  <div class="links-container">

    <el-link type="primary" @click="downloadTxtRules" class="fontTip">
      输入文本规则
    </el-link>
    <!--    <el-link type="primary" @click="downloadTxtFile(textContent)" class="fontTip">-->
    <!--      城市管理讲义示例1-->
    <!--    </el-link>-->
    <!--    <el-link type="primary" @click="downloadTxtFile(textContent2)" class="fontTip">-->
    <!--      中国近代史示例1-->
    <!--    </el-link>-->
    <!--    <el-link type="primary" @click="downloadTxtFile(textContent4)" class="fontTip">-->
    <!--      养老服务示例2-->
    <!--    </el-link>-->
    <el-link type="primary" @click="downloadTxtFile(textContent5, '城市摊贩市场规划与管理')" class="fontTip">
      城市摊贩市场规划与管理
    </el-link>
    <el-link type="primary" @click="downloadTxtFile(textContent6, 'Markdown示例')" class="fontTip">
      Markdown 示例
    </el-link>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    // 这里存放数据
    return {
      textContent: [
        '城市管理讲义',
        '一、导论',
        '1. 城市管理概述。城市管理涉及城市组织、运行和管理的原理和规律。',
        '二、城市管理学的基础理论',
        '一）城市管理学定义',
        '1. 公共管理理论。包括公共政策、公共行政等，是城市管理学的理论基础。',
        '二）城市管理的核心内容',
        '管理活动的对应性，城市管理活动严格依赖管理对象的特征。',
        '1.解决城市问题。改善城市环境和实现城市发展等四个阶段性目标。',
        '三、结语',
        '《城市管理学：公共视角》一书，系统阐述了城市管理理论、方法与实践，对于学习城市管理理论、提升城市管理实践能力具有重要意义。'
      ],
      rulesTip: [
        '文本的第一行是名字',
        '一级标题：中文数字（一） + （中/英）【， 。 、】 + 标题内容表示',
        '二级级标题：(中/英)【(】 + 中文数字（一） + （中/英）【)】 + 标题内容',
        '三标题：使用阿拉伯数字(1) + 【.】 + 标题 +（中/英）【. :】 + 正文',
        '如有总结，放在小标题上面 或者 单独一行',
        '各个标题之间必须换行'
      ],
      rulesTip2: [
        '格式：',
        '文本的第一行是 ppt名字',
        '一级标题：中文数字加顿号“、”加大标题内容表示',
        '二级级标题：中文数字加中文右括号“）',
        '三级标题：使用阿拉伯数字加英文“.”加标题以句号“。”结尾，后面跟着正文',
        '如果有总结，总结在小标题上面',
        '各个标题之间必须换行'
      ],
      textContent2: [
        '中国近代史'],
      textContent3: [
        '第12章城市摊贩市场规划与管理'],
      textContent4: [
        '养老服务',
        '4. 内容丰富。养老服务内容不断丰富，除了基本的生活照料和医疗保健，还注重老年人的精神文化需求和社会参与需求。'
      ],
      textContent5: [
        '第12章 城市摊贩市场规划与管理',
        '一、引言',
        '城市摊贩市场规划与管理是城市高质量发展的重要组成部分，对提升城市景观和市民生活品质具有重大意义。本章将详细介绍城市摊贩市场规划与管理的内容。',
        '二、城市摊贩概述',
        '（一）定义',
        '城市摊贩是指在城市特定区域内，从事商品销售或服务提供的个体经营者。他们通常以临时或半固定形式经营，依托摊位或流动设备，服务于城市居民的日常需求。摊贩经营的商品或服务涵盖广泛，包括食品、饮料、手工艺品、日用品等，具有较强的地域性和时效性。摊贩的灵活性和低成本经营模式使其成为城市商业的一种重要补充形式，有助于满足多样化的消费需求和提供便利服务。',
        '（二）类型与特征',
        '1. 类型多样化：包括街头小吃、杂货售卖、手工艺品等多种形式。',
        '2. 流动性：摊贩的经营场所不固定，具有较强的流动性。',
        '3. 灵活性：经营方式灵活，可以根据市场需求调整商品种类和销售方式。',
        '4. 低成本运作：与传统店铺相比，摊贩的启动成本低，进入门槛较低。',
        '（三）区域集中性',
        '1. 摊贩集中区域：摊贩通常集中在城市中的特定区域，如夜市、商业步行街和交通枢纽。',
        '2. 形成集聚效应：区域集中性能够形成摊贩集聚效应，进一步提升这些区域的经济活力和吸引力。',
        '3. 设立专门经营区：通过在特定区域设立摊贩经营区，城市管理者可以有效管理摊贩活动，减少对核心商业区和主要交通干道的干扰。',
        '4.促进区域繁荣：集中经营不仅促进了商业繁荣，还推动了文化交流，提升了区域的活力。',
        '5.配合公共设施：集中区域便于与城市公共设施配合，如卫生设施、停车场等，提高整体运营效率',
        '6.提高效率与体验：集中经营提升了摊贩的经营效率，同时改善了顾客的购物体验，带来了更好的便利性和服务质量。',
        '（四）摊贩的社会角色',
        '1. 满足低收入群体需求：摊贩提供价格低廉的商品和服务，适合城市低收入人群的消费能力。',
        '2. 促进城市经济活力：摊贩市场为城市提供了灵活的经济活动形式，活跃了城市的经济气氛。',
        '3. 增加就业机会：摊贩市场为一些就业困难的群体提供了重要的就业渠道。',
        '（五）摊贩市场的社会影响',
        '1. 影响城市形象：摊贩的存在可能会影响城市的整体美观和形象，需要通过管理措施加以改善。',
        '2. 增强社区互动：摊贩市场常常成为社区居民交流和互动的场所，增强了社区的凝聚力。',
        '三、经济文化波动与摊贩市场',
        '（一）文化交流的载体',
        '1. 文化交流平台：摊贩不仅是经济活动的一部分，也促进了传统手工艺品和地方特色食品的传播，推动了文化传承。',
        '2. 多元文化交融：摊贩市场汇集了不同文化，通过各自的商品和服务，丰富了城市的文化氛围。',
        '3. 节庆展示平台：摊贩市场也是展示节庆活动和地方特色的场所，增强了城市的文化吸引力。',
        '（二）灵活应对经济波动',
        '1. 灵活应对经济变化：摊贩市场具备较强的灵活性，能够迅速适应经济波动带来的变化。',
        '2. 提供就业机会：在经济下行时期，摊贩市场常能迅速扩展，为失业人员和经济困难家庭创造就业机会。',
        '3. 低门槛的经营方式：摊贩的经营门槛低、启动成本较低，使许多人能够通过摆摊获取收入，缓解经济压力。',
        '4. 调整经营策略：摊贩市场可以根据市场需求和经济环境的变化，灵活调整商品种类和价格策略，以满足不同消费者的需求。',
        '（三）经济危机中的摊贩角色',
        '1. 应对经济不景气：摊贩市场在经济危机时提供了更多的经济适应性解决方案。',
        '2. 支持低收入家庭：在经济困难时期，摊贩成为许多低收入家庭的重要经济来源。',
        '（四）文化多样性的促进',
        '1. 传统工艺的展示：摊贩市场成为传统手工艺品的展示和销售平台。',
        '2. 文化交流平台：不同文化背景的摊贩在市场中进行交流，促进了文化的多样性。',
        '四、城市摊贩市场产生的原因',
        '（一）消费需求推动',
        '1. 低成本的消费需求：城市中许多低收入群体倾向于在摊贩处购买价格低廉的商品。',
        '2. 多元化的消费需求：部分消费者更喜欢个性化的、手工制作的小商品或地方特色小吃。',
        '（二）城市化带来的社会结构变化',
        '1. 外来务工人员增加：城市化进程中，大量农民工涌入城市，成为摊贩的主要群体。',
        '2. 城市居民消费水平差异大：城市内部不同阶层的消费能力差异显著，摊贩市场可以填补低收入群体的消费需求。',
        '3. 城市空间的非正式利用：城市未规划的空间为摊贩提供了经营场所，特别是在交通枢纽和商业区附近。',
        '（三）政策与制度因素',
        '1. 就业压力：失业和再就业问题使得部分城市居民选择从事摊贩生意以谋生。',
        '2. 政策支持不足：政府针对低收入群体的就业政策尚不完善，促使他们通过摆摊谋生。',
        '3. 城市管理疏漏：部分城市未严格执行摊贩管理法规，导致摊贩数量迅速增加。',
        '（四）社会结构变化',
        '1. 政策执行难度：在大规模城市化进程中，如何有效执行摊贩管理政策成为一个挑战。',
        '2. 社会需求变迁：消费者的需求和偏好变化也会影响摊贩市场的形成和发展。',
        '（五）市场经济的特点',
        '1. 市场适应性：摊贩市场具有较高的市场适应性，可以迅速响应市场变化。',
        '2. 低门槛进入：摊贩市场的低门槛进入机制使得更多创业者能够参与其中。',
        '五、城市摊贩市场规划与管理',
        '（一）规划布局',
        '1. 摊贩市场的区域划分：通过合理划分摊贩市场，避免对城市核心商业区和主要交通干道的影响。',
        '2. 摊贩集中经营区：政府可以在城市的特定区域设立合法摊贩经营区，如夜市、早市等。',
        '3. 空间弹性利用：根据城市的不同功能区，在不同时段允许摊贩经营。',
        '（二）管理措施',
        '1. 许可证制度：摊贩必须向政府申请许可证，确保经营合法性。',
        '2. 摊贩活动时间规定：限制摊贩的经营时间，如规定只能在早晚高峰时段以外营业。',
        '3. 税费征收：根据摊贩的经营区域和规模收取相应的税费，确保政府对摊贩市场的收益。',
        '4. 违规处罚：设立专门的执法队伍，严厉打击无证摊贩以及违规经营行为。',
        '（三）动态监控',
        '1. 实时监控摊贩活动：通过现代科技手段，城市管理者可以有效跟踪摊贩的分布和经营情况，使用GPS定位、RFID技术以及摄像头和传感器获取现场数据。',
        '2. 优化管理策略：收集的数据用于分析摊贩的经营模式和市场需求，帮助及时调整管理措施，发现违规行为，维护市场秩序和公平。',
        '（四）市场整顿与优化',
        '1. 市场环境改善：对摊贩市场进行定期整顿，改善市场环境。',
        '2. 优化经营条件：提升摊贩经营区域的基础设施和服务条件。',
        '六、数字化管理',
        '（一）信息化平台搭建',
        '1. 信息系统的统一搭建：建立统一的摊贩管理信息系统，将摊贩许可证申请、续期、税务申报等服务整合到一个平台，简化申请和管理流程，提高办事效率。',
        '2. 在线支付与数据分析：平台提供在线支付功能，方便摊贩缴纳相关费用，并实现数据的集中管理和分析，帮助政府了解摊贩市场的运行状况和发展趋势。',
        '3. 政策查询与合规意识：信息化平台还提供信息查询服务，使摊贩能够随时了解最新政策和规定，提升合规经营的意识和能力。',
        '（二）数字化监控工具',
        '1. 实时监控：通过GPS和RFID技术，实时监控摊贩的位置和经营情况，提高管理效率。',
        '2. 快速识别与应对：GPS追踪摊贩轨迹，确保合法经营，RFID快速识别身份和产品，减少假冒风险，同时提供实时数据，助力管理应对突发问题。',
        '（三）智慧城市结合',
        '将摊贩管理系统与智慧城市平台结合，实现统一调度和管理。智慧城市平台整合了城市的各类信息系统，通过智能化手段优化资源配置和服务效率。摊贩管理系统与智慧城市平台的结合可以实现数据共享和跨部门协作，提升管理的精细化和智能化水平。例如，智慧城市平台可以提供实时交通信息，帮助摊贩选择最佳经营位置，减少对交通的干扰。同时，平台也能汇集市民反馈，优化摊贩经营区域的布局和管理措施。',
        '（四）数据分析',
        '1. 动态变化分析：通过数据分析了解摊贩市场的动态变化，制定更具针对性的管理措施。',
        '2. 提取价值信息：数据分析帮助政府从大量数据中提取有价值的信息，包括经营模式和市场趋势。',
        '3. 识别热点区域：对历史和实时数据的分析可以识别市场的热点区域和潜在问题。',
        '4. 预测未来趋势：通过数据分析预测未来的发展趋势，依据此调整政策和措施。',
        '（五）智能化服务提升',
        '1. 智能支付系统：推广智能支付系统，提升交易便利性。',
        '2. 智能监控系统：利用智能监控技术实时跟踪摊贩活动，确保市场秩序。',
        '十一、结语',
        '希望通过本章的学习，能够对城市摊贩市场的规划与管理有一个全面的理解，并能够在今后的工作和实践中，应用所学知识，推动城市摊贩市场的健康、可持续发展，为提升城市生活质量和经济活力作出贡献。同时，也鼓励读者关注和参与相关研究和实践，持续推动这一领域的创新和优化，以实现更加美好的城市环境和社会效益！'
      ],

      textContent6: [
        '第12章 城市摊贩市场规划与管理',
        "# 引言",
        "城市摊贩市场规划与管理是城市高质量发展的重要组成部分，对提升城市景观和市民生活品质具有重大意义。本章将详细介绍城市摊贩市场规划与管理的内容。",
        "# 城市摊贩概述",
        "## 定义",
        "城市摊贩是指在城市特定区域内，从事商品销售或服务提供的个体经营者。他们通常以临时或半固定形式经营，依托摊位或流动设备，服务于城市居民的日常需求。摊贩经营的商品或服务涵盖广泛，包括食品、饮料、手工艺品、日用品等，具有较强的地域性和时效性。摊贩的灵活性和低成本经营模式使其成为城市商业的一种重要补充形式，有助于满足多样化的消费需求和提供便利服务。",
        "## 类型与特征",
        "### 类型多样化：包括街头小吃、杂货售卖、手工艺品等多种形式。",
        "### 流动性：摊贩的经营场所不固定，具有较强的流动性。",
        "### 灵活性：经营方式灵活，可以根据市场需求调整商品种类和销售方式。",
        "### 低成本运作：与传统店铺相比，摊贩的启动成本低，进入门槛较低。",
        "## 区域集中性",
        "### 摊贩集中区域：摊贩通常集中在城市中的特定区域，如夜市、商业步行街和交通枢纽。",
        "### 形成集聚效应：区域集中性能够形成摊贩集聚效应，进一步提升这些区域的经济活力和吸引力。",
        "### 设立专门经营区：通过在特定区域设立摊贩经营区，城市管理者可以有效管理摊贩活动，减少对核心商业区和主要交通干道的干扰。",
        "### 促进区域繁荣：集中经营不仅促进了商业繁荣，还推动了文化交流，提升了区域的活力。",
        "### 配合公共设施：集中区域便于与城市公共设施配合，如卫生设施、停车场等，提高整体运营效率",
        "### 提高效率与体验：集中经营提升了摊贩的经营效率，同时改善了顾客的购物体验，带来了更好的便利性和服务质量。",
        "## 摊贩的社会角色",
        "### 满足低收入群体需求：摊贩提供价格低廉的商品和服务，适合城市低收入人群的消费能力。",
        "### 促进城市经济活力：摊贩市场为城市提供了灵活的经济活动形式，活跃了城市的经济气氛。",
        "### 增加就业机会：摊贩市场为一些就业困难的群体提供了重要的就业渠道。",
        "## 摊贩市场的社会影响",
        "### 影响城市形象：摊贩的存在可能会影响城市的整体美观和形象，需要通过管理措施加以改善。",
        "### 增强社区互动：摊贩市场常常成为社区居民交流和互动的场所，增强了社区的凝聚力。",
        "# 经济文化波动与摊贩市场",
        "## 文化交流的载体",
        "### 文化交流平台：摊贩不仅是经济活动的一部分，也促进了传统手工艺品和地方特色食品的传播，推动了文化传承。",
        "### 多元文化交融：摊贩市场汇集了不同文化，通过各自的商品和服务，丰富了城市的文化氛围。",
        "### 节庆展示平台：摊贩市场也是展示节庆活动和地方特色的场所，增强了城市的文化吸引力。",
        "## 灵活应对经济波动",
        "### 灵活应对经济变化：摊贩市场具备较强的灵活性，能够迅速适应经济波动带来的变化。",
        "### 提供就业机会：在经济下行时期，摊贩市场常能迅速扩展，为失业人员和经济困难家庭创造就业机会。",
        "### 低门槛的经营方式：摊贩的经营门槛低、启动成本较低，使许多人能够通过摆摊获取收入，缓解经济压力。",
        "### 调整经营策略：摊贩市场可以根据市场需求和经济环境的变化，灵活调整商品种类和价格策略，以满足不同消费者的需求。",
        "## 经济危机中的摊贩角色",
        "### 应对经济不景气：摊贩市场在经济危机时提供了更多的经济适应性解决方案。",
        "### 支持低收入家庭：在经济困难时期，摊贩成为许多低收入家庭的重要经济来源。",
        "## 文化多样性的促进",
        "### 传统工艺的展示：摊贩市场成为传统手工艺品的展示和销售平台。",
        "### 文化交流平台：不同文化背景的摊贩在市场中进行交流，促进了文化的多样性。",
        "# 城市摊贩市场产生的原因",
        "## 消费需求推动",
        "### 低成本的消费需求：城市中许多低收入群体倾向于在摊贩处购买价格低廉的商品。",
        "### 多元化的消费需求：部分消费者更喜欢个性化的、手工制作的小商品或地方特色小吃。",
        "## 城市化带来的社会结构变化",
        "### 外来务工人员增加：城市化进程中，大量农民工涌入城市，成为摊贩的主要群体。",
        "### 城市居民消费水平差异大：城市内部不同阶层的消费能力差异显著，摊贩市场可以填补低收入群体的消费需求。",
        "### 城市空间的非正式利用：城市未规划的空间为摊贩提供了经营场所，特别是在交通枢纽和商业区附近。",
        "## 政策与制度因素",
        "### 就业压力：失业和再就业问题使得部分城市居民选择从事摊贩生意以谋生。",
        "### 政策支持不足：政府针对低收入群体的就业政策尚不完善，促使他们通过摆摊谋生。",
        "### 城市管理疏漏：部分城市未严格执行摊贩管理法规，导致摊贩数量迅速增加。",
        "## 社会结构变化",
        "### 政策执行难度：在大规模城市化进程中，如何有效执行摊贩管理政策成为一个挑战。",
        "### 社会需求变迁：消费者的需求和偏好变化也会影响摊贩市场的形成和发展。",
        "## 市场经济的特点",
        "### 市场适应性：摊贩市场具有较高的市场适应性，可以迅速响应市场变化。",
        "### 低门槛进入：摊贩市场的低门槛进入机制使得更多创业者能够参与其中。",
        "# 城市摊贩市场规划与管理",
        "## 规划布局",
        "### 摊贩市场的区域划分：通过合理划分摊贩市场，避免对城市核心商业区和主要交通干道的影响。",
        "### 摊贩集中经营区：政府可以在城市的特定区域设立合法摊贩经营区，如夜市、早市等。",
        "### 空间弹性利用：根据城市的不同功能区，在不同时段允许摊贩经营。",
        "## 管理措施",
        "### 许可证制度：摊贩必须向政府申请许可证，确保经营合法性。",
        "### 摊贩活动时间规定：限制摊贩的经营时间，如规定只能在早晚高峰时段以外营业。",
        "### 税费征收：根据摊贩的经营区域和规模收取相应的税费，确保政府对摊贩市场的收益。",
        "### 违规处罚：设立专门的执法队伍，严厉打击无证摊贩以及违规经营行为。",
        "## 动态监控",
        "### 实时监控摊贩活动：通过现代科技手段，城市管理者可以有效跟踪摊贩的分布和经营情况，使用GPS定位、RFID技术以及摄像头和传感器获取现场数据。",
        "### 优化管理策略：收集的数据用于分析摊贩的经营模式和市场需求，帮助及时调整管理措施，发现违规行为，维护市场秩序和公平。",
        "## 市场整顿与优化",
        "### 市场环境改善：对摊贩市场进行定期整顿，改善市场环境。",
        "### 优化经营条件：提升摊贩经营区域的基础设施和服务条件。",
        "## 社区参与",
        "### 社区意见收集：定期收集社区居民对摊贩市场的意见和建议。",
        "### 合作共建：通过政府、社区和摊贩的合作，共同维护市场秩序和环境。",
        "# 数字化管理",
        "## 信息化平台搭建",
        "### 信息系统的统一搭建：建立统一的摊贩管理信息系统，将摊贩许可证申请、续期、税务申报等服务整合到一个平台，简化申请和管理流程，提高办事效率。",
        "### 在线支付与数据分析：平台提供在线支付功能，方便摊贩缴纳相关费用，并实现数据的集中管理和分析，帮助政府了解摊贩市场的运行状况和发展趋势。",
        "### 政策查询与合规意识：信息化平台还提供信息查询服务，使摊贩能够随时了解最新政策和规定，提升合规经营的意识和能力。",
        "## 数字化监控工具",
        "### 实时监控：通过GPS和RFID技术，实时监控摊贩的位置和经营情况，提高管理效率。",
        "### 快速识别与应对：GPS追踪摊贩轨迹，确保合法经营，RFID快速识别身份和产品，减少假冒风险，同时提供实时数据，助力管理应对突发问题。",
        "## 智慧城市结合",
        "将摊贩管理系统与智慧城市平台结合，实现统一调度和管理。智慧城市平台整合了城市的各类信息系统，通过智能化手段优化资源配置和服务效率。摊贩管理系统与智慧城市平台的结合可以实现数据共享和跨部门协作，提升管理的精细化和智能化水平。例如，智慧城市平台可以提供实时交通信息，帮助摊贩选择最佳经营位置，减少对交通的干扰。同时，平台也能汇集市民反馈，优化摊贩经营区域的布局和管理措施。",
        "(四) 数据分析",
        "### 动态变化分析：通过数据分析了解摊贩市场的动态变化，制定更具针对性的管理措施。",
        "### 提取价值信息：数据分析帮助政府从大量数据中提取有价值的信息，包括经营模式和市场趋势。",
        "### 识别热点区域：对历史和实时数据的分析可以识别市场的热点区域和潜在问题。",
        "### 预测未来趋势：通过数据分析预测未来的发展趋势，依据此调整政策和措施。",
        "## 智能化服务提升",
        "### 智能支付系统：推广智能支付系统，提升交易便利性。",
        "### 智能监控系统：利用智能监控技术实时跟踪摊贩活动，确保市场秩序。",
        "# 结语",
        "希望通过本章的学习，能够对城市摊贩市场的规划与管理有一个全面的理解，并能够在今后的工作和实践中，应用所学知识，推动城市摊贩市场的健康、可持续发展，为提升城市生活质量和经济活力作出贡献。同时，也鼓励读者关注和参与相关研究和实践，持续推动这一领域的创新和优化，以实现更加美好的城市环境和社会效益！",
      ]
    }

  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
  },
  beforeCreate() {
  },
  beforeMount() {
  }, // 生命周期 - 挂载之前
  beforeUpdate() {
  }, // 生命周期 - 更新之前
  updated() {
  }, // 生命周期 - 更新之后
  beforeDestroy() {
  }, // 生命周期 - 销毁之前
  destroyed() {
  }, // 生命周期 - 销毁完成
  activated() {
  },
  // 方法集合
  methods: {
    downloadTxtFile(data, name) {
      // 文本内容
      const content = data.join('\n') // 将数组转化为字符串，并用换行符连接

      // 创建 Blob 对象
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })

      // 创建下载链接
      const url = URL.createObjectURL(blob)

      // 创建一个临时的链接元素
      const a = document.createElement('a')
      a.href = url
      a.download = (name === '' || name === null) ? '示例.txt' : name + '.txt' // 设置下载文件的名称
      document.body.appendChild(a) // 将链接添加到 DOM 中
      a.click() // 触发点击事件下载文件
      document.body.removeChild(a) // 下载完成后移除链接

      // 释放 URL 对象
      URL.revokeObjectURL(url)
    },
    downloadTxtRules() {
      // 文本内容
      const content = this.rulesTip.join('\n') // 将数组转化为字符串，并用换行符连接
      // 创建 Blob 对象
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })

      // 创建下载链接
      const url = URL.createObjectURL(blob)

      // 创建一个临时的链接元素
      const a = document.createElement('a')
      a.href = url
      a.download = '文本输入规则.txt' // 设置下载文件的名称
      document.body.appendChild(a) // 将链接添加到 DOM 中
      a.click() // 触发点击事件下载文件
      document.body.removeChild(a) // 下载完成后移除链接

      // 释放 URL 对象
      URL.revokeObjectURL(url)
    }

  } // 如果页面有 keep-alive 缓存功能,这个函数会触发
}
</script>

<style scoped>
.fontTip {
  font-size: 11px;
}

.links-container {
  display: flex; /* 使用 flex 布局 */
  gap: 20px; /* 设置元素之间的间距 */
}

.fontTip {
  transition: all 0.2s ease;
}

.fontTip:hover {
  color: #00daa7;
  font-size: 12px;
  font-weight: bold;
}
</style>
