<template>
  <div class="progress-container">
    <div class="progress-box">
      <el-steps :active="active" finish-status="success" align-center>
        <el-step title="初始化" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 1"></i>
            <i class="el-icon-setting" v-else></i>
          </template>
        </el-step>
        <el-step title="文本校验" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 2"></i>
            <i class="el-icon-document-checked" v-else></i>
          </template>
        </el-step>
        <el-step title="文本解析" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 3"></i>
            <i class="el-icon-reading" v-else></i>
          </template>
        </el-step>
        <el-step title="PPT生成" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 4"></i>
            <i class="el-icon-picture-outline" v-else></i>
          </template>
        </el-step>
        <el-step title="字体修正" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 5"></i>
            <i class="el-icon-edit" v-else></i>
          </template>
        </el-step>
        <el-step title="添加Logo" class="progress-step" v-show="from.addLogo==='true'">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 6"></i>
            <i class="el-icon-picture" v-else></i>
          </template>
        </el-step>
        <el-step title="渲染预览" class="progress-step" v-show="from.showLongImg==='true'">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 7"></i>
            <i class="el-icon-view" v-else></i>
          </template>
        </el-step>
        <el-step title="完成" class="progress-step">
          <template slot="icon">
            <i class="el-icon-loading" v-if="active === 8"></i>
            <i class="el-icon-success" v-else></i>
          </template>
        </el-step>
      </el-steps>

      <!-- 添加进度提示 -->
      <div class="progress-status" v-if="active > 0 && active < 8">
        <div class="status-icon">
          <i class="el-icon-loading"></i>
        </div>
        <div class="status-text">
          正在{{ getStepDescription(active) }}...
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

import {getToken} from "@/utils/auth";
import EventSourcePolyfill from 'eventsource-polyfill';
import { v4 as uuidv4 } from 'uuid';
export default {
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    from: {
      type: Object,
    },
  },
  watch: {},
  data() {
    // 这里存放数据
    return {
      messages: [],
      warnMsg: '',
      eventSource: null,
      heartbeatInterval: 180000, // 心跳检测间隔（毫秒）
      userId: '', // 用户ID
      active: 0,
      num: 0,
      uniqueKey: '',
      token: '',
      reconnectInterval: 300, // 默认重连间隔时间 0.3 秒
      maxReconnectAttempts: 5, // 最大重连次数
      reconnectAttempts: 0,    // 当前重连次数
      sseConnected: false,     // SSE连接状态
      reachedMaxAttempts: false, // 是否已达到最大重连次数标志
    }
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    // console.log(this.from);
    this.startListening();
    this.startHeartbeat();
  },
  beforeDestroy() {
    this.cleanup();
  },
  created() {
    this.token = getToken();
  },
  // 方法集合
  methods: {
    startListening() {
      // 如果已达到最大重连次数，不再创建新连接
      if (this.reachedMaxAttempts) {
        console.warn('已达到最大重连次数，不再创建新连接');
        return;
      }

      const baseURL = process.env.VUE_APP_BASE_API;
      const uniqueKey = uuidv4();
      this.eventSource = new EventSourcePolyfill(`${baseURL}/ppt/sse?uniqueKey=${uniqueKey}`, {
        silentTimeout: 600000,
        bufferSizeLimit: 100 * 1024 * 1024,
      });
      this.eventSource.onmessage = (event) => {
        try {
          const parsedData = JSON.parse(event.data);
          console.log('Received message:', parsedData);
          if (parsedData) {
            if (parsedData.label === 'init') {
              // 初始化操作不添加到消息列表
            } else {
              console.log("'parsedData.uniqueKey",parsedData.uniqueKey)
              console.log("uniqueKey",uniqueKey)
              console.log("parsedData.uniqueKey === uniqueKey",parsedData.uniqueKey === uniqueKey)
              if (parsedData.uniqueKey === uniqueKey) {
                this.messages.push("id:" + parsedData.id);
                this.messages.push("message:" + parsedData.message);
                this.messages.push("label:" + parsedData.label);
                this.messages.push("uniqueKey:" + parsedData.uniqueKey);
                this.active = parseInt(parsedData.label, 10) + 1;
                console.log("active",  this.active)
                if (parsedData.label === '-8') {
                  setTimeout(() => {
                    this.dealPptStepWarningMsg(parsedData.message);
                  }, 8);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error parsing event data:', error);
        }
      };
      this.eventSource.onerror = () => {
        console.warn('SSE error: 连接超时，心跳检测，进行重连');

        // 如果已达到最大重连次数，不再尝试重连
        if (this.reachedMaxAttempts) {
          console.warn('已达到最大重连次数，不再重连');
          return;
        }

        this.reconnectAttempts++; // 增加重连尝试次数

        // 显示错误通知
        this.$notify.error({
          title: '连接错误',
          message: `SSE连接失败，正在尝试第${this.reconnectAttempts}次重连（最多${this.maxReconnectAttempts}次）`,
          duration: 3000
        });

        this.handleReconnect();
      };
    },
    startHeartbeat() {
      this.heartbeat = setInterval(() => {
        // 如果已达到最大重连次数，不再检查连接状态
        if (this.reachedMaxAttempts) {
          return;
        }

        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {
          console.log('SSE connection closed. Reconnecting...');
          this.handleReconnect();
        }
      }, this.heartbeatInterval);
    },
    handleReconnect() {
      // 检查是否超过重连限制
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.warn('达到最大重连次数限制，不再重连');

        // 设置标志，防止其他地方继续尝试重连
        this.reachedMaxAttempts = true;

        // 关闭现有连接
        if (this.eventSource) {
          this.eventSource.close();
          this.eventSource = null;
        }

        // 显示最终错误通知
        this.$notify.error({
          title: '连接失败',
          message: `SSE连接失败，已尝试${this.maxReconnectAttempts}次重连，请检查网络连接或稍后再试`,
          duration: 0 // 不自动关闭
        });
        return;
      }

      if (this.eventSource) {
        this.eventSource.close();
      }

      setTimeout(() => {
        this.startListening();
      }, this.reconnectInterval);
    },
    cleanup() {
      console.log("清除了")
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
      if (this.heartbeat) {
        clearInterval(this.heartbeat);
        this.heartbeat = null;
      }
      // 重置重连状态
      this.reconnectAttempts = 0;
      this.reachedMaxAttempts = false;
    },
    clearStep() {
      this.active = 0;
      this.messages = [];
    },
    dealPptStepWarningMsg(message) {
      this.$notify({
        title: '格式有误',
        dangerouslyUseHTMLString: true,
        type: 'error',
        message: message + '<br>' +
          `
<a style="color: #409EFF; text-decoration: underline; display: flex; justify-content: start;">查看格式</a>


          `,
        duration: 20 * 1000,
        offset: 10,
        onClick: () => {
          // 在这里处理点击事件
          this.$emit('show-text-tip');
        }
      });
    },
    getStepDescription(step) {
      const descriptions = {
        1: '初始化系统',
        2: '校验文本格式',
        3: '解析文本内容',
        4: '生成PPT',
        5: '修正字体样式',
        6: '添加Logo',
        7: '生成预览图片',
        8: '完成生成'
      };
      return descriptions[step] || '处理中';
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 1rem;

  ::v-deep .el-steps {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 2rem;

    .el-step {
      .el-step__main {
        min-width: 120px;
        .el-step__title {
          font-size: 14px;
          line-height: 1.4;
          white-space: nowrap;
        }
      }

      .el-step__head {
        .el-step__icon {
          width: 32px;
          height: 32px;

          .el-step__icon-inner {
            font-size: 16px;
            line-height: 32px;
          }
        }
      }

      &.is-horizontal {
        .el-step__line {
          height: 2px;
          top: 16px;
        }
      }
    }
  }
}

.progress-status {
  margin-top: 2rem;
  padding: 1rem 2rem;
  background: #f5f7fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  .status-icon {
    margin-right: 1rem;

    i {
      font-size: 20px;
      color: #409eff;

      &.el-icon-loading {
        animation: spin 1s linear infinite;
      }
    }
  }

  .status-text {
    font-size: 14px;
    color: #606266;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 768px) {
  .progress-container {
    padding: 0.5rem;

    ::v-deep .el-steps {
      .el-step {
        .el-step__main {
          min-width: 90px;

          .el-step__title {
            font-size: 12px;
          }
        }

        .el-step__head {
          .el-step__icon {
            width: 28px;
            height: 28px;

            .el-step__icon-inner {
              font-size: 14px;
              line-height: 28px;
            }
          }
        }
      }
    }
  }
}

.message-list {
  margin-top: 1.5rem;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  .message-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #f5f7fa;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #eef1f6;
      transform: translateX(4px);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 480px) {
  .progress-container {
    padding: 20px 12px;
    min-height: 280px;
    width: 96%;
  }

  .progress-box {
    ::v-deep .el-steps {
      padding: 12px;

      .el-step {
        .el-step__head {
          .el-step__icon {
            width: 34px;
            height: 34px;

            .el-step__icon-inner {
              font-size: 17px;
            }
          }
        }

        .el-step__main {
          .el-step__title {
            font-size: 13px;
          }
        }

        &.is-horizontal {
          .el-step__line {
            top: 17px;
          }
        }
      }
    }
  }

  .progress-status {
    margin-top: 24px;
    padding: 14px 16px;
    max-width: 92%;
    gap: 12px;

    .status-icon i {
      font-size: 20px;
    }

    .status-text {
      font-size: 14px;
    }
  }
}
</style>
