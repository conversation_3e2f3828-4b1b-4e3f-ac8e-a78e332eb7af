
open-platform-logo开放平台

open-platform-logo开放平台
开发文档
价格
充值
私有化部署
开源项目
文多多产品官网
进入工作台

15256772244
15
iFrame 方案
iFrame 方案

DOCMEE 官方团队 发布
探索 PPT + AI 的极限，为您提供卓越技术支持！
企业微信加企微
18601790226
<EMAIL>
文多多 AiPPT 可以通过 iframe 与您的系统紧密结合，通过简单的一些步骤，就能将 Docmee 嵌入到您的业务系统中。

快速使用您的 TOKEN 来体验 iframe 嵌入的效果

版本一
版本二

通过 NPM 使用 （推荐）
npm install @docmee/sdk-ui -S

安装我们提供的 SDK，即可引入我们的 UI

开源项目docmee/aippt-ui-iframe-v2 ↗中，提供了一个简单的事例，您可以参考
如果想要对接V1版本，您可以参考 docmee/aippt-ui-iframe ↗

TypeScript 支持
import { CreatorType, DocmeeUI } from '@docmee/sdk-ui' // 导入相关依赖

// CreatorType只能在TS中使用

// 初始化UI
const docmee = new DocmeeUI({
  container: 'app',
  page: 'creator-v2',
  token: 'xxx',
  mode: 'light',
  lang: 'zh',
  // 从外部传入主题
  creatorData: {
    type: CreatorType.AI_GEN,
    subject: 'Ai行业未来10年的发展预测',
  },
})

// 绑定事件回调
docmee.on('mounted', (msg) => {
  console.log(msg)
})

docmee.on('beforeGenerate', (msg) => {
  console.log(msg)
})

docmee.on('pageChange', (msg) => {
  console.log(msg)
})

* ⚠️ 注意：【升级提醒】当您使用creator-v2页面时，如果你需要使用creatorData来从外部直接传入数据，会直接开始 PPT 生成，请注意。

如果您使用的是 JS，您可能无法使用CreatorType这个枚举类型，此次您只能传入代表任务类型的数字，具体的：

1.智能生成（主题、要求）
2.上传文件生成
3.上传思维导图生成
4.通过 word 精准转 ppt
5.通过网页链接生成
6.粘贴文本内容生成
7.Markdown 大纲生成

准备
下载我们提供的 SDK 文件

在您的页面中引入

<head>
  <script src="docmee-ui-sdk-iframe.min.js"></script>
</head>

初始化
您需要实例化我们提供的类 DocmeeUI 来嵌入我们的 UI

⚠️ 不要在 file 协议下运行，请启动一个 http 服务来运行！

接入代码示例

github： https://github.com/veasion/aippt-ui-iframe

gitee： https://gitee.com/veasion/aippt-ui-iframe

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>文多多 AiPPT</title>
    <script src="docmee-ui-sdk-iframe.min.js"></script>
    <style>
      body {
        width: 100vw;
        height: 100vh;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      #container {
        width: calc(100% - 20px);
        height: calc(100% - 20px);
        margin: 0;
        padding: 0;
        border-radius: 12px;
        box-shadow: 0 0 12px rgba(120, 120, 120, 0.3);
        overflow: hidden;
        background: linear-gradient(-157deg, #f57bb0, #867dea);
        color: white;
      }
    </style>
  </head>
  <body>
    <div id="container"></div>
  </body>
  <script>
    // 请在服务端调用 createApiToken 接口生成token（不同uid生成的token数据相互隔离）
    // 接口文档：https://open.docmee.cn/open-platform/api#%E5%88%9B%E5%BB%BA%E6%8E%A5%E5%8F%A3-token
    var token = createApiToken()

    // 初始化 UI iframe
    const docmeeUI = new DocmeeUI({
      pptId: null,
      token: token, // token
      container: document.querySelector('#container'), // 挂载 iframe 的容器
      page: 'creator', // 'creator' 创建页面; 'dashboard' PPT列表; 'customTemplate' 自定义模版; 'editor' 编辑页（需要传pptId字段）
      lang: 'zh', // 国际化
      mode: 'light', // light 亮色模式, dark 暗色模式
      isMobile: false, // 移动端模式
      background: 'linear-gradient(-157deg,#f57bb0, #867dea)', // 自定义背景
      padding: '40px 20px 0px',
      onMessage(message) {
        console.log('监听事件', message)
        if (message.type === 'invalid-token') {
          // 在token失效时触发
          console.log('token 认证错误')
          // 更换新的 token
          // let newToken = createApiToken()
          // docmeeUI.updateToken(newToken)
        } else if (message.type === 'beforeGenerate') {
          const { subtype, fields } = message.data
          if (subtype === 'outline') {
            // 生成大纲前触发
            console.log('即将生成ppt大纲', fields)
            return true
          } else if (subtype === 'ppt') {
            // 生成PPT前触发
            console.log('即将生成ppt', fields)
            docmeeUI.sendMessage({
              type: 'success',
              content: '继续生成PPT',
            })
            return true
          }
        } else if (message.type === 'beforeCreateCustomTemplate') {
          const { file, totalPptCount } = message.data
          // 是否允许用户继续制作PPT
          console.log('用户自定义完整模版，PPT文件：', file.name)
          if (totalPptCount < 2) {
            console.log('用户积分不足，不允许制作自定义完整模版')
            return false
          }
          return true
        } else if (message.type == 'pageChange') {
          pageChange(message.data.page)
        } else if (message.type === 'beforeDownload') {
          // 自定义下载PPT的文件名称
          const { id, subject } = message.data
          return `PPT_${subject}.pptx`
        } else if (message.type == 'error') {
          if (message.data.code == 88) {
            // 创建token传了limit参数可以限制使用次数
            alert('您的次数已用完')
          } else {
            alert('发生错误：' + message.data.message)
          }
        }
      },
    })
  </script>
</html>

参数说明
参数名称	类型	必填	说明	例
token	string	✔︎	调用 API 创建接口 token 获取 token	sk_xxx
container	HTMLElement	✔︎	挂载 iframe 的容器	
themeColor	string	⨯	主题色	#4b39b8
page	'dashboard' | 'creator' | 'editor' | 'customTemplate'	⨯	进入页面，'dashboard'表示文档列表页，'creator'生成 ppt 页面, 'customTemplate'表示进入自定义模版页面，'editor'表示编辑页面（pptId 必须同时传递）	dashboard
lang	'zh' | 'en' | 'jp' | 'de' | 'fr' | 'ko' | 'pt'	⨯	语言(详见 国际化)	'zh'
pptId	string	○	进入 editor 页面时编辑的 pptId，如果 page 为 editor 时，pptId 不能为空	-
animation	boolean	⨯	进入 editor 页面时显示创建生成动画过程	-
background	string	⨯	iframe 背景颜色，可填入颜色或者图片 url 地址	#f1f1f1
mode	'light' , 'dark'	⨯	亮色，暗色模式	当前浏览器环境
isMobile	boolean	⨯	移动端模式	false
backgroundSize	string	⨯	iframe 背景大小 与 CSS 中的 background-size 语法相同	cover
padding	string	⨯	内边距（也就是 css 的 padding，语法相同）	20px 10px 20px 10px
onMessage	function	⨯	事件处理钩子 详见 事件类型	function (message)
creatorData	{subject: string, createNow?: boolean} | {text: string, createNow?: boolean}	⨯	生成页面传递 内容 subject 与 text 二选其一； createNow 如果为 true表示直接开始大纲生成（仅当 page=creator）时生效	-
downloadButton	boolean | ['pptx', 'pdf']	⨯	下载文件选项 返回 false 表示禁用下载，如果只想打开一种下载方式，可以传递数组['pptx']表示只允许下载为 pptx 格式	true
creatorMode	['topic', 'material']	⨯	生成 PPT 方式，topic：主题生成，material：外部资料	['topic', 'material']
outlineExportFormat	'txt'| 'md'	⨯	导出大纲的文件格式（注意：不管是 txt 还是 md 格式，内容都是按照 markdown 语法来导出的）	'md'
createCustomTemplateWhenEmpty	boolean	⨯	控制自定义模版选择界面，若自为空时，是否需要显示“立即创建”按钮	false
hidePdfWatermark	boolean	⨯	导出 PDF 文档隐藏文多多水印	false
createCustomTemplateWhenSelect	boolean	⨯	控制自定义模版选择界面，是否显示“添加自定义模版”按钮，若设置为 true，则 createCustomTemplateWhenEmpty 参数无效	false
css	string	⨯	注入 CSS 样式，可通过传递自定义 CSS 来更加深度地自定义文多多的样式，支持可访问的 URL 地址或直接传入 CSS 字符串，例如：#docmee_SdkContainer {background: white !important;}，或'https://abc.cn/style.css'	-
事件类型
iFrame 挂载完成

{
  type: 'mounted',
}

PPT 生成前触发 (用户点击“生成大纲”，以及选择完模版，点击“开始创作”时触发,通 过 subtype 区分)

{
  type: 'beforeGenerate',
  data: {
    subtype: 'outline', // "outline" 生成大纲前触发，"ppt"生成ppt前触发
    fields: {} // 用户用于生成PPT的参数
  }
}

该事件可以返回 true/false (也可以返回 异步 Promise<boolean>) 来决定用户是否能 够继续生成

自定义完整模版前触发

{
  type: 'beforeCreateCustomTemplate',
  data: {
    file: {}, // 用户上传的PPT文件
    totalPptCount: 99 // 用户剩余的积分数
  }
}

自定义模版完成触发

{
  type: 'afterCreateCustomTemplate',
  data: {
    do: 'create_complex', // 创建完整自定义模版 (积分 -2)
    do: 'modify_complex', // 修改完整自定义模版  (不扣积分)
    do: 'create_simple', // 创建简单自定义模版 (不扣积分)
    ...
    // 自定义模版数据
  }
}

PPT 生成完毕扣费时触发

{
  type: 'charge',
  data: {
    id: 'xxxxxx' // ppt id
  }
}

PPT 生成后触发

{
  type: 'afterGenerate',
  data: {
    id: 'xxxxxx' // ppt id
  }
}

下载前触发

{
  type: 'beforeDownload',
  data: {
    id: 'xxxxxx' // ppt id
  }
}


该事件可以返回 true/false 或 string (也可以返回 异步 Promise<boolean | string>) 来决定用户是否能够继续下载 PPT，或指定 ppt 文档名称，返回的名称需要 以.pptx结尾才表示重命名文件

用户信息

{
  type: 'user-info',
  data: {
   "uid": null, // 用户 id
   "availableCount": 0, // 可用生成次数
   "usedCount": 0 // 已使用的次数
  }
}

手动触发保存(仅用户点击触发)

{
  type: 'manuallySavePPT',
  data: {
    // 当前编辑的PPT信息
    coverUrl: 'xxx',
    createTime: 'xxx',
    id: 'xxx',
    name: 'xxx',
    subject: 'xxx',
    // ......
  }
}

错误

{
  type: 'error',
  data: {
    code: 88, // 次数用完了
    message:"您的次数已用完，请开通会员"
  }
}


生成 PPT 方式修改

只要生成方式有变化就会触发

{
  type: 'toggleGenerateMode',
  data: {
    /**
    * PPT生成方式
    * (可选值)
    * - subject: 主题或要求
    * - material: 导入资料
    */
    mode: 'subject',
    /**
    * 导入资料方式
    * (可选值，mode为subject时subMode必定为undefined)
    * - text: 输入文字
    * - file: 上传文件
    * - website: 输入网页地址
    * - outline: 导入大纲
    */
    subMode: undefined
  }
}


其他 API
以下方法都是 DocmeeUI 类 实例的成员，需要通过docmeeUI.来调用

docmeeUI.updateToken(newToken: string): void 更新用户 Token

docmeeUI.destroy(): void 卸载 iframe

docmeeUI.getInfo(): void 手动获取一次 用户信息，用户信息会在onMessage回调 中返回

docmeeUI.navigate(obj: {page: 'creator' | 'dashboard' | 'editor' | 'customTemplate', pptId?: string}): void 跳转页面, 同样地如果前往 editor 页面，pptId 是必须的

docmeeUI.navigate({ page: 'dashboard' })
docmeeUI.navigate({ page: 'editor', pptId: 'xxxx' })

docmeeUI.sendMessage(message: {type: string, content: string}): void 控制 SDK 发送消息
/**
 *  type: 'success' | 'error' | 'warning' | 'info' | undefined
 *  content: string
 */
docmeeUI.sendMessage({ type: 'success', content: '操作成功' })

docmeeUI.changeCreatorData(data: {subject: string, text: string}, createNow: boolean): void 在creator页面中修改输入框中的值
/**
 *  参数1: 用来生成大纲的数据 subject(主题) 或 text(文字内容) 二选其一
 *  参数2: 控制是否立即生成
 *    如果为true，表示方法调用时直接开始生成，如果不传递或者传递false时，仅输入内容，需要用户点击生成大纲按钮
 */
docmeeUI.changeCreatorData({ subject: 'AI未来的发展' }, true)

// 或
docmeeUI.changeCreatorData({ text: 'AI未来的发展' }, true)

docmeeUI.updateTemplate(templateId: string) 外部指定更换模板，并刷新

docmeeUI.showTemplateDialog(type?: 'custom' | 'system')  弹出模板选择弹框, type: 'custom' or 'system' (default)

docmeeUI.getCurrentPptInfo() 返回 ppt 信息（在事件中返回，事件类型currentPptInfo）

国际化
为了应对多语种环境，文多多 AiPPT 支持国际化。

目前支持的语言列表有:

中文 zh
英文 en
日本语 jp
韩语 ko
法语 fr
德语 de
葡萄牙语 pt
接口转发
如果你想对某些接口进行特殊处理，比如图片接口走你们自己的图库接口之类的扩展，或者 API 代理商 提供给用户 iframe 接入方式，都可以通过 nginx 进行接口转发实现。

示例：

假设你的服务器域名为 xxx.com

服务器端 nginx 配置如下：

server {
    listen 8080;
    server_name xxx.com;
    charset utf-8;

    # 图片接口特殊处理，走你们自己的图片接口逻辑
    # location /api/ppt/genImg {
    #     proxy_pass http://127.0.0.1/api/ppt/genImg;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Real-Port $remote_port;
    # }

    # 接口代理
    location ^~ /api {
        proxy_pass https://open.docmee.cn/api;
        proxy_read_timeout 600;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_cache off;
        proxy_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Host docmee.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto 'https';
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-Port $remote_port;
    }

    # 页面代理
    location / {
        proxy_pass https://docmee.cn;
        proxy_set_header Host docmee.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto 'https';
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-Port $remote_port;
    }

}

使用 DocmeeUI 是指定代理相关参数：

const docmeeUI = new DocmeeUI({
    // 前端域名代理
    DOMAIN: 'http://xxx.com:8080',
    // 服务端代理
    baseURL: 'http://xxx.com:8080/api',
    ....
})


2024 © 上海元符号智能科技有限公司 版权所有
沪ICP备2022005812号-3
Toggle theme



15256772244
15
API 接入
API 接入

DOCMEE 官方团队 发布
探索 PPT + AI 的极限，为您提供卓越技术支持！
企业微信加企微
18601790226
<EMAIL>
API 开放接口开发文档，文多多 AiPPT 可以通过 API 与您的系统紧密结合，通过 API 接入，您可以将文多多 AiPPT 完美地融入到您的业务系统中。

API 接口调用流程
文多多 AiPPT 技术团队提供全套的 API 接口，您可以使用任一您擅长的语言来通过 API 接入文多多。

如何使用此文档
在本文档中列出的所有接口，都会进行接口鉴权，请您熟知！
使用前请您查看产品价格，了解文多多 AiPPT 产品定价规则。
请您确保您的账户中剩余积分充足，点击查看
接入示例
UI接入示例 V2
https://github.com/docmee/aippt-ui-iframe-v2
UI接入示例 V1
https://github.com/docmee/aippt-ui-iframe
Python Api接入示例
https://github.com/docmee/aippt-api-python-demo
Java Api接入示例
https://github.com/docmee/aippt-api-java-demo
Go Api接入示例
https://github.com/docmee/aippt-api-go-demo
PHP Api接入示例
https://github.com/docmee/aippt-api-php-demo
接口鉴权
创建 API Token 接口用于生成调用鉴权 Token，支持限制生成次数与数据隔离，通过 Header 或 URL 拼接 Token 实现鉴权。

创建接口 token
post
https://open.docmee.cn/api/user/createApiToken


请求 header

Api-Key 在开放平台获取 获取 API-KEY

请求 body

{
  // 用户ID（自定义用户ID，非必填，建议不超过32位字符串）
  // 第三方用户ID，不同uid创建的token数据会相互隔离，主要用于数据隔离
  "uid": null,
  // 限制 token 最大生成PPT次数（数字，为空则不限制，为0时不允许生成PPT，大于0时限制生成PPT次数）
  // UI iframe 接入时强烈建议传 limit 参数，避免 token 泄露照成损失！
  "limit": null,
  // 过期时间，单位：小时
  // 默认两小时过期，最大可设置为48小时
  "timeOfHours": 2
}

响应 body

{
  "data": {
    "token": "sk_xxx", // token (调用api接口鉴权用，请求头传token)
    "expireTime": 7200 // 过期时间（秒）
  },
  "code": 0,
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/user/createApiToken' \
--header 'Content-Type: application/json' \
--header 'Api-Key: xxx' \
--data '{"uid": "xxx","limit": 10}'

注意：该接口请在服务端调用，同一个 uid 创建 token 时，之前通过该 uid 创建的 token 会在 10 秒内过期

场景说明：在 UI 集成中，为防止 token 滥用，limit 参数可以让第三方集成商维护自己平台用户的使用次数，未注册用户访问时可以创建 limit 为 0 的 token ，用户能使用但生成不了 PPT，UI 中监听次数用完的事件指导用户登录，登录用户访问时可以根据系统 vip 级别限制和维护系统用户 limit 次数

API 接口鉴权
请求 Header

token 通过 createApiToken 接口创建的 token，如果是服务端调用也可直接使用 API-KEY 作为 token

接口请求示例

  curl --location 'https://open.docmee.cn/api/ppt/xxx' \ --header 'token: xxx'

封面图片资源访问，需要在 url 上拼接 ?token=xxx

创建 PPT 生成任务
文多多 AiPPT 官方提供了非常多样的 PPT 生成方式，您可以按照自己业务系统的需求来挑选最适合您的生成方式。

PPT 生成方式说明
🔎 查看原图


官方最推荐您使用版本 2来创作 PPT，也是我们官方应用 文多多 AiPPT 中使用的方式。

版本 2 (官方推荐)
文多多 AiPPT 官方平台上使用的新版生成方式，整合了版本 1 中的若干种常用生成 PPT 方式，我们强烈推荐您使用此方案来生成您的 PPT。

调用 createTask 创建任务 创建一个新的 PPT 生成任务。
调用 generateContent 生成大纲内容 接口，流式生成 PPT 内容与大纲。
调用 generatePptx 生成 PPT接口创建 PPT 文件。
与 版本 2 相关的 API 接口，请您查看 AI PPT - V2 来获得更详细的内容。

直接生成 PPT
这是一种最简单的方案，直接调用接口来创作 PPT。

调用 directGeneratePptx 直接生成 PPT 接口，支持流式和非流式，PPT 文件通过接口返回的 fileUrl 下载
在项目施行的初期，您可以使用这种方案来快速体验文多多 AiPPT 的功能。若您想快速体验，我们更推荐您可以使用UI 接入来接入文多多创作 PPT 的能力！

实时流式生成 PPT (版本 1)
若您需要使用版本 1来生成 PPT，我们推荐您使用此方案来生成，用户体验较佳。

调用 generateOutline 生成大纲 接口，流式生成 PPT 大纲目录 markdown.

调用 generateContent 生成大纲内容同时异步生成 PPT 接口，这个接口 asyncGenPptx 参数需要传 true，流式生成，根据第一步返回的大纲目录生成完整大纲内容 markdown，异步生成 PPT

在第二步 generateContent 接口中，接收实时流式 json 数据过程中判断 json 中是否有 pptId ，存在时调用 asyncPptInfo 查询异步生成 PPT 信息 接口，获取异步生成 PPT 的进度和数据（前端实时渲染）

若您使用 V1 的功能来生成 PPT，这时我们最推荐的调用方式。得益于所有接口都能流式返回，能够给用户提供最佳的体验。这也是我们官方 V1 版本中实际生产使用的方案。但我们依旧推荐您使用 V2（版本 2）的方式来生成 PPT。

同步流式生成 PPT (版本 1)
调用 generateOutline 生成大纲 接口，流式生成 PPT 大纲目录 markdown.
调用 generateContent 生成大纲内容 接口，这个接口 asyncGenPptx 参数需要传 false，流式生成，根据第一步返回的大纲目录生成完整大纲内容 markdown
调用 generatePptx 生成 PPT 接口，通过完整 markdown 内容合成 PPT，通过 fileUrl 下载
openai chat 方式生成 PPT
调用 /chat/completions OpenAi-对话生成 PPT 接口，适用于接入支持 openai 接口的工具平台.
通过 markdown 生成 PPT
集成方生成 markdown 内容，内容格式如下：

# 主题

## 章节

### 页面标题

#### 内容标题一

这是文本内容...
![图片一](https://xxx.png)

#### 内容标题二

这是文本内容...
![图片二](https://xxx.png)

Markdown 具体规范说明，完整示例下载：markdown.md （支持包含图片、表格）

调用 generatePptx 生成 PPT 接口，通过完整 markdown 内容合成 PPT，通过 fileUrl 下载

MCP
兼容 Model Context Protocol (MCP) 生成 PPT。

SSE Server 端点: /api/mcp/sse?token=

鉴权：通过 URL 上的 token 参数鉴权，设置为你在平台的 Api-Key 或 通过接口创建的 token

tools/list

[
  {
    "name": "ai_generate_ppt",
    "description": "AI generate PPT",
    "inputSchema": {
      "type": "object",
      "properties": {
        "task_description": {
          "type": "string"
        }
      },
      "required": ["task_description"],
      "additionalProperties": false
    }
  }
]

Typescript 接入示例：

import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js'

async function main() {
  const apiKey = 'ak_xxx'
  const mcpClient = new Client({
    name: 'mcp-client-test',
    version: '1.0.0',
  })
  const transport = new SSEClientTransport(
    new URL('https://open.docmee.cn/api/mcp/sse?token=' + apiKey),
  )
  mcpClient.connect(transport)
  const toolsResult = await mcpClient.listTools()
  console.log('Tools:', toolsResult)
  const result = await mcpClient.callTool({
    name: 'ai_generate_ppt',
    arguments: {
      task_description: '请以AI未来的发展为主题生成PPT',
    },
  })
  console.log('Result:', result)
}

main()

生成 PPT 相关接口
AI PPT - V2
创建任务
接口说明： 调用此接口可获得一个 任务 ID，即开启了一个生成 PPT 的任务，后续不管是生成大纲内容还是修改大纲内容都需要此任务 ID。

post
https://open.docmee.cn/api/ppt/v2/createTask


multipart/form-data

参数	类型	是否必传	说明
type	1|2|3|4|5|6|7	是	类型：
1.智能生成（主题、要求）
2.上传文件生成
3.上传思维导图生成
4.通过 word 精准转 ppt
5.通过网页链接生成
6.粘贴文本内容生成
7.Markdown 大纲生成
content	String	否	内容：
type=1 用户输入主题或要求（不超过 1000 字符）
type=2、4 不传
type=3 幕布等分享链接
type=5 网页链接地址（http/https）
type=6 粘贴文本内容（不超过 20000 字符）
type=7 大纲内容（markdown）
file	File[]	否	文件列表（文件数不超过 5 个，总大小不超过 50M）：
type=1 上传参考文件（非必传，支持多个）
type=2 上传文件（支持多个）
type=3 上传思维导图（xmind/mm/md）（仅支持一个）
type=4 上传 word 文件（仅支持一个）
type=5、6、7 不传

支持格式：doc/docx/pdf/ppt/pptx/txt/md/xls/xlsx/csv/html/epub/mobi/xmind/mm
响应：

{
  "data": {
    "id": "xxx" // 任务ID
  },
  "code": 0,
  "message": "操作成功"
}

获取生成选项
接口说明： 调用此接口来获得调用生成大纲内容需要使用的相关选项。

get
https://open.docmee.cn/api/ppt/v2/options


该接口支持国际化，URL 携带 lang 参数指定。

响应

{
  "data": {
    "lang": [
      // 语种
      { "name": "简体中文", "value": "zh" },
      { "name": "繁體中文", "value": "zh-Hant" },
      { "name": "English", "value": "en" },
      { "name": "日本語", "value": "ja" },
      { "name": "한국어", "value": "ko" },
      { "name": "Français", "value": "fr" },
      { "name": "Русский", "value": "ru" },
      { "name": "العربية", "value": "ar" },
      { "name": "Deutsch", "value": "de" },
      { "name": "Español", "value": "es" },
      { "name": "Italiano", "value": "it" },
      { "name": "Português", "value": "pt" }
    ],
    "scene": [
      // 场景
      { "name": "通用场景", "value": "通用场景" },
      { "name": "教学课件", "value": "教学课件" },
      { "name": "工作总结", "value": "工作总结" },
      { "name": "工作计划", "value": "工作计划" },
      { "name": "项目汇报", "value": "项目汇报" },
      { "name": "解决方案", "value": "解决方案" },
      { "name": "研究报告", "value": "研究报告" },
      { "name": "会议材料", "value": "会议材料" },
      { "name": "产品介绍", "value": "产品介绍" },
      { "name": "公司介绍", "value": "公司介绍" },
      { "name": "商业计划书", "value": "商业计划书" },
      { "name": "科普宣传", "value": "科普宣传" },
      { "name": "公众演讲", "value": "公众演讲" }
    ],
    "audience": [
      // 受众
      { "name": "大众", "value": "大众" },
      { "name": "学生", "value": "学生" },
      { "name": "老师", "value": "老师" },
      { "name": "上级领导", "value": "上级领导" },
      { "name": "下属", "value": "下属" },
      { "name": "面试官", "value": "面试官" },
      { "name": "同事", "value": "同事" }
    ]
  },
  "code": 0,
  "message": "ok"
}

生成大纲内容
接口说明： 生成当前任务的大纲及内容

post
https://open.docmee.cn/api/ppt/v2/generateContent


参数

{
  "id": "xxx", // 任务ID
  "stream": true, // 是否流式（默认 true）
  "length": "medium", // 篇幅长度：short/medium/long => 10-15页/20-30页/25-35页
  "scene": null, // 演示场景：通用场景、教学课件、工作总结、工作计划、项目汇报、解决方案、研究报告、会议材料、产品介绍、公司介绍、商业计划书、科普宣传、公众演讲 等任意场景类型。
  "audience": null, // 受众：大众、学生、老师、上级领导、下属、面试官、同事 等任意受众类型。
  "lang": null, // 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
  "prompt": null // 用户要求（小于50字）
}

流式响应 event-stream

{ "text": "#", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{
	"text": "",
	"status": 4,
	"result": { // 最终markdown结构树
		"level": 1,
		"name": "主题",
		"children": [
			{
				"level": 2,
				"name": "章节",
				"children": [
					{
						"level": 3,
						"name": "页面标题",
						"children": [
							{
								"level": 4,
								"name": "内容标题",
								"children": [
									{
										"level": 0,
										"name": "内容"
									}
								]
							}
						]
					}
				]
			}
		]
	}
}

非流式响应（application/json）：

{
  "code": 0,
  "data": {
    "text": "# 主题\n## 章节\n### 页面标题\n#### 内容标题\n- 内容", // markdown 文本
    "result": {
      // markdown 结构树
      "level": 1,
      "name": "主题",
      "children": [
        {
          "level": 2,
          "name": "章节",
          "children": [
            {
              "level": 3,
              "name": "页面标题",
              "children": [
                {
                  "level": 4,
                  "name": "内容标题",
                  "children": [
                    {
                      "level": 0,
                      "name": "内容"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  "message": "ok"
}

修改大纲内容
接口说明： 根据用户指令（question）修改大纲内容。

post
https://open.docmee.cn/api/ppt/v2/updateContent


参数

{
  "id": "xxx", // 任务ID
  "stream": true, // 是否流式（默认 true）
  "markdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题\n- 内容", // 大纲内容markdown
  "question": null // 用户修改建议
}

响应 event-stream 或 application/json，结构同生成大纲内容

生成 PPT
接口说明： 根据 markdown 格式的 PPT 大纲与内容生成 PPT 作品。

post
https://open.docmee.cn/api/ppt/v2/generatePptx


参数

{
  "id": "xxx", // 任务ID
  "templateId": "xxx", // 模板ID（调用模板接口获取）
  "markdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题\n- 内容" // 大纲内容markdown
}

响应

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "templateId": "xxx", // 模板ID
      "pptxProperty": "xxx", // PPT数据结构（json gzip base64）
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  },
  "message": "操作成功"
}

通过 API 生成 PPT 后，如果需要在前端进行编辑和渲染，推荐使用以下 iframe 方式编辑器，开源地址：

github https://github.com/veasion/aippt-ui-ppt-editor

gitee https://gitee.com/veasion/aippt-ui-ppt-editor

AI PPT - V1
解析文件内容
接口说明： 调用此接口个将若干支持的参数选项转化成 dataUrl 以便后续接口使用。

post
https://open.docmee.cn/api/ppt/parseFileData


请求参数 (multipart/form-data)

参数	类型	是否必传	说明
file	File	否	文件（限制 50M 以内，最大解析 2 万字）
支持格式：doc/docx/pdf/ppt/pptx/txt/md/xls/xlsx/csv/html/epub/mobi/xmind/mm
content	string	否	用户粘贴文本内容
fileUrl	string	否	文件公网链接
website	string	否	网址（http/https）
websearch	string	否	网络搜索关键词
响应：

{
  "data": {
    "dataUrl": "https://xxx" // 文件数据url（有效期：当天）
  },
  "code": 0,
  "message": "操作成功"
}

请求示例

  curl -X POST --location 'https://open.docmee.cn/api/ppt/parseFileData' \ --header
  'Content-Type: multipart/form-data' \ --header 'token: {token}' \ --form
  'file=@test.doc;filename=test.doc' \ --form 'content=文本内容' \ --form
  'fileUrl=https://xxx.pdf' \ --form 'website=https://example.com' \ --form
  'websearch=上海元符号智能科技有限公司'

生成大纲
接口说明： V1 版本的生成大纲接口。

post
https://open.docmee.cn/api/ppt/generateOutline


参数

{
  "stream": true, // 是否流式生成（默认流式）
  "length": "medium", // 篇幅长度：short/medium/long, 默认 medium, 分别对应: 10-15页/20-30页/25-35页
  "lang": null, // 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
  "prompt": null, // 用户要求（小于50字）

  // 方式一：通过主题创建
  "subject": "xxx", // 主题（与dataUrl可同时存在）
  // 方式二：通过文件内容创建
  "dataUrl": "https://xxx" // 文件数据url，通过解析文件内容接口返回（与subject可同时存在）
}

响应 event-stream 流式生成

{
  "text": "",
  "status": 1 // 状态：-1异常 1解析文件 3生成中 4完成
}

{ "text": "# ", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{
	"text": "",
	"status": 4,
	"result": {
		"level": 1,
		"name": "主题",
		"children": [
			{
				"level": 2,
				"name": "章节",
				"children": [
					{
						"level": 3,
						"name": "页面标题",
						"children": [
							{
								"level": 4,
								"name": "内容标题"
							}
						]
					}
				]
			}
		]
	}
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/generateOutline' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"subject": "AI未来的发展"}'

修改大纲
接口说明： 根据用户指令修改大纲

post
https://open.docmee.cn/ppt/updateOutline


参数

{
  "outlineMarkdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题", // 大纲markdown内容
  "length": "medium", // 篇幅长度：short/medium/long, 默认 medium, 分别对应: 10-15页/20-30页/25-35页
  // 用户修改建议
  // 系统内置：用金字塔原理优化、强化大纲结构和过渡、突出大纲主题、让大纲更专业
  // 用户自定义：比如 “帮我把大纲改成金字塔结构”
  "question": null
}

响应 event-stream，结构同 generateOutline 生成大纲，该接口用来修改调整大纲结构生成大纲内容

接口说明： 通过 Markdown 格式的大纲生成 PPT 内容

post
https://open.docmee.cn/api/ppt/generateContent


参数

{
  "stream": true, // 是否流式生成（默认流式）
  "outlineMarkdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题", // 大纲 markdown 文本
  "asyncGenPptx": false, // 是否异步生成，这里为 false 同步，异步请见 "AI PPT (异步)" 菜单
  "lang": null, // 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
  "prompt": null, // 用户要求
  "dataUrl": null // 文件数据url，调用解析文件内容接口返回
}

响应 event-stream 流式生成

{
	"text": "",
	"status": 3 // 状态：-1异常 1解析文件 3生成中 4完成
}

{ "text": "#", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{
	"text": "",
	"status": 4,
	"result": {
		"level": 1,
		"name": "主题",
		"children": [
			{
				"level": 2,
				"name": "章节",
				"children": [
					{
						"level": 3,
						"name": "页面标题",
						"children": [
							{
								"level": 4,
								"name": "内容标题",
								"children": [
									{
										"level": 0,
										"name": "内容"
									}
								]
							}
						]
					}
				]
			}
		]
	}
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/generateContent' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"outlineMarkdown": "xxx"}'

生成 PPT
接口说明： 通过大纲与内容生成 PPT

post
https://open.docmee.cn/api/ppt/generatePptx


参数

{
  "templateId": "xxx", // 模板ID（非必填）
  "pptxProperty": false, // 是否返回PPT数据结构
  "outlineContentMarkdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题\n- 内容", // 大纲内容markdown
  "notes": null // 备注（PPT页面备注，非必填，数组 ["内容页面一备注", "内容页面二备注"]）
}

响应

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "fileUrl": "https://xxx.pptx", // PPT文件
      "templateId": "xxx", // 模板ID
      "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/generatePptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"outlineContentMarkdown": "xxx", "pptxProperty": false}'

Word 转 PPT
不同于通过解析文件生成 PPT，Word 转 PPT 会尽可能保留 word 文件中的层级结构和内容表述去生成 PPT。

post
https://open.docmee.cn/api/ppt/v1/word2pptx


请求参数 (multipart/form-data)

字段	类型	说明
file	文件	docx 文件（小于 30M）
templateId	string	模板 ID（可空，为空时随机）
stream	boolean	是否流式
非流式-响应（application/json）

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "fileUrl": "https://xxx.pptx", // PPT文件
      "templateId": "xxx", // 模板ID
      "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  },
  "message": "操作成功"
}

请求示例

  curl -X POST --location 'https://open.docmee.cn/api/ppt/v1/word2pptx' \ --header
  'Content-Type: multipart/form-data' \ --header 'token: {token}' \ --form
  'stream=false' \ --form 'file=@test.docx;filename=test.docx'

流式-响应（event-stream）

{
	"text": "",
	"status": 3 // 状态：-1异常 1解析文件 3生成中 4完成
}

{ "text": "#", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{ "text": "", "status": 3, "pptId": "xxx" }

{
	"text": "",
	"status": 4,
	"result": {
		// ppt信息
        "id": "xxx", // ppt id
        "subject": "xxx", // 主题
        "coverUrl": "https://xxx.png", // 封面
        "fileUrl": "https://xxx.pptx", // PPT文件
        "templateId": "xxx", // 模板ID
        "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
        "userId": "xxx", // 用户ID
        "userName": "xxx", // 用户名称
        "companyId": 1000,
        "updateTime": null,
        "createTime": "2024-01-01 10:00:00"
	}
}

请求示例

  curl -X POST --location 'https://open.docmee.cn/api/ppt/v1/word2pptx' \ --header
  'Content-Type: multipart/form-data' \ --header 'token: {token}' \ --form
  'stream=true' \ --form 'file=@test.docx;filename=test.docx'

直接生成 PPT
接口说明： 直接让模型生成 PPT

post
https://open.docmee.cn/api/ppt/directGeneratePptx


参数

{
  "stream": false, // 是否流式生成
  "templateId": "xxx", // 模板ID（非必填，为空则随机模板）
  "pptxProperty": false, // 是否返回PPT数据结构
  "length": "medium", // 篇幅长度：short/medium/long, 默认 medium, 分别对应: 10-15页/20-30页/25-35页
  "lang": null, // 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
  "prompt": null, // 用户要求（小于50字）

  // 方式一：通过主题创建
  "subject": "xxx", // 主题（与dataUrl可同时存在）
  // 方式二：通过文件内容创建
  "dataUrl": "https://xxx" // 文件数据url，调用解析文件内容接口返回（与subject可同时存在）
}

非流式-响应（application/json）

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "fileUrl": "https://xxx.pptx", // PPT文件
      "templateId": "xxx", // 模板ID
      "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/directGeneratePptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"stream": false, "subject": "AI未来的发展", "pptxProperty": false}'

流式-响应（event-stream）

{
	"text": "",
	"status": 3 // 状态：-1异常 1解析文件 3生成中 4完成
}

{ "text": "#", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{ "text": "", "status": 3, "pptId": "xxx" }

{
	"text": "",
	"status": 4,
	"result": {
		// ppt信息
        "id": "xxx", // ppt id
        "subject": "xxx", // 主题
        "coverUrl": "https://xxx.png", // 封面
        "fileUrl": "https://xxx.pptx", // PPT文件
        "templateId": "xxx", // 模板ID
        "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
        "userId": "xxx", // 用户ID
        "userName": "xxx", // 用户名称
        "companyId": 1000,
        "updateTime": null,
        "createTime": "2024-01-01 10:00:00"
	}
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/directGeneratePptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"stream": true, "subject": "AI未来的发展", "pptxProperty": false}'

AI PPT (异步)
Ai 异步流式生成 PPT，只需在调用生成大纲接口后调用下面的 generateContent 接口即可生成 PPT，无需再次调用生成 PPT 接口

生成大纲内容同时异步生成 PPT
post
https://open.docmee.cn/api/ppt/generateContent


参数

{
  "templateId": "xxx", // 模板ID（非必填）
  "outlineMarkdown": "# 主题\n## 章节\n### 页面标题\n#### 内容标题", // 大纲 markdown 文本
  "asyncGenPptx": true, // 异步生成PPT（这里必须为 true 才会流式生成）
  "prompt": null, // 用户要求
  "dataUrl": null // 文件数据url，调用解析文件内容接口返回
}

响应 event-stream 流式生成

{
	"text": "",
	"status": 3, // 状态：-1异常 0模型重置 1解析文件 2搜索网页 3生成中 4完成
	// 下面为异步生成PPT时返回的数据，可以每出现一次就调用 asyncPptInfo 接口获取最新的PPT数据
	"pptId": "xxx", // 异步生成pptId
	"total": 23, // 总页数
	"current": 1 // 当前已生成页数
}

{ "text": "#", "status": 3 }

{ "text": " ", "status": 3 }

{ "text": "主题", "status": 3 }

...

{
	"text": "",
	"status": 4,
	"result": {
		"level": 1,
		"name": "主题",
		"children": [
			{
				"level": 2,
				"name": "章节",
				"children": [
					{
						"level": 3,
						"name": "页面标题",
						"children": [
							{
								"level": 4,
								"name": "内容标题",
								"children": [
									{
										"level": 0,
										"name": "内容"
									}
								]
							}
						]
					}
				]
			}
		]
	}
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/generateContent' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"outlineMarkdown":"xxx","asyncGenPptx":true}'

查询异步生成 PPT 信息
get
https://open.docmee.cn/api/ppt/asyncPptInfo?pptId=


参数

pptId 为 generateContent 接口流式返回的 pptId

响应

{
  "code": 0,
  "data": {
    "total": 23, // 总页数
    "current": 1, // 当前已生成页数（如果current >= total时表示PPT生成完成）
    "pptxProperty": "xxx" // PPT数据结构（json gzip base64）
  }
}

说明：该接口不需要轮询，在 generateContent 接口流式返回 pptId 数据时调用，每出现一次 pptId 就调用一次获取最新的 PPT 信息

注意：这个接口只有在流式生成过程中能查询到数据（临时缓存数据），在 PPT 生成完成的 30 秒内过期（查不到数据），此时需要调用 loadPptx 加载 PPT 数据 接口查询。

请求示例

  curl -X GET --location 'https://open.docmee.cn/api/ppt/asyncPptInfo?pptId=xxx' \
  --header 'token: {token}'

对话生成 PPT
接口说明： 兼容 openai chat 接口 生成 PPT，鉴权支持请求头 Api-Key 和 Authorization Bearer 两种方式。

post
https://open.docmee.cn/api/ppt-openai/v1/chat/completions


参数

{
  "stream": true, // 是否流式
  "model": "direct-generate-pptx", // 模型（固定）
  "messages": [
    // 消息体：不支持连续对话（多个时默认取最后一个user）
    {
      "role": "user",
      "content": "AI未来的发展" // 支持：主题、文档内容、文件链接（公网可访问）
    }
  ]
  // 其他扩展参数
  // appendLink: true 大纲内容生成完成后是否在文本后面追加封面图片和下载链接（默认true）
  // templateId: null 模板ID（默认为 null，系统随机）
  // lang: null 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
  // prompt: null // 用户要求（不超过50字）
}

非流式-响应（application/json）

{
  "id": "1833839690764124160",
  "model": "direct-generate-pptx",
  "choices": [
    {
      "index": 0,
      "finish_reason": "stop",
      "message": {
        "role": "assistant",
        "content": "# AI未来的发展\n## 1 技术进步\n### 1.1 算法优化\n#### 1.1.1 深度学习\n- 深度学习模型不断优化，提高准确性和效率...|\n\n[封面图片]\n\n[点击下载]" // PPT大纲内容 & 封面和下载链接，请通过markdown渲染
      },
      "ppt_data": {
        // PPT数据（扩展属性）
        "id": "1833839690764124160", // id
        "subject": "AI未来的发展", // 主题
        "templateId": "xxx", // 模板ID
        "coverUrl": "https://xxx.png", // 封面（可直接访问，有效期8小时）
        "fileUrl": "https://xxx.pptx" // PPT文件（可直接访问，有效期8小时）
      }
    }
  ],
  "object": "chat.completion",
  "created": 1726056513,
  "usage": {
    "completion_tokens": 10000,
    "prompt_tokens": 2000,
    "total_tokens": 12000
  }
}

流式-响应（event-stream）

data: {"id":"1833839690764124160","choices":[{"delta":{"content":"#","role":"assistant"},"finish_reason":null,"index":0}],"created":1726056518,"model":"direct-generate-pptx","object":"chat.completion.chunk"}

data: {"id":"1833839690764124160","choices":[{"delta":{"content":" ","role":"assistant"},"finish_reason":null,"index":0}],"created":1726056518,"model":"direct-generate-pptx","object":"chat.completion.chunk"}

data: {"id":"1833839690764124160","choices":[{"delta":{"content":"AI","role":"assistant"},"finish_reason":null,"index":0}],"created":1726056518,"model":"direct-generate-pptx","object":"chat.completion.chunk"}

data: {"id":"1833839690764124160","choices":[{"delta":{"content":"未来","role":"assistant"},"finish_reason":null,"index":0}],"created":1726056518,"model":"direct-generate-pptx","object":"chat.completion.chunk"}

...

data: {"id":"1833839690764124160","choices":[{"delta":{"content":null,"role":"assistant"},"finish_reason":"stop","index":0,"ppt_data":{"companyId":1000,"coverUrl":"https://xxx.png","createTime":1726056519624,"fileUrl":"https://xxx.pptx","id":"1833839690764124160","name":"AI未来的发展","subject":"AI未来的发展","templateId":"1815308477845987328","updateTime":1726056519624,"userId":"xxx","userName":"xxx"}}],"created":1726056529,"model":"direct-generate-pptx","object":"chat.completion.chunk"}

请求示例（python）：

import json
from openai import OpenAI

if __name__ == '__main__':
    # 通过 openai 库直接请求
    client = OpenAI(base_url='{域名}/api/ppt-openai/v1', api_key='sk-xxx')
    # 是否流式请求
    stream = True
    response = client.chat.completions.create(
        timeout=120,
        stream=stream,
        model='direct-generate-pptx',
        messages=[
            {
                'role': 'user',
                'content': 'AI未来的发展'
            }
        ]
    )
    if stream:
        # 流式
        for trunk in response:
            choice = trunk['choices'][0]
            print(choice['delta']['content'], end='')
            if 'ppt_data' in choice:
                print(json.dumps(choice['ppt_data']))
    else:
        # 非流式
        choice = response['choices'][0]
        print(choice['message']['content'])
        if 'ppt_data' in choice:
            print(json.dumps(choice['ppt_data']))

获取模版
获取模板过滤选项
接口说明： 获取查询模版的过滤选项

get
https://open.docmee.cn/api/ppt/template/options


响应

{
  "data": {
    "category": [
      // 类目筛选
      { "name": "全部", "value": "" },
      { "name": "年终总结", "value": "年终总结" },
      { "name": "教育培训", "value": "教育培训" },
      { "name": "医学医疗", "value": "医学医疗" },
      { "name": "商业计划书", "value": "商业计划书" },
      { "name": "企业介绍", "value": "企业介绍" },
      { "name": "毕业答辩", "value": "毕业答辩" },
      { "name": "营销推广", "value": "营销推广" },
      { "name": "晚会表彰", "value": "晚会表彰" },
      { "name": "个人简历", "value": "个人简历" }
    ],
    "style": [
      // 风格筛选
      { "name": "全部", "value": "" },
      { "name": "扁平简约", "value": "扁平简约" },
      { "name": "商务科技", "value": "商务科技" },
      { "name": "文艺清新", "value": "文艺清新" },
      { "name": "卡通手绘", "value": "卡通手绘" },
      { "name": "中国风", "value": "中国风" },
      { "name": "创意时尚", "value": "创意时尚" },
      { "name": "创意趣味", "value": "创意趣味" }
    ],
    "themeColor": [
      // 主题颜色筛选
      { "name": "全部", "value": "" },
      { "name": "橙色", "value": "#FA920A" },
      { "name": "蓝色", "value": "#589AFD" },
      { "name": "紫色", "value": "#7664FA" },
      { "name": "青色", "value": "#65E5EC" },
      { "name": "绿色", "value": "#61D328" },
      { "name": "黄色", "value": "#F5FD59" },
      { "name": "红色", "value": "#E05757" },
      { "name": "棕色", "value": "#8F5A0B" },
      { "name": "白色", "value": "#FFFFFF" },
      { "name": "黑色", "value": "#000000" }
    ]
  },
  "code": 0,
  "message": "ok"
}

分页查询 PPT 模板
接口说明： 分页查询 PPT 模版

post
https://open.docmee.cn/api/ppt/templates


参数

{
  "page": 1,
  "size": 10,
  "filters": {
    "type": 1, // 模板类型（必传）：1系统模板、4用户自定义模板
    "category": null, // 类目筛选
    "style": null, // 风格筛选
    "themeColor": null // 主题颜色筛选
  }
}

响应

{
  "code": 0,
  "total": 1,
  "data": [
    {
      "id": "xxx", // 模板ID
      "type": 1, // 模板类型：1大纲完整PPT、4用户模板
      "coverUrl": "https://xxx.png", // 封面（需要拼接?token=${token}访问）
      "category": null, // 类目
      "style": null, // 风格
      "themeColor": null, // 主题颜色
      "subject": "", // 主题
      "num": 20, // 模板页数
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/templates' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"page": 1, "size":10, "filters": { "type": 1 }}'

封面图片资源访问，需要在 url 上拼接 ?token=xxx

模板接口支持国际化，在请求 URL 上传 lang 参数，示例：/api/ppt/templates?lang=zh-CN

国际化语种支持：zh,zh-Hant,en,ja,ko,ar,de,fr,it,pt,es,ru

随机 PPT 模板
接口说明： 随机获取若干数量的 PPT 模版

post
https://open.docmee.cn/api/ppt/randomTemplates


参数

{
  "size": 30,
  "filters": {
    "type": 1, // 模板类型（必传）：1系统模板、4用户自定义模板
    "category": null, // 类目
    "style": null, // 风格
    "themeColor": null, // 主题颜色
    "neq_id": [] // 排查ID集合（把之前查询返回的id排除）
  }
}

响应

{
  "code": 0,
  "total": 1,
  "data": [
    {
      "id": "xxx", // 模板ID
      "type": 1, // 模板类型：1大纲完整PPT、4用户模板
      "coverUrl": "https://xxx.png", // 封面（需要拼接?token=${token}访问）
      "category": null, // 类目
      "style": null, // 风格
      "themeColor": null, // 主题颜色
      "subject": "", // 主题
      "num": 20, // 模板页数
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/randomTemplates' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"size":10, "filters": { "type": 1 }}'

封面图片资源访问，需要在 url 上拼接 ?token=xxx

模板接口支持国际化，在请求 URL 上传 lang 参数，示例：/api/ppt/randomTemplates?lang=zh-CN

国际化语种支持：zh,zh-Hant,en,ja,ko,ar,de,fr,it,pt,es,ru

获取 PPT
获取 PPT 列表
接口说明： 分页查询您的 PPT 作品列表

post
https://open.docmee.cn/api/ppt/listPptx


参数

{
  "page": 1,
  "size": 10
}

响应

{
  "code": 0,
  "total": 1,
  "data": [
    {
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "templateId": "xxx", // 模板ID
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/listPptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"page": 1, "size": 10}'

加载 PPT 数据
接口说明： 加载一个 PPT 的完整数据内容

get
https://open.docmee.cn/api/ppt/loadPptx?id=


响应

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      // 数据同 generatePptx 生成PPT 接口结构...
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面
      "templateId": "xxx", // 模板ID
      "pptxProperty": "xxx", // PPT数据结构（json 数据通过 gzip 压缩 base64 编码返回，具体解码和数据结构请见【PPT前端渲染】部分讲解）
      "userId": "xxx", // 用户ID
      "userName": "xxx", // 用户名称
      "companyId": 1000,
      "updateTime": null,
      "createTime": "2024-01-01 10:00:00"
    }
  },
  "message": "操作成功"
}

请求示例

  curl -X GET --location 'https://open.docmee.cn/api/ppt/loadPptx?id=xxx' \ --header
  'token: {token}'

加载 PPT 大纲内容
接口说明： 获取生成 PPT 所使用的大纲内容

post
https://open.docmee.cn/api/ppt/loadPptxMarkdown


请求

{
  "id": "xxx", // pptId
  "format": "tree" // 输出格式：text 大纲文本； tree 大纲结构树
}

响应

{
  "code": 0,
  "data": {
    "markdownText": "# 主题\n## 章节标题\n### 页面标题\n#### 内容标题\n- 文本内容...", // 大纲markdown文本（当 format 为 text 时返回）
    "markdownTree": {
      // 大纲结构树（当 format 为 tree 时返回）
      "level": 1,
      "name": "主题",
      "children": [
        {
          "level": 2,
          "name": "章节",
          "children": [
            {
              "level": 3,
              "name": "页面标题",
              "children": [
                {
                  "level": 4,
                  "name": "内容标题",
                  "children": [
                    {
                      "level": 0,
                      "name": "文本内容..."
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/loadPptxMarkdown' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id": "xxx", "format": "tree"}'

下载 PPT
接口说明： 下载 PPT 到本地

post
https://open.docmee.cn/api/ppt/downloadPptx


请求

{
  "id": "xxx",
  "refresh": false
}

响应

{
  "code": 0,
  "data": {
    "id": "xxx",
    "name": "xxx",
    "subject": "xxx",
    "fileUrl": "https://xxx" // 文件链接（有效期：2小时）
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/downloadPptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id":"xxx"}'

下载-智能动画 PPT
接口说明： 给 PPT 自动加上动画再下载到本地

get
https://open.docmee.cn/api/ppt/downloadWithAnimation?type=1&id=xxx


URL 请求

参数	类型	描述
type	number	动画类型，1 依次展示（默认）；2 单击展示
id	string	PPT ID
响应（application/octet-stream）

文件数据流

请求示例

  curl -X GET --location
  'https://open.docmee.cn/api/ppt/downloadWithAnimation?type=1&id=xxx' \ --header
  'token: {token}'

该接口会在原有的 PPT 元素对象上智能添加动画效果（元素入场动画 & 页面切场动画）

动画类型介绍：

1 依次展示，表示上一个元素动画结束后立马展示下一个元素动画

2 单击展示，表示在内容页，上一项内容展示完成后需要单击才会展示下一项内容，其他页面效果同依次展示。

操作 PPT
更换 PPT 模板
post
https://open.docmee.cn/api/ppt/updatePptTemplate


参数

{
  "pptId": "xxx", // ppt id
  "templateId": "xxx", // 模板ID
  "sync": false // 是否同步更新PPT文件（默认 false 异步更新，速度快）
}

响应

{
  "code": 0,
  "data": {
    "pptId": "xxx",
    "templateId": "xxx",
    "pptxProperty": {
      // 更换后的pptx结构数据（json）
      // ...
    }
  }
}

更新 PPT 属性
接口说明： 修改 PPT 的名称或主题

post
https://open.docmee.cn/api/ppt/updatePptxAttr


参数

{
  "id": "xxx",
  // 下面字段不能为空则修改
  "name": null,
  "subject": null
}

响应

{
  "code": 0,
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/updatePptxAttr' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id":"xxx","name":"xxx"}'

设置 Logo
接口说明： 设置 PPT 的 LOGO

post
https://open.docmee.cn/api/ppt/setPptLogo


请求参数 (multipart/form-data)

参数	类型	是否必传	说明
image	File	是	Logo 图片文件（png / jpg）
pptId	string	是	PPT ID
width	integer	否	logo 宽度 px，默认 48
height	integer	否	logo 高度 px, 默认 48
响应：

{
  "data": {
    "fileUrl": "https://xxx.pptx" // pptx 文件下载地址
  },
  "code": 0,
  "message": "操作成功"
}

请求示例

  curl -X POST --location 'https://open.docmee.cn/api/ppt/setPptLogo' \ --header
  'Content-Type: multipart/form-data' \ --header 'token: {token}' \ --form
  'image=@test.png;filename=test.png' \ --form 'pptId=12354768'

保存 PPT
post
https://open.docmee.cn/api/ppt/savePptx


参数

{
  "id": "xxx", // ppt id
  "drawPptx": true, // 是否重新渲染PPT文件并上传
  "drawCover": true, // 是否重新渲染PPT封面并上传
  "pptxProperty": {
    // 修改过后的 pptx 页面数据结构树
  }
}

响应

{
  "code": 0,
  "data": {
    "pptInfo": {
      // ppt信息
      // 数据同 generatePptx 生成PPT 接口结构...
    }
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/savePptx' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id":"xxx","drawPptx":true,"drawCover":true,"pptxProperty":{}}'

删除 PPT
post
https://open.docmee.cn/api/ppt/delete


参数

{
  "id": "xxx" // ppt id
}

响应

{
  "code": 0,
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/delete' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id":"xxx"}'

自定义模板
上传用户自定义模板
post
https://open.docmee.cn/api/ppt/uploadTemplate


请求参数 (multipart/form-data)

参数	类型	是否必传	说明
type	int	是	类型，用户自定义模板传 4（写死）
file	File	是	文件（仅支持 pptx，幻灯片大小 960x540）
templateId	string	否	模板 ID（更新时传，会覆盖该模板）
响应：

{
  "code": 0,
  "data": {
    "id": "xxx", // 模板ID
    "type": 4, // 模板类型：4用户模板
    "coverUrl": "https://xxx.png", // 封面（需要拼接token才能访问）
    "subject": "", // 主题
    "pptxProperty": "xxx", // PPT数据结构（json gzip base64）
    "num": 20, // 页码
    "createTime": "2024-01-01 10:00:00"
  },
  "message": "操作成功"
}

请求示例

  curl -X POST --location 'https://open.docmee.cn/api/ppt/uploadTemplate' \ --header
  'Content-Type: multipart/form-data' \ --header 'token: {token}' \ --form
  'type=4' \ --form 'file=@test.doc;filename=test.doc'

模板标准幻灯片大小（16:9）960x540 (33.867x19.05 厘米) ，如果尺寸非标准大小，可在 microsoft office 中修改步骤：设计 > 幻灯片大小 > 自定义幻灯片大小 => 33.867x19.05 厘米

上传用户自定义模板后，AI 会自动标注学习，如果您觉得生成效果有问题，对不上，可以访问下面链接手动纠正 AI 标注结果： https://docmee.cn/marker/\{templateId\}?token=\{apiKey\} 请把 {templateId} 替换成真实的模板 ID，{apiKey} 替换成你的 api-key，示例：https://docmee.cn/marker/10000?token=xxx

注意：如果是覆盖公共模板，请把 token 换成 Api-Key 调用，不然无权限访问。

下载自定义模板
post
https://open.docmee.cn/api/ppt/downloadTemplate


请求

{
  "id": "xxx" // 模板ID
}

响应

{
  "code": 0,
  "data": {
    "id": "xxx", // 模板ID
    "subject": "", // 主题
    "type": 4, // 模板类型：4用户模板
    "coverUrl": "https://xxx.png", // 封面（需要拼接token才能访问）
    "fileUrl": "https://xxx.pptx?xxx", // 模板下载地址（可直接访问）
    "createTime": "2024-01-01 10:00:00"
  },
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/downloadTemplate' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id": "xxx"}'

删除自定义模板
post
https://open.docmee.cn/api/ppt/delTemplateId


请求

{
  "id": "xxx" // 模板ID
}

响应

{
  "code": 0,
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/delTemplateId' \
--header 'Content-Type: application/json' \
--header 'token: {token}' \
--data '{"id": "xxx"}'

设置为公共模板
接口说明： 将自定义模版设置为 Api-Key 账号级别的公共模板，但并非平台公共模板。

post
https://open.docmee.cn/api/ppt/updateUserTemplate


请求 header

Api-Key 在开放平台获取 获取 API-KEY

参数

{
  "templateId": "xxx", // 模板ID
  "isPublic": true // 是否公开（true 公开，API-KEY下创建的所有token可以看到）
}

响应

{
  "code": 0,
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/updateUserTemplate' \
--header 'Content-Type: application/json' \
--header 'Api-Key: \{apiKey\}' \
--data '{"templateId": "xxx", "isPublic": true}'

积分和使用记录
查询 API 信息
接口说明： 获取当前用户的积分使用情况

get
https://open.docmee.cn/api/user/apiInfo


请求 header

Api-Key 在开放平台获取 获取 API-KEY

响应 body

{
  "data": {
    "availableCount": 100, // 可用次数
    "usedCount": 10 // 已使用次数
  },
  "code": 0,
  "message": "操作成功"
}

请求示例

  curl -X GET --location 'https://open.docmee.cn/api/user/apiInfo' \ --header
  'Api-Key: xxx'

查询积分使用记录
接口说明： 获取一段时间内的积分使用记录

post
https://open.docmee.cn/api/record/listPage


请求 header

Api-Key 在开放平台获取 获取 API-KEY

参数

{
  "page": 1, // 分页：第几页
  "size": 100, // 分页：每页大小
  "type": null, // 类型：1 PPT生成；2 模板上传；（默认全部）
  "uid": null, // 第三方用户ID
  "startDate": "2025-01-01 00:00:00", // 查询开始时间（必须）
  "endDate": "2025-01-30 23:59:59" // 查询结束时间（必须）
}

响应

{
  "total": 1, // 总数
  "data": [
    {
      "id": "xxx", // 记录ID（ppt ID 或 模板ID）
      "type": 1, // 记录类型：1 ppt生成; 2 模板上传
      "amount": 1, // 消耗积分
      "uid": "xxx", // 第三方用户ID
      "createTime": "2025-01-01 10:10:00" // 创建时间
    }
  ],
  "code": 0,
  "message": "ok"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/record/listPage' \
--header 'Content-Type: application/json' \
--header 'Api-Key: xxx' \
--data '{ "page": 1, "size": 100, "type": 1, "startDate": "2025-01-01", "endDate": "2025-12-31" }'

查询记录详情
接口说明： 获取一条积分使用记录的详细信息

get
https://open.docmee.cn/api/record/getById?id=


id 参数: 记录 ID

响应：

{
  "data": {
    "id": "xxx", // PPT Id 或 模板ID
    "name": "名称", // 名称
    "subject": "主题", // 主题
    "coverUrl": "https://xxx.png", // 封面图（需在URL拼接token才能访问）
    "fileUrl": "https://xxx.pptx", // pptx 文件（需在URL拼接token才能访问）
    "templateId": "1807658348435464192", // type = 1 的 PPT 记录才有 templateId 字段
    "userId": "xxx", // 用户ID
    "userName": "xxx", // 用户名称
    "updateTime": "2025-01-07 14:13:00", // 修改时间
    "createTime": "2025-01-07 14:13:00" // 创建时间
  },
  "code": 0,
  "message": "ok"
}

请求示例

  curl -X GET --location 'https://open.docmee.cn/api/record/getById?id=xxx' \
  --header 'Api-Key: xxx'

按小时统计积分使用
post
https://open.docmee.cn/api/record/statisticHours


请求 header

Api-Key 在开放平台获取 获取 API-KEY

参数

{
  "type": null, // 类型：1 生成PPT；2 上传模板；默认全部
  "uid": null, // 第三方用户ID
  "date": "2025-01-01" // 日期（必须）
}

响应

{
  "data": [
    {
      "count": 3, // 使用次数
      "hour": 10, // 时间-小时整点 (0-24)
      "amount": 3 // 消耗积分数
    },
    {
      "count": 5,
      "hour": 16,
      "amount": 5
    }
  ],
  "code": 0,
  "message": "ok"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/record/statisticHours' \
--header 'Content-Type: application/json' \
--header 'Api-Key: xxx' \
--data '{ "type": 1, "date": "2025-01-01" }'

按天统计积分使用
post
https://open.docmee.cn/api/record/statisticDays


请求 header

Api-Key 在开放平台获取 获取 API-KEY

参数

{
  "type": null, // 类型：1 生成PPT；2 上传模板；默认全部
  "uid": null, // 第三方用户ID
  "startDate": "2025-01-01 00:00:00", // 查询开始时间（必须）
  "endDate": "2025-01-30 23:59:59" // 查询结束时间（必须）
}

响应

{
  "data": [
    {
      "count": 4, // 使用次数
      "date": "2025-01-07", // 日期
      "amount": 4 // 消耗积分
    },
    {
      "count": 8,
      "date": "2025-01-06",
      "amount": 8
    }
  ],
  "code": 0,
  "message": "ok"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/record/statisticDays' \
--header 'Content-Type: application/json' \
--header 'Api-Key: xxx' \
--data '{ "type": 1, "startDate": "2025-01-01", "endDate": "2025-12-31" }'

查询所有 PPT 列表
接口说明： 获取当前 Api-Key 下一段时间内所有生成的 PPT 文件

post
https://open.docmee.cn/api/ppt/listAllPptx


请求 header

Api-Key 在开放平台获取 获取 API-KEY

参数

{
  "page": 1, // 分页
  "size": 10, // 每页大小（最大不超过100）
  "id": null, // ppt id（非必填）
  "uid": null, // 第三方用户ID（非必填）
  "templateId": null, // 模板ID（非必填）
  "startDate": "2024-01-01 00:00:00", // 创建开始时间（非必填）
  "endDate": "2025-01-01 23:59:59", // 创建结束时间（非必填）
  "desc": true // 按时间倒序返回（非必填）
}

响应

{
  "code": 0,
  "total": 1,
  "data": [
    {
      "id": "xxx", // ppt id
      "subject": "xxx", // 主题
      "coverUrl": "https://xxx.png", // 封面（需要拼接?token={API-KEY}访问）
      "fileUrl": "https://xxx.pptx", // 文件（需要拼接?token={API-KEY}访问）
      "templateId": "xxx", // 模板ID
      "userId": "xxx", // 用户ID / uid
      "companyId": 1000,
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "message": "操作成功"
}

请求示例

curl -X POST --location 'https://open.docmee.cn/api/ppt/listAllPptx' \
--header 'Content-Type: application/json' \
--header 'Api-Key: xxx' \
--data '{"page": 1, "size": 10}'

常见错误码
接口 application/json 错误

{
  "code": 0, // 错误码：0 代表成功；其他为失败
  "message": "操作成功" // 错误提示
}

错误码	说明
0	操作成功（正常）
-1	操作失败（未知错误）
88	功能受限（积分已用完 或 非 VIP）
98	认证失败（检查 token 是否过期）
99	登录过期
1001	数据不存在
1002	数据访问异常
1003	无权限访问
1006	内容涉及敏感信息
1009	AI 服务异常
1010	参数错误
1012	请求太频繁，限流
SEE 流式请求错误分两种

初始化流式调用时发生错误会返回 application/json 错误信息

{
  "code": 1010,
  "message": "参数错误：name 不能为空"
}

流式过程中遇到错误，会在流中返回 text/event-stream 流式错误信息：

data: {"status":-1, "error":"AI模型执行异常"}

PPT 前端渲染
关于 ppt 数据结构在前端渲染问题，我们已经把前端代码开源到 github：

https://github.com/docmee/aippt-js

https://github.com/docmee/aippt-vue-all

https://github.com/docmee/aippt-react

Markdown
适用于调用方需要通过自己内容和模型生成 PPT 内容，调用方根据我们的规范生成 markdown 内容，然后调用接口合成 PPT。

markdown 规范：

# 主题

## 目录章节

### 页面标题

#### 段落标题

- 段落内容 1
- 段落内容 2

markdown 规范说明：

# 主题（一级标题，必须包含，只能有一个）

## 章节一（二级标题，目录章节，必须包含，建议 6 个章节左右）

### 页面一（三级标题，页面标题，每个章节下建议 3 个左右，30 字以内）

#### 段落标题一（四级标题，段落标题，每个页面下建议 3 个左右，30 字以内）

- 段落文本内容（内容长度建议在 40-80 字之间）
- 段落文本内容
  ![配图一](https://xxx.png)

#### 段落标题二

- 段落文本内容
  ![配图二](https://xxx.png)

#### 段落标题三

- 段落文本内容
  ![配图三](https://xxx.png)

### 页面二

#### 段落标题一

段落文本内容...

#### 段落标题二

段落文本内容...

## 章节二

### 页面三（表格）

| 季度     | 销售额 |
| -------- | ------ |
| 第一季度 | 380.0  |
| 第二季度 | 826.5  |
| 第三季度 | 512.2  |
| 第四季度 | 674.0  |

markdown 规范没有定这么死，除了主题（一级标题）和章节（二级标题）必须包含外，其他标题可以没有。

完整 markdown 示例下载：markdown.md

生成 markdown 后，调用 generatePptx 生成 PPT 接口，对应 outlineContentMarkdown 参数。

通过 API 生成 PPT 后，如果需要在前端进行编辑和渲染，推荐使用以下 iframe 方式编辑器，开源地址：

github https://github.com/docmee/aippt-ui-ppt-editor

gitee https://gitee.com/veasion/aippt-ui-ppt-editor


2024 © 上海元符号智能科技有限公司 版权所有
沪ICP备2022005812号-3
Toggle theme
