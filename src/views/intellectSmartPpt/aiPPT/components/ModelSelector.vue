<template>
  <div class="model-selector" v-click-outside="handleClickOutside">
    <div class="model-header" @click="toggleCollapse">
      <div class="title-container">
        <span class="model-title">模型</span>
        <span v-if="selectedLabel" class="selected-value">{{ selectedLabel }}</span>
      </div>
      <i :class="['collapse-icon', isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
    </div>

    <transition name="collapse">
      <div v-show="!isCollapsed" class="model-cards-container" :style="containerStyle" ref="cardsContainer">
        <div
          v-for="item in options"
          :key="item.value"
          class="model-card"
          :class="{ 'model-card-selected': value === item.value }"
          @click="handleSelect(item)"
        >
          <el-tooltip :content="item.label" placement="top" :open-delay="300">
            <div class="model-card-content">
              <i :class="getModelIcon(item)" class="model-card-icon"></i>
              <span class="model-card-label">{{ item.label }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import clickOutside from '../utils/clickOutside';

export default {
  name: 'ModelSelector',
  directives: {
    clickOutside
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize);
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isCollapsed: false,
      containerStyle: {}, // 用于动态设置容器位置
      modelIcons: {
        'default': 'el-icon-cpu',
        '默认': 'el-icon-cpu',
      }
    }
  },
  computed: {
    selectedLabel() {
      if (!this.value || !this.options.length) return '';
      const selected = this.options.find(item => item.value === this.value);
      return selected ? selected.label : '';
    }
  },
  methods: {
    handleSelect(item) {
      this.$emit('input', item.value);
      this.$emit('change', item.value);
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
      if (!this.isCollapsed) {
        this.$nextTick(() => {
          this.updateContainerPosition();
        });
      }
    },
    handleClickOutside() {
      if (!this.isCollapsed) {
        this.isCollapsed = true;
      }
    },
    getModelIcon(item) {
      // 先尝试精确匹配label
      if (this.modelIcons[item.label]) {
        return this.modelIcons[item.label];
      }

      // 如果没有精确匹配，则尝试通过模型名称前缀匹配
      for (const key of Object.keys(this.modelIcons)) {
        if (key !== 'default' && item.label.toLowerCase().includes(key.toLowerCase())) {
          return this.modelIcons[key];
        }
      }

      // 如果都没匹配到，返回默认图标
      return this.modelIcons['default'];
    },
    // 智能计算容器位置
    updateContainerPosition() {
      if (!this.$el || !this.$refs.cardsContainer) return;

      const headerRect = this.$el.querySelector('.model-header').getBoundingClientRect();

      // 预估容器高度（基于选项数量）
      const estimatedItemHeight = 100; // 每个选项卡片的估计高度
      const padding = 24; // 容器内边距
      const estimatedContainerHeight = Math.min(
        Math.ceil(this.options.length / 2) * estimatedItemHeight + padding, // 2列布局
        300 // 最大高度限制
      );

      // 计算向下和向上展示的可用空间
      const spaceBelow = window.innerHeight - headerRect.bottom - 10;
      const spaceAbove = headerRect.top - 10;

      // 判断展示方向：优先向下，如果空间不足且向上空间更大则向上
      const showAbove = spaceBelow < estimatedContainerHeight && spaceAbove > spaceBelow;

      let top, maxHeight;
      if (showAbove) {
        // 向上展示
        top = headerRect.top - Math.min(estimatedContainerHeight, spaceAbove);
        maxHeight = spaceAbove;
      } else {
        // 向下展示（默认）
        top = headerRect.bottom + 2;
        maxHeight = spaceBelow;
      }

      // 计算水平位置
      const containerWidth = headerRect.width;
      let left = headerRect.left;

      // 确保不超出屏幕右边界
      if (left + containerWidth > window.innerWidth) {
        left = Math.max(10, window.innerWidth - containerWidth - 10);
      }

      // 设置容器样式
      this.containerStyle = {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        width: `${containerWidth}px`,
        maxHeight: `${maxHeight}px`,
        zIndex: 1000
      };
    },
    // 处理窗口大小变化
    handleResize() {
      if (!this.isCollapsed) {
        // 当窗口大小变化时重新计算容器位置
        this.updateContainerPosition();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "styles/selectorStyles.scss";

.model-selector {
  @include selector-base;
}

.model-header {
  @include selector-header;
}

.title-container {
  @include title-container;
}

.model-title {
  @include selector-title;
}

.selected-value {
  @include selected-value;
}

.collapse-icon {
  color: #909399;
  transition: transform 0.3s;
}

.model-cards-container {
  @include cards-container;
}

.model-card {
  @include selector-card;

  &:hover {
    .model-card-icon {
      transform: scale(1.1);
      color: #667eea;
    }

    .model-card-label {
      color: #667eea;
    }
  }
}

.model-card-selected {
  @include card-selected;

  .model-card-icon {
    color: #667eea;
    transform: scale(1.1);
  }

  .model-card-label {
    color: #667eea;
    font-weight: 600;
  }
}

.model-card-content {
  @include card-content;
}

.model-card-icon {
  @include card-icon;
}

.model-card-label {
  @include card-label;
}

/* 折叠动画 */
@include collapse-animation;
</style>
