<template>
  <div class="text-to-image-content">
    <!-- 聊天记录 -->
    <div class="chat-history">
      <div v-for="(item, index) in chatHistory" :key="index" :class="['chat-item', item.type]">
        <div v-if="item.type === 'user'" class="user-prompt">
          <strong>你：</strong> {{ item.prompt }}
        </div>
        <div v-if="item.type === 'assistant'" class="assistant-message-wrapper">
          <div class="assistant-response">
            <!-- <strong>助手：</strong> -->
            <div v-if="item.images && item.images.length > 0" class="image-grid">
              <div v-for="(imgData, imgIndex) in item.images" :key="imgIndex" class="image-item">
                <el-image :src="'data:image/png;base64,' + imgData" fit="contain" lazy :preview-src-list="item.images.map(d => 'data:image/png;base64,' + d)">
                  <div slot="error" class="image-slot">加载失败</div>
                  <div slot="error" class="image-slot">加载失败</div>
                </el-image>
                <span class="download-single-icon" @click="downloadImage(imgData, `generated_${index}_${imgIndex}.png`)" title="下载此图片">
                  <i class="el-icon-download"></i>
                </span>
              </div>
            </div>
            <el-alert v-else-if="item.error" :title="item.error" type="error" show-icon :closable="false"></el-alert>
          </div>
<!--          <div v-if="item.images && item.images.length > 0" class="message-download-all-icon" @click="downloadAllImages(item.images, `batch_msg_${index}`)" title="下载此条回复中的全部图片">-->
<!--            <i class="el-icon-folder-checked"></i>-->
<!--          </div>-->
        </div>
      </div>
      <!-- 新的加载状态显示 -->
      <div v-if="isGenerating" class="chat-item assistant">
        <div class="image-grid">
          <div v-for="i in parseInt(form.num || 1)" :key="`loader-${i}`" class="image-item loading-item">
            <div class="loading-content-wrapper">
              <i class="el-icon-loading"></i>
              <span>生成中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <el-form :model="form" label-width="100px" size="small" class="input-form prevent-drag">
      <el-form-item label="提示词" prop="prompt" label-width="60px">
        <el-input type="textarea" maxlength="220" show-word-limit v-model="form.prompt" placeholder="输入你想要生成的图片描述"  :autosize="{ minRows: 2, maxRows: 4}"></el-input>
      </el-form-item>

      <el-form-item class="submit-button-container">
        <el-button type="primary" @click="handleGetText2image" :loading="isGenerating" :disabled="!form.prompt || isGenerating">
          <i class="el-icon-picture-outline"></i> {{ isGenerating ? '生成中...' : '开始生成' }}
        </el-button>
      </el-form-item>
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="高级选项" name="1">
          <el-row :gutter="15">
            <el-col :span="12">
              <el-form-item label="Negative">
                <el-input type="textarea" show-word-limit maxlength="220" v-model="form.negativePrompt" placeholder="不希望图片中出现的内容"  :autosize="{ minRows: 1, maxRows: 5}"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模型">
                <el-select v-model="form.model" placeholder="选择模型">
                  <el-option v-for="opt in modelOptions" :key="opt.value" :label="opt.label" :value="opt.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
               <el-form-item label="图片风格">
                 <el-select v-model="form.style" placeholder="选择风格">
                   <el-option v-for="opt in styleOptions" :key="opt.value" :label="opt.label" :value="opt.value"></el-option>
                 </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="12">
                <el-form-item label="图片尺寸">
                  <el-cascader
                    v-model="form.size"
                    :options="sizeOptions"
                    :props="{ expandTrigger: 'hover' }"
                    :show-all-levels="false"
                    placeholder="选择尺寸"
                    style="width: 100%;"
                  ></el-cascader>
                </el-form-item>
             </el-col>
             <el-col :span="8">
                 <el-form-item label="生成数量">
                   <el-input-number v-model="form.num" :min="1" :max="3" controls-position="right"></el-input-number>
                 </el-form-item>
             </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
    </el-form>
  </div>
</template>

<script>
import {getImgStyleOptions, getText2image} from "@/api/intellectSmartPpt/intellectSmartPpt";
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import {getToken} from "@/utils/auth";

export default {
  name: 'textToImage',
  inject: ['apiKeyFatherProvider'],
  data() {
    return {
      form: {
        prompt: '',
        negativePrompt: '',
        style: 'Base', // 默认值
        size: ['1:1', '1024x1024'], // 修改：默认值改为数组以适应el-cascader
        num: 2,
        model: 'Stable-Diffusion-XL',
      },
      modelOptions: [
        {value: 'Stable-Diffusion-XL', label: 'Stable-Diffusion-XL'},
        {value: 'irag-1.0', label: 'irag-1.0'},
      ],
      styleOptions: [
        // { value: 'Base', label: '基础风格' },
        // { value: '3D Model', label: '3D模型' },
        // { value: 'Analog Film', label: '模拟胶片' },
        // { value: 'AnimeAnime', label: '动漫' },
        // { value: 'Cinematic', label: '电影' },
        // { value: 'Comic Book', label: '漫画' },
        // { value: 'Craft Clay', label: '工艺粘土' },
        // { value: 'Digital Art', label: '数字艺术' },
        // { value: 'Enhance', label: '增强' },
        // { value: 'Fantasy Art', label: '幻想艺术' },
        // { value: 'Isometric', label: '等距风格' },
        // { value: 'Line Art', label: '线条艺术' },
        // { value: 'Lowpoly', label: '低多边形' },
        // { value: 'Neonpunk', label: '霓虹朋克' },
        // { value: 'Origami', label: '折纸' },
        // { value: 'Photographic', label: '摄影' },
        // { value: 'Pixel Art', label: '像素艺术' },
        // { value: 'Texture', label: '纹理' },
      ],
      sizeOptions: [ // 修改：更新为层级结构
        {
          value: '1:1',
          label: '1:1',
          children: [
            {value: '768x768', label: '768x768'},
            {value: '1024x1024', label: '1024x1024'},
            {value: '1536x1536', label: '1536x1536'},
            {value: '2048x2048', label: '2048x2048'},
          ],
        },
        {
          value: '3:4',
          label: '3:4',
          children: [
            {value: '768x1024', label: '768x1024'},
            {value: '1536x2048', label: '1536x2048'},
          ],
        },
        {
          value: '4:3',
          label: '4:3',
          children: [
            {value: '1024x768', label: '1024x768'},
            {value: '2048x1536', label: '2048x1536'},
          ],
        },
        {
          value: '9:16',
          label: '9:16',
          children: [
            {value: '576x1024', label: '576x1024'},
            {value: '1152x2048', label: '1152x2048'},
          ],
        },
        {
          value: '16:9',
          label: '16:9',
          children: [
            {value: '1024x576', label: '1024x576'},
            {value: '2048x1152', label: '2048x1152'},
          ],
        },
      ],
      samplerOptions: [
        {value: 'DPM++ 2M SDE Karras', label: 'DPM++ 2M SDE Karras'},
      ],
      chatHistory: [],
      isGenerating: false,
      activeCollapse: '',
    };
  },
  computed: {
    apiKey() {
      return this.apiKeyFatherProvider && this.apiKeyFatherProvider.apiKey ? this.apiKeyFatherProvider.apiKey : '';
    },
  },
  created() {
    console.log('textToImage - Injected apiKeyFatherProvider object:', this.apiKeyFatherProvider);
    this.$nextTick(() => {
        console.log('textToImage - Computed apiKey:', this.apiKey);
    });
  },
  methods: {
    /**
     * 处理文生图
     * @returns {Promise<void>}
     */
    async handleGetText2image() {
      if (!this.form.prompt || (!this.apiKey && !getToken())) {
        this.$notify.error({ title: '错误', message: '请输入提示词或检查接口授权' });
        return;
      }

      this.isGenerating = true;
      const userMessage = { type: 'user', prompt: this.form.prompt };
      this.chatHistory.push(userMessage);
      this.scrollToBottom();

      // 从form.size数组中获取实际的分辨率值
      const selectedSizeArray = this.form.size;
      const actualSizeForApi = Array.isArray(selectedSizeArray) && selectedSizeArray.length > 0
        ? selectedSizeArray[selectedSizeArray.length - 1]
        : '1024x1024'; // 如果不是预期的数组，提供一个默认值

      const params = {
        prompt: this.form.prompt,
        negativePrompt: this.form.negativePrompt,
        style: this.form.style,
        size: actualSizeForApi, // 修改：使用处理后的尺寸
        num: this.form.num,
        model: this.form.model,
      };

      try {
        const res = await getText2image(params, { "apiKey": this.apiKey });
        if (res.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
          const imageBase64List = res.data.map(item => item.b64Image).filter(img => img);
          if (imageBase64List.length > 0) {
            this.chatHistory.push({
              type: 'assistant',
              images: imageBase64List,
              error: null
            });
          } else {
            this.chatHistory.push({
              type: 'assistant',
              images: [],
              error: '生成成功，但未返回有效图片数据'
            });
            this.$notify.warning({
              title: '提示',
              message: '生成成功，但未返回有效图片数据',
              duration: 5000,
            });
          }
        } else {
          const errorMsg = res.msg || (res.data ? '返回数据格式不正确' : '生成失败');
          this.chatHistory.push({
            type: 'assistant',
            images: [],
            error: errorMsg
          });
          this.$notify.error({
            title: '生成失败',
            message: errorMsg,
            duration: 5000,
          });
        }
      } catch (error) {
        console.error("API call error:", error);
        this.chatHistory.push({
          type: 'assistant',
          images: [],
          error: error || '请求失败，请检查网络或联系管理员'
        });
        this.$notify.error({
          title: '请求错误',
          message: error || '无法连接到生成服务',
          duration: 5000,
        });
      } finally {
        this.isGenerating = false;
        this.scrollToBottom();
      }
    },

    /**
     * 下载图
     * @param base64Data
     * @param filename
     */
    downloadImage(base64Data, filename) {
      const link = document.createElement('a');
      link.href = `data:image/png;base64,${base64Data}`;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /**
     * 下载所有图片
     * @param images
     * @param batchName
     * @returns {Promise<void>}
     */
    async downloadAllImages(images, batchName) {
       if (!images || images.length === 0) return;
       const zip = new JSZip();
       images.forEach((imgData, index) => {
         // 从 base64 数据中移除 data URI scheme 前缀 (data:image/png;base64,)
         const base64String = imgData.split(',')[1] || imgData;
         zip.file(`${batchName}_${index + 1}.png`, base64String, { base64: true });
       });
       try {
         const content = await zip.generateAsync({ type: "blob" });
         saveAs(content, `${batchName}.zip`);
       } catch (error) {
         console.error("Error creating zip file:", error);
         this.$notify.error("创建压缩文件失败");
       }
    },

    /**
     * 滚动
     */
    scrollToBottom() {
      this.$nextTick(() => {
        const chatContainer = this.$el.querySelector('.chat-history');
        if (chatContainer) {
          chatContainer.scrollTop = chatContainer.scrollHeight;
        }
      });
    },

    /**
     * 获取文生图样式风格
     */
    handleGetImgStyleOptions() {
      getImgStyleOptions(null, {"apiKey": this.apiKey}).then(res=>{
        if (res.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
          this.styleOptions = res.data;
        }
      })
    },
  },
  mounted() {
    // 添加事件监听器来阻止文本区域的拖动事件冒泡
    const preventDrag = (e) => {
      e.stopPropagation();
    };

    // 获取所有文本输入区域
    const textareas = this.$el.querySelectorAll('.el-textarea__inner, .el-input__inner');
    textareas.forEach(textarea => {
      textarea.addEventListener('mousedown', preventDrag);
      textarea.addEventListener('mousemove', preventDrag);
    });
  },
  beforeDestroy() {
    // 清理事件监听器
    const textareas = this.$el.querySelectorAll('.el-textarea__inner, .el-input__inner');
    textareas.forEach(textarea => {
      textarea.removeEventListener('mousedown', preventDrag);
      textarea.removeEventListener('mousemove', preventDrag);
    });
  },
}
</script>

<style lang="scss" scoped>
.text-to-image-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .chat-history {
    flex-grow: 1;
    overflow-y: auto;
    padding: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;

    .chat-item {
      margin-bottom: 15px;
      display: flex;

      &.user {
        justify-content: flex-end;
        .user-prompt {
          background-color: #e6f7ff;
          padding: 8px 12px;
          border-radius: 8px;
          max-width: 70%;
          word-wrap: break-word;
        }
      }

      &.assistant {
        justify-content: flex-start;

        .assistant-message-wrapper {
          position: relative;
          width: 100%;
        }

        .assistant-response {
          background-color: #f0f2f5;
          padding: 10px;
          border-radius: 8px;
          max-width: 100%;
          word-wrap: break-word;
        }

        .image-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
          margin-top: 8px;
          width: 100%;

          .image-item {
            position: relative;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: hidden;
            background-color: #fff;
            max-height: 300px;
            width: 100%;

            .el-image {
              width: 100%;
              height: auto;
              display: block;
            }

            &.loading-item {
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #f9f9f9;
              border: 2px solid #409EFF;
              min-height: 300px;
              animation: pulse-border 1.5s infinite ease-in-out;

              .loading-content-wrapper {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;

                .el-icon-loading {
                  font-size: 24px;
                  color: #409EFF;
                }

                span {
                  color: #909399;
                  font-size: 14px;
                }
              }
            }

            .download-single-icon {
              position: absolute;
              top: 5px;
              right: 5px;
              font-size: 13px;
              color: #fff;
              background-color: rgba(0, 0, 0, 0.5);
              padding: 3px;
              border-radius: 50%;
              cursor: pointer;
              opacity: 0;
              visibility: hidden;
              transition: opacity 0.2s, visibility 0.2s;
              z-index: 1;
            }

            &:hover .download-single-icon {
              opacity: 0.8;
              visibility: visible;
            }
          }
        }

        .message-download-all-icon {
          position: absolute;
          bottom: -10px;
          right: -10px;
          font-size: 13px;
          color: #5cb85c;
          background-color: #ffffff;
          padding: 4px;
          border-radius: 50%;
          cursor: pointer;
          opacity: 0.9;
          transition: opacity 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s;
          z-index: 5;
          box-shadow: 0 2px 6px rgba(0,0,0,0.1);
          border: 1px solid #e0e0e0;
          &:hover {
            opacity: 1;
            color: #4cae4c;
            transform: scale(1.1);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
          }
        }
      }
    }
  }

  .input-form {
    padding: 15px;
    border-top: 1px solid #eee;
    background-color: #fafafa;

    &.prevent-drag {
      .el-textarea__inner {
        cursor: text !important;
      }

      ::v-deep .el-textarea__inner,
      ::v-deep .el-input__inner {
        &:hover {
          cursor: text !important;
        }
        &:focus {
          cursor: text !important;
        }
      }
    }

    .el-form-item {
      margin-bottom: 12px;
    }

    .submit-button-container {
      text-align: right;
      margin-top: 0px;
      margin-bottom: 0px !important;
    }

     .el-collapse {
      border-top: none;
      border-bottom: none;
      .el-collapse-item__header {
        font-size: 13px;
        background-color: transparent;
        border-bottom: none;
      }
      .el-collapse-item__wrap {
        background-color: transparent;
        border-bottom: none;
        .el-collapse-item__content {
          padding-bottom: 0;
        }
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding-bottom: 0px !important;
  }
}

@keyframes pulse-border {
  0% {
    border-color: rgba(64, 158, 255, 0.4);
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.1);
  }
  50% {
    border-color: rgba(64, 158, 255, 1);
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.4);
  }
  100% {
    border-color: rgba(64, 158, 255, 0.4);
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.1);
  }
}
</style>
