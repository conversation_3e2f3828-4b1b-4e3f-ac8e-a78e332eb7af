<template>
  <div class="ppt-editor-container"
       v-loading="loading"
       element-loading-text="编辑器加载中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(255, 255, 255, 0.8)">
    <!-- 编辑器容器 -->
    <div ref="editorContainer" class="editor-container"></div>

    <!-- 新增：关闭按钮 -->
    <!--    <el-button-->
    <!--      type="danger"-->
    <!--      icon="el-icon-close"-->
    <!--      circle-->
    <!--      class="close-button"-->
    <!--      @click="handleCloseClick"-->
    <!--      title="关闭编辑器"-->
    <!--    ></el-button>-->
    <el-button icon="el-icon-close"
               class="close-button"
               @click="handleCloseClick"
               title="关闭编辑器"
               circle>
    </el-button>


    <div class="buttonDraggable" v-draggable @start="onStart" @move="onMove" @stop="onStop">
      <!-- 文生图浮动按钮 -->
      <el-button
        @click="handleShowTextToImageDialog"
        type="primary"
        icon="el-icon-picture-outline"
        circle
        class="text-to-image-fab"
        title="AI绘图">
      </el-button>
    </div>


    <!-- 文生图对话框 -->
    <el-dialog
      v-draggable
      title="AI绘图"
      :visible.sync="showTextToImageDialog"
      width="55%"
      :modal="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      custom-class="text-to-image-dialog"
      @close="clearError">
      <text-to-image
        v-show="showTextToImageDialog"
        ref="textToImageComponent"
        @request-close="showTextToImageDialog = false">
      </text-to-image>
    </el-dialog>

    <!-- 错误提示 -->
    <div v-if="error" class="editor-error">
      <el-alert
        :title="error.title"
        :description="error.message"
        type="error"
        show-icon
        @close="clearError">
      </el-alert>
    </div>
  </div>
</template>

<script>
import TextToImage from "@/views/intellectSmartPpt/aiPPT/components/Editor/textToImage.vue";
import {Draggable, DraggableEvent} from '@braks/revue-draggable';

export default {
  name: 'AiPptEditor',
  components: {Draggable, TextToImage},

  props: {
    pptId: {
      type: String,
      required: true
    },
    token: {
      type: String,
      required: true
    },
    animation: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      loading: true,
      error: null,
      editorInstance: null,
      sdkLoaded: false,
      sdkLoadingPromise: null,
      showTextToImageDialog: false,
      preventClickAfterDrag: false, // 新增：防止拖拽后触发点击的标志位
    }
  },

  watch: {
    token(newToken) {
      if (this.editorInstance && newToken) {
        this.editorInstance.updateToken(newToken);
      }
    }
  },

  async mounted() {
    await this.initEditor();
  },

  beforeDestroy() {
    this.destroyEditor();
  },

  methods: {
    onStart(e) {
      console.log("start", e);
      this.preventClickAfterDrag = false;
    },
    onMove(e) {
      console.log("move", e);
      this.preventClickAfterDrag = true;
    },
    onStop(e) {
      console.log("stop", e);
    },


    // 新增：处理关闭按钮点击事件
    handleCloseClick() {
      this.$emit('request-close');
    },

    // 加载SDK
    async loadDocmeeSDK() {
      if (this.sdkLoadingPromise) {
        return this.sdkLoadingPromise;
      }

      if (window.DocmeeUI) {
        console.log('DocmeeUI SDK已全局加载');
        this.sdkLoaded = true;
        return Promise.resolve();
      }

      console.log('开始加载DocmeeUI SDK...');
      this.sdkLoadingPromise = new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/static/docmee-ui-sdk-iframe.min.js';
        script.async = true;

        const timeoutId = setTimeout(() => {
          console.error('SDK加载超时');
          reject(new Error('SDK加载超时'));
        }, 10000);

        script.onload = () => {
          clearTimeout(timeoutId);
          if (window.DocmeeUI) {
            console.log('DocmeeUI SDK加载成功');
            this.sdkLoaded = true;
            resolve();
          } else {
            console.error('DocmeeUI对象不存在');
            reject(new Error('DocmeeUI对象不存在'));
          }
        };

        script.onerror = (error) => {
          clearTimeout(timeoutId);
          console.error('SDK加载失败:', error);
          reject(error);
        };

        document.head.appendChild(script);
      }).finally(() => {
        this.sdkLoadingPromise = null;
      });

      return this.sdkLoadingPromise;
    },

    // 初始化编辑器
    async initEditor() {
      try {
        console.log('开始初始化编辑器...');
        console.log('PPT ID:', this.pptId);
        console.log('Token:', this.token);

        await this.loadDocmeeSDK();

        if (!this.$refs.editorContainer) {
          throw new Error('编辑器容器未找到');
        }

        const config = {
          token: this.token,
          pptId: this.pptId,
          animation: this.animation,
          container: this.$refs.editorContainer,
          page: 'editor',
          lang: 'zh',
          mode: 'light',
          background: '#f5f7fa',
          padding: '0',
          onMessage: this.handleEditorMessage,
          DOMAIN: 'https://iframe.docmee.cn',
          downloadButton: ['pptx'],
        };

        this.editorInstance = new window.DocmeeUI(config);
        console.log('编辑器初始化成功');
      } catch (error) {
        console.error('编辑器初始化失败:', error);
        this.handleError('编辑器初始化失败', error.message);
      } finally {
        this.loading = false;
      }
    },

    // 处理编辑器消息
    handleEditorMessage(message) {
      console.log('编辑器消息:', message);

      switch (message.type) {
        case 'mounted':
          this.loading = false;
          console.log('编辑器挂载完成');
          this.$emit('editor-ready');
          break;

        case 'invalid-token':
          console.warn('Token无效');
          this.$emit('token-invalid');
          break;

        case 'beforeDownload':
          const {id, subject} = message.data;
          console.log('准备下载PPT:', {id, subject});
          return `PPT_${subject}_${id}.pptx`;

        case 'error':
          console.error('编辑器错误:', message.data);
          this.handleError('编辑器错误', message.data);
          break;

        default:
          console.log('其他编辑器消息:', message);
          this.$emit('editor-message', message);
      }
    },

    // 错误处理
    handleError(title, message) {
      this.error = {title, message};
      this.$emit('editor-error', {title, message});
    },

    // 清除错误
    clearError() {
      this.error = null;
    },

    // 销毁编辑器
    destroyEditor() {
      if (this.editorInstance) {
        // 清理编辑器实例
        this.editorInstance = null;
      }
    },

    handleShowTextToImageDialog() {
      if (this.preventClickAfterDrag) {
        // 如果 preventClickAfterDrag 为 true，说明是拖拽操作触发的，则直接返回，不打开对话框
        return;
      }
      this.showTextToImageDialog = true;
      // 新增：当对话框显示时，调用子组件的方法
      this.$nextTick(() => {
        if (this.$refs.textToImageComponent && typeof this.$refs.textToImageComponent.handleGetImgStyleOptions === 'function') {
          this.$refs.textToImageComponent.handleGetImgStyleOptions();
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.ppt-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 新增：关闭按钮样式
  .close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1001;
  }

  .editor-container {
    flex: 1;
    width: 100%;
    height: 100%;
    min-height: 0;
    position: relative;
  }

  .editor-error {
    position: absolute;
    top: 70px;
    left: 20px;
    right: 20px;
    z-index: 101;
  }

  // 文生图浮动按钮样式
  .text-to-image-fab {
    position: absolute;
    right: 30px;
    top: 35%;
    transform: translateY(-50%);
    z-index: 1005;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-50%) scale(1.1);
    }
  }
}

// 对话框样式
::v-deep .text-to-image-dialog {
  .el-dialog__body {
    padding: 10px 20px 20px;
    max-height: 75vh;
    overflow-y: auto;
  }
}

.buttonDraggable {
  position: absolute;
  right: 10px; /* 初始位置 */
  top: 15%; /* 初始位置 */
  width: 80px;
  height: 80px;
  z-index: 1005; /* 确保在顶层 */
  cursor: grab;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-item {
  position: relative;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  width: 250px; /* 新增：明确宽度 */
  height: 250px; /* 新增：明确高度 */
}

.image-item .el-image { /* 或者如果你的CSS选择器是 .image-item > .el-image 也没问题 */
  width: 100%; /* 使其填充父容器 .image-item 的宽度 */
  height: 100%; /* 使其填充父容器 .image-item 的高度 */
  display: block;
}

.image-item.loading-item {
  /* 继承 .image-item 的宽度、高度、边框等样式 */
  /* height: 150px; */
  /* 可以移除此行 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #fdfdfd;
}

.message-download-all-icon {
  position: absolute;
  /*
   * 位置调整说明：
   * bottom: -10px; 控制图标相对于气泡底部的垂直位置。
   *   负值越大（例如 -15px），图标越靠下（离气泡越远）。
   *   负值越小（例如 -5px），图标越靠上（越贴近气泡底部边缘）。
   * right: -12px; 控制图标相对于气泡右侧的水平位置。
   *   负值越大（例如 -15px），图标越靠右（离气泡越远）。
   *   负值越小（例如 -5px），图标越靠左（越贴近气泡右侧边缘）。
   * 你可以根据视觉效果微调这两个值。
  */
  bottom: -10px;
  right: -12px;
  font-size: 18px; /* 缩小图标字体大小 */
  color: #5cb85c;
  background-color: #ffffff;
  padding: 5px; /* 缩小内边距，使整体变小 */
  border-radius: 50%;
  cursor: pointer;
  opacity: 0.9;
  transition: opacity 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s;
  z-index: 5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
}

/* .message-download-all-icon:hover 的样式可以保持不变 */

.image-grid {
  display: flex; /* 核心修改：使图片横向排列 */
  flex-wrap: wrap; /* 图片换行 */
  gap: 10px; /* 图片之间的间距 */
  margin-top: 8px; /* 图片组与上方内容的间距 */
}

.assistant-response {
  background-color: #f0f2f5;
  padding: 10px;
  border-radius: 8px;
  max-width: 100%; // 助手消息可以更宽
  word-wrap: break-word;

  .image-grid {
    display: flex; // 核心修改：使图片横向排列
    flex-wrap: wrap; // 图片换行
    gap: 10px; // 图片之间的间距
    margin-top: 8px; // 图片组与上方内容的间距

    .image-item {
      position: relative;
      border: 1px solid #eee;
      border-radius: 4px;
      overflow: hidden;
      background-color: #fff;
      width: 150px; // 图片项宽度
      height: 150px; // 图片项高度
      display: flex; // 用于内部 el-image 和加载状态的对齐
      justify-content: center;
      align-items: center;

      .el-image {
        width: 100%;
        height: 100%;
        display: block;
      }

      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
      }

      .download-single-icon {
        position: absolute;
        bottom: 5px;
        right: 5px;
        font-size: 16px;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }

      &.loading-item {
        flex-direction: column; // 加载状态下图标和文字垂直排列
        color: #909399;
        background-color: #f9f9f9; // 加载占位背景色
        font-size: 12px; // 加载文字大小
        .el-icon-loading {
          font-size: 24px; // 加载图标大小
          margin-bottom: 5px; // 图标和文字间距
        }
      }
    }
  }
}
</style>
