<template>
  <div class="ai-ppt-editor-container">
    <!-- 加载状态显示 -->
    <div v-if="loading || loadingError" class="loading-container">
      <!-- 正常加载动画 -->
      <div v-if="loading && !loadingError" class="liquid-loader">
        <div class="liquid-wave"></div>
        <div class="liquid-wave second-wave"></div>
        <div class="liquid-wave third-wave"></div>
        <div class="loading-text">{{ currentLoadingText }}</div>
      </div>

      <!-- 加载失败提示 -->
      <div v-if="loadingError" class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">加载失败</div>
        <div class="error-message">{{ errorMessage }}</div>
        <div class="error-actions">
          <button @click="retryLoad" class="retry-btn">重新加载</button>
          <button @click="handleClose" class="close-btn">关闭</button>
        </div>
      </div>
    </div>

    <!-- 编辑器 iframe -->
    <iframe
        ref="editorIframe"
        :src="currentEditorUrl"
        class="editor-iframe"
        @load="handleIframeLoad"
        @error="handleIframeError"
        allow="fullscreen *; clipboard-read; clipboard-write; payment; cross-origin-isolated"
    ></iframe>
  </div>
</template>

<script>
// AiPptEditor 组件：通过 iframe 嵌入 AI PPT 编辑器，并处理与编辑器的通信。
import { getPptEditData } from "@/api/intellectSmartPpt/intellectSmartPpt";
import { createNotificationTopRight } from '@/utils/common'

export default {
  name: 'aiPptCustomEditor',

  props: {
    // PPT ID，用于标识要加载的 PPT
    pptId: {
      type: String,
      required: true
    },
    // 模板ID
    templateId: {
      type: String,
      required: false
    },
    // 用户认证 Token
    token: {
      type: String,
      required: true
    },
    // 是否开启动画效果
    animation: {
      type: Boolean,
      default: false
    },
    // 编辑器后端 API 的基础 URL
    baseUrl: {
      type: String,
      default: 'https://docmee.cn/api'
    },
    // 编辑器url
    editorUrl: {
      type: String,
      default: ''
    },
    // 是否强制重新加载
    forceReload: {
      type: Boolean,
      default: false
    },
    // 加载文字
    loadingText: {
      type: String,
      default: 'PPT加载中...'
    }
  },

  data() {
    return {
      // 编辑器状态
      editorReady: false,
      currentEditorUrl: '',

      // 加载状态
      loading: true,
      loadingError: false,
      errorMessage: '',
      currentLoadingText: this.loadingText,

      // 超时控制配置
      EDITOR_INIT_TIMEOUT: 5000, // 编辑器初始化超时时间
      initTimeout: null,

      // 重试机制
      retryCount: 0,
      maxRetries: 3,

      // 通信数据
      sendData: null,

      // 侧边栏状态记录 - 用于进入编辑器时完全隐藏侧边栏，退出时恢复
      previousSidebarState: null,      // 记录进入前侧边栏是否打开
      previousSidebarHideState: null   // 记录进入前侧边栏是否隐藏
    }
  },

  watch: {
    // 监听关键属性变化，重新加载编辑器
    pptId() {
      this.reloadEditor()
    },
    templateId() {
      this.reloadEditor()
    },
    token() {
      this.reloadEditor()
    },
    editorUrl: {
      handler(newUrl) {
        if (newUrl && newUrl !== this.currentEditorUrl) {
          this.currentEditorUrl = newUrl
          this.resetLoadingState()
        }
      },
      immediate: true
    },
    // 监听强制重新加载
    forceReload(val) {
      if (val) {
        this.handleForceReload()
        this.$nextTick(() => {
          this.$emit('update:forceReload', false)
        })
      }
    },
    // 监听加载文字变化
    loadingText(newText) {
      this.currentLoadingText = newText
    }
  },

  created() {
    // 初始化编辑器URL
    if (!this.editorUrl) {
      this.fetchEditorUrl()
    }
  },

  mounted() {
    window.addEventListener('message', this.handleEditorMessage)
    // 进入编辑器时完全隐藏侧边栏
    this.hideSidebar()
  },

  beforeDestroy() {
    this.clearAllTimers()
    window.removeEventListener('message', this.handleEditorMessage)
    // 退出编辑器时恢复侧边栏原始状态
    this.restoreSidebar()
  },

  methods: {
    /**
     * 清除所有定时器
     */
    clearAllTimers() {
      if (this.initTimeout) {
        clearTimeout(this.initTimeout)
        this.initTimeout = null
      }
    },

    /**
     * 重置加载状态
     */
    resetLoadingState() {
      this.loading = true
      this.loadingError = false
      this.errorMessage = ''
      this.editorReady = false
      this.clearAllTimers()
    },

    /**
     * 开始编辑器初始化超时检测
     */
    startInitTimeout() {
      this.initTimeout = setTimeout(() => {
        if (!this.editorReady && this.loading) {
          this.handleInitTimeout()
        }
      }, this.EDITOR_INIT_TIMEOUT)
    },

    /**
     * 处理编辑器初始化超时
     */
    handleInitTimeout() {
      console.warn('编辑器初始化超时')
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        this.currentLoadingText = `重试中... (${this.retryCount}/${this.maxRetries})`
        console.log(`编辑器初始化超时，开始第${this.retryCount}次重试`)
        this.$emit('on-retry', {
          count: this.retryCount,
          maxRetries: this.maxRetries
        })
        // 重新初始化编辑器
        this.initEditor()
        this.startInitTimeout()
      } else {
        this.loading = false
        this.loadingError = true
        this.errorMessage = '编辑器初始化超时，请重试'
        this.$emit('on-error', {
          type: 'INIT_TIMEOUT',
          message: this.errorMessage
        })
      }
    },

    /**
     * iframe加载完成处理
     */
    handleIframeLoad() {
      console.log('Iframe加载完成')
      if (this.currentEditorUrl) {
        this.initEditor()
        // 开始编辑器初始化超时检测
        this.startInitTimeout()
      }
    },

    /**
     * iframe加载错误处理
     */
    handleIframeError() {
      console.error('Iframe加载失败')
      this.loading = false
      this.loadingError = true
      this.errorMessage = 'iframe加载失败，请检查网络连接'
      this.$emit('on-error', {
        type: 'IFRAME_LOAD_ERROR',
        message: this.errorMessage
      })
    },



    /**
     * 处理强制重新加载
     */
    handleForceReload() {
      console.log('强制重新加载编辑器')
      this.resetLoadingState()
      this.retryCount = 0

      const iframe = this.$refs.editorIframe
      if (iframe) {
        iframe.src = 'about:blank'
        setTimeout(() => {
          iframe.src = this.currentEditorUrl
          this.startLoadTimeout()
        }, 100)
      }
    },

    /**
     * 重试加载
     */
    retryLoad() {
      this.resetLoadingState()
      this.retryCount = 0
      this.currentLoadingText = this.loadingText

      if (!this.currentEditorUrl) {
        this.fetchEditorUrl()
      } else {
        const iframe = this.$refs.editorIframe
        if (iframe) {
          iframe.src = 'about:blank'
          setTimeout(() => {
            iframe.src = this.currentEditorUrl
          }, 100)
        }
      }
    },

    /**
     * 关闭编辑器
     */
    handleClose() {
      this.$emit('close')
    },

    /**
     * 重新加载编辑器（属性变化时）
     */
    reloadEditor() {
      if (this.editorReady) {
        this.updateEditor()
      }
    },

    /**
     * 初始化编辑器
     */
    initEditor() {
      const iframe = this.$refs.editorIframe
      if (!iframe || !iframe.contentWindow) {
        this.loading = false
        this.loadingError = true
        this.errorMessage = 'iframe初始化失败'
        this.$emit('on-error', {
          type: 'IFRAME_ERROR',
          message: this.errorMessage
        })
        return
      }

      try {
        this.sendData = {
          pptId: this.templateId || this.pptId,
          isTemplate: !!this.templateId,
          token: this.token,
          baseUrl: this.baseUrl,
          animation: this.animation,
          editorURL: this.currentEditorUrl
        }

        iframe.contentWindow.postMessage({
          type: 'INIT_EDITOR',
          data: this.sendData
        }, '*')

        console.log('发送编辑器初始化消息')
      } catch (error) {
        this.loading = false
        this.loadingError = true
        this.errorMessage = '消息发送失败'
        this.$emit('on-error', {
          type: 'POST_MESSAGE_ERROR',
          message: this.errorMessage,
          error
        })
      }
    },

    /**
     * 更新编辑器配置
     */
    updateEditor() {
      if (!this.editorReady) {
        return
      }

      const iframe = this.$refs.editorIframe
      if (!iframe || !iframe.contentWindow) return

      try {
        this.sendData = {
          pptId: this.templateId || this.pptId,
          isTemplate: !!this.templateId,
          token: this.token,
          baseUrl: this.baseUrl,
          animation: this.animation,
          editorURL: this.currentEditorUrl
        }

        iframe.contentWindow.postMessage({
          type: 'UPDATE_EDITOR',
          data: this.sendData
        }, '*')

        console.log('发送编辑器更新消息')
      } catch (error) {
        this.$emit('on-error', {
          type: 'UPDATE_ERROR',
          message: '更新配置失败',
          error
        })
      }
    },

    /**
     * 处理来自iframe的消息
     */
    handleEditorMessage(event) {
      try {
        if (!event.data || !event.data.type) return

        switch (event.data.type) {
          case 'EDITOR_READY':
            this.handleEditorReady()
            break
          case 'EDITOR_EXIT':
            this.handleEditorExit(event.data.data)
            break
          case 'EDITOR_ERROR':
            this.handleEditorError(event.data.error)
            break
        }
      } catch (error) {
        this.$emit('on-error', {
          type: 'MESSAGE_HANDLER_ERROR',
          message: '消息处理失败',
          error
        })
      }
    },

    /**
     * 处理编辑器就绪
     */
    handleEditorReady() {
      console.log('编辑器已就绪')
      this.editorReady = true
      this.clearAllTimers()

      // 添加淡出效果
      const loaderContainer = document.querySelector('.loading-container')
      if (loaderContainer) {
        loaderContainer.classList.add('fade-out')
        setTimeout(() => {
          this.loading = false
        }, 800)
      } else {
        this.loading = false
      }

      this.$emit('on-ready')
    },

    /**
     * 处理编辑器退出
     */
    handleEditorExit(data) {
      console.log('编辑器退出', data)
      this.$emit('close')
      if (data && data.isTemplate) {
        createNotificationTopRight("完成编辑后建议重新进行标注", 'warning')
      }
    },

    /**
     * 处理编辑器错误
     */
    handleEditorError(error) {
      console.error('编辑器错误', error)
      this.loading = false
      this.loadingError = true
      this.errorMessage = error.message || '编辑器内部错误'
      this.$emit('on-error', {
        type: 'EDITOR_ERROR',
        message: this.errorMessage,
        error
      })
    },

    /**
     * 重置编辑器状态
     */
    reset() {
      this.resetLoadingState()
      this.retryCount = 0
      this.currentEditorUrl = ''
      this.fetchEditorUrl()
    },

    /**
     * 获取编辑器URL
     */
    fetchEditorUrl() {
      this.currentLoadingText = '获取编辑器地址中...'

      getPptEditData().then(res => {
        console.log('获取编辑器URL响应：', res)
        if (res.code === 200 && res.data) {
          const editUrlItem = res.data.find(item => item.label === 'editUrl')
          if (editUrlItem && editUrlItem.value) {
            this.currentEditorUrl = editUrlItem.value
            this.currentLoadingText = this.loadingText
            console.log('获取到编辑器URL：', this.currentEditorUrl)
          } else {
            this.handleFetchUrlError('未找到编辑器URL配置')
          }
        } else {
          this.handleFetchUrlError(res.message || '获取编辑器URL失败')
        }
      }).catch(error => {
        console.error('获取编辑器URL失败：', error)
        this.handleFetchUrlError('网络请求失败，请检查网络连接')
      })
    },

    /**
     * 处理获取URL失败
     */
    handleFetchUrlError(message) {
      this.loading = false
      this.loadingError = true
      this.errorMessage = message
      this.$emit('on-error', {
        type: 'FETCH_URL_ERROR',
        message: this.errorMessage
      })
    },

    /**
     * 完全隐藏侧边栏
     * 进入编辑器时调用，提供全屏编辑体验
     */
    hideSidebar() {
      // 记录当前侧边栏状态，用于退出时恢复
      this.previousSidebarState = this.$store.state.app.sidebar.opened
      this.previousSidebarHideState = this.$store.state.app.sidebar.hide

      // 完全隐藏侧边栏（宽度变为0，完全不可见）
      this.$store.dispatch('app/toggleSideBarHide', true)
      console.log('编辑器模式：已完全隐藏侧边栏')
    },

    /**
     * 恢复侧边栏原始状态
     * 退出编辑器时调用，恢复用户进入前的侧边栏状态
     */
    restoreSidebar() {
      // 先恢复侧边栏的隐藏状态
      if (this.previousSidebarHideState !== null) {
        this.$store.dispatch('app/toggleSideBarHide', this.previousSidebarHideState)
      }

      // 如果进入前侧边栏是打开的且没有被隐藏，则恢复打开状态
      if (this.previousSidebarState && !this.previousSidebarHideState) {
        this.$store.dispatch('app/toggleSideBar')
      }

      console.log('编辑器退出：已恢复侧边栏状态')
    }
  }
}

</script>

<style scoped>
.ai-ppt-editor-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.editor-iframe {
  width: 100%;
  height: 100%;
  border: none;
  position: absolute;
  top: 0;
  left: 0;
}

/* 加载容器样式 */
.loading-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(-45deg,
    rgba(230, 245, 255, 0.6),
    rgba(240, 235, 255, 0.5),
    rgba(235, 255, 245, 0.6),
    rgba(225, 240, 255, 0.5),
    rgba(245, 240, 255, 0.6),
    rgba(240, 255, 250, 0.5)
  );
  background-size: 400% 400%;
  z-index: 999;
  backdrop-filter: blur(15px) saturate(1.4) brightness(1.05);
  transition: opacity 0.8s ease, visibility 0.8s ease;
  opacity: 1;
  visibility: visible;
  animation: gradientBG 18s ease infinite;
}

.loading-container.fade-out {
  opacity: 0;
  visibility: hidden;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 40px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 248, 255, 0.9) 20%,
    rgba(245, 240, 255, 0.85) 40%,
    rgba(240, 255, 250, 0.9) 60%,
    rgba(235, 245, 255, 0.85) 80%,
    rgba(255, 255, 255, 0.95) 100%
  );
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(135, 206, 250, 0.2),
    0 15px 30px rgba(221, 160, 221, 0.15),
    0 8px 16px rgba(152, 251, 152, 0.1),
    inset 0 3px 6px rgba(255, 255, 255, 0.9),
    inset 0 -3px 6px rgba(230, 245, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px) saturate(1.4) brightness(1.05);
  width: 480px;
  max-width: 90vw;
  margin: 0 auto;
  position: relative;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 24px;
  filter: drop-shadow(0 0 12px rgba(231, 76, 60, 0.3));
  opacity: 0.9;
}

.error-title {
  font-size: 28px;
  font-weight: 700;
  color: #e74c3c;
  margin-bottom: 16px;
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

.error-message {
  font-size: 18px;
  color: #666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-shadow: 0.5px 0.5px 1px rgba(255, 255, 255, 0.9);
  max-width: 360px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-btn, .close-btn {
  padding: 14px 32px;
  border: none;
  border-radius: 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.retry-btn {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.retry-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.retry-btn:hover::before {
  left: 100%;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #357abd 0%, #2c5f8f 100%);
  transform: translateY(-3px);
  box-shadow:
    0 8px 25px rgba(74, 144, 226, 0.4),
    0 4px 12px rgba(74, 144, 226, 0.2);
}

.close-btn {
  background: linear-gradient(135deg, #a0a0a0 0%, #888888 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.close-btn:hover {
  background: linear-gradient(135deg, #888888 0%, #707070 100%);
  transform: translateY(-3px);
  box-shadow:
    0 8px 25px rgba(160, 160, 160, 0.4),
    0 4px 12px rgba(160, 160, 160, 0.2);
}

.retry-btn:active, .close-btn:active {
  transform: translateY(-1px);
  transition: all 0.1s ease;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.liquid-loader {
  position: relative;
  width: 220px;
  height: 220px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 248, 255, 0.9) 20%,
    rgba(245, 240, 255, 0.85) 40%,
    rgba(240, 255, 250, 0.9) 60%,
    rgba(235, 245, 255, 0.85) 80%,
    rgba(255, 255, 255, 0.95) 100%
  );
  border-radius: 50%;
  box-shadow:
    0 25px 50px rgba(135, 206, 250, 0.2),
    0 15px 30px rgba(221, 160, 221, 0.15),
    0 8px 16px rgba(152, 251, 152, 0.1),
    inset 0 3px 6px rgba(255, 255, 255, 0.9),
    inset 0 -3px 6px rgba(230, 245, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: breathing 5s infinite ease-in-out;
  backdrop-filter: blur(3px);
}

/* 添加装饰性光环效果 */
.liquid-loader::before {
  content: '';
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  border-radius: 50%;
  background: linear-gradient(45deg,
    rgba(173, 216, 230, 0.4),  /* 浅蓝 */
    rgba(221, 160, 221, 0.3),  /* 浅紫 */
    rgba(152, 251, 152, 0.4),  /* 浅绿 */
    rgba(230, 245, 255, 0.3),  /* 更浅蓝 */
    rgba(245, 240, 255, 0.4),  /* 更浅紫 */
    rgba(240, 255, 250, 0.3)   /* 更浅绿 */
  );
  animation: rotate-halo 10s infinite linear;
  z-index: -1;
  filter: blur(1px);
}

@keyframes breathing {
  0%, 100% {
    transform: scale(0.95);
    box-shadow:
      0 20px 40px rgba(24, 144, 255, 0.15),
      0 8px 16px rgba(135, 206, 250, 0.2),
      inset 0 2px 4px rgba(255, 255, 255, 0.8),
      inset 0 -2px 4px rgba(173, 216, 230, 0.3);
  }
  50% {
    transform: scale(1.08);
    box-shadow:
      0 25px 50px rgba(24, 144, 255, 0.25),
      0 12px 24px rgba(135, 206, 250, 0.3),
      inset 0 3px 6px rgba(255, 255, 255, 0.9),
      inset 0 -3px 6px rgba(173, 216, 230, 0.4);
  }
}

@keyframes rotate-halo {
  0% {
    transform: rotate(0deg);
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.6;
  }
}

.liquid-wave {
  position: absolute;
  width: 420px;
  height: 420px;
  top: 50%;
  left: 50%;
  background: linear-gradient(45deg,
    rgba(173, 216, 230, 0.5),  /* 浅蓝 */
    rgba(152, 251, 152, 0.6),  /* 浅绿 */
    rgba(135, 206, 250, 0.5),  /* 天蓝 */
    rgba(144, 238, 144, 0.6)   /* 淡绿 */
  );
  border-radius: 42%;
  transform: translate(-50%, 0) rotate(0);
  animation: wave 7s infinite linear, fill 14s forwards;
  filter: drop-shadow(0 0 20px rgba(173, 216, 230, 0.3));
  opacity: 0.6;
}

.liquid-wave.second-wave {
  background: linear-gradient(45deg,
    rgba(221, 160, 221, 0.4),  /* 浅紫 */
    rgba(230, 230, 250, 0.5),  /* 薰衣草色 */
    rgba(216, 191, 216, 0.4),  /* 蓟色 */
    rgba(238, 130, 238, 0.5)   /* 紫罗兰 */
  );
  width: 390px;
  height: 390px;
  animation: wave 9s infinite linear reverse, fill 12s forwards;
  opacity: 0.4;
  filter: drop-shadow(0 0 18px rgba(221, 160, 221, 0.25));
}

.liquid-wave.third-wave {
  background: linear-gradient(45deg,
    rgba(240, 255, 240, 0.3),  /* 蜜瓜色 */
    rgba(245, 255, 250, 0.4),  /* 薄荷奶油 */
    rgba(240, 248, 255, 0.3),  /* 爱丽丝蓝 */
    rgba(248, 248, 255, 0.4)   /* 幽灵白 */
  );
  width: 360px;
  height: 360px;
  animation: wave 11s infinite linear, fill 10s forwards;
  opacity: 0.3;
  filter: drop-shadow(0 0 15px rgba(152, 251, 152, 0.2));
}

.loading-text {
  position: relative;
  color: #333;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.7);
  z-index: 1;
  animation: pulse-text 2.5s infinite ease-in-out;
}

@keyframes pulse-text {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.03);
  }
}

@keyframes wave {
  0% {
    transform: translate(-50%, 0) rotate(0);
  }
  100% {
    transform: translate(-50%, 0) rotate(360deg);
  }
}

@keyframes fill {
  0% {
    top: 150%;
    border-radius: 40%;
  }
  50% {
    top: 60%;
    border-radius: 40%;
  }
  100% {
    top: 50%;
    border-radius: 40%;
  }
}


/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    margin: 0 10px;
    padding: 30px 20px;
  }

  .error-title {
    font-size: 20px;
  }

  .error-message {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
    width: 100%;
  }

  .retry-btn, .close-btn {
    width: 100%;
  }
}
</style>


