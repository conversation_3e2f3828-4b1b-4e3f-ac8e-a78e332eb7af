<template>
  <div class="card-selector">
    <!-- 选择器头部/触发器 -->
    <div class="card-selector-header" :class="{'active': isOpen || value}" @click.stop="toggleDropdown"
         ref="selectorHeader"
    >
      <div class="selector-icon">
        <i :class="icon"></i>
      </div>
      <div class="selector-info">
        <div class="selector-label">{{ label }}</div>
        <div class="selector-value" :class="{'placeholder': !selectedLabel}">
          {{ selectedLabel || placeholder }}
        </div>
      </div>
      <div class="selector-arrow">
        <i :class="isOpen ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </div>
    </div>

    <!-- 下拉卡片选择面板 -->
    <div v-show="isOpen" class="card-dropdown-panel-wrapper" ref="dropdownWrapper" @click.stop>
      <!-- 遮罩层，用于点击外部关闭下拉面板 -->
      <div class="dropdown-backdrop" @click="closeDropdown"></div>

      <div
          ref="dropdownPanel"
          class="card-dropdown-panel"
          :style="dropdownStyle"
          @click.stop=""
      >
        <div class="cards-container">
          <div
              v-for="item in options"
              :key="item.value"
              class="option-card"
              :class="{'selected': value === item.value}"
              @click="selectOption(item)"
          >
            <div class="card-icon">
              <i :class="getItemIcon(item)"></i>
            </div>
            <div class="card-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardSelector',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    label: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    icon: {
      type: String,
      default: 'el-icon-tickets'
    }
  },
  data() {
    return {
      isOpen: false,
      /* 不同类型的默认图标映射 */
      iconMap: {
        /* 场景图标 */
        '通用': 'el-icon-tickets',
        '论文': 'el-icon-notebook-2',
        '论文答辩': 'el-icon-reading',
        '营销策划': 'el-icon-data-analysis',
        '教学教案': 'el-icon-reading',
        '教学课件': 'el-icon-collection',
        '演讲': 'el-icon-microphone',
        '公众演讲': 'el-icon-microphone',
        '工作汇报': 'el-icon-document-checked',
        '工作总结': 'el-icon-document-checked',
        '工作计划': 'el-icon-date',
        '研究报告': 'el-icon-data-analysis',
        '会议材料': 'el-icon-document',
        '团队会议': 'el-icon-user-solid',
        '产品介绍': 'el-icon-goods',
        '公司介绍': 'el-icon-office-building',
        '商业计划书': 'el-icon-document',
        '科普宣传': 'el-icon-reading',
        '培训材料': 'el-icon-notebook-1',
        '述职报告': 'el-icon-document-checked',
        '邮件': 'el-icon-message',
        '危机公关说明': 'el-icon-warning',
        '年度报告': 'el-icon-data-line',
        '通知': 'el-icon-bell',
        '证明': 'el-icon-document',
        '申请': 'el-icon-edit-outline',
        '计划': 'el-icon-date',
        '活动策划': 'el-icon-goblet',
        '项目汇报': 'el-icon-document-checked',
        '解决方案': 'el-icon-success',

        /* 语言图标 */
        '简体中文': 'el-icon-notebook-1',
        '繁體中文': 'el-icon-notebook-2',
        'English': 'el-icon-document',
        '日本语': 'el-icon-collection',

        /* 模型图标 */
        'deepseek-v3': 'el-icon-cpu',
        'deepseek-v3-241226': 'el-icon-s-promotion',
        'ernie-4.5-turbo-32k': 'el-icon-s-operation',
        'qianfan-70b': 'el-icon-s-data',
        'qwen3-235b-a22b': 'el-icon-data-board',
        '默认': 'el-icon-tickets',
        'GPT-3.5': 'el-icon-s-platform',
        'GPT-4': 'el-icon-monitor',
        'Claude': 'el-icon-chat-line-round'
      },
      dropdownStyle: {} /* 存储下拉框的位置样式 */
    }
  },
  computed: {
    selectedLabel() {
      if (!this.value || !this.options.length) return ''
      const selected = this.options.find(item => item.value === this.value)
      return selected ? selected.label : ''
    }
  },
  mounted() {
    // 添加全局点击事件以关闭dropdown
    document.addEventListener('click', this.handleOutsideClick)
  },
  beforeDestroy() {
    // 移除全局点击事件
    document.removeEventListener('click', this.handleOutsideClick)
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen
      if (this.isOpen) {
        this.$nextTick(() => {
          this.updateDropdownPosition()
        })
      }
      this.$emit('dropdown-toggle', this.isOpen)
    },
    closeDropdown() {
      this.isOpen = false
      // 触发自定义事件通知父组件
      this.$emit('dropdown-toggle', false)
    },
    selectOption(item) {
      // 先触发选择事件
      this.$emit('input', item.value) // v-model绑定
      this.$emit('change', item.value, item) // 额外提供change事件和完整item

      // 轻微延迟关闭，改善视觉效果
      setTimeout(() => {
        this.closeDropdown()
      }, 150)
    },
    handleOutsideClick(event) {
      // 判断点击是否在组件外部
      if (this.$el && !this.$el.contains(event.target) && this.isOpen) {
        this.closeDropdown()
      }
    },
    getItemIcon(item) {
      // 首先查找是否有为该特定项指定的图标
      if (item.icon) return item.icon

      // 如果没有，通过label查找预设图标
      return this.iconMap[item.label] || this.icon
    },
    // 优化的定位逻辑，解决各种模式下的位置问题
    updateDropdownPosition() {
      if (!this.$refs.selectorHeader || !this.$refs.dropdownPanel) return

      const headerRect = this.$refs.selectorHeader.getBoundingClientRect()

      // 下拉框尺寸配置
      const dropdownWidth = 480

      // 动态计算高度：根据实际选项数量
      const itemsPerRow = 4
      const maxRows = 3
      const actualRows = Math.min(Math.ceil(this.options.length / itemsPerRow), maxRows)
      const rowHeight = 85
      const padding = 40 // 上下内边距
      const dropdownHeight = (actualRows * rowHeight) + padding + (actualRows - 1) * 12 // 12px是gap

      // 获取文件上传模式的特殊偏移
      const fileOffset = this.getFileUploadOffset()
      const isInFileMode = this.detectFileUploadMode()

      // 水平位置：左对齐按钮，文件模式特殊处理
      let left = headerRect.left + fileOffset.x
      if (left + dropdownWidth > window.innerWidth) {
        left = window.innerWidth - dropdownWidth - 10
      }

      // 垂直位置：增加向上距离，为文件模式特殊处理
      let upwardDistance = isInFileMode ? (35 + fileOffset.y) : 20
      const top = headerRect.top - dropdownHeight - upwardDistance - 300

      // 设置下拉框样式
      this.dropdownStyle = {
        position: 'fixed',
        top: `${Math.max(10, top)}px`,
        left: `${left}px`,
        width: `${dropdownWidth}px`,
        height: `${dropdownHeight}px`,
        zIndex: 99999
      }
    },

    // 检测是否在文件上传模式中
    detectFileUploadMode() {
      let element = this.$el
      while (element && element !== document.body) {
        if (element.classList && (
          element.classList.contains('file-upload-container') ||
          element.classList.contains('file-toolbar-container') ||
          element.classList.contains('file-upload-area') ||
          element.classList.contains('upload-container')
        )) {
          return true
        }
        // 检查父元素的ID或其他特征
        if (element.id && element.id.includes('file')) {
          return true
        }
        element = element.parentElement
      }

      // 额外检查：通过路由或页面状态判断
      if (this.$route && this.$route.query && this.$route.query.mode === 'file') {
        return true
      }

      // 检查父组件是否有文件上传相关的class
      const parentEl = this.$parent && this.$parent.$el
      if (parentEl && parentEl.querySelector && parentEl.querySelector('.file-upload, .upload-area, [class*="file"]')) {
        return true
      }

      return false
    },

    // 获取文件上传模式下的特殊定位偏移
    getFileUploadOffset() {
      const isFileMode = this.detectFileUploadMode()
      if (!isFileMode) return { x: 0, y: 0 }

      // 文件上传模式下的特殊偏移量
      return {
        x: -20, // 向左偏移，更靠近按钮
        y: -220   // 额外向下偏移
      }
    },

  }
}
</script>

<style lang="scss" scoped>
/* 定义主题颜色 */
$primary-color: #13ce66;
$bg-color: #f5f7fa;
$border-color: #e4e7eb;
$text-color: #303133;
$text-secondary: #909399;
$hover-color: rgba(19, 206, 102, 0.05);
$shadow-color: rgba(0, 0, 0, 0.1);

.card-selector {
  position: relative;
  user-select: none;
}

.card-selector-header {
  display: flex;
  align-items: center;
  padding: 8px 12px; /* 调整为与其他组件一致的padding */
  background-color: #fff; /* 调整为与其他组件一致的背景色 */
  border: 1px solid $border-color;
  border-radius: 8px; /* 调整为与其他组件一致的圆角 */
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: none; /* 移除阴影，与其他组件保持一致 */

  &:hover {
    border-color: lighten($primary-color, 10%);
    box-shadow: 0 2px 8px rgba($primary-color, 0.1);
  }
}

.selector-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* 调整图标容器大小 */
  height: 20px;
  margin-right: 8px; /* 调整间距 */
  color: $text-secondary;

  i {
    font-size: 16px; /* 调整图标大小 */
  }
}

.selector-info {
  flex: 1;
  min-width: 0;
}

.selector-label {
  font-size: 14px; /* 调整为与其他组件一致的字体大小 */
  color: #5E6D82; /* 调整为与其他组件一致的颜色 */
  font-weight: 500; /* 添加字体粗细 */
  margin-bottom: 2px;
}

.selector-value {
  font-size: 14px;
  color: #409EFF; /* 调整选中值颜色 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.placeholder {
    color: $text-secondary;
  }
}

.selector-arrow {
  margin-left: 8px; /* 调整间距 */
  color: $text-secondary;
  transition: transform 0.3s;

  i {
    font-size: 14px;
  }
}

.card-selector-header.active {
  .selector-value {
    color: $primary-color;
    font-weight: 500;
  }
}

.card-dropdown-panel-wrapper {
  position: relative;
  z-index: 2200;
}

.card-dropdown-panel {
  position: fixed !important;
  background: white;
  border-radius: 16px;
  border: 1px solid #f0f2f5;
  z-index: 99999 !important;
  backdrop-filter: blur(10px);
  width: 480px;
  /* 高度由JavaScript动态设置 */
  box-shadow: 0 -12px 40px rgba(0, 0, 0, 0.15);
  overflow: visible;
  padding: 0;
  /* 简单的淡入效果 */
  animation: fadeIn 0.15s ease-out;
}

/* 简单的淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}



.cards-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4列布局 */
  grid-auto-rows: minmax(85px, auto); /* 最小85px高度，允许自动增高 */
  gap: 12px;
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto; /* 超过3行时显示滚动条 */
  overflow-x: hidden; /* 禁止横向滚动 */

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;

    &:hover {
      background-color: #c0c4cc;
    }
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
    border-radius: 3px;
  }
}

.option-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 6px; /* 调整内边距，为文字换行留出空间 */
  background: #f8f9fa;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  min-height: 85px; /* 最小高度，允许根据内容增高 */
  box-sizing: border-box;
  border: 1px solid transparent;

  &:hover {
    background: #f0e6ff;
    border-color: rgba($primary-color, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background: rgba($primary-color, 0.1);
    border: 1px solid $primary-color;
    color: $primary-color;
    box-shadow: 0 2px 8px rgba($primary-color, 0.2);

    .card-icon {
      color: $primary-color;
    }

    .card-label {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

.card-icon {
  margin-bottom: 8px; /* 标准化间距 */
  color: $text-secondary;
  height: 32px; /* 固定图标容器高度 */
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-size: 22px; /* 标准化图标大小 */
  }
}

.card-label {
  font-size: 11px; /* 稍微减小字体，为换行留出空间 */
  color: $text-color;
  text-align: center;
  line-height: 1.3;
  font-weight: 500;
  min-height: 24px; /* 最小高度，允许增高 */
  max-height: 48px; /* 最大高度，限制为2行 */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  word-wrap: break-word; /* 允许单词内换行 */
  word-break: break-all; /* 允许在任意字符间换行 */
  white-space: normal; /* 允许换行 */
  padding: 0 4px;
  hyphens: auto; /* 自动断字 */
}

.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1999;
  background-color: transparent;
}

/* 动画效果 */
.zoom-in-top-enter-active,
.zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
  opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.zoom-in-top-enter,
.zoom-in-top-leave-to {
  opacity: 0;
  transform: scaleY(0);
}


</style>
