<template>
  <div class="file-upload">
    <el-upload
      class="upload-container"
      :action="uploadAction"
      :auto-upload="false"
      :show-file-list="true"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :before-upload="beforeUpload"
      :file-list="fileList"
      :multiple="false"
      drag>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <div class="el-upload__tip" slot="tip">
        支持的文件类型: {{ allowedFileTypes.join(', ') }}，
        文件大小不超过 {{ formatFileSize(maxFileSize) }}
      </div>
    </el-upload>

    <div v-if="currentFile" class="upload-info">
      <div class="file-info">
        <span>{{ currentFile.name }}</span>
        <span>{{ formatFileSize(currentFile.size) }}</span>
      </div>
      <el-progress
        v-if="uploadProgress > 0"
        :percentage="uploadProgress"
        :status="uploadStatus">
      </el-progress>
    </div>

    <div class="upload-actions" v-if="currentFile">
      <el-button
        type="primary"
        size="small"
        :loading="uploading"
        @click="startUpload">
        开始上传
      </el-button>
      <el-button
        size="small"
        @click="cancelUpload"
        v-if="uploading">
        取消上传
      </el-button>
    </div>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import aipptApi from '@/api/intellectSmartPpt/aipptv3';

export default {
  name: 'FileUpload',

  props: {
    // 允许的文件类型
    allowedTypes: {
      type: Array,
      default: () => ['.txt', '.doc', '.docx', '.pdf', '.pptx']
    },
    // 最大文件大小（字节）
    maxSize: {
      type: Number,
      default: 50 * 1024 * 1024
    },
    // 上传类型（template/file）
    uploadType: {
      type: String,
      default: 'file'
    }
  },

  data() {
    return {
      fileList: [],
      currentFile: null,
      uploadProgress: 0,
      uploading: false,
      uploadStatus: '',
      cancelTokenSource: null
    }
  },

  computed: {
    uploadAction() {
      return '#'; // 使用自定义上传，不需要action
    },

    allowedFileTypes() {
      return this.allowedTypes.map(type => type.toUpperCase());
    },

    maxFileSize() {
      return this.maxSize;
    }
  },

  methods: {
    handleFileChange(file) {
      this.currentFile = file;
      this.uploadProgress = 0;
      this.uploadStatus = '';
    },

    handleFileRemove() {
      this.currentFile = null;
      this.uploadProgress = 0;
      this.uploadStatus = '';
      this.cancelUpload();
    },

    beforeUpload(file) {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        Message.error(`文件大小不能超过${this.formatFileSize(this.maxFileSize)}`);
        return false;
      }

      // 检查文件类型
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      if (!this.allowedTypes.includes(fileExtension)) {
        Message.error(`只支持以下文件类型: ${this.allowedFileTypes.join(', ')}`);
        return false;
      }

      return true;
    },

    async startUpload() {
      if (!this.currentFile || this.uploading) return;

      try {
        this.uploading = true;
        this.uploadStatus = '';

        const formData = new FormData();
        formData.append('file', this.currentFile.raw);

        if (this.uploadType === 'template') {
          formData.append('type', 4); // 模板类型
        }

        const uploadMethod = this.uploadType === 'template'
          ? aipptApi.uploadTemplate
          : aipptApi.uploadFile;

        const res = await uploadMethod(formData, (progressEvent) => {
          this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        });

        if (res.code === 200) {
          this.uploadStatus = 'success';
          Message.success('上传成功');
          this.$emit('upload-success', res.data);
        }
      } catch (error) {
        console.error('上传失败:', error);
        this.uploadStatus = 'exception';
        Message.error(error.message || '上传失败');
        this.$emit('upload-error', error);
      } finally {
        this.uploading = false;
      }
    },

    cancelUpload() {
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel('Upload canceled by user');
        this.cancelTokenSource = null;
      }
      this.uploading = false;
      this.uploadProgress = 0;
      this.uploadStatus = '';
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload {
  .upload-container {
    width: 100%;

    ::v-deep .el-upload {
      width: 100%;
    }

    ::v-deep .el-upload-dragger {
      width: 100%;
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .el-icon-upload {
        font-size: 48px;
        color: #409EFF;
        margin-bottom: 10px;
      }

      .el-upload__text {
        font-size: 14px;
        color: #606266;

        em {
          color: #409EFF;
          font-style: normal;
        }
      }
    }

    .el-upload__tip {
      font-size: 12px;
      color: #909399;
      margin-top: 10px;
    }
  }

  .upload-info {
    margin-top: 20px;

    .file-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      font-size: 14px;
      color: #606266;
    }
  }

  .upload-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
  }
}
</style>
