/*
 * 选择器组件共享样式
 * 用于SceneThemeSelector, LanguageSelector, ModelSelector组件
 */

// 为各组件提供统一基础样式的混合
@mixin selector-base {
  margin-bottom: 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: visible;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

// 统一的头部样式
@mixin selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background: rgba(240, 242, 245, 0.8);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &:hover {
    background: rgba(230, 232, 235, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// 统一的标题样式
@mixin selector-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

// 统一的卡片容器样式 - 支持智能定位
@mixin cards-container {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  gap: 10px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  position: fixed; // 改为fixed定位以支持智能位置计算
  z-index: 3000; // 提高层级确保显示在最上层
  max-height: 320px;
  overflow-y: auto;
  // 移除静态的top、left、right定位，改为动态计算

  // 确保容器在动态背景上的可见性
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    z-index: -1;
  }
}

// 统一的卡片样式
@mixin selector-card {
  width: calc(50% - 10px);
  border-radius: 8px;
  border: 1px solid rgba(224, 224, 224, 0.6);
  padding: 14px 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: relative;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.4);
    background: rgba(255, 255, 255, 0.95);
  }

  @media (max-width: 768px) {
    width: calc(50% - 10px);
    padding: 12px 8px;
  }

  @media (max-width: 480px) {
    width: 100%;
  }
}

// 统一的卡片选中样式
@mixin card-selected {
  position: relative;
  border: 2px solid transparent;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.6);

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 6px;
    border: 2px solid #409EFF;
    animation: pulse 1.5s infinite;
    pointer-events: none;
  }
}

// 统一的卡片内容样式
@mixin card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

// 统一的图标样式
@mixin card-icon {
  font-size: 26px;
  margin-bottom: 10px;
  color: #667eea;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

// 统一的标签样式
@mixin card-label {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  word-break: break-word;
  hyphens: auto;
  line-height: 1.4;
  width: 100%;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

// 折叠动画
@mixin collapse-animation {
  .collapse-enter-active, .collapse-leave-active {
    transition: all 0.2s;
    opacity: 1;
    transform-origin: top;
    transform: scaleY(1);
  }

  .collapse-enter, .collapse-leave-to {
    opacity: 0;
    transform-origin: top;
    transform: scaleY(0);
  }
}

// 呼吸灯动画
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

// 添加当前选中值样式
@mixin selected-value {
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  margin-left: 10px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

// 标题区域包含标题和选中值
@mixin title-container {
  display: flex;
  align-items: center;
  overflow: hidden;
}
