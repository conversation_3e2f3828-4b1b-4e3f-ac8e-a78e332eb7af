  <template>
  <div
    class="template-selector"
  >
    <!-- 左侧预览区域 -->
    <div class="preview-section">
      <div class="main-preview" v-if="selectedTemplate">
        <!-- 主预览图 - 系统模板显示pageCoverUrls[0]，自定义模板显示coverUrl -->
        <div
          class="main-preview-image"
          :style="{
            backgroundImage: getMainPreviewImage(),
          }"
        >
          <div class="preview-overlay">
            <h3 class="template-title">{{ getTemplateTitle(selectedTemplate) }}</h3>
            <div class="template-meta">
              <span><i class="el-icon-document"></i> {{ selectedTemplate.num }}页</span>
              <!-- 根据模板类型显示不同信息 -->
              <template v-if="selectedTemplate.type === 1">
                <!-- 系统模板：显示分类和风格 -->
                <span v-if="selectedTemplate.category"><i class="el-icon-folder"></i> {{ selectedTemplate.category }}</span>
                <span v-if="selectedTemplate.style"><i class="el-icon-brush"></i> {{ selectedTemplate.style }}</span>
              </template>
              <template v-else-if="selectedTemplate.type === 4">
                <!-- 自定义模板：只显示"自定义"标识 -->
                <span><i class="el-icon-folder"></i> 自定义</span>
              </template>
            </div>
          </div>
        </div>

        <!-- 缩略图预览 - 使用 pageCoverUrls[1-3] -->
        <div class="thumbnail-preview" v-if="selectedTemplate.pageCoverUrls && selectedTemplate.pageCoverUrls.length > 1">
          <div
            v-for="(url, index) in selectedTemplate.pageCoverUrls.slice(1, 4)"
            :key="index"
            class="thumbnail-item"
            :style="{ backgroundImage: `url(${url}?token=${token})` }"
          ></div>
        </div>
      </div>

      <!-- 未选择模板时的占位符 -->
      <div v-else class="preview-placeholder">
        <i class="el-icon-picture-outline"></i>
        <p>请选择一个模板查看预览</p>
      </div>
    </div>

    <!-- 右侧选择区域 -->
    <div class="selection-section">
      <!-- 筛选器 -->
      <div class="filters-section">
        <div class="filter-content">
          <!-- 模板类型选择和刷新按钮 -->
          <div class="template-type-row">
            <el-radio-group v-model="selectedTemplateType" size="medium" @change="handleTemplateTypeChange" class="modern-radio-group">
              <el-radio-button :label="1" class="modern-radio-button">系统模板</el-radio-button>
              <el-radio-button :label="4" class="modern-radio-button">自定义模板</el-radio-button>
            </el-radio-group>
            <div class="refresh-button" @click="handleRefreshTemplates">
              <i class="el-icon-refresh"></i>
              <span>换一换</span>
            </div>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-row">
            <!-- 场景筛选 -->
            <div class="filter-group" v-if="availableCategories.length > 0">
              <label class="filter-label">
                <i class="el-icon-collection-tag"></i>
                场景分类
              </label>
              <div class="custom-select-wrapper">
                <el-select
                  v-model="selectedCategory"
                  placeholder="全部场景"
                  clearable
                  size="medium"
                  @change="handleCategoryChange"
                  class="modern-select enhanced-select enhanced-select-input"
                  popper-class="custom-select-dropdown"
                >
                  <el-option
                    label="全部场景"
                    value=""
                    class="select-option-all"
                  >
                    <span class="option-content">
                      <i class="el-icon-menu"></i>
                      <span class="option-text">全部场景</span>
                      <span class="option-count">({{ templates.length }})</span>
                    </span>
                  </el-option>
                  <el-option
                    v-for="category in availableCategories"
                    :key="category"
                    :label="category"
                    :value="category"
                    class="select-option-item"
                  >
                    <span class="option-content">
                      <i class="el-icon-folder-opened"></i>
                      <span class="option-text">{{ category }}</span>
                      <span class="option-count">({{ getCategoryCount(category) }})</span>
                    </span>
                  </el-option>
                </el-select>
              </div>
            </div>

            <!-- 风格筛选 -->
            <div class="filter-group" v-if="availableStyles.length > 0">
              <label class="filter-label">
                <i class="el-icon-brush"></i>
                设计风格
              </label>
              <div class="custom-select-wrapper">
                <el-select
                  v-model="selectedStyle"
                  placeholder="全部风格"
                  clearable
                  size="medium"
                  @change="handleStyleChange"
                  class="modern-select enhanced-select enhanced-select-input"
                  popper-class="custom-select-dropdown"
                >
                  <el-option
                    label="全部风格"
                    value=""
                    class="select-option-all"
                  >
                    <span class="option-content">
                      <i class="el-icon-picture-outline"></i>
                      <span class="option-text">全部风格</span>
                      <span class="option-count">({{ templates.length }})</span>
                    </span>
                  </el-option>
                  <el-option
                    v-for="style in availableStyles"
                    :key="style"
                    :label="style"
                    :value="style"
                    class="select-option-item"
                  >
                    <span class="option-content">
                      <i class="el-icon-magic-stick"></i>
                      <span class="option-text">{{ style }}</span>
                      <span class="option-count">({{ getStyleCount(style) }})</span>
                    </span>
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="search-section">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索模板标题、分类或风格..."
              prefix-icon="el-icon-search"
              clearable
              size="medium"
              @input="handleSearch"
              class="modern-search enhanced-search-input"
            ></el-input>
          </div>
        </div>
      </div>

      <!-- 模板网格 -->
      <div class="templates-grid" v-loading="loading">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="template-card"
          :class="{ 'selected': selectedTemplateId === template.id }"
          @click="selectTemplate(template)"
        >
          <div class="template-card-cover">
            <el-image
              :src="template.coverUrl + '?token=' + token"
              fit="cover"
              :preview-src-list="[template.coverUrl + '?token=' + token]"
            >
              <div slot="placeholder" class="image-slot">
                <el-skeleton :rows="1" animated class="skeleton-full-container">
                  <template slot="template">
                    <el-skeleton-item variant="image" style="width: 100%; height: 100%; display: block;" />
                  </template>
                </el-skeleton>
              </div>
              <div slot="error" class="image-slot">
                <el-skeleton :rows="1" animated class="skeleton-full-container">
                  <template slot="template">
                    <el-skeleton-item variant="image" style="width: 100%; height: 100%; display: block;" />
                  </template>
                </el-skeleton>
              </div>
            </el-image>

            <!-- 主题色标识 -->
            <div
              class="theme-color-indicator"
              v-if="template.themeColor"
              :style="{ backgroundColor: template.themeColor }"
            ></div>
          </div>

          <div class="template-card-info">
            <h3>{{ getTemplateTitle(template) }}</h3>
            <div class="template-meta">
              <span><i class="el-icon-document"></i> {{ template.num }}页</span>
              <!-- 根据模板类型显示不同信息 -->
              <template v-if="template.type === 1">
                <!-- 系统模板：显示分类和风格 -->
                <span v-if="template.category"><i class="el-icon-folder"></i> {{ template.category }}</span>
                <span v-if="template.style"><i class="el-icon-brush"></i> {{ template.style }}</span>
              </template>
              <template v-else-if="template.type === 4">
                <span><i class="el-icon-folder"></i> 自定义</span>
              </template>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && filteredTemplates.length === 0" class="empty-state">
          <i class="el-icon-document-remove"></i>
          <p>暂无符合条件的模板</p>
          <el-button type="text" @click="clearFilters">清除筛选条件</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplateSelector',
  props: {
    // 模板列表
    templates: {
      type: Array,
      default: () => []
    },
    // 当前选中的模板ID
    selectedTemplateId: {
      type: String,
      default: ''
    },
    // API Token
    token: {
      type: String,
      required: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 初始分类筛选
    category: {
      type: String,
      default: ''
    },
    // 初始风格筛选
    styleFilter: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 当前选择的模板类型 (1: 系统模板, 4: 自定义模板)
      selectedTemplateType: 1,
      // 筛选条件
      selectedCategory: this.category,
      selectedStyle: this.styleFilter,
      searchKeyword: ''
    }
  },
  computed: {
    // 当前选中的模板对象
    selectedTemplate() {
      return this.templates.find(t => t.id === this.selectedTemplateId) || null
    },

    // 背景图片URL - 根据模板类型选择背景图片
    backgroundImageUrl() {
      if (!this.selectedTemplate) return ''

      // 系统模板(type=1)：优先使用pageCoverUrls[0]
      if (this.selectedTemplate.type === 1) {
        if (this.selectedTemplate.pageCoverUrls && this.selectedTemplate.pageCoverUrls.length > 0) {
          return `url(${this.selectedTemplate.pageCoverUrls[0]}?token=${this.token})`
        } else if (this.selectedTemplate.coverUrl) {
          return `url(${this.selectedTemplate.coverUrl}?token=${this.token})`
        }
      }
      // 自定义模板(type=4)：使用coverUrl
      else if (this.selectedTemplate.type === 4) {
        if (this.selectedTemplate.coverUrl) {
          return `url(${this.selectedTemplate.coverUrl}?token=${this.token})`
        }
      }

      return ''
    },

    // 可用的分类选项
    availableCategories() {
      const categories = [...new Set(this.templates.map(t => t.category).filter(Boolean))]
      return categories.sort()
    },

    // 可用的风格选项
    availableStyles() {
      const styles = [...new Set(this.templates.map(t => t.style).filter(Boolean))]
      return styles.sort()
    },

    // 筛选后的模板列表
    filteredTemplates() {
      let filtered = this.templates

      // 按分类筛选
      if (this.selectedCategory) {
        filtered = filtered.filter(t => t.category === this.selectedCategory)
      }

      // 按风格筛选
      if (this.selectedStyle) {
        filtered = filtered.filter(t => t.style === this.selectedStyle)
      }

      // 按关键词搜索
      if (this.searchKeyword && this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase().trim()
        filtered = filtered.filter(t => {
          // 使用统一的标题获取方法
          const title = this.getTemplateTitle(t).toLowerCase()
          const category = (t.category || '').toString().toLowerCase()
          const style = (t.style || '').toString().toLowerCase()

          // 安全地进行搜索匹配
          return (
            title.includes(keyword) ||
            category.includes(keyword) ||
            style.includes(keyword)
          )
        })
      }

      return filtered
    }
  },
  methods: {
    // 获取主预览图片URL
    getMainPreviewImage() {
      if (!this.selectedTemplate) return ''

      // 系统模板(type=1)：优先使用pageCoverUrls[0]
      if (this.selectedTemplate.type === 1) {
        if (this.selectedTemplate.pageCoverUrls && this.selectedTemplate.pageCoverUrls.length > 0) {
          return `url(${this.selectedTemplate.pageCoverUrls[0]}?token=${this.token})`
        } else if (this.selectedTemplate.coverUrl) {
          return `url(${this.selectedTemplate.coverUrl}?token=${this.token})`
        }
      }
      // 自定义模板(type=4)：使用coverUrl
      else if (this.selectedTemplate.type === 4) {
        if (this.selectedTemplate.coverUrl) {
          return `url(${this.selectedTemplate.coverUrl}?token=${this.token})`
        }
      }

      return ''
    },

    // 选择模板
    selectTemplate(template) {
      this.$emit('template-select', template)
    },

    // 处理模板类型切换
    handleTemplateTypeChange() {
      this.$emit('template-type-change', this.selectedTemplateType)
    },

    // 刷新模板列表
    handleRefreshTemplates() {
      this.$emit('refresh-templates')
    },

    // 处理分类筛选
    handleCategoryChange() {
      this.$emit('category-change', this.selectedCategory)
    },

    // 处理风格筛选
    handleStyleChange() {
      this.$emit('style-change', this.selectedStyle)
    },

    // 处理搜索
    handleSearch() {
      // 搜索是通过计算属性实现的，这里可以添加防抖逻辑
      this.$emit('search', this.searchKeyword)
    },

    // 清除筛选条件
    clearFilters() {
      this.selectedCategory = ''
      this.selectedStyle = ''
      this.searchKeyword = ''
      this.$emit('clear-filters')
    },

    // 获取分类下的模板数量
    getCategoryCount(category) {
      return this.templates.filter(t => t.category === category).length
    },

    // 获取风格下的模板数量
    getStyleCount(style) {
      return this.templates.filter(t => t.style === style).length
    },

    // 获取模板标题 - 统一处理系统模板和自定义模板的标题显示
    getTemplateTitle(template) {
      if (!template) return '未命名模板'

      // 系统模板优先使用subject，自定义模板优先使用name
      if (template.type === 1) {
        // 系统模板
        return template.subject || template.name || '未命名模板'
      } else if (template.type === 4) {
        // 自定义模板
        return template.name || template.subject || '未命名模板'
      }

      // 兜底处理
      return template.subject || template.name || '未命名模板'
    },

    // 调整颜色亮度
    adjustColor(color, percent) {
      if (!color) return '#667eea'

      // 移除 # 号
      const hex = color.replace('#', '')

      // 转换为 RGB
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)

      // 调整亮度
      const newR = Math.max(0, Math.min(255, r + (r * percent / 100)))
      const newG = Math.max(0, Math.min(255, g + (g * percent / 100)))
      const newB = Math.max(0, Math.min(255, b + (b * percent / 100)))

      // 转换回十六进制
      return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`
    }
  },
  watch: {
    // 监听外部传入的筛选条件变化
    category(newVal) {
      this.selectedCategory = newVal
    },
    styleFilter(newVal) {
      this.selectedStyle = newVal
    },
    // 监听模板列表变化，自动选择第一个模板
    templates: {
      handler(newTemplates) {
        // 当模板列表有数据且当前没有选中任何模板时，自动选择第一个
        if (newTemplates && newTemplates.length > 0 && !this.selectedTemplateId) {
          const firstTemplate = newTemplates[0]
          console.log('TemplateSelector: 自动选择第一个模板', firstTemplate.id)
          this.$emit('template-select', firstTemplate)
        }
      },
      immediate: true // 立即执行一次
    },
    // 监听筛选后的模板列表变化，确保选中的模板在当前筛选结果中
    filteredTemplates: {
      handler(newFilteredTemplates, oldFilteredTemplates) {
        // 只有在筛选结果真正发生变化时才处理
        if (newFilteredTemplates && newFilteredTemplates.length > 0 && this.selectedTemplateId) {
          const currentTemplateInFiltered = newFilteredTemplates.find(t => t.id === this.selectedTemplateId)
          // 如果当前选中的模板不在新的筛选结果中，则选择筛选结果的第一个
          if (!currentTemplateInFiltered) {
            console.log('TemplateSelector: 当前选中模板不在筛选结果中，自动选择筛选结果的第一个')
            this.$emit('template-select', newFilteredTemplates[0])
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.template-selector {
  display: flex;
  height: 100%;
  width: 100%;

  overflow: hidden;
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}


// 左侧预览区域
.preview-section {
  width: 55%;
  backdrop-filter: blur(5px);
  // border-right: 1px solid rgba(228, 231, 237, 0.6); // 消除左右分割线
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  border-radius: 16px 0 0 16px;

  // 大屏幕下调整宽度比例
  @media (min-width: 1600px) {
    width: 60%; // 增加预览区域宽度
  }
  .main-preview {
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .main-preview-image {
    flex: 1;
    border-radius: 16px;
    background-size: contain; // 改为contain确保完整显示内容
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    min-height: 320px;
    max-height: 70vh; // 限制最大高度
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    // 移除固定的视口单位尺寸，使用弹性布局
    aspect-ratio: 16/9; // 保持合适的宽高比

    &:hover {
      transform: scale(1.02); // 减小缩放避免布局问题
      box-shadow: 0 16px 40px rgba(0, 0, 0, 0.18);
      border-color: rgba(102, 126, 234, 0.6);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 0.8;
    }
  }

  .preview-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: clamp(16px, 3vw, 28px); // 响应式内边距
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    z-index: 2;
    backdrop-filter: blur(2px);

    .template-title {
      font-size: clamp(16px, 2.5vw, 22px); // 响应式字体大小
      font-weight: 700;
      margin: 0 0 clamp(8px, 1.5vw, 16px) 0;
      text-shadow: 0 2px 8px rgba(0,0,0,0.5);
      line-height: 1.3;
      background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      // 确保文字不会被裁剪
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .template-meta {
      display: flex;
      flex-wrap: wrap;
      gap: clamp(8px, 1.5vw, 16px); // 响应式间距
      font-size: clamp(12px, 1.2vw, 14px); // 响应式字体大小
      opacity: 0.95;

      span {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: clamp(4px, 0.8vw, 6px) clamp(8px, 1.2vw, 12px); // 响应式内边距
        background: rgba(255, 255, 255, 0.15);
        border-radius: 20px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        // 确保文字不会被裁剪
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;

        &:hover {
          background: rgba(255, 255, 255, 0.25);
          transform: translateY(-1px);
        }

        i {
          font-size: clamp(14px, 1.4vw, 16px); // 响应式图标大小
          opacity: 0.9;
          flex-shrink: 0; // 防止图标被压缩
        }
      }
    }
  }

  .thumbnail-preview {
    display: flex;
    gap: 12px;
    height: 100px;
    margin-top: 16px;
  }

  .thumbnail-item {
    flex: 1;
    border-radius: 12px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(102, 126, 234, 0.15);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: #667eea;
      transform: scale(1.05) translateY(-3px);
      box-shadow: 0 12px 24px rgba(102, 126, 234, 0.25);

      &::before {
        opacity: 1;
      }
    }
  }

  .preview-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.05) 0%,
      rgba(118, 75, 162, 0.05) 100%
    );
    border-radius: 16px;
    margin: 24px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(102, 126, 234, 0.4);
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.08) 0%,
        rgba(118, 75, 162, 0.08) 100%
      );
    }

    i {
      font-size: 72px;
      margin-bottom: 20px;
      opacity: 0.4;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 18px;
      margin: 0;
      font-weight: 500;
      opacity: 0.7;
    }
  }
}

// 右侧选择区域
.selection-section {
  width: 45%;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(5px);
  position: relative;
  z-index: 2;
  border-radius: 0 16px 16px 0;

  // 大屏幕下调整宽度比例
  @media (min-width: 1600px) {
    width: 40%; // 减少选择区域宽度，给预览区域更多空间
  }
}

// 筛选器区域
.filters-section {
  padding: 20px;
  // border-bottom: 1px solid rgba(228, 231, 237, 0.6); // 消除筛选器底部分割线
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px 16px 0 0;

  .filter-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  // 模板类型选择和刷新按钮同行布局
  .template-type-row {
    display: flex;
    align-items: center;
    gap: 16px;

    .modern-radio-group {
      flex: 1;

      ::v-deep .el-radio-button {
        flex: 1;

        .el-radio-button__inner {
          width: 100%;
          border-radius: 8px;
          border: 2px solid #e4e7ed;
          background: #fff;
          color: #606266;
          font-weight: 500;
          padding: 10px 16px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.15);
          }
        }

        &.is-active .el-radio-button__inner {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-color: #667eea;
          color: white;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        &:first-child .el-radio-button__inner {
          margin-right: 6px;
        }

        &:last-child .el-radio-button__inner {
          margin-left: 6px;
        }
      }
    }

    .refresh-button {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      i {
        font-size: 16px;
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: rotate(180deg);
      }
    }
  }

  .filter-row {
    display: flex;
    gap: 16px;

    .filter-group {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      background: rgba(255, 255, 255, 0.08);
      padding: 16px;
      border-radius: 12px;
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.12);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .filter-label {
        font-size: 14px;
        color: #303133;
        font-weight: 600;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 6px;
        letter-spacing: 0.5px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        padding: 6px 12px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);

        &:hover {
          background: rgba(255, 255, 255, 0.95);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        i {
          font-size: 14px;
          color: var(--theme-color, #667eea);
          opacity: 0.9;
          transition: all 0.3s ease;
        }

        &:hover i {
          opacity: 1;
          transform: scale(1.1);
        }
      }

      .custom-select-wrapper {
        position: relative;
      }

      .enhanced-select {
        width: 100%;
      }

      .modern-select {
        ::v-deep .el-input__inner {
          border-radius: 8px;
          border: 2px solid #e4e7ed;
          background: #fff;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #409eff;
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.15);
          }

          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
    }
  }

  .search-section {
    background: rgba(255, 255, 255, 0.08);
    padding: 16px;
    border-radius: 12px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .modern-search {
      ::v-deep .el-input__inner {
        border-radius: 12px;
        border: 2px solid rgba(228, 231, 237, 0.8);
        background: rgba(255, 255, 255, 0.95);
        padding: 12px 16px 12px 40px;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        backdrop-filter: blur(10px);
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);

        &:hover {
          border-color: rgba(102, 126, 234, 0.6);
          background: rgba(255, 255, 255, 0.98);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
          transform: translateY(-1px);
        }

        &:focus {
          border-color: #667eea;
          background: rgba(255, 255, 255, 1);
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 6px 16px rgba(102, 126, 234, 0.2);
          transform: translateY(-2px);
        }

        &::placeholder {
          color: rgba(192, 196, 204, 0.8);
          font-style: italic;
          text-shadow: none;
        }
      }

      ::v-deep .el-input__prefix {
        left: 12px;
        color: rgba(144, 147, 153, 0.8);
        transition: all 0.3s ease;
      }

      &:hover ::v-deep .el-input__prefix {
        color: #667eea;
        transform: scale(1.1);
      }
    }
  }


}

// 模板网格
.templates-grid {
  flex: 1;
  padding: 14px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
  align-content: start;
  backdrop-filter: blur(5px);
  border-radius: 0 0 16px 0;

  // 优化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(102, 126, 234, 0.5);
    }
  }
}

// 模板卡片
.template-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(228, 231, 237, 0.6);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 260px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  &:hover {
    border-color: #667eea;
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 12px 24px rgba(102, 126, 234, 0.2);
    background: rgba(255, 255, 255, 0.98);
  }

  &.selected {
    border-color: #667eea;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 0.98);
    transform: translateY(-2px);

    .template-card-cover::after {
      content: '';
      position: absolute;
      top: 12px;
      right: 12px;
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 3;
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
      animation: checkmarkPulse 0.6s ease-out;

      &::before {
        content: '✓';
        color: white;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.template-card-cover {
  height: 160px;
  position: relative;
  overflow: hidden;
  border-radius: 12px 12px 0 0;

  .el-image {
    width: 100%;
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover .el-image {
    transform: scale(1.05);
  }

  .theme-color-indicator {
    position: absolute;
    top: 12px;
    left: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    z-index: 2;
    transition: all 0.3s ease;
  }

  .image-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
    color: #909399;
    font-size: 14px;
  }

  .skeleton-full-container {
    width: 100%;
    height: 100%;
  }

  // 添加遮罩层效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.05) 0%,
      rgba(118, 75, 162, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .template-card:hover &::before {
    opacity: 1;
  }
}

.template-card-info {
  padding: 16px 18px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);

  h3 {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 10px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.3s ease;
  }

  .template-card:hover & h3 {
    color: #667eea;
  }

  .template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 12px;
    font-size: 12px;
    color: #909399;

    span {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      background: rgba(102, 126, 234, 0.08);
      border-radius: 6px;
      transition: all 0.3s ease;

      i {
        font-size: 12px;
        width: 12px;
        color: #667eea;
      }
    }

    .template-card:hover & span {
      background: rgba(102, 126, 234, 0.12);
      color: #667eea;
    }
  }
}

// 空状态
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #909399;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  border: 2px dashed rgba(102, 126, 234, 0.2);
  margin: 20px;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(102, 126, 234, 0.4);
    background: rgba(255, 255, 255, 0.9);
  }

  i {
    font-size: 80px;
    margin-bottom: 20px;
    opacity: 0.4;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    font-size: 18px;
    margin: 0 0 20px 0;
    font-weight: 500;
    opacity: 0.8;
  }

  .el-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
    }
  }
}

// 响应式设计

// 超大屏幕优化 (>1920px)
@media (min-width: 1920px) {
  .preview-section {
    .main-preview {
      padding: 24px;
      gap: 24px;
    }

    .main-preview-image {
      max-height: 80vh; // 增加最大高度
    }
  }

  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    padding: 24px;
  }

  .template-card {
    height: 320px;
  }

  .template-card-cover {
    height: 200px;
  }

  .template-card-info {
    padding: 24px;

    h3 {
      font-size: 18px;
      margin-bottom: 14px;
    }

    .template-meta {
      gap: 12px 16px;

      span {
        font-size: 14px;
        padding: 4px 10px;
      }
    }
  }
}

// 大屏幕优化 (1600px-1920px)
@media (min-width: 1600px) and (max-width: 1919px) {
  @media (min-width: 1600px) {
    .templates-grid {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 20px;
      padding: 20px;
    }

    .template-card {
      height: 300px;
    }

    .template-card-cover {
      height: 180px;
    }

    .template-card-info {
      padding: 20px;

      h3 {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .template-meta {
        gap: 10px 14px;

        span {
          font-size: 13px;
          padding: 3px 8px;
        }
      }
    }

    .filters-section {
      padding: 24px;

      .filter-content {
        gap: 20px;
      }

      .filter-row {
        gap: 20px;
      }
    }
  }

  // 中大屏幕优化 (1400px-1600px)
  @media (min-width: 1400px) and (max-width: 1599px) {
    .templates-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 18px;
      padding: 18px;
    }

    .template-card {
      height: 280px;
    }

    .template-card-cover {
      height: 170px;
    }

    .filters-section {
      padding: 22px;
    }
  }

  @media (max-width: 1200px) {
    .template-selector {
      flex-direction: column;
    }

    .preview-section {
      width: 100%;
      height: 320px;
      border-right: none;
      // border-bottom: 1px solid rgba(228, 231, 237, 0.6); // 消除响应式设计中的分割线

      .main-preview {
        flex-direction: row;
        gap: 20px;
        padding: 20px;
      }

      .main-preview-image {
        width: 65%;
        min-height: auto;
        max-height: 280px; // 限制最大高度
        border-radius: 12px;
        background-size: contain; // 确保内容完整显示
      }

      .thumbnail-preview {
        width: 35%;
        flex-direction: column;
        height: auto;
        gap: 12px;
      }

      .thumbnail-item {
        height: 70px;
        border-radius: 8px;
      }
    }

    .selection-section {
      width: 100%;
      flex: 1;
    }

    .filters-section {
      padding: 16px;

      .template-type-row {
        flex-direction: column;
        gap: 12px;

        .modern-radio-group {
          ::v-deep .el-radio-button .el-radio-button__inner {
            padding: 8px 12px;
            font-size: 14px;
          }
        }

        .refresh-button {
          align-self: stretch;
          justify-content: center;
          padding: 8px 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .templates-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 12px;
      padding: 16px;
    }

    .template-card {
      height: 220px;
      border-radius: 12px;
    }

    .template-card-cover {
      height: 130px;
    }

    .template-card-info {
      padding: 12px 14px;

      h3 {
        font-size: 13px;
        margin-bottom: 8px;
      }

      .template-meta {
        gap: 6px 8px;

        span {
          font-size: 11px;
          padding: 1px 4px;
        }
      }
    }

    .filters-section {
      padding: 12px;

      .filter-content {
        gap: 12px;
      }

      .template-type-row {
        flex-direction: column;
        gap: 10px;

        .modern-radio-group {
          ::v-deep .el-radio-button {
            &:first-child .el-radio-button__inner {
              margin-right: 3px;
            }

            &:last-child .el-radio-button__inner {
              margin-left: 3px;
            }

            .el-radio-button__inner {
              padding: 8px 12px;
              font-size: 13px;
            }
          }
        }

        .refresh-button {
          padding: 8px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }
      }

      .filter-row {
        flex-direction: column;
        gap: 12px;

        .filter-group {
          .modern-select {
            ::v-deep .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
        }
      }

      .search-section .modern-search {
        ::v-deep .el-input__inner {
          padding: 10px 12px 10px 36px;
          font-size: 14px;
        }
      }
    }

    .preview-section {
      height: 240px;

      .main-preview {
        padding: 12px;
        gap: 12px;
      }

      .main-preview-image {
        width: 60%;
        border-radius: 8px;
        background-size: contain; // 确保内容完整显示
        max-height: 200px; // 限制最大高度
      }

      .thumbnail-preview {
        width: 40%;
        gap: 8px;
      }

      .thumbnail-item {
        height: 50px;
        border-radius: 6px;
      }

      .preview-overlay {
        padding: 12px;

        .template-title {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .template-meta {
          font-size: 11px;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
