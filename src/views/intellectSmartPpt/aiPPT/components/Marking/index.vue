<template>
  <div class="marking-container" v-if="visible">
    <!-- 全屏iframe -->
    <iframe
      :src="url"
      class="marking-iframe"
      frameborder="0"
      allowfullscreen>
    </iframe>

    <!-- 遮罩层 - 覆盖iframe顶部不需要的元素 -->
    <div class="iframe-mask-overlay">
      <!-- 左侧遮罩 - 覆盖返回按钮 -->
      <div class="head-mask"></div>
    </div>

    <!-- 自定义关闭按钮 - 浮动在右上角，层级最高 -->
    <div class="custom-close-button" @click="handleClose">
      <i class="el-icon-close"></i>
    </div>
  </div>
</template>




<script>
export default {
  name: 'MarkingViewer',
  props: {
    /**
     * 要在 iframe 中加载的 URL
     * @type {String}
     */
    url: {
      type: String,
      required: true
    },
    /**
     * 控制组件可见性 (为了方便父组件控制，增加一个显式控制)
     */
    visible: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close'], // 声明会触发 'close' 事件



  // {{ AURA-X: Add - 添加生命周期方法处理滚动锁定. Approval: 寸止(ID:marking-scroll-management). }}
  mounted() {
    // 锁定body滚动，防止背景滚动
    document.body.style.overflow = 'hidden';
  },

  beforeDestroy() {
    // 恢复body滚动
    document.body.style.overflow = '';
  },

  methods: {
    /**
     * 处理关闭按钮点击事件
     */
    handleClose() {
      this.$emit('close'); // 触发 close 事件通知父组件关闭
    },


  }
}
</script>

// {{ AURA-X: Modify - 优化标注组件为全屏iframe显示，移除header遮挡. Approval: 寸止(ID:fullscreen-iframe-optimization). }}
<style scoped lang="scss">
.marking-container {
  position: fixed; // 固定定位，覆盖全屏
  top: 0;
  left: 0;
  width: 100vw; // 使用视口单位确保完全覆盖
  height: 100vh; // 使用视口单位确保完全覆盖
  background: white; // 纯白背景，不遮挡iframe内容
  z-index: 3000; // 提高层级，确保在模板创建组件之上
  overflow: hidden; // 防止容器滚动
  box-sizing: border-box;

  // 添加淡入动画
  animation: markingFadeIn 0.3s ease-out;
}

@keyframes markingFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 自定义关闭按钮 - 浮动在右上角，层级最高
.custom-close-button {
  position: absolute;
  top: 18px;
  right: 20px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 240, 240, 0.9) 100%);
  color: #606266;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  z-index: 3003; // 确保在所有元素之上，包括遮罩层
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.8);

  &:hover {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(245, 245, 245, 0.95) 100%);
    color: #f56565; // 红色表示关闭
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
    border-color: rgba(245, 101, 101, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }

  i {
    font-size: 22px;
    font-weight: bold;
  }
}

// 全屏iframe，完全占满容器
.marking-iframe {
  width: 100%; // 占满容器宽度
  height: 109vh; // 占满整个视口高度
  border: none; // 移除 iframe 边框
  display: block; // 确保为块级元素
  box-sizing: border-box;
}

// {{ AURA-X: Modify - 修改遮罩层为两边不透明中间透明的布局. Approval: 寸止(ID:side-mask-transparent-center). }}
.iframe-mask-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px; // 遮罩高度
  z-index: 3001; // 在iframe之上，但在关闭按钮之下
  pointer-events: none; // 允许点击穿透到iframe
  display: flex; // 使用flex布局
}

// 左侧遮罩 - 不透明遮挡
.head-mask {
  position: relative;
  width: 100%;
  height: 50px;
  background: linear-gradient(
    to right,
    #fff 0%,           // 左边完全不透明
    #fff 25%,          // 左侧1/4处仍然不透明
    rgba(255, 255, 255, 0) 40%,  // 开始透明过渡
    rgba(255, 255, 255, 0) 60%,  // 中间完全透明
    rgba(255, 255, 255, 0) 75%,  // 继续透明
    #fff 100%          // 右边完全不透明
  );
  z-index: 3002;
  pointer-events: auto; // 左右两边阻止点击穿透，中间允许穿透
  border: none;
  box-shadow: none;

  // 使用伪元素创建更精确的遮罩区域
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 30%; // 左侧30%不透明
    height: 100%;
    background: #fff;
    pointer-events: auto;
  }

  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 30%; // 右侧30%不透明
    height: 100%;
    background: #fff;
    pointer-events: auto;
  }
}

</style>
