<template>
  <div class="language-selector" v-click-outside="handleClickOutside">
    <div class="language-header" @click="toggleCollapse">
      <div class="title-container">
        <span class="language-title">语言</span>
        <span v-if="selectedLabel" class="selected-value">{{ selectedLabel }}</span>
      </div>
      <i :class="['collapse-icon', isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
    </div>

    <transition name="collapse">
      <div v-show="!isCollapsed" class="language-cards-container">
        <div
          v-for="item in options"
          :key="item.value"
          class="language-card"
          :class="{ 'language-card-selected': value === item.value }"
          @click="handleSelect(item)"
        >
          <el-tooltip :content="item.label" placement="top" :open-delay="300">
            <div class="language-card-content">
              <i :class="getLanguageIcon(item)" class="language-card-icon"></i>
              <span class="language-card-label">{{ item.label }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import clickOutside from '../utils/clickOutside';

export default {
  name: 'LanguageSelector',
  directives: {
    clickOutside
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize);
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isCollapsed: false,
      languageIcons: {
        'default': 'el-icon-document',
        '简体中文': 'el-icon-paperclip',
        '繁體中文': 'el-icon-notebook-2',
        'English': 'el-icon-collection',
        '日本语': 'el-icon-notebook-1',
        '한국어': 'el-icon-collection-tag',
        'Español': 'el-icon-tickets',
        'Français': 'el-icon-tickets',
        'Deutsch': 'el-icon-notebook-1'
      }
    }
  },
  computed: {
    selectedLabel() {
      if (!this.value || !this.options.length) return '';
      const selected = this.options.find(item => item.value === this.value);
      return selected ? selected.label : '';
    }
  },
  methods: {
    handleSelect(item) {
      this.$emit('input', item.value);
      this.$emit('change', item.value);
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    handleClickOutside() {
      if (!this.isCollapsed) {
        this.isCollapsed = true;
      }
    },
    getLanguageIcon(item) {
      // 优先按label匹配图标，如果没有对应的就使用默认图标
      return this.languageIcons[item.label] || this.languageIcons['default'];
    },
    // 智能计算容器位置
    updateContainerPosition() {
      if (!this.$el || !this.$refs.cardsContainer) return;

      const headerRect = this.$el.querySelector('.language-header').getBoundingClientRect();

      // 预估容器高度（基于选项数量）
      const estimatedItemHeight = 100; // 每个选项卡片的估计高度
      const padding = 24; // 容器内边距
      const estimatedContainerHeight = Math.min(
        Math.ceil(this.options.length / 2) * estimatedItemHeight + padding, // 2列布局
        300 // 最大高度限制
      );

      // 计算向下和向上展示的可用空间
      const spaceBelow = window.innerHeight - headerRect.bottom - 10;
      const spaceAbove = headerRect.top - 10;

      // 判断展示方向：优先向下，如果空间不足且向上空间更大则向上
      const showAbove = spaceBelow < estimatedContainerHeight && spaceAbove > spaceBelow;

      let top, maxHeight;
      if (showAbove) {
        // 向上展示
        top = headerRect.top - Math.min(estimatedContainerHeight, spaceAbove);
        maxHeight = spaceAbove;
      } else {
        // 向下展示（默认）
        top = headerRect.bottom + 2;
        maxHeight = spaceBelow;
      }

      // 计算水平位置
      const containerWidth = headerRect.width;
      let left = headerRect.left;

      // 确保不超出屏幕右边界
      if (left + containerWidth > window.innerWidth) {
        left = Math.max(10, window.innerWidth - containerWidth - 10);
      }

      // 设置容器样式
      this.containerStyle = {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        width: `${containerWidth}px`,
        maxHeight: `${maxHeight}px`,
        zIndex: 1000
      };
    },
    // 处理窗口大小变化
    handleResize() {
      if (!this.isCollapsed) {
        // 当窗口大小变化时重新计算容器位置
        this.updateContainerPosition();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "styles/selectorStyles.scss";

.language-selector {
  @include selector-base;
}

.language-header {
  @include selector-header;
}

.title-container {
  @include title-container;
}

.language-title {
  @include selector-title;
}

.selected-value {
  @include selected-value;
}

.collapse-icon {
  color: #909399;
  transition: transform 0.3s;
}

.language-cards-container {
  @include cards-container;
}

.language-card {
  @include selector-card;

  &:hover {
    .language-card-icon {
      transform: scale(1.1);
      color: #667eea;
    }

    .language-card-label {
      color: #667eea;
    }
  }
}

.language-card-selected {
  @include card-selected;

  .language-card-icon {
    color: #667eea;
    transform: scale(1.1);
  }

  .language-card-label {
    color: #667eea;
    font-weight: 600;
  }
}

.language-card-content {
  @include card-content;
}

.language-card-icon {
  @include card-icon;
}

.language-card-label {
  @include card-label;
}

/* 折叠动画 */
@include collapse-animation;
</style>
