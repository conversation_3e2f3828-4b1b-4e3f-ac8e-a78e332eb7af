<template>
  <div
    class="web-search-card"
    :class="{'active': value}"
    @click.stop="toggleValue"
  >
    <div class="card-icon">
      <i class="el-icon-connection"></i>
    </div>
    <div class="card-content">
      <div class="card-title">联网</div>
      <div class="card-status">{{ value ? '已开启' : '已关闭' }}</div>
    </div>
    <div class="toggle-switch">
      <el-switch
        v-model="internalValue"
        active-color="#13ce66"
        inactive-color="#dcdfe6"
        @change="handleChange"
      ></el-switch>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebSearchCard',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      internalValue: this.value
    };
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
    }
  },
  methods: {
    toggleValue() {
      this.internalValue = !this.internalValue;
      this.$emit('input', this.internalValue);
      this.$emit('change', this.internalValue);
    },
    handleChange(val) {
      this.$emit('input', val);
      this.$emit('change', val);
    }
  }
}
</script>

<style lang="scss" scoped>
// 定义主题颜色
$primary-color: #409EFF;
$success-color: #13ce66;
$bg-color: #f5f7fa;
$border-color: #e4e7eb;
$text-color: #303133;
$text-secondary: #909399;
$hover-color: #ecf5ff;

.web-search-card {
  display: flex;
  align-items: center;
  padding: 8px 12px; // 调整为与其他组件一致的padding
  background-color: #fff; // 调整为与其他组件一致的背景色
  border: 1px solid $border-color;
  border-radius: 8px; // 调整为与其他组件一致的圆角
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: none; // 移除阴影，与其他组件保持一致
  position: relative;
  z-index: 10; /* 确保不被其他元素遮挡 */

  &:hover {
    border-color: $success-color;
    box-shadow: 0 2px 8px rgba($success-color, 0.1);
  }

  &.active {
    background-color: rgba($success-color, 0.05);
    border-color: $success-color;

    .card-icon {
      color: $success-color;
    }

    .card-title, .card-status {
      color: $success-color;
    }
  }
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; // 调整图标容器大小
  height: 20px;
  margin-right: 8px; // 调整间距
  color: $text-secondary;

  i {
    font-size: 16px; // 调整图标大小
  }
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 14px; // 保持与其他组件一致的字体大小
  color: #5E6D82; // 调整为与其他组件一致的颜色
  font-weight: 500; // 添加字体粗细
  margin-bottom: 2px;
}

.card-status {
  font-size: 12px;
  color: $text-secondary;
}

.toggle-switch {
  margin-left: 10px; // 调整间距
}
</style>
