<template>
  <div class="template-creation-container">
    <!-- 关闭按钮 -->
    <el-button icon="el-icon-close" class="close-button" title="关闭" @click="handleClose" circle></el-button>

    <!-- 左侧上传和指南 -->
    <div class="left-panel">
      <div class="upload-section">
        <div class="upload-header">
          <h2>上传文件</h2>
          <div class="example-links">
            下载示例文件：
            <el-link type="primary" href="/ppt/aippt/template/template.pptx" download="格式模板.pptx">格式模板</el-link>
            <el-link type="primary" href="/ppt/aippt/template/template1.pptx" download="示例1.pptx">示例1</el-link>
            <el-link type="primary" href="/ppt/aippt/template/template2.pptx" download="示例2.pptx">示例2</el-link>
          </div>
        </div>
        <el-upload
          class="template-uploader"
          drag
          action="#"
          :limit="1"
          :file-list="fileList"
          :auto-upload="false"
          accept=".pptx"
          :on-change="handleFileChange"
          :on-remove="handleRemove"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">拖动文件或<em>点击上传模板文件 (.pptx)</em>，文件大小限制50M</div>
          <div class="el-upload__tip">您上传的文件仅用于制作模板，不会用做其他用途</div>
        </el-upload>
      </div>

      <div class="guide-section">
        <div class="guide-header">
          <h2>上传指南</h2>
          <el-link type="primary" @click="showSizeHelp" class="help-link-header">如何修改页面尺寸?</el-link>
        </div>
        <p class="guide-text">
          PPT模板比例需为16:9，页面尺寸固定为 960px * 540px (33.867cm * 19.05cm)，包含以下页面：
        </p>
        <div class="page-previews">
          <div class="page-preview-item">
            <div class="preview-image-placeholder">首页/尾页</div>
            <span>首页/尾页</span>
          </div>
          <div class="page-preview-item">
            <div class="preview-image-placeholder">章节/目录页</div>
            <span>章节/目录页</span>
          </div>
          <div class="page-preview-item">
            <div class="preview-image-placeholder">内容页</div>
            <span>内容页</span>
          </div>
        </div>
      </div>

      <el-button
        type="primary"
        class="parse-button"
        :disabled="fileList.length === 0 || isParsing"
        :loading="isParsing"
        @click="handleParseTemplate"
      >
        {{ isParsing ? '正在解析...' : '立即开始解析' }}
      </el-button>
    </div>

    <!-- 右侧预览 -->
    <div class="right-panel">

      <div class="preview-content-wrapper" v-if="parsedTemplateId">
        <el-image
          :src="parsedCoverUrl"
          fit="contain"
          class="cover-preview-image"
        >
          <div slot="placeholder" class="image-slot loading-placeholder">
            <i class="el-icon-loading"></i>
            <span>加载中...</span>
          </div>
          <div slot="error" class="image-slot error-placeholder">
            <i class="el-icon-picture-outline"></i>
            <span>加载失败</span>
          </div>
        </el-image>

        <!-- 操作按钮组 -->
        <div class="action-buttons">
<!--          <el-button-->
<!--            size="small"-->
<!--            icon="el-icon-download"-->
<!--            class="action-btn download-btn"-->
<!--            @click="handleDownloadTemplate"-->
<!--          >-->
<!--            下载模板-->
<!--          </el-button>-->
          <el-button
            size="small"
            icon="el-icon-edit"
            class="action-btn edit-btn"
            @click="handleEditTemplate(parsedTemplateId)"
          >
            编辑模板
          </el-button>
          <el-button
            size="small"
            icon="el-icon-s-flag"
            class="action-btn mark-btn"
            @click="handleOpenMarking(parsedTemplateId)"
          >
            模板标注
          </el-button>
        </div>
      </div>
      <template v-else>
        <div class="preview-placeholder" ref="skeletonContainer">
          <el-skeleton :rows="skeletonRows" animated />
        </div>
      </template>

    </div>

    <!-- 标注视图 -->
    <marking
        v-if="showMarkingView"
        :url="markingUrl"
        :visible="showMarkingView"
        @close="showMarkingView = false">
    </marking>
  </div>
</template>

<script>
import {aiPptUploadTemplate} from "@/api/intellectSmartPpt/intellectSmartPpt";
import Marking from "@/views/intellectSmartPpt/aiPPT/components/Marking/index.vue";

export default {
  name: 'aIPptTemplateCreation',
  components: {Marking},
  props: {
    apiKey: {
      type: String,
      required: true,
    },
    pptToken: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      fileList: [],
      selectedFile: null, // Store the raw file object
      isParsing: false, // Add parsing state
      parsedCoverUrl: null, // Add state for parsed cover URL
      parsedFileUrl: null, // 1. Add state for parsed file URL
      parsedTemplateId: null, // Add state for parsed template ID

      // --- 标注视图相关 ---
      /** 控制标注视图的显示 */
      showMarkingView: false,
      /** 传递给标注视图的 URL */
      markingUrl: '',

      // --- 骨架屏相关 ---
      /** 动态计算的骨架屏行数 */
      skeletonRows: 10, // 默认值
    };
  },
  computed: { // Add computed property for the correction URL
    correctionUrl() {
      if (this.parsedTemplateId && this.pptToken) {
        return `https://docmee.cn/marker/${this.parsedTemplateId}?token=${this.pptToken}`;
      }
      return '#'; // Return a dummy href if data is missing
    }
  },
  mounted() {
    // 组件挂载后计算骨架屏行数
    this.calculateSkeletonRows();
    // 监听窗口大小变化
    window.addEventListener('resize', this.calculateSkeletonRows);

    // {{ AURA-X: Add - 锁定body滚动，防止背景滚动条出现. Approval: 寸止(ID:scroll-lock). }}
    // 锁定body滚动
    document.body.style.overflow = 'hidden';
  },
  updated() {
    // 组件更新后重新计算，确保在DOM变化后能正确计算
    if (!this.parsedTemplateId) {
      this.calculateSkeletonRows();
    }
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.calculateSkeletonRows);

    // {{ AURA-X: Add - 恢复body滚动. Approval: 寸止(ID:scroll-unlock). }}
    // 恢复body滚动
    document.body.style.overflow = '';
  },
  methods: {
    handleClose() {
      this.$emit('close'); // Emit close event
    },
    handleFileChange(file, fileList) {
      // Validate file type and size
      const isPPTX = file.name.toLowerCase().endsWith('.pptx');
      const isLt50M = file.size / 1024 / 1024 < 50;

      if (!isPPTX) {
        this.$message.error('上传模板文件只能是 .pptx 格式!');
        // Remove the invalid file
        this.fileList = fileList.filter(f => f.uid !== file.uid);
        return false;
      }
      if (!isLt50M) {
        this.$message.error('上传模板文件大小不能超过 50MB!');
        // Remove the invalid file
        this.fileList = fileList.filter(f => f.uid !== file.uid);
        return false;
      }

      // Keep only the latest file due to limit: 1
      this.fileList = [fileList[fileList.length - 1]];
      this.selectedFile = this.fileList[0].raw; // Store the raw file
      console.log("File selected:", this.selectedFile);
      this.isParsing = false; // Reset parsing state if a new file is selected
      this.parsedCoverUrl = null; // Clear previous cover on new file selection
      this.parsedFileUrl = null; // Clear file URL on new selection
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      this.selectedFile = null;
      this.isParsing = false; // Reset parsing state on removal
      this.parsedCoverUrl = null; // Clear cover on removal
      this.parsedFileUrl = null; // Clear file URL on removal
      console.log("File removed");
    },
    async handleParseTemplate() {
      if (!this.selectedFile || this.isParsing) return;

      this.isParsing = true;
      this.parsedCoverUrl = null;
      this.parsedFileUrl = null; // Clear previous file URL before parsing
      console.log("Starting template parsing for:", this.selectedFile.name);

      const formData = new FormData();
      formData.append('file', this.selectedFile);
      formData.append('type', 4);
      formData.append('pptToken', this.pptToken);

      try {
        const res = await aiPptUploadTemplate(formData, {"apiKey": this.apiKey});
        console.log("Upload response:", res);

        if (res.code === 200 && res.data.error_msg) {
          const errorMsg = res.data?.error_msg || '解析失败';
          this.$notify.error({
            title: '错误',
            message: errorMsg,
            duration: 4000
          });
          return
        }
        if (res.code === 200 && res.data.error) {
          this.$notify.error({title: '错误', message: res.data.error})
        }
        if (res.code === 200 && res.data) { // Check if data exists
          if (res.data.coverUrl) {
            this.parsedCoverUrl = res.data.coverUrl + '?token=' + this.pptToken;
          }
          if (res.data.fileUrl) { // 2. Store fileUrl
            this.parsedFileUrl = res.data.fileUrl;
            this.$notify.success({title: '成功', message: '模板上传解析成功'})
          }
          if (res.data.id) {
            this.parsedTemplateId = res.data.id;
          }
        } else {
          const errorMsg = res.msg || '模板上传解析失败';
          this.$notify.error({title: '错误', message: errorMsg})
        }
      } catch (error) {
        console.error("Template upload error:", error);
        this.$notify.error({title: '错误', message: '模板上传解析请求失败，请检查网络或联系管理员。'})
      } finally {
        this.isParsing = false;
      }
    },
    handleDownloadTemplate() {
      if (!this.parsedFileUrl) {
        this.$notify.error({title: '错误', message: '无法下载：未找到模板文件URL。'})
        return;
      }
      if (!this.pptToken) {
        this.$notify.error({title: '错误', message: '无法下载：缺少必要的Token。'})
        return;
      }

      const downloadUrl = `${this.parsedFileUrl}?token=${this.pptToken}`;
      console.log('Attempting to download:', downloadUrl);

      // Create a temporary link to trigger the download
      const link = document.createElement('a');
      link.href = downloadUrl;

      // Try to extract filename from URL or use a default
      let filename = '模板_' + this.parsedTemplateId + '.pptx'
      try {
        const urlParts = this.parsedFileUrl.split('/');
        const lastPart = urlParts[urlParts.length - 1];
        if (lastPart.includes('.pptx')) {
          filename = lastPart.split('?')[0]; // Remove query params if any
        }
      } catch (e) {
        console.warn("Could not extract filename from URL, using default.")
      }
      link.setAttribute('download', filename);

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    showSizeHelp() {
      this.$alert('请在 PowerPoint/WPS 中调整页面尺寸至 16:9 (960x540 像素)。具体操作请参考 PowerPoint/WPS 帮助文档或在线教程。', '如何修改页面尺寸', {
        confirmButtonText: '确定'
      });
    },


    handleOpenMarking(parsedTemplateId) {
      this.markingUrl = `https://docmee.cn/marker/${parsedTemplateId}?token=${this.pptToken}`;
      this.showMarkingView = true;
    },

    handleEditTemplate(parsedTemplateId) {
      const item ={
        id: parsedTemplateId,
      }
      this.$emit('edit-template', item);
    },

    /**
     * 动态计算骨架屏行数，根据容器高度自适应
     */
    calculateSkeletonRows() {
      this.$nextTick(() => {
        const container = this.$refs.skeletonContainer;
        if (container) {
          const containerHeight = container.clientHeight;
          const containerPadding = 32; // 容器内边距 16px * 2

          // ElementUI 骨架屏每行的实际高度：
          // - 行高: 16px
          // - 底部间距: 8px (除最后一行)
          const rowHeight = 16;
          const rowMargin = 8;

          // 可用高度 = 容器高度 - 内边距
          const availableHeight = containerHeight - containerPadding;

          // 计算能容纳的行数
          // 公式: (可用高度 + 行间距) / (行高 + 行间距)
          const calculatedRows = Math.floor((availableHeight + rowMargin) / (rowHeight + rowMargin));

          // 设置合理的行数范围：最少3行，最多30行
          this.skeletonRows = Math.max(3, Math.min(30, calculatedRows));

          console.log(`容器高度: ${containerHeight}px, 可用高度: ${availableHeight}px, 计算骨架屏行数: ${this.skeletonRows}`);
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
// {{ AURA-X: Modify - 优化容器定位和层级，确保完全覆盖并防止滚动条. Approval: 寸止(ID:container-optimization). }}
.template-creation-container {
  display: flex;
  padding: 20px;
  // {{ AURA-X: Modify - 应用父组件主题背景系统，替换固定背景色. Approval: 寸止(ID:template-theme-integration). }}
  background: var(--theme-gradient, linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%));
  height: 100vh; // 固定视口高度
  width: 100vw; // 固定视口宽度
  gap: 20px;
  position: fixed; // 改为fixed定位
  top: 0;
  left: 0;
  right: 0; // 添加right确保完全覆盖
  bottom: 0; // 添加bottom确保完全覆盖
  z-index: 2500; // 提高层级，确保在其他元素之上
  overflow: hidden; // 防止整页滚动
  box-sizing: border-box; // 确保padding包含在高度内

  // 添加渐变覆盖层效果，与父组件保持一致
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--theme-gradient, linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%));
    opacity: 0.8;
    z-index: -1;
    transition: opacity 0.6s ease-in-out;
  }
}

// {{ AURA-X: Modify - 统一关闭按钮样式，参考父组件现代化设计. Approval: 寸止(ID:close-button-styling). }}
.close-button {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 2501; // 确保在容器之上
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.8) 100%);
  color: #606266;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);

  &:hover {
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 245, 245, 0.9) 100%);
    color: #409EFF;
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }

  // 确保图标居中
  i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}

// {{ AURA-X: Modify - 优化面板样式，增加透明度和现代化效果，与主题背景融合. Approval: 寸止(ID:panel-styling-enhancement). }}
.left-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 30px; // 增加内边距
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  height: 100%; // 使用全部可用高度
  overflow: hidden; // 防止面板溢出
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.right-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  height: 100%; // 使用全部可用高度
  overflow: hidden; // 防止面板溢出
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

h2 {
  font-size: 18px; // 稍微增大标题字体
  color: #303133;
  margin: 0 0 4px 0; // 增加底部间距
  font-weight: 600;
}

.upload-section {
  margin-bottom: 30px; // 增加底部间距
  flex-shrink: 0; // 防止上传区域被压缩
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px; // 增加底部间距

  .example-links {
    font-size: 14px;
    color: #606266;

    .el-link {
      margin-left: 10px;
    }
  }
}

// {{ AURA-X: Modify - 优化上传区域样式，增加现代化效果和渐变边框. Approval: 寸止(ID:upload-area-styling). }}
.template-uploader {
  width: 100%;

  ::v-deep .el-upload {
    width: 100%;
  }

  ::v-deep .el-upload-dragger {
    border: 2px dashed rgba(64, 158, 255, 0.3);
    border-radius: 12px;
    padding: 30px 20px;
    transition: all 0.3s ease;
    width: 100%;
    height: 160px; // 增加高度确保文字完整显示
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(248, 252, 255, 0.6);
    position: relative;
    overflow: hidden;

    // 添加渐变边框效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 12px;
      padding: 2px;
      background: linear-gradient(135deg, rgba(64, 158, 255, 0.4), rgba(123, 35, 211, 0.4));
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: rgba(64, 158, 255, 0.6);
      background: rgba(248, 252, 255, 0.8);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);

      &::before {
        opacity: 1;
      }
    }

    .el-icon-upload {
      font-size: 50px;
      color: #409EFF;
      margin-bottom: 10px;
      transition: all 0.3s ease;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
      text-align: center;
      margin-bottom: 8px;
      word-wrap: break-word;
      white-space: normal;
      font-weight: 500;

      em {
        color: #409eff;
        font-style: normal;
        font-weight: 600;
      }
    }

    .el-upload__tip {
      font-size: 12px;
      color: #909399;
      text-align: center;
      line-height: 1.4;
      word-wrap: break-word;
      white-space: normal;
    }
  }
}

.guide-section {
  flex: 1; // 占用剩余空间
  margin-bottom: 0; // 移除底部间距，让按钮更靠近内容
  overflow-y: auto; // 如果内容过多，允许滚动
  min-height: 0; // 允许flex项目收缩

  .guide-text {
    font-size: 14px; // 增大字体，提高可读性
    color: #606266;
    margin: 0 0 25px 0; // 增加底部间距
    line-height: 1.6;
  }

  .guide-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px; // 增加底部间距
  }

  .help-link-header {
    font-size: 13px; // 稍微增大字体
    flex-shrink: 0;
  }
}

.page-previews {
  display: flex;
  justify-content: space-between; // 改为space-between，更好的分布
  margin-bottom: 20px; // 增加底部间距
  gap: 12px; // 适当调整间距
}

.page-preview-item {
  text-align: center;
  flex: 1; // Allow items to take equal space

  .preview-image-placeholder {
    width: 100%; // Make placeholder responsive
    position: relative; // Needed for pseudo-element positioning
    background-color: #f0f2f5;
    border-radius: 6px; // 增加圆角
    margin-bottom: 12px; // 增加底部间距
    display: flex; // For centering text inside (like '01')
    justify-content: center;
    align-items: center;
    font-size: 13px; // 稍微减小字体
    font-weight: 500; // 调整字重
    color: #a0a4a8; // 调整颜色，更柔和
    transition: background-color 0.2s ease; // 添加过渡效果

    // 添加悬停效果
    &:hover {
      background-color: #e8ecf0;
    }

    // Add pseudo-element for aspect ratio
    &::before {
      content: '';
      display: block;
      padding-top: 50%; // 稍微增加高度比例，更好的视觉效果
    }
  }

  span {
    font-size: 13px;
    color: #606266;
    font-weight: 500; // 增加字重
  }
}



// {{ AURA-X: Modify - 优化解析按钮样式，增加渐变背景和现代化效果. Approval: 寸止(ID:parse-button-styling). }}
.parse-button {
  margin-top: 10px; // 设置固定的上边距，让按钮与内容保持合适距离
  width: 100%;
  height: 50px; // 增加按钮高度
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  color: white;
  flex-shrink: 0; // 防止按钮被压缩
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #C0C4CC 0%, #909399 100%); // 默认禁用状态

  // 添加光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  &:not(.is-disabled) {
    background: linear-gradient(135deg, #409EFF 0%, #5B9BD5 50%, #7B68EE 100%);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 35px rgba(64, 158, 255, 0.4);
      background: linear-gradient(135deg, #5B9BD5 0%, #409EFF 50%, #7B68EE 100%);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }
  }

  &.is-disabled {
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

.preview-placeholder {
  width: 90%; // Adjusted size
  height: 82%;
  background-color: #e9edf1; // Slightly different placeholder color
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  padding: 16px; // 添加内边距
  box-sizing: border-box; // 确保padding包含在尺寸内
  overflow: hidden; // 防止内容溢出

  // 确保骨架屏填充整个容器
  ::v-deep .el-skeleton {
    width: 100%;
    height: 100%;

    .el-skeleton__item {
      margin-bottom: 8px; // 控制行间距

      &:last-child {
        margin-bottom: 0; // 最后一行不需要底部间距
      }
    }
  }
}

.preview-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  justify-content: center; // Center items vertically within the wrapper
}

.cover-preview-image {
  max-width: 85%; // Adjust max-width if needed
  max-height: 75%; // Adjust max-height to leave space for button
  border-radius: 6px; // Add slight rounding
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Add subtle shadow
  margin-bottom: 20px; // Add space below the image
}

// 操作按钮组样式
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
  flex-wrap: wrap; // 小屏幕时允许换行
}

// {{ AURA-X: Modify - 优化操作按钮样式，增加现代化效果和更好的视觉层次. Approval: 寸止(ID:action-buttons-styling). }}
.action-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  min-width: 90px;
  position: relative;
  overflow: hidden;

  // 添加光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  // 悬停效果
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// 下载按钮 - 蓝色主题
.download-btn {
  background: linear-gradient(135deg, #409EFF 0%, #5B9BD5 50%, #66b3ff 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);

  &:hover {
    background: linear-gradient(135deg, #337ab7 0%, #409EFF 50%, #5599ff 100%);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
  }
}

// 编辑按钮 - 绿色主题
.edit-btn {
  background: linear-gradient(135deg, #67C23A 0%, #7ED321 50%, #85ce61 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(103, 194, 58, 0.3);

  &:hover {
    background: linear-gradient(135deg, #529b2e 0%, #67C23A 50%, #6cb33f 100%);
    box-shadow: 0 8px 25px rgba(103, 194, 58, 0.4);
  }
}

// 标注按钮 - 橙色主题
.mark-btn {
  background: linear-gradient(135deg, #E6A23C 0%, #F5A623 50%, #f0c78a 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(230, 162, 60, 0.3);

  &:hover {
    background: linear-gradient(135deg, #b88230 0%, #E6A23C 50%, #d4944a 100%);
    box-shadow: 0 8px 25px rgba(230, 162, 60, 0.4);
  }
}

.image-slot {
  display: flex;
  flex-direction: column; // Stack icon and text vertically
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;

  i {
    font-size: 30px;
    margin-bottom: 10px; // Space between icon and text
  }
}

.loading-placeholder i {
  animation: rotating 2s linear infinite;
}

// 响应式设计
@media (max-width: 1200px) {
  .template-creation-container {
    padding: 15px;
    gap: 15px;
  }

  .left-panel, .right-panel {
    padding: 18px; // 稍微增加padding
  }
}

@media (max-width: 768px) {
  .template-creation-container {
    flex-direction: column;
    padding: 10px;
    gap: 10px;
    height: 100vh;
    overflow-y: auto; // 小屏幕允许滚动
  }

  .left-panel, .right-panel {
    flex: none;
    height: auto;
    padding: 15px;
  }

  .left-panel {
    order: 1;
  }

  .right-panel {
    order: 2;
    min-height: 300px;
  }

  .page-previews {
    flex-direction: column;
    gap: 10px;
  }

  .page-preview-item {
    .preview-image-placeholder::before {
      padding-top: 30%; // 移动端更紧凑
    }
  }

  // 操作按钮在平板上的样式
  .action-buttons {
    gap: 10px;
    margin-top: 18px;
  }

  .action-btn {
    padding: 7px 14px;
    font-size: 12px;
    min-width: 75px;
  }
}

@media (max-width: 480px) {
  .template-creation-container {
    padding: 8px;
  }

  .left-panel, .right-panel {
    padding: 12px;
  }

  h2 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .guide-text {
    font-size: 13px; // 移动端也增大字体
  }

  .parse-button {
    height: 36px;
    font-size: 14px;
  }

  // 操作按钮响应式
  .action-buttons {
    gap: 8px;
    margin-top: 15px;
  }

  .action-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 70px;
  }
}


</style>
