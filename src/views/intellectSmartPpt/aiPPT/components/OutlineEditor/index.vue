<template>
  <div class="outline-editor-container" v-draggable>
    <!-- 修改：顶部操作栏 -->
    <div class="editor-actions-bar">
      <div class="editor-info">
        <span class="drag-tip"><i class="el-icon-info"></i> 拖动节点可在同级内调整顺序</span>
      </div>
      <div class="editor-controls">
        <el-dropdown @command="handleExportCommand" style="margin-right: 15px;">
          <span class="export-icon-wrapper" title="导出大纲">
            <i class="el-icon-download"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="md">导出为 MD</el-dropdown-item>
            <el-dropdown-item command="txt">导出为 TXT</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tooltip content="下一步" placement="top">
          <el-button
            icon="el-icon-arrow-right"
            circle
            size="mini"
            title="下一步"
            @click="handleNextStep"
          ></el-button>
        </el-tooltip>

        <el-tooltip content="关闭编辑器" placement="top">
          <el-button icon="el-icon-close"
                     size="mini"
                     class="close-button"
                     @click="requestCloseEditor"
                     title="关闭编辑器"
                     circle>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <div class="outline-header" @click="startTitleEdit">
      <!-- 主题标题编辑 -->
      <span v-if="!isEditingTitle" class="outline-title-display">
        <span class="outline-title-text">{{ currentTitle }}</span>
        <i class="el-icon-edit title-edit-icon"></i>
      </span>
      <el-input
        v-else
        v-model="currentTitle"
        ref="titleInput"
        placeholder="请输入PPT主题"
        class="outline-title-input"
        maxlength="200"
        @blur="finishTitleEdit"
        @keyup.enter.native="finishTitleEdit"
        @click.native.stop
        show-word-limit
        :rows="1"
      ></el-input>
    </div>
    <div class="outline-tree-container">
      <el-tree
        :data="currentData"
        node-key="id"
        :expand-on-click-node="false"
        draggable
        :allow-drop="allowDrop"
        @node-drop="handleNodeDrop"
        :default-expanded-keys="computedExpandedKeysArray"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        class="outline-tree"
        ref="outlineTree"
      >
        <span :class="['custom-tree-node', 'level-' + data.level + '-node']" slot-scope="{ node, data }">
          <!-- 引用 node.key 或 node.level 消除 Linter 警告 -->
          <span :data-node-key="node.key" style="display:none"></span>
          <!-- 非编辑状态 -->
          <template v-if="editingNodeId !== data.id">
            <span @click.stop="startNodeEdit(data)" class="node-label-display">
              <!-- 修改：将序号和手动标记包裹 -->
              <span class="prefix-container">
                <span v-if="data.level === 2" class="chapter-badge">{{ data.prefix }}</span>
                <span :class="['node-prefix', 'level-' + data.level + '-prefix']" v-if="data.prefix && data.level > 2">{{
                    data.prefix
                  }}</span>
                <!-- 将手动标记移到这里 -->
                <el-tooltip content="手动添加" placement="top" :open-delay="500">
                  <span v-if="data.isManual" class="manual-add-indicator">
                    <i class="el-icon-edit-outline"></i>
                  </span>
                </el-tooltip>
              </span>
              <span class="node-label-text">{{ data.label }}</span>
            </span>
            <!-- 新增：节点操作按钮 -->
            <div class="node-actions" v-if="data.level !== 0 || data.level === 0"> <!-- Level 0 内容节点也显示部分操作 -->
              <el-tooltip content="添加子项" placement="top" :open-delay="500" v-if="canAddChild(data)">
                <i class="el-icon-plus node-action-icon add-icon" @click.stop="addChildNode(node)"></i>
              </el-tooltip>
              <!-- 新增：添加同级项按钮 -->
              <el-tooltip content="添加同级项" placement="top" :open-delay="500">
                <i class="el-icon-s-operation node-action-icon sibling-icon"
                   @click.stop="addSiblingNode(node, data)"></i>
              </el-tooltip>
              <el-tooltip content="删除" placement="top" :open-delay="500">
                <i class="el-icon-delete node-action-icon delete-icon" @click.stop="deleteNode(node, data)"></i>
              </el-tooltip>
            </div>
          </template>
          <!-- 编辑状态: 根据 level 决定使用 input 还是 textarea -->
          <template v-else>
            <el-input
              v-if="data.level !== 0"
              v-model="editingNodeLabel"
              :ref="'nodeInput-' + data.id"
              size="mini"
              class="node-edit-input"
              :maxlength="getNodeMaxLength(data)"
              @blur="finishNodeEdit(data)"
              @keyup.enter.native="finishNodeEdit(data)"
              @click.native.stop
              show-word-limit
              :rows="1"
            ></el-input>
            <el-input
              v-else
              type="textarea"
              v-model="editingNodeLabel"
              :ref="'nodeInput-' + data.id"
              :autosize="{ minRows: 1, maxRows: 10 }"
              class="node-edit-textarea"
              :maxlength="getNodeMaxLength(data)"
              @blur="finishNodeEdit(data)"
              @click.native.stop
              show-word-limit
            ></el-input>
          </template>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>

export default {
  name: 'OutlineEditor',
  props: {
    // 修改 prop 为接收 Markdown 字符串
    markdownContent: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      currentTitle: '',
      currentData: [],
      isEditingTitle: false,
      editingNodeId: null,
      editingNodeLabel: '',
      nodeIdCounter: 0,
      expandedNodeIdsSet: new Set(),
      isInitialMarkdownParse: true,
      pathsToReExpand: null, // 新增：用于存储需要重新展开的节点路径
      isResettingTreeData: false, // 新增：标记是否正在重置树数据，以临时禁用 handleNodeExpand
    };
  },
  computed: {
    computedExpandedKeysArray() {
      const keys = Array.from(this.expandedNodeIdsSet);
      console.log('[Computed] computedExpandedKeysArray returns:', JSON.parse(JSON.stringify(keys)));
      return keys;
    }
  },
  watch: {
    // 监听 markdownContent prop 的变化
    markdownContent: {
      handler(newVal, oldVal) {
        console.log('[Watch markdownContent] Triggered. New length:', newVal?.length, 'Old length:', oldVal?.length);
        console.log('[Watch markdownContent] Current isInitialMarkdownParse at entry:', this.isInitialMarkdownParse);

        if (!this.isEditingTitle && this.editingNodeId === null) {
          // --- 新增：在非首次解析前，捕获当前展开节点的路径 ---
          if (!this.isInitialMarkdownParse && this.currentData && this.currentData.length > 0) {
            this.pathsToReExpand = new Set();
            const currentExpandedOldIds = Array.from(this.expandedNodeIdsSet); // 获取的是旧的ID集合

            const findPathAndStore = (nodesToSearch, targetOldId, currentPathLabels = []) => {
              for (const node of nodesToSearch) {
                const pathWithThisNode = currentPathLabels.concat(node.label);
                if (node.id === targetOldId) {
                  this.pathsToReExpand.add(JSON.stringify(pathWithThisNode));
                  // console.log(`[Watch] Storing path for old ID ${targetOldId}: ${JSON.stringify(pathWithThisNode)}`);
                  return true; // Found
                }
                if (node.children && node.children.length > 0) {
                  if (findPathAndStore(node.children, targetOldId, pathWithThisNode)) {
                    return true; // Found in children
                  }
                }
              }
              return false; // Not found in this branch
            };

            // currentData 此刻包含的是拖拽/删除等操作后，但未重新 parseMarkdown 前的数据模型
            // 其节点 ID 仍然是旧的 ID
            for (const oldId of currentExpandedOldIds) {
              findPathAndStore(this.currentData, oldId);
            }
            console.log('[Watch markdownContent] Captured paths to re-expand based on old IDs:', Array.from(this.pathsToReExpand));
          } else {
            // 对于首次解析或 currentData 为空的情况，确保 pathsToReExpand 为空或null
            this.pathsToReExpand = null;
          }
          // --- 新增结束 ---
          console.log('[Watch markdownContent] Parsing markdown because not editing.');
          this.parseMarkdown(newVal);

          this.$nextTick(() => {
            console.log('[Watch $nextTick AFTER parseMarkdown] expandedNodeIdsSet:', Array.from(this.expandedNodeIdsSet));
            if (this.$refs.outlineTree && this.$refs.outlineTree.store) {
              let currentlyExpandedInTreeAfterParse = [];
              if (this.$refs.outlineTree.store.nodesMap) {
                for (const key in this.$refs.outlineTree.store.nodesMap) {
                  if (this.$refs.outlineTree.store.nodesMap[key].expanded) {
                    currentlyExpandedInTreeAfterParse.push(this.$refs.outlineTree.store.nodesMap[key].data.id);
                  }
                }
              }
              console.log('[Watch $nextTick AFTER parseMarkdown] IDs ACTUALLY expanded in tree store:', currentlyExpandedInTreeAfterParse);
            }
            // 隐藏Level 0节点的展开图标
            this.hideLevel0ExpandIcons();
          });
        } else {
          console.log('[Watch markdownContent] Skipping parse because editor is active.');
        }
      },
      immediate: true,
    }
  },
  mounted() {
    if (this.$refs.outlineTree && this.$refs.outlineTree.$el) {
      this.$refs.outlineTree.$el.addEventListener('mousedown', this.handleTreeMouseDownCapture, true);
    }
    // 隐藏Level 0节点的展开图标
    this.$nextTick(() => {
      this.hideLevel0ExpandIcons();
    });
  },
  beforeDestroy() {
    if (this.$refs.outlineTree && this.$refs.outlineTree.$el) {
      this.$refs.outlineTree.$el.removeEventListener('mousedown', this.handleTreeMouseDownCapture, true);
    }
  },
  methods: {
    // 新增：在 mousedown 捕获阶段阻止事件冒泡的方法
    handleTreeMouseDownCapture(event) {
      event.stopPropagation();
    },

    // --- 新增：Markdown 解析逻辑 ---
    parseMarkdown(markdownString) {
      console.log('[parseMarkdown entry] Current isInitialMarkdownParse:', this.isInitialMarkdownParse); // 新增日志
      // 添加日志，确认收到的 prop 值和类型
      // console.log('OutlineEditor received markdown prop:', markdownString, typeof markdownString);
      // 修改日志格式，更清晰地显示值
      console.log(`[OutlineEditor] Received markdown prop. Type: ${typeof markdownString}, Value: "${markdownString}"`);
      // console.log("OutlineEditor received markdown:", markdownString); // 移除重复日志
      if (!markdownString) {
        this.currentTitle = '';
        this.currentData = [];
        console.log("Markdown empty, resetting data.");
        return;
      }

      const lines = markdownString.trim().split('\n');
      let rootData = []; // 修改变量名以示区分
      let nodeStack = [];
      this.nodeIdCounter = 0;
      let chapterCounter = 0;
      let sectionCounters = {};

      // 单独处理第一行作为标题
      const firstLine = lines[0].trim();
      if (firstLine.startsWith('# ')) {
        this.currentTitle = firstLine.substring(2).trim();
        console.log("Parsed title:", this.currentTitle);
        lines.shift(); // 从待处理行中移除标题行
      } else {
        // 如果第一行不是 # 开头，尝试作为标题，或设置默认值
        this.currentTitle = firstLine || '未命名大纲';
        console.warn("Markdown does not start with H1 (# ), using first line or default as title:", this.currentTitle);
        if (firstLine) lines.shift(); // 如果第一行非空，则移除
      }

      lines.forEach(line => {
        line = line.trim();
        if (!line) return;

        let level = 0;
        let label = line;
        let isContentNode = false;

        // --- 更严格地匹配层级 ---
        if (line.startsWith('## ')) {
          level = 2;
          label = line.substring(3).trim();
        } else if (line.startsWith('### ')) {
          level = 3;
          label = line.substring(4).trim();
        } else if (line.startsWith('#### ')) {
          level = 4;
          label = line.substring(5).trim();
        } else {
          // --- 改进内容识别：假设非 # 开头的行是其栈顶父节点的内容 ---
          if (nodeStack.length > 0) {
            // 检查是否是列表项
            if (line.startsWith('- ') || line.startsWith('* ') || /^\d+\.\s/.test(line)) {
              level = 0; // 标记为内容
              label = line.replace(/^(- |\* |\d+\.\s)/, '').trim(); // 移除标记
              isContentNode = true;
            } else {
              // 如果不是明确的列表项，也暂时视为内容（可能需要根据实际情况调整）
              level = 0;
              label = line; // 保留原始文本
              isContentNode = true;
            }
          } else {
            console.warn("Orphan line (not a heading and no parent in stack):", line);
            return; // 忽略孤立行
          }
        }

        // 如果解析出非内容节点，调整栈
        if (!isContentNode) {
          while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
            nodeStack.pop();
          }
        }

        const parentNode = nodeStack.length > 0 ? nodeStack[nodeStack.length - 1] : null;

        // --- 仅当父节点存在或是 Level 2 时才创建节点 ---
        if (parentNode || level === 2) {
          const newNode = {
            id: ++this.nodeIdCounter,
            label: label,
            level: level,
            prefix: '',
            children: []
          };

          let parentId = parentNode ? parentNode.id : 0;
          if (!sectionCounters[parentId]) sectionCounters[parentId] = 0;
          sectionCounters[parentId]++;
          let currentIndex = sectionCounters[parentId];

          // --- 修改：优先提取 Markdown 中的序号 ---
          let extractedPrefix = '';
          let finalLabel = label;
          const prefixMatch = label.match(/^(\d+(\.\d+)*)\.?\s+/);
          if (prefixMatch && level > 1) { // 只为标题提取
            extractedPrefix = prefixMatch[1]; // 提取序号部分，例如 "1.1"
            finalLabel = label.substring(prefixMatch[0].length).trim(); // 移除序号和空格后的标签
            newNode.prefix = extractedPrefix;
            newNode.label = finalLabel;
            console.log(`Extracted prefix: "${extractedPrefix}", Label: "${finalLabel}"`);
          } else {
            // --- 回退：自动生成序号 ---
            newNode.label = label; // 使用原始或处理后的 label
            if (level === 2) {
              chapterCounter++;
              newNode.prefix = `第 ${chapterCounter} 章`;
            } else if (level === 3 && parentNode && parentNode.prefix) {
              // 尝试从父节点 prefix 推断章节号
              const parentPrefixClean = parentNode.prefix.replace(/^第\s*|\s*章$/g, '').split('.')[0];
              const chapterNum = parentPrefixClean ? parentPrefixClean + '.' : '';
              newNode.prefix = `${chapterNum}${currentIndex}`;
            } else if (level === 4 && parentNode && parentNode.prefix) {
              newNode.prefix = `${parentNode.prefix}.${currentIndex}`;
            } // Level 0 不需要前缀
            console.log(`Generated prefix: "${newNode.prefix}", Label: "${newNode.label}"`);
          }
          // --- 序号处理结束 ---


          if (parentNode) {
            // 如果是内容节点 (level 0)，直接加到父节点的 children
            // 如果是非内容节点，也加到父节点的 children
            parentNode.children.push(newNode);
          } else { // 没有父节点，只能是 Level 2
            rootData.push(newNode);
          }

          // 如果新节点不是内容节点，压入栈中作为后续节点的潜在父节点
          if (!isContentNode) {
            nodeStack.push(newNode);
          }
          // console.log("Added node:", JSON.stringify(newNode, null, 2));
        } else if (isContentNode) {
          console.warn("Content node found without suitable parent:", line);
        }
        // 忽略其他孤立的标题行 (例如 ## 之后直接 ####)

      });
      console.log("Final parsed data:", JSON.stringify(rootData, null, 2));
      this.currentData = rootData;

      if (this.isInitialMarkdownParse) {
        this.expandedNodeIdsSet.clear();
        const initialExpandedIds = this.getExpandableNodeIds(this.currentData);
        initialExpandedIds.forEach(id => this.expandedNodeIdsSet.add(id));
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log("[parseMarkdown] Initial parse, setting expanded nodes:", Array.from(this.expandedNodeIdsSet));
        this.isInitialMarkdownParse = false; // 完成首次解析后，置为 false
      } else {
        // 非首次解析（通常由内部操作如拖拽、删除后，父组件更新prop触发）
        // 修改：根据捕获的路径恢复展开状态
        console.log("[parseMarkdown ELSE branch] Attempting to restore expansion state.");
        this.isResettingTreeData = true; // 设置标志，阻止 handleNodeExpand 响应 el-tree 的自动展开事件
        const previouslyCapturedPaths = this.pathsToReExpand; // 获取之前 watcher 保存的路径
        this.expandedNodeIdsSet.clear(); // 清空，准备用新的ID填充

        if (previouslyCapturedPaths && previouslyCapturedPaths.size > 0 && this.currentData && this.currentData.length > 0) {
          const restoreExpansionByPathRecursive = (nodesToSearch, currentPathLabels = []) => {
            for (const node of nodesToSearch) {
              // currentData 中的 node 现在拥有的是全新的 ID
              const pathWithThisNode = currentPathLabels.concat(node.label);
              if (previouslyCapturedPaths.has(JSON.stringify(pathWithThisNode))) {
                // 如果节点的路径匹配，并且该节点有子节点（可以展开）
                if (node.children && node.children.length > 0) {
                  this.expandedNodeIdsSet.add(node.id); // 添加的是新ID
                  // console.log(`[parseMarkdown ELSE] Restored expansion for new ID ${node.id} using path: ${JSON.stringify(pathWithThisNode)}`);
                }
              }
              if (node.children && node.children.length > 0) {
                restoreExpansionByPathRecursive(node.children, pathWithThisNode);
              }
            }
          };
          restoreExpansionByPathRecursive(this.currentData);
        }
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log("[parseMarkdown ELSE branch] Restored expandedNodeIdsSet based on paths:", Array.from(this.expandedNodeIdsSet));
        this.pathsToReExpand = null; // 清理临时存储的路径

        // 新增：在 parseMarkdown 的 else 分支末尾添加 $nextTick 日志
        this.$nextTick(() => {
          if (this.$refs.outlineTree && this.$refs.outlineTree.store) {
            console.log('[parseMarkdown $nextTick in ELSE] el-tree internal defaultExpandedKeys:', JSON.parse(JSON.stringify(this.$refs.outlineTree.store.defaultExpandedKeys)));
            let currentlyExpandedInTree = [];
            if (this.$refs.outlineTree.store.nodesMap) {
              for (const key in this.$refs.outlineTree.store.nodesMap) {
                if (this.$refs.outlineTree.store.nodesMap[key].expanded) {
                  currentlyExpandedInTree.push(this.$refs.outlineTree.store.nodesMap[key].data.id);
                }
              }
            }
            console.log('[parseMarkdown $nextTick in ELSE] IDs ACTUALLY expanded in tree store:', JSON.parse(JSON.stringify(currentlyExpandedInTree)));
          }
          this.isResettingTreeData = false; // 清除标志
          // 隐藏Level 0节点的展开图标
          this.hideLevel0ExpandIcons();
        });
      }
    },

    // --- 新增：递归获取所有可展开节点的 ID ---
    getExpandableNodeIds(nodes) {
      let ids = [];
      if (!Array.isArray(nodes)) {
        return ids;
      }
      nodes.forEach(node => {
        if (node && node.children && node.children.length > 0) {
          ids.push(node.id); // 添加父节点 ID
          // 递归获取子孙节点中可展开节点的 ID
          const childIds = this.getExpandableNodeIds(node.children);
          ids = ids.concat(childIds);
        }
      });
      // 去重，虽然理论上递归结构不会重复添加，但保险起见
      return [...new Set(ids)];
    },
    // --- 新增结束 ---

    // --- 新增：递归获取所有节点 ID 的辅助函数 ---
    getAllNodeIds(nodes, ids = new Set()) {
      if (!Array.isArray(nodes)) {
        return ids;
      }
      nodes.forEach(node => {
        if (node && node.id) {
          ids.add(node.id);
          if (node.children && node.children.length > 0) {
            this.getAllNodeIds(node.children, ids);
          }
        }
      });
      return ids;
    },
    // --- 新增结束 ---

    // --- 编辑相关方法保持不变 ---
    // 开始编辑节点标签
    startNodeEdit(nodeData) {
      // 先完成可能存在的上一个编辑
      if (this.editingNodeId !== null) {
        this.finishNodeEdit({id: this.editingNodeId}); // 模拟一个nodeData
      }
      if (this.isEditingTitle) {
        this.finishTitleEdit();
      }

      this.editingNodeId = nodeData.id;
      this.editingNodeLabel = nodeData.label;
      this.$nextTick(() => {
        const inputRef = this.$refs['nodeInput-' + nodeData.id];
        if (inputRef) {
          inputRef.focus();
        }
      });
    },

    // 完成编辑节点标签
    finishNodeEdit(nodeData) {
      const idToUpdate = this.editingNodeId;
      if (idToUpdate === null) return;

      // --- 修改：在更新前截断文本 ---
      const nodeToUpdate = this.findNodeById(this.currentData, idToUpdate);
      let finalLabel = this.editingNodeLabel; // 获取当前输入框内容

      if (nodeToUpdate) {
        const maxLength = this.getNodeMaxLength(nodeToUpdate);
        if (finalLabel.length > maxLength) {
          finalLabel = finalLabel.slice(0, maxLength);
          // 可选：给用户一个提示
          this.$notify.warning({
            title: '警告',
            message: `内容已超过最大长度 ${maxLength} 字，已自动截断。`,
            position: 'top-left'
          });
        }
      } else {
        console.warn("未能找到要更新的节点以检查层级: ", idToUpdate);
        // 如果找不到节点，可以根据默认规则截断，或不截断
        // 这里暂时不截断，依赖 input/textarea 的 maxlength
      }

      // 使用可能已截断的 finalLabel 更新
      const success = this.findAndUpdateNodeLabel(this.currentData, idToUpdate, finalLabel);
      // --- 修改结束 ---

      if (!success) {
        console.warn("未能找到要更新的节点: ", idToUpdate);
      }

      this.editingNodeId = null;
      this.editingNodeLabel = '';
      this.regenerateAndUpdate(); // 添加调用
    },

    // 递归查找并更新节点标签
    findAndUpdateNodeLabel(nodes, id, newLabel) {
      for (const node of nodes) {
        if (node.id === id) {
          node.label = newLabel;
          return true;
        }
        if (node.children && node.children.length > 0) {
          if (this.findAndUpdateNodeLabel(node.children, id, newLabel)) {
            return true;
          }
        }
      }
      return false;
    },

    // 开始编辑主题标题
    startTitleEdit() {
      if (this.editingNodeId !== null) {
        this.finishNodeEdit({id: this.editingNodeId});
      }
      this.isEditingTitle = true;
      this.$nextTick(() => {
        this.$refs.titleInput?.focus();
      });
    },

    // 完成编辑主题标题
    finishTitleEdit() {
      if (!this.isEditingTitle) return;
      // --- 新增：截断标题 ---
      if (this.currentTitle.length > 30) {
        this.currentTitle = this.currentTitle.slice(0, 30);
        this.$notify.warning({title: '警告', message: '主题已超过最大长度 30 字，已自动截断。', position: 'top-left'});

      }
      // --- 新增结束 ---
      this.isEditingTitle = false;
      this.regenerateAndUpdate(); // 添加调用
    },

    // --- 新增：获取节点最大长度的辅助方法 ---
    getNodeMaxLength(nodeData) {
      if (!nodeData) return 300; // 默认值或错误处理
      switch (nodeData.level) {
        case 0:
          return 500; // 段落内容
        case 2:
          return 300;  // 一级标题 目录章节
        case 3:
          return 300;  // 二级标题 页面标题
        case 4:
          return 300;  // 三级标题 段落标题
        default:
          return 300; // 其他情况（理论上不应出现可编辑的）
      }
    },
    // --- 新增结束 ---

    // --- 新增：拖拽相关方法 ---
    /**
     * 判断节点是否允许拖拽。
     * 只允许在同层级内排序 (即拖拽到目标节点的前或后，且父节点相同)。
     */
    allowDrop(draggingNode, dropNode, type) {
      // console.log('allowDrop:', draggingNode.data.label, dropNode.data.label, type);
      // 不允许拖到内部，只允许前后；且必须是兄弟节点
      return type !== 'inner' && draggingNode.parent === dropNode.parent;
    },

    /**
     * 节点拖拽成功完成时触发。
     */
    handleNodeDrop(draggingNode, dropNode, dropType, ev) {
      console.log('Node dropped:', draggingNode.data.label, dropType, dropNode.data.label);
      // el-tree 内部会更新 currentData 的顺序
      this.$nextTick(() => {
        console.log("[handleNodeDrop $nextTick] Updating prefixes and regenerating markdown...");
        this.updateAllPrefixes();      // 先更新所有节点的序号
        // this.cleanUpExpandedNodeIds(); // 移除调用
        this.regenerateAndUpdate();  // 再根据更新后的数据生成 Markdown
      });
    },

    /**
     * 隐藏Level 0节点的展开图标
     */
    hideLevel0ExpandIcons() {
      this.$nextTick(() => {
        if (!this.$refs.outlineTree || !this.$refs.outlineTree.$el) return;

        // 查找所有Level 0节点的展开图标并隐藏
        const level0Nodes = this.$refs.outlineTree.$el.querySelectorAll('.level-0-node');
        level0Nodes.forEach(level0Node => {
          // 找到包含这个level-0-node的el-tree-node
          const treeNode = level0Node.closest('.el-tree-node');
          if (treeNode) {
            const expandIcon = treeNode.querySelector('.el-tree-node__expand-icon');
            if (expandIcon) {
              expandIcon.style.display = 'none';
              expandIcon.style.width = '0';
              expandIcon.style.margin = '0';
            }
          }
        });
      });
    },

    /**
     * 检查节点是否可以添加子项
     */
    canAddChild(nodeData) {
      const children = nodeData.children || [];
      if (nodeData.level === 2) {
        // Level 2 最多 8 个 Level 3 子节点
        const level3ChildrenCount = children.filter(c => c && c.level === 3).length;
        return level3ChildrenCount < 8;
      } else if (nodeData.level === 3) {
        // Level 3 最多 6 个 Level 4 子节点
        const level4ChildrenCount = children.filter(c => c && c.level === 4).length;
        return level4ChildrenCount < 6;
      } else if (nodeData.level === 4) { // 新增：Level 4 的判断
        // Level 4 最多 3 个 Level 0 子节点 (内容)
        const level0ChildrenCount = children.filter(c => c && c.level === 0).length;
        return level0ChildrenCount < 3; // 修改：允许最多3个内容节点
      }
      return false; // Level 0 不能添加子项
    },

    /**
     * 添加子节点
     */
    addChildNode(parentNode) { // 接收的是 el-tree 的 node 对象
      if (!this.canAddChild(parentNode.data)) {
        this.$notify.warning({title: '警告', message: '已达到子节点数量上限', position: 'top-left'});
        return;
      }

      const parentData = parentNode.data; // 获取父节点的数据对象
      let newLevel = parentData.level + 1;

      // --- 修改：如果父节点是 Level 4，则新节点强制为 Level 0 ---
      if (parentData.level === 4) {
        newLevel = 0;
      }
      // --- 修改结束 ---

      // --- 新增：预先计算新节点的 Prefix ---
      const siblings = parentData.children || [];
      // 只计算同层级的标题节点数量来确定序号
      const headingSiblingsCount = siblings.filter(s => s && s.level === newLevel).length;
      let newPrefix = '';
      if (newLevel === 2) { // 添加的是一级标题 (章)
        newPrefix = `${headingSiblingsCount + 1}`;
        // 如果需要"第 N 章"格式，可以在这里处理或在 updateAllPrefixes 中统一处理
        // 保持简洁，先用数字
      } else { // 添加的是二级或三级标题
        // 使用父节点的 prefix (如果存在) 作为基础
        const basePrefix = parentData.prefix || '';
        newPrefix = basePrefix ? `${basePrefix}.${headingSiblingsCount + 1}` : `${headingSiblingsCount + 1}`;
      }
      // --- 新增：如果新节点是 Level 0，则强制 prefix 为空 ---
      if (newLevel === 0) {
        newPrefix = '';
      }
      // --- 新增结束 ---
      console.log(`[addChildNode] Calculated prefix for new node: ${newPrefix}`);
      // --- 计算结束 ---
      let newLabel = '新节点'; // 默认标签
      switch (newLevel) {
        case 0:
          newLabel = '新段落内容';
          break;
        case 2:
          newLabel = '新章节';
          break;
        case 3:
          newLabel = '新页面标题';
          break;
        case 4:
          newLabel = '新段落标题';
          break;
      }
      const newNode = {
        id: ++this.nodeIdCounter,
        label: newLabel,
        level: newLevel,
        prefix: newPrefix,
        children: [],
        isManual: true
      };

      this.$refs.outlineTree.append(newNode, parentNode);

      // --- 新增：确保父节点是展开的 ---
      if (!this.expandedNodeIdsSet.has(parentData.id)) {
        this.expandedNodeIdsSet.add(parentData.id);
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log(`[addChildNode] Expanded parent node by adding to Set: ${parentData.id}`);
      }
      // --- 新增结束 ---


      // 延迟后启动新节点的编辑状态 (传递 newNode 这个 data 对象)
      this.$nextTick(() => {
        this.$nextTick(() => { // 再延迟一帧确保节点已渲染
          this.startNodeEdit(newNode);
          this.regenerateAndUpdate();
          // 隐藏Level 0节点的展开图标
          this.hideLevel0ExpandIcons();
        });
      });
    },

    /**
     * 删除节点
     */
    deleteNode(node, nodeData) {
      this.$confirm(`确定要删除节点 "${nodeData.label}" 及其所有子项吗？`, '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // --- 修改：回退到直接修改数据模型 ---
        const parent = node.parent;
        const children = parent.data.children || parent.data; // 兼容顶层节点
        const index = children.findIndex(d => d.id === nodeData.id);
        if (index !== -1) {
          children.splice(index, 1);
          // this.cleanUpExpandedNodeIds(); // 移除调用
          // --- 修改：立即更新序号和 Markdown ---
          this.updateAllPrefixes();
          this.regenerateAndUpdate();
          // --- 修改结束 ---
          this.$notify.success({title: '成功', message: '删除节点成功', position: 'top-left'});
        } else {
          console.error("无法在父节点中找到要删除的节点:", nodeData.label);
          this.$notify.error({title: '错误', message: '删除节点失败', position: 'top-left'});
        }
        // --- 修改结束 ---
      }).catch(() => {
        // 用户取消
      });
    },

    /**
     * 封装重新生成 Markdown 和触发更新的逻辑
     */
    regenerateAndUpdate() {
      this.$nextTick(() => {
        const newMarkdown = this.regenerateMarkdownFromData();
        console.log("[regenerateAndUpdate] New Markdown:", newMarkdown);
        this.$emit('outline-updated', newMarkdown);
      });
    },

    /**
     * 根据当前的 currentData 重新生成 Markdown 字符串，并重新计算序号。
     * (确认此方法已存在且逻辑正确)
     */
    regenerateMarkdownFromData() {
      console.warn("[regenerateMarkdownFromData] Regenerating markdown from currentData..."); // 保留日志
      let markdown = `# ${this.currentTitle}\n\n`;

      const buildMarkdownRecursive = (nodes, parentPrefix = '') => {
        if (!Array.isArray(nodes)) return;

        nodes.forEach(node => {
          if (!node || typeof node !== 'object') return;

          const prefix = node.prefix || ''; // 获取已计算好的 prefix
          const label = node.label || '';

          if (node.level >= 2 && node.level <= 4) {
            markdown += `${'#'.repeat(node.level)} ${prefix} ${label}\n`;
          } else if (node.level === 0 && label) {
            markdown += `\n- ${label}\n`; // 内容节点使用列表项
          }

          // 递归处理所有子节点
          if (node.children && node.children.length > 0) {
            markdown += '\n'; // 在子节点块之前加空行（如果需要的话）
            buildMarkdownRecursive(node.children, prefix);
          }
        });
      };

      buildMarkdownRecursive(this.currentData); // 初始调用，parentPrefix 默认为 ''
      return markdown.replace(/\n{3,}/g, '\n\n').trim();
    },

    // --- 新增：导出大纲方法（处理下拉菜单命令） ---
    handleExportCommand(format) {
      const markdownString = this.regenerateMarkdownFromData();
      if (!markdownString) {
        this.$notify.warning({title: '警告', message: '无法生成大纲内容', position: 'top-left'});
        return;
      }

      let blobContent = markdownString;
      let mimeType = 'text/plain;charset=utf-8'; // 默认为 txt
      let fileExtension = 'txt';

      if (format === 'md') {
        mimeType = 'text/markdown;charset=utf-8';
        fileExtension = 'md';
      } else {
        // 对于 txt，可以选择性地移除 Markdown 标记，这里简单保留
        // 如果需要纯文本，可以在此处理 markdownString
      }

      const blob = new Blob([blobContent], {type: mimeType});
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const filename = `${this.currentTitle || 'outline'}.${fileExtension}`;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 使用 $notify
      this.$notify({
        title: '导出成功',
        message: `大纲已导出为 ${filename}`,
        type: 'success',
        position: 'top-left'
      });
    },

    // --- 新增：请求关闭编辑器方法 ---
    requestCloseEditor() {
      this.$emit('close'); // 触发父组件监听的事件
    },

    // --- 新增：根据 ID 查找节点对象的辅助函数 ---
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    /**
     * 新增：递归更新所有节点的 Prefix 属性
     */
    updateAllPrefixes() {
      console.log("[updateAllPrefixes] Starting prefix update...");
      // 修改：将 recursiveUpdate 定义移到外部，以便访问 this
      const recursiveUpdate = (nodes, parentPrefix = '') => {
        if (!Array.isArray(nodes)) return;

        // 修改：为当前层级的标题节点初始化计数器
        let headingSiblingCounter = 0;

        nodes.forEach(node => {
          // 修改：检查 node 是否有效且为对象
          if (!node || typeof node !== 'object') return;

          let currentPrefixNumber = ''; // 用于计算子节点前缀的纯数字/点分数字
          let displayPrefix = ''; // 用于显示的前缀 (可能包含 "第 N 章")

          if (node.level >= 2 && node.level <= 4) { // 仅为标题节点计算和更新序号
            headingSiblingCounter++; // 修改：在此处递增计数器
            currentPrefixNumber = headingSiblingCounter;

            if (node.level === 2) {
              // Level 2 节点，使用数字格式作为基础 prefix
              displayPrefix = `${currentPrefixNumber}`; // 移除不必要的反斜杠
              // 注意：模板中 chapter-badge 会添加 "第 N 章"，这里只存数字
              // 确认模板逻辑：模板中 level-2 用 chapter-badge 显示 {{ data.prefix }}
              // 模板中 level > 2 用 node-prefix 显示 {{ data.prefix }}
              // 因此，为保持一致，level 2 也直接存数字串
            } else {
              // Level 3 & 4
              // 移除不必要的反斜杠
              displayPrefix = parentPrefix ? `${parentPrefix}.${currentPrefixNumber}` : `${currentPrefixNumber}`;
            }
            // 使用 $set 确保响应性
            this.$set(node, 'prefix', displayPrefix);
            console.log(`[updateAllPrefixes] Updated node ${node.id} (${node.label}) prefix to: ${displayPrefix}`);

            // 递归处理子节点，传递新的 *纯数字* 父前缀
            // 修改：传递 displayPrefix 作为新的 parentPrefix
            if (node.children && node.children.length > 0) {
              recursiveUpdate(node.children, displayPrefix); // 传递刚计算出的前缀
            }
          } else if (node.level === 0) {
            // 内容节点 (Level 0) 没有序号
            this.$set(node, 'prefix', ''); // 确保内容节点没有 prefix
            // 内容节点可能有子节点吗？根据 canAddChild，Level 0 不能加子项，但以防万一
            if (node.children && node.children.length > 0) {
              recursiveUpdate(node.children, ''); // 内容节点的子节点也没有父前缀
            }
          } else {
            // 处理其他可能存在的节点类型 (理论上不应有) 或需要递归的容器
            this.$set(node, 'prefix', ''); // 其他节点确保无 prefix
            if (node.children && node.children.length > 0) {
              recursiveUpdate(node.children, parentPrefix); // 沿用父前缀
            }
          }
        });
      };
      recursiveUpdate(this.currentData); // 从根节点开始更新
      console.log("[updateAllPrefixes] Prefix update finished.");
    },

    // --- 新增：处理节点展开/折叠事件 ---
    handleNodeExpand(data) {
      if (this.isResettingTreeData) {
        console.log('[handleNodeExpand] Skipped adding to expanded set because isResettingTreeData is true for node ID:', data.id);
        return; // 如果正在重置数据，则不处理 el-tree 可能触发的自动展开事件
      }
      if (!this.expandedNodeIdsSet.has(data.id)) {
        this.expandedNodeIdsSet.add(data.id);
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log("[handleNodeExpand] Added to expandedNodeIdsSet:", data.id, "Current Set:", Array.from(this.expandedNodeIdsSet));
        // 触发计算属性更新, Vue 2 可能需要手动 $forceUpdate 或确保 Set 的变更能被追踪
        // 不过，由于 computedExpandedKeysArray 依赖于 expandedNodeIdsSet，其变更应自动触发更新
      }
    },
    handleNodeCollapse(data) {
      if (this.expandedNodeIdsSet.has(data.id)) {
        this.expandedNodeIdsSet.delete(data.id);
        console.log("[handleNodeCollapse] Removed from expandedNodeIdsSet:", data.id);
        // 新增：递归移除所有子孙节点的ID
        this.recursivelyRemoveIdsFromSet(data);
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log("[handleNodeCollapse] Current Set after recursive remove:", Array.from(this.expandedNodeIdsSet));
      }
    },
    // --- 新增结束 ---

    // --- 新增：递归从 expandedNodeIdsSet 中移除节点及其子孙的ID ---
    recursivelyRemoveIdsFromSet(nodeDataInstance) {
      if (!nodeDataInstance || !nodeDataInstance.children || nodeDataInstance.children.length === 0) {
        return;
      }
      nodeDataInstance.children.forEach(child => {
        if (this.expandedNodeIdsSet.has(child.id)) {
          this.expandedNodeIdsSet.delete(child.id);
          // console.log(`[recursivelyRemoveIdsFromSet] Removed child ID: ${child.id}`);
        }
        this.recursivelyRemoveIdsFromSet(child); // 递归处理子孙
      });
    },
    // --- 新增结束 ---

    /**
     * 新增：处理下一步按钮点击
     */
    handleNextStep() {
      // 在触发下一步之前，确保当前编辑已完成
      if (this.editingNodeId !== null) {
        this.finishNodeEdit({id: this.editingNodeId});
      }
      if (this.isEditingTitle) {
        this.finishTitleEdit();
      }
      // 触发事件通知父组件
      this.$emit('request-next-step');
    },

    /**
     * 新增：添加同级节点方法
     */
    addSiblingNode(node, data) {
      const parentNode = node.parent;
      const parentData = parentNode.data;
      const targetLevel = data.level;
      let siblingsContainer;
      let limit = 0;
      let currentCount = 0;

      // 确保任何正在进行的编辑都已完成
      if (this.editingNodeId !== null) {
        this.finishNodeEdit({id: this.editingNodeId});
      }
      if (this.isEditingTitle) {
        this.finishTitleEdit();
      }

      if (targetLevel === 2) { // 添加同级一级标题
        siblingsContainer = this.currentData;
        limit = 100;
        currentCount = siblingsContainer.filter(n => n && n.level === 2).length;
        if (currentCount >= limit) {
          this.$notify.warning({
            title: '警告',
            message: `最多只能添加 ${limit} 个一级标题（章节）。`,
            position: 'top-left'
          });
          return;
        }
      } else { // 添加同级二级、三级标题或内容节点
        if (!parentData || !parentData.children) {
          this.$notify.warning({title: '警告', message: `无法找到父节点来添加同级项`, position: 'top-left'});
          console.error("Cannot find parent to add sibling for node:", data);
          return;
        }
        siblingsContainer = parentData.children;

        if (targetLevel === 3) { // 同级二级标题
          limit = 8;
          currentCount = siblingsContainer.filter(n => n && n.level === 3).length;
          if (currentCount >= limit) {
            this.$notify.warning({
              title: '警告',
              message: `当前章节下最多只能添加 ${limit} 个二级标题。`,
              position: 'top-left'
            });
            return;
          }
        } else if (targetLevel === 4) { // 同级三级标题
          limit = 6;
          currentCount = siblingsContainer.filter(n => n && n.level === 4).length;
          if (currentCount >= limit) {
            this.$notify.warning({
              title: '警告',
              message: `当前二级标题下最多只能添加 ${limit} 个三级标题。`,
              position: 'top-left'
            });
            return;
          }
        } else if (targetLevel === 0) { // 同级内容节点
          limit = 3;
          currentCount = siblingsContainer.filter(n => n && n.level === 0).length;
          if (currentCount >= limit) {
            this.$notify.warning({
              title: '警告',
              message: `当前内容组下最多只能添加 ${limit} 个内容节点。`,
              position: 'top-left'
            });
            return;
          }
        }
      }

      let newLabel = '新同级节点'; // 默认标签
      switch (targetLevel) {
        case 0:
          newLabel = '新段落内容';
          break;
        case 2:
          newLabel = '新章节';
          break;
        case 3:
          newLabel = '新页面标题';
          break;
        case 4:
          newLabel = '新段落标题';
          break;
      }

      const newNode = {
        id: ++this.nodeIdCounter,
        label: newLabel, // 使用新的标签逻辑
        level: targetLevel,
        prefix: '', // 将由 updateAllPrefixes 更新
        children: [],
        isManual: true // 标记为手动添加
      };

      // 找到当前节点在兄弟中的索引，在其后插入
      const currentIndex = siblingsContainer.findIndex(s => s.id === data.id);
      if (currentIndex !== -1) {
        siblingsContainer.splice(currentIndex + 1, 0, newNode);
      } else {
        // 如果由于某种原因找不到当前节点（理论上不应发生），则添加到末尾
        console.warn("Could not find current node in siblings, appending to end for level:", targetLevel);
        siblingsContainer.push(newNode);
      }

      // 确保父节点是展开的（如果父节点存在且不是根currentData）
      if (parentNode && parentNode.data && parentNode.data.id && !this.expandedNodeIdsSet.has(parentNode.data.id)) {
        this.expandedNodeIdsSet.add(parentNode.data.id);
        this.expandedNodeIdsSet = new Set(this.expandedNodeIdsSet); // 增强响应性
        console.log("[addSiblingNode] Ensuring parent expanded by adding to expandedNodeIdsSet:", parentNode.data.id);
      }


      this.$nextTick(() => {
        this.updateAllPrefixes();
        this.regenerateAndUpdate();
        this.$nextTick(() => {
          // const addedNode = this.findNodeById(this.currentData, newNode.id); // 使用现有方法查找
          // if (addedNode) {
          //   this.startNodeEdit(addedNode);
          // }
          // 直接使用 newNode 对象，因为它已经被添加到数据模型中
          this.startNodeEdit(newNode);
          // 隐藏Level 0节点的展开图标
          this.hideLevel0ExpandIcons();
        });
      });
    },
  }
};
</script>

<style scoped lang="scss">
// 主题色变量定义
$theme-color: #409EFF;
$theme-light: #ecf5ff;
$theme-lighter: #f0f9ff;
$cyan-color: #17a2b8;
$cyan-light: #e8f8fa;
$success-color: #67C23A;
$success-light: #f0f9eb;

.outline-editor-container {
  padding: 16px;
  border-radius: 8px;
  background: transparent;
  border: none;
  position: static;
  /* 移除过多的动画效果以防止滚动闪烁 */
}

.outline-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 20px;
  cursor: pointer;
}

.outline-title-display {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(64, 158, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(64, 158, 255, 0.1);
    border-color: rgba(64, 158, 255, 0.2);
  }
}

.outline-title-text {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.title-edit-icon {
  margin-left: 12px;
  color: $theme-color;
  font-size: 18px;
  opacity: 0.7;
  transition: all 0.3s ease;

  .outline-title-display:hover & {
    opacity: 1;
    transform: scale(1.1);
    color: $cyan-color;
  }
}

.outline-title-input {
  font-size: 22px;
  font-weight: 600;
  text-align: center;
  max-width: 70%;

  .el-input__inner {
    font-weight: 600;
    text-align: center;
    border: 2px solid $theme-color;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 249, 255, 0.9) 100%);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    transition: all 0.3s ease;

    &:focus {
      border-color: $cyan-color;
      box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.2);
    }
  }
}

.outline-tree-container {
  overflow-y: auto;
  border-radius: 8px;
  background: transparent;
  padding: 8px;
  border: none;
  /* 修复滚动闪烁问题 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  will-change: scroll-position;
}

.outline-tree {
  background: transparent;
}

// 自定义树节点样式 - 嵌套卡片设计
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 14px;
  width: 100%;
  position: relative;

  // Level 2 - 章节卡片样式（主要容器）
  &.level-2-node {
    background: transparent;
    border: 1px solid rgba(64, 158, 255, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 6px 0;
    box-shadow: 0 1px 4px rgba(64, 158, 255, 0.05);

    .node-prefix {
      font-weight: 600;
      color: $theme-color;
    }

    .node-label-text {
      font-weight: 600;
      font-size: 15px;
      color: #2c3e50;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.03);
      border-color: rgba(64, 158, 255, 0.15);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
    }
  }

  // Level 3 - 页面标题样式（嵌套在章节内）
  &.level-3-node {
    background: transparent;
    border: 1px solid rgba(64, 158, 255, 0.06);
    border-radius: 6px;
    padding: 6px 10px;
    margin: 4px 0;

    .node-prefix {
      color: $theme-color;
      font-weight: 500;
    }

    .node-label-text {
      font-weight: 500;
      color: #34495e;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.02);
      border-color: rgba(64, 158, 255, 0.12);
      box-shadow: 0 1px 4px rgba(64, 158, 255, 0.06);
    }
  }

  // Level 4 - 段落标题样式（嵌套在页面内）
  &.level-4-node {
    border: 1px solid rgba(23, 162, 184, 0.04);
    border-radius: 4px;
    padding: 4px 8px;
    margin: 3px 0;

    .node-prefix {
      color: $cyan-color;
      font-weight: 500;
    }

    .node-label-text {
      color: #5a6c7d;
    }

    &:hover {
      background: rgba(248, 250, 252, 0.8);
      border-color: rgba(23, 162, 184, 0.08);
    }
  }

  // Level 0 - 内容样式（嵌套在段落内）
  &.level-0-node {
    border: 1px solid rgba(206, 212, 218, 0.15);
    border-radius: 3px;
    padding: 3px 6px;
    margin: 2px 0;

    .node-label-display {
      color: #6c757d;
      white-space: pre-wrap;
      line-height: 1.5;
      font-style: italic;
      font-size: 13px;
    }

    &:hover {
      background: rgba(248, 249, 250, 0.6);
      border-color: rgba(173, 181, 189, 0.2);
    }
  }

  &:hover {
    .node-actions {
      opacity: 1;
      visibility: visible;
    }
  }
}

.node-label-display {
  flex: 1;
  cursor: pointer;
  padding: 3px 6px;
  border-radius: 3px;
  transition: all 0.2s ease;
  width: calc(100% - 50px);
  min-height: 22px;
  line-height: 1.4;
  display: flex;
  align-items: center;

  &:hover {
    background: rgba(64, 158, 255, 0.05);
  }

  // 不同层级的显示样式
  .level-2-node & {
    font-weight: 600;
    font-size: 14px;
  }

  .level-3-node & {
    font-weight: 500;
    font-size: 13px;
  }

  .level-4-node & {
    font-size: 13px;
  }

  .level-0-node & {
    font-size: 12px;
    font-style: italic;
    color: #6c757d;
  }
}

.node-edit-input {
  flex: 1;
  font-size: 14px;
  width: calc(100% - 50px);

  ::v-deep .el-input__inner {
    height: auto;
    min-height: 22px;
    line-height: 1.4;
    padding: 3px 6px;
    border: none;
    border-radius: 0;
    background: transparent;
    transition: all 0.2s ease;
    font-size: 15px;
    box-shadow: none;

    &:focus {
      border: 1px solid $theme-color;
      border-radius: 3px;
      background: rgba(64, 158, 255, 0.05);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      outline: none;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.02);
      border-radius: 3px;
    }
  }

  // 不同层级的输入框样式
  .level-2-node & ::v-deep .el-input__inner {
    font-weight: 600;
    font-size: 16px;
  }

  .level-3-node & ::v-deep .el-input__inner {
    font-weight: 500;
    font-size: 15px;
  }

  .level-4-node & ::v-deep .el-input__inner {
    font-size: 15px;
  }

  .level-0-node & ::v-deep .el-input__inner {
    font-size: 14px;
    font-style: italic;
  }
}

.node-edit-textarea {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  width: calc(100% - 50px);

  ::v-deep .el-textarea__inner {
    padding: 3px 6px;
    line-height: 1.4;
    border: none;
    border-radius: 0;
    background: transparent;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 22px;
    font-size: 15px;
    box-shadow: none;

    &:focus {
      border: 1px solid $theme-color;
      border-radius: 3px;
      background: rgba(64, 158, 255, 0.05);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      outline: none;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.02);
      border-radius: 3px;
    }
  }

  // 针对Level 0内容节点增加文本域高度
  .level-0-node & ::v-deep .el-textarea__inner {
    min-height: 80px;
    height: 80px;
    line-height: 1.5;
  }

  // 不同层级的文本域样式
  .level-2-node & ::v-deep .el-textarea__inner {
    font-weight: 600;
    font-size: 16px;
  }

  .level-3-node & ::v-deep .el-textarea__inner {
    font-weight: 500;
    font-size: 15px;
  }

  .level-4-node & ::v-deep .el-textarea__inner {
    font-size: 15px;
  }

  .level-0-node & ::v-deep .el-textarea__inner {
    font-size: 14px;
    font-style: italic;
  }
}

.node-prefix {
  color: $theme-color;
  margin-right: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  .level-2-node & {
    font-weight: 600;
    color: $theme-color;
  }

  .level-3-node & {
    color: $theme-color;
  }

  .level-4-node & {
    color: $cyan-color;
  }
}

.node-label-text {
  transition: all 0.3s ease;

  .level-2-node & {
    font-weight: 600;
    color: #2c3e50;
  }

  .level-3-node & {
    font-weight: 500;
    color: #34495e;
  }

  .level-4-node & {
    color: #5a6c7d;
  }
}

// 节点操作按钮容器样式
.node-actions {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 2px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  background: rgba(64, 158, 255, 0.1);
  padding: 1px 3px;
  border-radius: 3px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

// 节点操作图标样式
.node-action-icon {
  font-size: 12px;
  color: #909399;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 1px;
  border-radius: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: $theme-color;
    background: rgba(64, 158, 255, 0.1);
    transform: scale(1.05);
  }

  &.add-icon:hover {
    color: $success-color;
    background: rgba(103, 194, 58, 0.1);
  }

  &.sibling-icon:hover {
    color: $cyan-color;
    background: rgba(23, 162, 184, 0.1);
  }

  &.delete-icon:hover {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
  }
}



// 章节标记样式 - 现代化渐变设计
.chapter-badge {
  display: inline-block;
  background: linear-gradient(135deg, $theme-color 0%, #6366f1 100%);
  color: white;
  padding: 6px 14px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
  line-height: 1.4;
  vertical-align: middle;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  }
}

// Level 3 和 4 前缀标记样式 - 现代化设计
.level-3-prefix,
.level-4-prefix {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 10px;
  line-height: 1.4;
  vertical-align: middle;
  text-align: center;
  min-width: 32px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.level-3-prefix {
  background: linear-gradient(135deg, $theme-lighter 0%, rgba(64, 158, 255, 0.15) 100%);
  color: $theme-color;
  border: 1px solid rgba(64, 158, 255, 0.2);

  &:hover {
    background: linear-gradient(135deg, $theme-light 0%, rgba(64, 158, 255, 0.2) 100%);
    transform: scale(1.05);
  }
}

.level-4-prefix {
  background: linear-gradient(135deg, $success-light 0%, rgba(103, 194, 58, 0.15) 100%);
  color: $success-color;
  border: 1px solid rgba(103, 194, 58, 0.2);

  &:hover {
    background: linear-gradient(135deg, rgba(240, 249, 235, 1) 0%, rgba(103, 194, 58, 0.2) 100%);
    transform: scale(1.05);
  }
}

// 顶部操作栏样式 - 去除盒子包裹
.editor-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0;
}

.editor-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.editing-status {
  font-size: 16px;
  font-weight: 600;
  color: $theme-color;
}

.drag-tip {
  font-size: 12px;
  color: #8492a6;
  font-style: italic;
  display: inline-flex;
  align-items: center;

  i {
    margin-right: 6px;
    font-size: 14px;
    color: $theme-color;
  }
}

.export-icon-wrapper {
  cursor: pointer;
  padding: 5px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  i.el-icon-download {
    font-size: 18px;
    color: #606266;
    transition: color 0.2s ease;
  }

  &:hover {
    transform: scale(1.1);

    i.el-icon-download {
      color: $theme-color;
    }
  }
}

// 编辑器控件组样式 - 现代化布局
.editor-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

// 关闭按钮样式 - 现代化设计
.close-button {
  background: linear-gradient(135deg, #f56c6c 0%, #ff7875 100%);
  border: 1px solid #f56c6c;
  color: white;
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);
  transition: all 0.3s ease;
  border-radius: 8px;

  &:hover {
    background: linear-gradient(135deg, #f78989 0%, #ff9c9c 100%);
    border-color: #f78989;
    color: #fff;
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
  }

  &:active {
    background: linear-gradient(135deg, #f35454 0%, #f56c6c 100%);
    border-color: #f35454;
    transform: scale(1.02);
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.5);
  }
}

// 下一步按钮样式增强
::v-deep .el-button {
  &.el-button--mini {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
  }
}

// 包裹序号和标记的容器样式
.prefix-container {
  display: inline-block;
  position: relative;
  margin-right: 10px;
  vertical-align: middle;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// Element UI Tree 组件样式覆盖 - 嵌套结构
::v-deep .el-tree {
  background: transparent;

  .el-tree-node {
    &:focus > .el-tree-node__content {
      background-color: transparent;
    }

    // 让子节点真正嵌套在父节点内部
    .el-tree-node__children {
      padding-left: 0;
      margin-left: 12px;
      // 移除缩进线：注释掉 border-left 和 ::before 伪元素
      // border-left: 1px solid rgba(64, 158, 255, 0.08);
      position: relative;

      // 移除渐变缩进线
      // &::before {
      //   content: '';
      //   position: absolute;
      //   left: -1px;
      //   top: 0;
      //   bottom: 0;
      //   width: 1px;
      //   background: linear-gradient(to bottom, rgba(64, 158, 255, 0.15), transparent);
      // }
    }
  }

  .el-tree-node__content {
    background: transparent;
    padding: 0;
    margin: 0;
    height: auto;

    &:hover {
      background: transparent;
    }
  }

  .el-tree-node__expand-icon {
    color: $theme-color;
    font-size: 12px;
    transition: all 0.2s ease;
    margin-right: 6px;

    &.expanded {
      transform: rotate(90deg);
    }

    &:hover {
      color: $cyan-color;
      transform: scale(1.1);
    }

    &.expanded:hover {
      transform: rotate(90deg) scale(1.1);
    }

    // 只隐藏真正的叶子节点的展开箭头
    &.is-leaf {
      visibility: hidden;
      width: 0;
      margin-right: 0;
    }
  }

  // Level 0节点的展开图标通过JavaScript动态隐藏

  .el-tree-node__loading-icon {
    color: $theme-color;
  }
}

// 手动添加标记样式 - 现代化设计
.manual-add-indicator {
  position: absolute;
  top: -6px;
  right: -6px;
  color: #E6A23C;
  font-size: 10px;
  cursor: help;
  background: linear-gradient(135deg, #fef7e6 0%, #fff7e6 100%);
  border: 1px solid #faecd8;
  border-radius: 50%;
  padding: 2px;
  line-height: 1;
  z-index: 2;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(230, 162, 60, 0.3);

  &:hover {
    transform: scale(1.15);
    background: linear-gradient(135deg, #fef0d1 0%, #fef7e6 100%);
    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.4);
  }

  i {
    display: block;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .outline-editor-container {
    padding: 8px;
    margin: 4px;
  }

  .custom-tree-node {
    &.level-2-node {
      padding: 6px 40px 6px 10px;
      margin: 4px 0;
    }

    &.level-3-node {
      padding: 4px 40px 4px 8px;
      margin: 2px 0;
    }

    &.level-4-node {
      padding: 3px 40px 3px 6px;
      margin: 1px 0;
    }

    &.level-0-node {
      padding: 2px 40px 2px 4px;
      margin: 1px 0;
    }
  }

  .node-actions {
    right: 2px;
    gap: 1px;
    padding: 1px 2px;
  }

  .node-action-icon {
    font-size: 11px;
    width: 14px;
    height: 14px;
  }

  .node-edit-input,
  .node-edit-textarea {
    width: calc(100% - 40px);
  }

  .node-label-display {
    width: calc(100% - 40px);
  }
}
</style>
