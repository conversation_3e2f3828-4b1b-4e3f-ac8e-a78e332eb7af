<template>
  <div class="aippt-fullpage-container">
    <div class="container">
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载中...</div>
      </div>

      <!-- 自定义导航按钮 -->
      <div v-if="showNavigation" class="page-navigate">
        <div :class="['nav-item', currentPage === 'creator' ? 'selected' : '']" @click="navigateTo('creator')">生成PPT
        </div>
        <div :class="['nav-item', currentPage === 'dashboard' ? 'selected' : '']" @click="navigateTo('dashboard')">
          PPT列表
        </div>
        <div :class="['nav-item', currentPage === 'customTemplate' ? 'selected' : '']"
             @click="navigateTo('customTemplate')">自定义模板
        </div>
      </div>

      <div id="aippt-iframe-container" ref="aipptContainer" class="aippt-iframe-container"></div>
    </div>

  </div>
</template>

<script>
// 改用本地SDK文件和CDN备选

import Snow from "quill/themes/snow";
import {getId} from "@/api/explorationCenter/experience";
import Cookies from "js-cookie";
import {createApiToken} from "@/api/intellectSmartPpt/intellectSmartPpt";

export default {
  name: 'AI_PPT_V2',
  data() {
    return {
      docmeeUI: null,
      loading: true,
      token: '',
      userId: '',
      creatorParams: null,
      sdkLoaded: false,
      currentPage: 'creator',  // 'creator' 创建页面; 'dashboard' PPT列表; 'customTemplate' 自定义模版; 'editor' 编辑页（需要传pptId字段）
      showNavigation: true,
      sdkScriptId: 'docmee-ui-sdk-script',
      sdkLoadingPromise: null,
      initRetryCount: 0,
      maxRetries: 3,
      isInitializing: false,

      // 高级功能数据
      selectedType: 'subject',
      subjectInput: '',
      textInput: '',
      selectedFile: null,
      dataUrlInput: '',
      selectedOutlineFile: null,
      generating: false
    }
  },
  computed: {
    canGenerate() {
      switch (this.selectedType) {
        case 'subject':
          return this.subjectInput.trim() !== '';
        case 'text':
          return this.textInput.trim() !== '';
        case 'file':
          return this.selectedFile !== null;
        case 'dataUrl':
          return this.dataUrlInput.trim() !== '' && this.dataUrlInput.startsWith('http');
        case 'outline':
          return this.selectedOutlineFile !== null;
        default:
          return false;
      }
    }
  },
  watch: {
    // 如果token发生变化，更新DocmeeUI实例
    token(newToken) {
      if (this.docmeeUI && newToken) {
        this.docmeeUI.updateToken(newToken)
      }
    }
  },
  created() {
    // 在created中获取token，成功后初始化
    this.getToken()
      .then(() => {
        return this.initializeAll();
      })
      .catch(error => {
        console.error('初始化失败:', error);
        this.loading = false;
      });
  },
  mounted() {
    console.log('Ai_PPT_V2组件已挂载');
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      console.log('AI_PPT_V2组件挂载');
    });
  },
  beforeRouteLeave(to, from, next) {
    console.log('AI_PPT_V2组件将被卸载');
    this.destroyDocmeeUI();
    next();
  },
  beforeRouteUpdate(to, from, next) {
    console.log('AI_PPT_V2路由参数更新');
    this.currentPage = 'creator'; // 重置页面
    next();
  },
  beforeDestroy() {
    console.log('AI_PPT_V2组件销毁');
    this.destroyDocmeeUI();

    // 移除相关事件监听器和定时器
    if (window.DocmeeUI) {
      // 这里我们不从全局删除DocmeeUI，因为它可能被其他组件使用
      console.log('保留全局DocmeeUI对象供其他组件使用');
    }
  },
  // 组件将被缓存
  activated() {
    console.log('AI_PPT_V2组件被激活');
    if (window.DocmeeUI && !this.docmeeUI && this.token) {
      this.initDocmeeUI();
    }
  },
  // 组件将被缓存
  deactivated() {
    console.log('AI_PPT_V2组件被停用');
    this.destroyDocmeeUI();
  },

  methods: {
    // 获取token
    getToken() {
      // this.userId = Cookies.get("userId");
      const data = {
        // "uid": this.userId ? this.userId : 'test',
        "uid": "test001",
        "timestamp": Date.now(),
      };

      return new Promise((resolve, reject) => {
        createApiToken(data, null).then(res => {
          if (res.code === 200 && res.data.token) {
            this.token = res.data.token;
            console.log('Token获取成功');
            resolve(this.token);
          } else {
            const error = new Error(res.data.error_msg || '获取token失败');
            this.$notify({
              title: '错误',
              message: res.data.error_msg,
              type: 'error',
              duration: 5000
            });
            reject(error);
          }
        }).catch(error => {
          console.error('获取token失败:', error);
          this.$message.error('获取授权信息失败，请稍后再试');
          reject(error);
        });
      });
    },

    // 统一的初始化流程
    initializeAll() {
      if (this.isInitializing) {
        console.log('初始化正在进行中，请等待...');
        return Promise.resolve();
      }

      this.isInitializing = true;
      console.log('开始初始化流程...');

      return new Promise((resolve, reject) => {
        // 1. 先检查SDK是否已加载
        if (window.DocmeeUI) {
          this.sdkLoaded = true;
          console.log('DocmeeUI SDK已存在');
          this.initDocmeeUI()
            .then(resolve)
            .catch(reject)
            .finally(() => {
              this.isInitializing = false;
            });
        } else {
          // 2. 加载SDK
          this.loadDocmeeSDK()
            .then(() => {
              return this.initDocmeeUI();
            })
            .then(resolve)
            .catch(reject)
            .finally(() => {
              this.isInitializing = false;
            });
        }
      });
    },

    // 加载SDK
    loadDocmeeSDK() {
      if (this.sdkLoadingPromise) {
        return this.sdkLoadingPromise;
      }

      if (window.DocmeeUI) {
        console.log('DocmeeUI SDK已全局加载');
        this.sdkLoaded = true;
        return Promise.resolve();
      }

      this.sdkLoadingPromise = new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/static/docmee-ui-sdk-iframe.min.js';
        script.id = this.sdkScriptId;
        script.async = true;

        const timeoutId = setTimeout(() => {
          reject(new Error('SDK加载超时'));
        }, 10000);

        script.onload = () => {
          clearTimeout(timeoutId);
          if (window.DocmeeUI) {
            console.log('DocmeeUI SDK加载成功');
            this.sdkLoaded = true;
            resolve();
          } else {
            reject(new Error('DocmeeUI对象不存在'));
          }
        };

        script.onerror = (error) => {
          clearTimeout(timeoutId);
          reject(error);
        };

        document.head.appendChild(script);
      }).finally(() => {
        this.sdkLoadingPromise = null;
      });

      return this.sdkLoadingPromise;
    },

    // 初始化DocmeeUI
    initDocmeeUI() {
      if (this.docmeeUI) {
        console.log('DocmeeUI实例已存在');
        return Promise.resolve();
      }

      if (!this.token) {
        return Promise.reject(new Error('Token未获取，无法初始化DocmeeUI'));
      }

      return new Promise((resolve, reject) => {
        if (!this.$refs.aipptContainer) {
          reject(new Error('容器元素未找到'));
          return;
        }

        try {
          console.log('初始化DocmeeUI实例...');
          this.docmeeUI = new window.DocmeeUI({
            token: this.token,
            container: this.$refs.aipptContainer,
            page: this.currentPage,
            lang: 'zh',
            mode: 'light',
            background: '#f5f7fa',
            padding: '15px',
            downloadButton: ['pptx'],
            onMessage: this.handleMessage
          });
          console.log('DocmeeUI实例创建成功');
          resolve();
        } catch (error) {
          console.error('初始化DocmeeUI实例失败:', error);
          reject(error);
        }
      });
    },

    // Token刷新处理
    handleTokenRefresh() {
      this.getToken()
        .then(() => {
          if (this.docmeeUI && this.token) {
            this.docmeeUI.updateToken(this.token);
          } else {
            return this.initializeAll();
          }
        })
        .catch(error => {
          console.error('Token刷新失败:', error);
          this.loading = false;
        });
    },

    // 处理消息
    handleMessage(message) {
      console.log('AI_PPT_V2消息:', message);

      switch (message.type) {
        case 'mounted':
          // iframe加载完成
          this.loading = false;
          console.log('DocmeeUI iframe加载完成');
          break;

        case 'invalid-token':
          // token失效处理
          this.$message.warning('授权已过期，正在重新获取...');
          this.handleTokenRefresh();
          break;

        case 'beforeGenerate':
          return this.handleBeforeGenerate(message.data);

        case 'afterGenerate':
          this.handleAfterGenerate(message.data);
          break;

        case 'error':
          this.handleError(message.data);
          break;

        case 'user-info':
          // 用户信息更新
          break;

        case 'pageChange':
          this.currentPage = message.data.page;
          break;
      }
    },

    // 生成前处理
    handleBeforeGenerate(data) {
      const {subtype, fields} = data;
      console.log(`准备生成${subtype === 'outline' ? 'PPT大纲' : 'PPT'}`, fields);
      return true;
    },

    // 生成后处理
    handleAfterGenerate(data) {
      const {id} = data;
      console.log('PPT生成完成, ID:', id);
      this.$message.success('PPT生成成功');
      this.generating = false;
    },

    // 错误处理
    handleError(data) {
      const {code, message: errorMsg} = data;
      this.generating = false;
      if (code === 88) {
        this.$message.error('您的使用次数已用完');
      } else {
        this.$message.error(`发生错误: ${errorMsg}`);
      }
    },

    // 销毁处理
    destroyDocmeeUI() {
      if (this.docmeeUI) {
        try {
          console.log('销毁DocmeeUI实例');
          this.docmeeUI.destroy();
        } catch (e) {
          console.error('销毁DocmeeUI实例时出错:', e);
        }
        this.docmeeUI = null;
      }
    },

    // 切换页面方法
    navigateTo(page, pptId) {
      if (!this.docmeeUI) return

      const params = {page}
      if (page === 'editor' && pptId) {
        params.pptId = pptId
      }

      this.currentPage = page
      this.docmeeUI.navigate(params)
    },

    // 更改创建内容
    changeCreatorData(data, createNow = false) {
      if (!this.docmeeUI) return

      this.docmeeUI.changeCreatorData(data, createNow)
    },

    // 解析文件数据
    async parseFileData(file) {
      if (!file) return null

      const formData = new FormData()
      formData.append('file', file)

      try {
        const response = await this.$http.post('https://docmee.cn/api/ppt/parseFileData', formData, {
          headers: {
            'token': this.token
          }
        })

        if (response.data.code !== 0) {
          this.$message.error('解析文件异常：' + response.data.message)
          return null
        }

        return response.data.data.dataUrl
      } catch (error) {
        this.$message.error('解析文件网络异常')
        console.error('解析文件失败:', error)
        return null
      }
    },

    // 提取文件大纲
    async extractFileOutline(file) {
      if (!file) return null

      const formData = new FormData()
      formData.append('file', file)

      try {
        const response = await this.$http.post('https://docmee.cn/api/ppt/extractFileOutline?format=text', formData, {
          headers: {
            'token': this.token
          }
        })

        if (response.data.code !== 0) {
          this.$message.error('提取文件大纲异常：' + response.data.message)
          return null
        }

        return response.data.data
      } catch (error) {
        this.$message.error('提取文件大纲网络异常')
        console.error('提取文件大纲失败:', error)
        return null
      }
    },

    // 生成PPT
    async generatePPT() {
      if (!this.docmeeUI || !this.canGenerate) return

      this.generating = true
      this.navigateTo('creator')

      try {
        switch (this.selectedType) {
          case 'subject':
            this.docmeeUI.changeCreatorData({subject: this.subjectInput}, true)
            break

          case 'text':
            this.docmeeUI.changeCreatorData({text: this.textInput}, true)
            break

          case 'file':
            const dataUrl = await this.parseFileData(this.selectedFile)
            if (dataUrl) {
              this.docmeeUI.changeCreatorData({dataUrl}, true)
            } else {
              this.generating = false
            }
            break

          case 'dataUrl':
            if (this.dataUrlInput.startsWith('http')) {
              this.docmeeUI.changeCreatorData({dataUrl: this.dataUrlInput}, true)
            } else {
              this.$message.error('文件链接格式错误')
              this.generating = false
            }
            break

          case 'outline':
            const data = await this.extractFileOutline(this.selectedOutlineFile)
            if (data) {
              this.docmeeUI.changeCreatorData({
                outlineMarkdown: data.outlineText,
                dataUrl: data.dataUrl
              }, true)
            } else {
              this.generating = false
            }
            break

          default:
            this.generating = false
            break
        }
      } catch (error) {
        console.error('生成PPT失败:', error)
        this.$message.error('生成PPT失败，请稍后再试')
        this.generating = false
      }
    },
  },

}
</script>

<
<style lang="scss" scoped>
.aippt-container {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
}

.aippt-iframe-container {
  flex: 1;
  width: 100%;
  height: calc(100vh - 70px); // 减去导航和输入区域的高度
  min-height: 600px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #333;
}

.page-navigate {
  padding: 10px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.nav-item {
  padding: 8px 16px;
  margin-right: 8px;
  cursor: pointer;
  user-select: none;
  border: 2px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    background-color: #f0f0f0;
  }

  &.selected {
    background: #409EFF;
    color: white;
    border-color: #409EFF;
  }
}

.input-div {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.select-type {
  margin-right: 8px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.input-field {
  flex: 1;
  margin: 0 8px;
  min-width: 200px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  &:focus {
    outline: none;
    border-color: #409EFF;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

