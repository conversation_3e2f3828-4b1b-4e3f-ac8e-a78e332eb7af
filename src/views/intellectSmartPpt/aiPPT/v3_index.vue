/**
 * @fileoverview AI PPT生成器主组件
 * @description
 * 该组件实现了基于AI的PPT自动生成功能，包括：
 * 1. 多种生成方式支持（主题、文本、文件等）
 * 2. 实时生成进度展示
 * 3. 模板管理和预览
 * 4. 批量操作功能
 *
 * @requires element-ui 使用Element UI组件库
 * @requires echarts 图表展示
 * @requires lodash-es 工具函数
 * @requires js-cookie Cookie管理
 *
 * <AUTHOR> Name
 * @version 1.0.0
 */

<template>
  <!-- 整体容器 -->
  <div class="ai-ppt-container">
    <!-- 顶部选项卡导航 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <el-tab-pane label="生成PPT" name="generate"></el-tab-pane>
      <el-tab-pane label="PPT列表" name="list"></el-tab-pane>
      <el-tab-pane label="自定义模板" name="template"></el-tab-pane>
    </el-tabs>

    <!-- 生成PPT内容区域 -->
    <div v-if="activeTab === 'generate'" class="generate-container">
      <!-- 欢迎提示语 -->
      <div class="welcome-message">
        Hi，请问您想怎样创作您的 PPT 呢？
      </div>

      <!-- 生成方式选择卡片组 -->
      <div class="generation-methods">
        <el-card
          v-for="method in generationMethods"
          :key="method.type"
          :class="['method-card', {'active': currentMethod === method.type}]"
          @click.native="selectMethod(method.type)"
        >
          <div class="method-content">
            <div class="method-icon">
              <i :class="method.icon"></i>
            </div>
            <div class="method-name">{{ method.name }}</div>
            <div class="method-desc">{{ method.description }}</div>
          </div>
        </el-card>
      </div>

      <!-- 内容展示区域 -->
      <transition name="fade-transform" mode="out-in">
        <!-- AI智能创作模式 -->
        <div v-if="currentMethod === 1" key="ai" class="method-content">
          <div class="content-header">
            <h3>AI智能创作</h3>
            <p class="desc">输入您的主题或要求，AI将为您智能生成PPT</p>
          </div>
          <div class="input-area">
            <!-- 主题/要求输入框 -->
            <el-input
              v-model="formData.content"
              type="textarea"
              placeholder="请输入您想创建的PPT主题或要求(不超过1000字符)"
              :maxlength="1000"
              :rows="4"
              @input="validateContent"
            ></el-input>
            <!-- 操作按钮区 -->
            <div class="action-buttons">
              <el-upload
                class="upload-reference"
                action=""
                :auto-upload="false"
                :on-change="handleFileChange"
                multiple
                :file-list="formData.files"
              >
                <el-button type="text">
                  <i class="el-icon-upload2"></i>
                  上传参考文件
                </el-button>
              </el-upload>
              <el-button
                type="primary"
                @click="handleCreate"
                :disabled="!isContentValid"
              >
                <i class="el-icon-magic-stick"></i>
                立即创作
              </el-button>
            </div>
          </div>

          <!-- 选项设置卡片 -->
          <el-card class="options-card" shadow="hover">
            <div class="options-header">
              <i class="el-icon-setting"></i>
              <span>高级设置</span>
            </div>
            <div class="options-grid">
              <!-- 页数选择 -->
              <div class="option-item">
                <span class="option-label">页数</span>
                <el-select v-model="formData.pageCount" placeholder="选择页数">
                  <el-option label="10-15页" value="10-15"></el-option>
                  <el-option label="20-30页" value="20-30"></el-option>
                  <el-option label="25-35页" value="25-35"></el-option>
                </el-select>
              </div>
              <!-- 演示场景选择 -->
              <div class="option-item">
                <span class="option-label">演示场景</span>
                <el-select v-model="formData.scene" placeholder="选择场景">
                  <el-option label="通用场景" value="general"></el-option>
                </el-select>
              </div>
              <!-- 受众选择 -->
              <div class="option-item">
                <span class="option-label">受众</span>
                <el-select v-model="formData.audience" placeholder="选择受众">
                  <el-option label="大众" value="public"></el-option>
                </el-select>
              </div>
              <!-- 智能搜索开关 -->
              <div class="option-item">
                <span class="option-label">智能搜索</span>
                <el-switch v-model="formData.smartSearch"></el-switch>
              </div>
              <!-- 语言选择 -->
              <div class="option-item">
                <span class="option-label">语言</span>
                <el-select v-model="formData.language" placeholder="选择语言">
                  <el-option label="简体中文" value="zh-CN"></el-option>
                </el-select>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 上传文件生成模式 -->
        <div v-else-if="currentMethod === 2" key="upload" class="method-content">
          <div class="content-header">
            <h3>上传文件生成</h3>
            <p class="desc">支持多种文件格式，自动识别内容生成PPT</p>
          </div>
          <el-card class="upload-container" shadow="hover">
            <div class="upload-methods">
              <div class="upload-item">
                <el-upload
                  class="upload-area"
                  drag
                  action=""
                  :auto-upload="false"
                  :on-change="handleUploadChange"
                  multiple
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">
                    支持格式：doc/docx/pdf/ppt/pptx/txt/md等，单个文件不超过50MB
                  </div>
                </el-upload>
              </div>
              <div class="upload-actions">
                <el-button type="primary" icon="el-icon-upload2" @click="handleUploadFile">上传文件</el-button>
                <el-button icon="el-icon-document" @click="handleGeneratePPT">生成PPT</el-button>
                <el-button icon="el-icon-refresh-right" @click="handleWordToPPT">Word精准转PPT</el-button>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 粘贴内容生成模式 -->
        <div v-else-if="currentMethod === 6" key="paste" class="method-content">
          <div class="content-header">
            <h3>粘贴内容生成</h3>
            <p class="desc">支持大纲格式或自由文本，快速生成PPT</p>
          </div>
          <div class="paste-methods">
            <el-card class="paste-card" shadow="hover">
              <div slot="header" class="paste-header">
                <i class="el-icon-document-checked"></i>
                <span>输入大纲内容生成PPT</span>
              </div>
              <div class="paste-content">
                <el-input
                  type="textarea"
                  :rows="6"
                  placeholder="请输入大纲内容，支持Markdown格式"
                  v-model="formData.outlineContent"
                ></el-input>
                <div class="paste-footer">
                  <el-button type="primary" icon="el-icon-document" @click="handleGenerateFromOutline">生成PPT</el-button>
                </div>
              </div>
            </el-card>
            <el-card class="paste-card" shadow="hover">
              <div slot="header" class="paste-header">
                <i class="el-icon-edit-outline"></i>
                <span>自由输入文本生成PPT</span>
              </div>
              <div class="paste-content">
                <el-input
                  type="textarea"
                  :rows="6"
                  placeholder="请输入任意文本内容，AI将自动分析并生成PPT"
                  v-model="formData.freeContent"
                ></el-input>
                <div class="paste-footer">
                  <el-button type="primary" icon="el-icon-document" @click="handleGenerateFromText">生成PPT</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </transition>

      <!-- 对话展示区域 -->
      <div class="chat-container" ref="chatContainer">
        <div class="chat-messages" v-if="messages.length > 0">
          <div v-for="(message, index) in messages"
               :key="index"
               :class="['message', message.type]">
            <div class="message-content" v-html="message.content"></div>
          </div>
        </div>
      </div>

      <!-- 编辑大纲弹框 -->
      <el-dialog
        title="编辑大纲内容"
        :visible.sync="dialogVisible"
        width="60%"
        class="outline-dialog"
      >
        <div class="outline-content">
          <el-input
            type="textarea"
            v-model="editableContent"
            :rows="15"
            resize="none"
          ></el-input>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleRegenerate">
            <i class="el-icon-refresh-right"></i>
            重新生成
          </el-button>
          <el-button type="primary" @click="handleConfirmEdit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 模板选择按钮 -->
      <div class="template-select-btn">
        <el-button
          type="primary"
          icon="el-icon-picture-outline"
          circle
          @click="showTemplateSelect"
        ></el-button>
      </div>
    </div>
  </div>
</template>

<script>
// 导入API模块
import aipptv3 from '@/api/intellectSmartPpt/aipptv3';
import {createTask} from "@/api/intellectSmartPpt/aiPPT";

export default {
  name: 'AI_PPT_V3',

  data() {
    return {
      /** API密钥值 进入后台的密钥 */
      apiKey: 'ak_3kCK1V7T8jXdtOsu3fOv5aGhD7-Tqo-lvFa7ZjejtcQ',
      // 当前激活的选项卡
      activeTab: 'generate',
      // 当前选择的生成方式
      currentMethod: 1,
      // 生成方式配置
      generationMethods: [
        {
          type: 1,
          name: 'AI智能创作',
          icon: 'el-icon-magic-stick',
          description: '输入主题，AI智能生成PPT'
        },
        {
          type: 2,
          name: '上传文件',
          icon: 'el-icon-upload',
          description: '上传文件，自动转换为PPT'
        },
        {
          type: 6,
          name: '粘贴内容',
          icon: 'el-icon-document-copy',
          description: '粘贴文本，快速生成PPT'
        }
      ],
      // 表单数据
      formData: {
        content: '', // 主题/要求内容
        pageCount: '20-30', // 页数范围
        scene: 'general', // 演示场景
        audience: 'public', // 目标受众
        smartSearch: true, // 智能搜索开关
        language: 'zh-CN', // 语言选择
        outlineContent: '', // 大纲内容
        freeContent: '', // 自由文本内容
        files: [] // 上传的文件列表
      },
      isContentValid: false, // 添加内容验证状态
      messages: [], // 对话消息列表
      dialogVisible: false, // 编辑弹框显示状态
      editableContent: '', // 可编辑的内容
      currentTaskId: '', // 当前任务ID
      isGenerating: false, // 是否正在生成
      lastGeneratedContent: '', // 最后生成的完整内容
    }
  },

  created() {
  },

  methods: {
    /**
     * 选择 ai智能创造 生成方式
     * @param {number} type - 生成方式类型
     */
    selectMethod(type) {
      console.log('selectMethod被调用，参数type:', type);
      try {
        this.currentMethod = type;
        this.formData.files = [];
        console.log('当前选择生成方式：', this.currentMethod);
        console.log('formData更新状态：', this.formData);
      } catch(error) {
        console.error('selectMethod执行出错：', error);
      }
    },

    /**
     * 处理文件变化
     * @param {Object} file - 文件对象
     */
    handleFileChange(file) {
      this.formData.files.push(file.raw);
    },

    /**
     * 验证内容是否有效
     */
    validateContent() {
      const content = this.formData.content;
      this.isContentValid = content && content.trim().length > 0;

      // 如果内容为空，显示提示
      if (!this.isContentValid && content !== '') {
        this.$message({
          message: '请输入有效的PPT主题或要求',
          type: 'warning'
        });
      }
    },

    /**
     * 创建PPT任务
     * @async
     * @description 调用createTask接口创建PPT生成任务
     * type说明：
     * 1.智能生成（主题、要求）
     * 2.上传文件生成
     * 3.上传思维导图生成
     * 4.通过word精准转ppt
     * 5.通过网页链接生成
     * 6.粘贴文本内容生成
     * 7.Markdown大纲生成
     */
    async handleCreate() {
      if (!this.isContentValid) {
        this.$message({
          message: '请先输入PPT主题或要求',
          type: 'warning'
        });
        return;
      }

      try {
        this.isGenerating = true;
        // 清空之前的消息
        this.messages = [];

        // 添加用户输入消息
        this.addMessage('user', this.formData.content);

        // 创建FormData对象
        const formData = new FormData();

        // 添加必要参数 - type必须是数字1，不能是字符串'1'
        formData.append('type', 1);

        // 添加内容（不超过1000字符）
        const content = this.formData.content.trim();
        if (content.length === 0) {
          this.$notify.error({
            title: '错误',
            message: '内容不能为空',
            duration: 4000
          });
        }

        if (content.length > 1000) {
          this.$notify.error({
            title: '错误',
            message: '内容不能超过1000字符',
            duration: 4000
          });
        }
        formData.append('content', content);

        // 添加参考文件（如果有）
        if (this.formData.files && this.formData.files.length > 0) {
          // 检查文件数量限制
          if (this.formData.files.length > 5) {
            this.$notify.error({
              title: '错误',
              message: '文件数量不能超过5个',
              duration: 4000
            });
          }

          // 检查文件大小限制
          const totalSize = this.formData.files.reduce((sum, file) => sum + file.size, 0);
          if (totalSize > 50 * 1024 * 1024) { // 50MB
            this.$notify.error({
              title: '错误',
              message: '文件总大小不能超过50MB',
              duration: 4000
            });
          }

          // 添加文件到FormData
          this.formData.files.forEach(file => {
            formData.append('file', file);
          });
        }

        // 调试日志
        console.log('创建任务请求参数：');
        for (let pair of formData.entries()) {
          console.log(pair[0] + ': ' + pair[1]);
        }

        console.log('创建任务请求体：', formData)
        // 调用创建任务接口
        const heads = {
          "api-key": this.apiKey
        }
        const res = await createTask(formData,heads);
        console.log('创建任务响应：', res);

        if (res.code !== 200 || res.data.error_msg) {
          this.$notify.error({
            title: '错误',
            message: res.data?.error_msg || '创建任务失败',
            duration: 4000
          });
          return;
        }
        this.currentTaskId = res.data.taskId;
        console.log('创建任务成功，任务ID：', this.currentTaskId)
        // 开始生成内容
        await this.generateContent();
      } catch (error) {
        console.error('创建任务错误：', error);
        this.isGenerating = false;
      }
    },

    /**
     * 生成内容
     * @async
     * @description 调用generateContent接口生成PPT大纲内容
     * 使用EventSource实现流式响应
     * status说明：
     * 3: 正在生成
     * 4: 生成完成
     */
    async generateContent() {
      try {
        // 准备请求参数
        const params = {
          "id": this.currentTaskId, // 任务ID
          "stream": true, // 是否流式（默认 true）
          "length": "long", // 篇幅长度：short/medium/long => 10-15页/20-30页/25-35页
          "scene": null, // 演示场景：通用场景、教学课件、工作总结、工作计划、项目汇报、解决方案、研究报告、会议材料、产品介绍、公司介绍、商业计划书、科普宣传、公众演讲 等任意场景类型。
          "audience": null, // 受众：大众、学生、老师、上级领导、下属、面试官、同事 等任意受众类型。
          "lang": "zh", // 语言: zh/zh-Hant/en/ja/ko/ar/de/fr/it/pt/es/ru
          "prompt": null // 用户要求（小于50字）
        };

        // 创建POST请求
        // const response = await fetch('https://open.docmee.cn/api/ppt/v2/generateContent', {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json'
        //   },
        //   body: JSON.stringify(params)
        // });

        // 检查响应状态
        if (!response.ok) {
          this.$notify.error({
            title: '错误',
            message: `HTTP error! status: ${response.status}`,
            duration: 4000
          });
        }

        // 获取响应的ReadableStream
        const reader = response.body.getReader();
        let currentContent = '';

        // 读取流数据
        while (true) {
          const {done, value} = await reader.read();

          if (done) {
            break;
          }

          // 解析接收到的数据
          const text = new TextDecoder().decode(value);
          const lines = text.split('\n').filter(line => line.trim());

          for (const line of lines) {
            try {
              if (line.startsWith('data:')) {
                const data = JSON.parse(line.slice(5));

                if (data.status === 3) {
                  // 追加内容
                  currentContent += data.text;
                  // 更新最后一条消息
                  if (this.messages.length > 0) {
                    this.messages[this.messages.length - 1].content = this.formatMarkdown(currentContent);
                  } else {
                    this.addMessage('assistant', this.formatMarkdown(currentContent));
                  }
                  this.scrollToBottom();
                } else if (data.status === 4) {
                  // 生成完成
                  this.lastGeneratedContent = currentContent;
                  this.isGenerating = false;
                  this.showEditDialog(currentContent);
                  return;
                }
              }
            } catch (error) {
              console.error('解析数据失败:', error);
            }
          }
        }
      } catch (error) {
        this.isGenerating = false;
        this.$message.error('生成内容失败：' + error.message);
      }
    },

    /**
     * 转换页数范围到接口所需格式
     * @param {string} pageCount - 页数范围
     * @returns {string} - 转换后的长度类型
     */
    convertPageCountToLength(pageCount) {
      const lengthMap = {
        '10-15': 'short',
        '20-30': 'medium',
        '25-35': 'long'
      };
      return lengthMap[pageCount] || 'medium';
    },

    /**
     * 转换语言代码
     * @param {string} language - 语言选择
     * @returns {string} - 转换后的语言代码
     */
    convertLanguage(language) {
      const langMap = {
        'zh-CN': 'zh',
        'zh-TW': 'zh-Hant'
      };
      return langMap[language] || language;
    },

    /**
     * 添加消息到列表
     * @param {string} type - 消息类型：user/assistant
     * @param {string} content - 消息内容
     */
    addMessage(type, content) {
      this.messages.push({
        type,
        content: this.formatMarkdown(content)
      });
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    /**
     * 格式化Markdown内容
     * @param {string} content - markdown内容
     * @returns {string} - 格式化后的HTML
     */
    formatMarkdown(content) {
      // 这里可以使用markdown-it等库来格式化内容
      return content;
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

    /**
     * 显示编辑弹框
     * @param {string} content - 要编辑的内容
     */
    showEditDialog(content) {
      this.editableContent = content;
      this.dialogVisible = true;
    },

    /**
     * 处理重新生成
     */
    async handleRegenerate() {
      this.dialogVisible = false;
      this.isGenerating = true;
      await this.generateContent();
    },

    /**
     * 确认编辑内容
     */
    handleConfirmEdit() {
      this.lastGeneratedContent = this.editableContent;
      this.dialogVisible = false;
      // 更新最后一条消息
      if (this.messages.length > 0) {
        this.messages[this.messages.length - 1].content = this.formatMarkdown(this.editableContent);
      }
    },

    /**
     * 显示模板选择
     */
    showTemplateSelect() {
      // TODO: 实现模板选择逻辑
    },

    handleUploadFile() {
      // 实现上传文件逻辑
    },

    handleGeneratePPT() {
      // 实现生成PPT逻辑
    },

    handleWordToPPT() {
      // 实现Word转PPT逻辑
    },

    handleGenerateFromOutline() {
      // 实现从大纲生成PPT逻辑
    },

    handleGenerateFromText() {
      // 实现从文本生成PPT逻辑
    },

    handleUploadChange(file) {
      // 处理文件上传变化
      if (file.size > 50 * 1024 * 1024) {
        this.$message.error('文件大小不能超过50MB');
        return false;
      }
      this.formData.files.push(file);
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-ppt-container {
  padding: 20px;

  // 顶部选项卡样式
  .main-tabs {
    margin-bottom: 20px;
  }

  // 生成容器样式
  .generate-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  // 欢迎消息样式
  .welcome-message {
    font-size: 24px;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
  }

  // 生成方式选择区域样式
  .generation-methods {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;

    // 方式选择卡片样式
    .method-card {
      width: 200px;
      height: 150px;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 12px;
      overflow: hidden;

      // 卡片内容容器
      .method-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }

      &.active {
        border-color: #409EFF;
        background-color: #ecf5ff;
      }

      // 图标样式
      .method-icon {
        font-size: 40px;
        color: #409EFF;
        margin-bottom: 15px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      // 名称样式
      .method-name {
        font-size: 16px;
        color: #333;
        text-align: center;
      }

      .method-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        text-align: center;
      }
    }
  }

  // 方法内容区域样式
  .method-content {
    // 输入区域样式
    .input-area {
      margin-bottom: 20px;

      // 操作按钮样式
      .action-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
      }
    }

    // 选项卡片样式
    .options-card {
      border-radius: 12px;

      // 选项网格布局
      .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        padding: 20px;

        // 选项项样式
        .option-item {
          display: flex;
          align-items: center;
          gap: 10px;

          // 选项标签样式
          .option-label {
            white-space: nowrap;
            color: #666;
            min-width: 70px;
          }
        }
      }
    }
  }

  // 上传方法样式
  .upload-methods {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
  }

  // 粘贴方法样式
  .paste-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;

    // 粘贴卡片样式
    .paste-card {
      border-radius: 12px;

      .el-button {
        margin-top: 15px;
        width: 100%;
      }
    }
  }
}

.content-header {
  text-align: center;
  margin-bottom: 30px;

  h3 {
    font-size: 24px;
    color: #303133;
    margin: 0 0 10px;
  }

  .desc {
    font-size: 14px;
    color: #909399;
    margin: 0;
  }
}

.method-content {
  .options-card {
    .options-header {
      padding: 0 0 15px;
      margin-bottom: 15px;
      border-bottom: 1px solid #EBEEF5;
      display: flex;
      align-items: center;
      gap: 8px;
      color: #303133;
      font-weight: 500;
    }
  }

  .upload-container {
    padding: 20px;

    .upload-area {
      border: 2px dashed #DCDFE6;
      border-radius: 8px;
      padding: 30px 0;
      text-align: center;

      &:hover {
        border-color: #409EFF;
      }

      .el-upload__text {
        margin: 10px 0;
        font-size: 14px;
        color: #606266;

        em {
          color: #409EFF;
          font-style: normal;
        }
      }

      .el-upload__tip {
        font-size: 12px;
        color: #909399;
      }
    }

    .upload-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 20px;
    }
  }

  .paste-methods {
    .paste-card {
      .paste-header {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          font-size: 18px;
        }
      }

      .paste-content {
        padding: 20px;

        .paste-footer {
          margin-top: 15px;
          text-align: right;
        }
      }
    }
  }
}

// 过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.chat-container {
  margin-top: 20px;
  height: 400px;
  overflow-y: auto;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;

  .chat-messages {
    .message {
      margin-bottom: 20px;
      max-width: 80%;

      &.user {
        margin-left: auto;
        .message-content {
          background: #409EFF;
          color: white;
          border-radius: 12px 12px 0 12px;
        }
      }

      &.assistant {
        margin-right: auto;
        .message-content {
          background: white;
          border-radius: 12px 12px 12px 0;
        }
      }

      .message-content {
        padding: 12px 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.outline-dialog {
  .outline-content {
    margin: 20px 0;
  }
}

.template-select-btn {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 1000;

  .el-button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>


