<template>
  <div class="app-container">
  </div>
</template>

<script>


import {getCasEnter} from "@/api/intellectSmartPpt/intellectSmartPpt";

export default {
  name: 'v1_index_cas',
  data() {
    return {
      // API密钥相关
      hasValidApiKey: false,

    };
  },
  watch: {},
  /**
   * 组件创建时的生命周期钩子
   * 初始化页面数据和事件监听
   */
  created() {
    this.handleGetCasEnter()
  },
  mounted() {
  },

  methods: {
    handleGetCasEnter() {
      try {
        getCasEnter().then(res => {
          if (res.code === 200 && res.data) {
            console.log("cas enter" + res.data[0].value)
            window.location.href = res.data[0].value
          }
        });
      } catch (e){
        this.$notify.error({title: '错误', message: e.message})
      }
    }
  }
}
</script>
