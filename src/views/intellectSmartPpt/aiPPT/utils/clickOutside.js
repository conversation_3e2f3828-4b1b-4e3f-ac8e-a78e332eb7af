/**
 * v-click-outside 指令
 * 用于在点击元素外部时触发回调函数
 * 
 * 使用方法:
 * v-click-outside="onClickOutside"
 */

const clickOutside = {
  bind(el, binding, vnode) {
    el.clickOutsideHandler = (e) => {
      // 检查点击是否发生在元素外部
      if (el && !el.contains(e.target)) {
        // 调用组件方法
        binding.value(e);
      }
    }
    
    // 添加事件监听器到document
    document.addEventListener('click', el.clickOutsideHandler);
  },
  
  unbind(el) {
    // 清理事件监听器
    document.removeEventListener('click', el.clickOutsideHandler);
  }
}

export default clickOutside; 