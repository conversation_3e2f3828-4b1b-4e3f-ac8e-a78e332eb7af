<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="课程" prop="courseName">
        <!-- <el-select class="ck-input" v-model="form.courseName" placeholder="请选择课程">
          <el-option v-for="item in courseOptions" :key="item.id" :label="item.courseName" :value="item.courseName">
          </el-option>
        </el-select> -->
        <el-input class="ck-input" v-model="form.courseName" placeholder="请输入课程" disabled/>
      </el-form-item>
      <el-form-item label="学期" prop="term">
        <el-select class="ck-input" v-model=" form.term" filterable placeholder="请选择学期">
          <el-option v-for="dict in dict.type.semester" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item ref="radio" label="必修/选修" prop="courseType">
        <el-radio-group v-model="form.courseType" @change="changeCourseType">
          <el-radio label="0">必修</el-radio>
          <el-radio label="1">选修</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="班级" prop="kcglList">
        <el-cascader class="ck-input" v-model="form.kcglList" :options="classOptions" :props="props">
        </el-cascader>
      </el-form-item>
      <el-form-item label="学生列表上传" prop="deiDesc" v-if="fileUploadShow">
        <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple
          :on-success="handleUploadSuccess" :on-remove="handleRemove" accept=".xlsx">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <span>支持xlsx,xls件上传</span><br/>
            <el-link type="primary" style="font-size: 12px" @click="downLoadExample">示例模板</el-link>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { addCourseManagement, getDictData } from "@/api/courseManagement/courseManagement.js";
import { getUniversityll } from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";
import {downLoad} from "@/api/intellectSmartPpt/intellectSmartPpt";
export default {
  name: 'FormContent',
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  dicts: ["semester"],
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/test/courseManagement/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'student' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      form: {
        courseType: '0',
        xhList: [],
        term: ''
      },
      rules: {
        courseName: [
          { required: true, message: '请输入课程', trigger: ['blur', 'change'] },
        ],
        term: [
          { required: true, message: '请选择学期', trigger: ['blur', 'change'] },
        ],
        courseType: [
          { required: true, message: '请选择必修/选修', trigger: ['blur', 'change'] },
        ],
      },
      // courseOptions: [],
      classOptions: [],
      props: { multiple: true, emitPath: false },
      fileUploadShow: false
    }
  },
  created() {
    // this.cheackChargeClass()
    this.getUniversityll()
    this.getDictData()
    this.form.courseName = this.$router.currentRoute.query && this.$router.currentRoute.query.courseName
  },
  methods: {
    downLoadExample(){
      const link = document.createElement('a');  // 创建一个链接元素
      link.href = '/example/创建课程导入学生名单示例.xlsx';  // 文件的路径
      link.download = '创建课程导入学生名单示例.xlsx';  // 设置下载时文件的名字
      link.click();  // 模拟点击下载
    },
    downLoad,
    // // 查询当前登陆人参与的课程
    // cheackChargeClass() {
    //   cheackChargeClass({}).then(res => {
    //     this.courseOptions = res.data
    //   })
    // },
    getDictData() {
      getDictData('semester').then(res => {
        if (res.code == 200) {
          const semesterArr = res.data
          const semesterList = semesterArr.filter(item => item.cssClass === "defaultShow")
          if (semesterList.length > 0) {
            this.form.term = semesterList[0].dictValue
          }
        }
      })
      console.log(this.form)
    },
    handleUploadSuccess(res,) {
      this.form.xhList = res
    },
    handleRemove() {
      this.form.xhList = []
    },
    handleSubmint() {
      if (this.validateForm()) {
        addCourseManagement(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('新增成功')
            this.handleBack()
          }
        })
      } else {
        this.loading = false
      }
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push('/explorationCenter6/courseManagement')
    },
    // 获取班级
    getUniversityll() {
      getUniversityll().then((res) => {
        this.classOptions = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
              return {
                value: item.id,
                label: item.name,
                children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                      return {
                        value: item.id,
                        label: item.name
                      };
                    }) : null
                  };
                }) : null
              };
            }) : null
          };
        });
      })
    },
    changeCourseType(val) {
      if (val == 0) {
        this.fileUploadShow = false
      } else {
        this.fileUploadShow = true
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
</style>

