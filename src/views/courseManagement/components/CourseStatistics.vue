<!-- 课程统计 -->
<template>
  <div class="base">
    <div class="course-name-display">{{ statisticsData.courseName }}</div>
    <el-card shadow="never" class="course-statistics-card">
      <el-row :gutter="30" v-if="statisticsData" class="full-height-row">
        <!-- 左侧：教师团队 -->
        <el-col :span="12" class="teacher-section">
          <div class="header">
            <span class="title">教师团队人数</span>
            <span class="total-count">{{ statisticsData.teamMemberCount }} 人</span>
          </div>
          <div ref="pieChartContainer" style="width: 100%; height: 270px;"></div>
          <div class="legend-details">
            <el-row>
              <el-col :span="6" v-for="item in teacherDetails" :key="item.name" class="legend-item">
                <span class="dot" :style="{ backgroundColor: item.color }"></span>
                <span class="name">{{ item.name }}</span>
                <span class="count">{{ item.value }} 人</span>
              </el-col>
            </el-row>
          </div>
        </el-col>
        <el-col :span="1"></el-col>
        <!-- 右侧：学生信息 -->
        <el-col :span="12" class="student-section">
          <div class="header">
            <span class="title">学生统计</span>
          </div>
          <div ref="studentChartContainer" style="width: 100%; height: 250px;"></div>
        </el-col>
      </el-row>
      <div v-else class="full-height-row">
        <el-skeleton :rows="8" animated/>
      </div>
    </el-card>
  </div>

</template>

<script>
import {courseStatistics} from "@/api/courseManagement/courseManagement";
import * as echarts from 'echarts/core';
import {PieChart, BarChart} from 'echarts/charts';
import {
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  GridComponent,
  ToolboxComponent
} from 'echarts/components';
import {CanvasRenderer} from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  BarChart,
  CanvasRenderer,
  TitleComponent,
  GridComponent,
  ToolboxComponent
]);

export default {
  name: "CourseStatistics",
  data() {
    return {
      statisticsData: {// 存储接口返回的数据
        courseName: "",
        teamMemberCount: 1, // 教师人数
        associateProfessorCount: 0, // 副教授
        lecturerCount: 0, // 讲师
        professorCount: 0, // 教授
        otherPersonnelCount: 1, // 其他
        totalStudentCount: 93, // 学生人数
        participantCount: 93 // 参与学习人数
      },
      chart: null, // 教师饼图 ECharts 实例
      studentChart: null, // 学生柱状图 ECharts 实例
      // 定义教师分类和颜色，与 ECharts 对应
      teacherDetailsConfig: [
        {key: 'associateProfessorCount', name: '副教授', color: '#5470c6'}, // 蓝色 (ECharts 默认色)
        {key: 'lecturerCount', name: '讲师', color: '#91cc75'}, // 绿色
        {key: 'professorCount', name: '教授', color: '#fac858'}, // 黄色
        {key: 'otherPersonnelCount', name: '其他', color: '#ee6666'} // 红色
      ]
    };
  },
  computed: {
    // 计算用于下方图例显示的数据
    teacherDetails() {
      if (!this.statisticsData) return [];
      return this.teacherDetailsConfig.map(item => ({
        name: item.name,
        value: this.statisticsData[item.key] || 0, // 处理可能不存在或为 null 的情况
        color: item.color
      }));
    },
    // 添加 studentTableData 计算属性
    studentTableData() {
      if (!this.statisticsData) return [];
      return [
        {label: '学生人数', value: this.statisticsData.totalStudentCount || 0},
        {label: '参与学习人数', value: this.statisticsData.participantCount || 0}
      ];
    }
  },
  mounted() {
    this.handleCourseStatistics(); // 获取数据
    window.addEventListener('resize', this.handleResize); // 添加窗口大小监听
  },
  beforeDestroy() {
    // 组件销毁前移除监听并销毁图表
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    // 销毁学生图表
    if (this.studentChart) {
      this.studentChart.dispose();
      this.studentChart = null;
    }
  },
  methods: {
    // 获取统计数据
    handleCourseStatistics() {
      const data = {
        courseName: this.$route.query && this.$route.query.courseName,
      };
      courseStatistics(data).then(res => {
        console.log("获取统计数据:", res);
        if (res.code === 200 && res.data) {
          this.statisticsData = res.data;
          // 数据加载后初始化或更新图表
          this.$nextTick(() => { // 确保 DOM 更新完毕
            this.initOrUpdateChart();
            this.initOrUpdateStudentChart(); // 添加学生图表初始化调用
          });
        } else {
          console.error("获取课程统计数据失败或无数据:", res);
          this.statisticsData = {};
          // 清理可能存在的旧图表
          this.$nextTick(() => {
            this.clearCharts();
          });
        }
      }).catch(error => {
        console.error("请求课程统计数据出错:", error);
        this.statisticsData = {};
        this.$nextTick(() => {
          this.clearCharts();
        });
      });
    },
    // 初始化或更新教师饼图
    initOrUpdateChart() {
      if (!this.statisticsData) return; // 无数据则不操作

      // 准备饼图数据，现在包含颜色信息
      const pieData = this.teacherDetailsConfig.map(item => ({
        value: this.statisticsData[item.key] || 0,
        name: item.name,
        itemStyle: { // 为每个数据项指定颜色
          color: item.color
        }
      })).filter(item => item.value > 0); // 过滤掉人数为0的项

      // 如果没有有效的教师数据，则不显示饼图或显示提示
      if (pieData.length === 0) {
        if (this.chart) {
          this.chart.clear(); // 清除旧图表
        }
        // 可选：在容器内显示提示信息，例如 "暂无教师数据"
        const container = this.$refs.pieChartContainer;
        if (container) container.innerHTML = '<p style="text-align: center; color: #909399; padding-top: 100px;">暂无教师分布数据</p>';
        return;
      } else {
        // 如果之前显示了提示信息，清除它
        const container = this.$refs.pieChartContainer;
        if (container && container.firstChild && container.firstChild.tagName === 'P') {
          container.innerHTML = '';
        }
      }

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}人 ({d}%)' // 提示框格式：名称 : 人数 (百分比)
        },
        toolbox: { // 添加 toolbox 配置
          show: true,
          feature: {
            saveAsImage: {show: true, title: '保存图片'},
            restore: {show: true, title: '还原'}
          },
          right: '5%'
        },
        legend: {
          show: true // 显示 ECharts 默认图例
        },
        series: [
          {
            name: '教师团队构成',
            type: 'pie',
            radius: ['45%', '80%'], // 设置为环形图
            center: ['50%', '50%'], // 图表居中
            avoidLabelOverlap: false,
            label: {
              show: false, // 不在饼图上显示标签
              position: 'center'
            },
            emphasis: { // 高亮状态下的样式
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
                formatter: '{b}\n{c}人' // 高亮时显示名称和人数
              }
            },
            labelLine: {
              show: false // 不显示标签引导线
            },
            data: pieData // 使用处理后的数据
          }
        ]
      };

      // 初始化或更新图表
      if (!this.chart) {
        // 确保容器存在
        if (!this.$refs.pieChartContainer) {
          console.error("饼图容器未找到!");
          return;
        }
        this.chart = echarts.init(this.$refs.pieChartContainer);
      }
      this.chart.setOption(option);
    },
    // --- 新增方法：初始化或更新学生柱状图 ---
    initOrUpdateStudentChart() {
      if (!this.statisticsData || this.statisticsData.totalStudentCount === undefined || this.statisticsData.participantCount === undefined) {
        // 如果没有学生数据，可以选择清空或显示提示
        const studentContainer = this.$refs.studentChartContainer;
        if (this.studentChart) {
          this.studentChart.clear();
        }
        if (studentContainer) studentContainer.innerHTML = '<p style="text-align: center; color: #909399; padding-top: 100px;">暂无学生数据</p>';
        return;
      }

      // 清除可能存在的提示信息
      const studentContainer = this.$refs.studentChartContainer;
      if (studentContainer && studentContainer.firstChild && studentContainer.firstChild.tagName === 'P') {
        studentContainer.innerHTML = '';
      }

      const studentOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: { // 调整网格边距
          left: '3%',
          right: '10%', // 为 toolbox 留出空间
          bottom: '3%',
          top: '15%', // 为 toolbox 留出空间
          containLabel: true
        },
        toolbox: { // 添加 toolbox 配置
          show: true,
          feature: {
            saveAsImage: {show: true, title: '保存图片'},
            magicType: {show: true, type: ['line', 'bar',], title: {line: '切换为折线图', bar: '切换为柱状图'}},
            restore: {show: true, title: '还原'}
          },
          right: '5%'
        },
        xAxis: [
          {
            type: 'category',
            data: ['学生总数', '参与人数'],
            axisTick: {alignWithLabel: true}
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: { // 格式化 Y 轴标签
              formatter: '{value} 人'
            }
          }
        ],
        series: [
          {
            name: '人数',
            type: 'bar',
            barWidth: '40%',
            label: { // 添加柱顶标签配置
              show: true,
              position: 'top',
              formatter: '{c} 人',
              color: '#303133',
              fontSize: 12
            },
            data: [
              this.statisticsData.totalStudentCount || 0,
              this.statisticsData.participantCount || 0
            ],
            itemStyle: { // 设置柱子颜色
              color: function (params) {
                var colorList = ['#5470c6', '#91cc75'];
                return colorList[params.dataIndex];
              }
            }
          }
        ]
      };

      if (!this.studentChart) {
        if (!this.$refs.studentChartContainer) {
          console.error("学生图表容器未找到!");
          return;
        }
        this.studentChart = echarts.init(this.$refs.studentChartContainer);
      }
      this.studentChart.setOption(studentOption);
    },
    // --- 新增方法：清理所有图表 ---
    clearCharts() {
      if (this.chart) {
        this.chart.clear();
      }
      if (this.studentChart) {
        this.studentChart.clear();
      }
      // 可选：显示无数据提示
      const pieContainer = this.$refs.pieChartContainer;
      const studentContainer = this.$refs.studentChartContainer;
      if (pieContainer) pieContainer.innerHTML = '<p style="text-align: center; color: #909399; padding-top: 100px;">暂无教师分布数据</p>';
      if (studentContainer) studentContainer.innerHTML = '<p style="text-align: center; color: #909399; padding-top: 100px;">暂无学生数据</p>';
    },
    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
      // 调整学生图表
      if (this.studentChart) {
        this.studentChart.resize();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.base{
  background-color: #f5f7fa;
  border: none;
}
.course-statistics-card {
  background-color: #f5f7fa;
  border: none;
  padding: 5px 10px;
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

.course-name-display {
  color: #595a5d;
  font-size: 18px;
  font-weight: bold;
  padding: 0 10px;
}

.full-height-row {
  flex-grow: 1;
  display: flex;
}

.teacher-section, .student-section {
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.student-section {
  padding-left: 15px;
}

.header {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 16px;
  color: #606266;
  font-weight: bold;
}

.total-count {
  font-size: 20px;
  color: #303133;
  font-weight: bold;
}

.legend-details {
  margin-top: 10px;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.name {
  margin-right: 5px;
}

.count {
  font-weight: bold;
  color: #303133;
}

::v-deep .el-skeleton {
  padding: 15px;
}
</style>



