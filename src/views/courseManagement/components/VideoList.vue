<!-- 资料 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="视频名称" prop="videoName">
        <el-input v-model="queryParams.videoName" placeholder="请输入视频名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="videoList">
      <el-table-column label="视频名称" prop="videoName" min-width="150" />
      <el-table-column label="视频简介" prop="videoSynopsis" min-width="150" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-video-play" @click="handlePlay(scope.row)">播放</el-button>
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)">下载</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="600px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="80px">
        <el-form-item label="视频名称" prop="videoName">
          <el-input v-model="form.videoName" />
        </el-form-item>
        <el-form-item label="视频简介" prop="videoSynopsis">
          <el-input v-model="form.videoSynopsis" />
        </el-form-item>
        <el-form-item label="上传文件" prop="deiDesc">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple limit="1"
                     :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept=".mp4">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持mp4件上传</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getVideoList,
    addVideo,
    delVideo,
  } from "@/api/courseManagement/courseManagement.js";
  import { getToken } from "@/utils/auth";
  export default {
    name: "VideoList",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        videoList: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          courseName: this.$route.query && this.$route.query.courseName,
        },
        title: '添加视频',
        form: {},
        rules: {
          videoName: [
            { required: true, message: '请输入视频名称', trigger: ['blur', 'change'] },
          ],
          videoSynopsis:[],
        },
        dialogOpen: false,
        btnLoading: false,
        uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
        uploadData: { modeltype: 'courseVideo' },
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        fileList: [],
        disabledConfirmDownload: false,
      };
    },
    watch: {
      // 监视搜索词变化
      "$route.query.courseName": {
        immediate: true,
        handler(newValue) {
          if (newValue) {
            this.courseName = newValue
            this.queryParams.courseName = newValue
            this.getList()
          }
        },
      },
    },
    created() {
      this.getList();
      this.form.courseName = this.$router.currentRoute.query && this.$router.currentRoute.query.courseName

    },
    activated() {
      this.getList();
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true;
        getVideoList(this.queryParams).then((response) => {
          this.videoList = response.rows
          this.total = response.total;
          this.loading = false;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.dialogOpen = true;
        this.title = '创建资料';
      },
      /** 删除按钮操作 */
      handleDel(row) {
        this.$modal
          .confirm("是否确认删除？")
          .then(function () {
            return delVideo(row.id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      },
      /** 播放按钮操作 */
      handlePlay(row) {
        // 假设row.filePath是视频的可访问URL
        console.log(row.filePath)
        if (row.filePath) {
          window.open(this.getImgUrl(row.filePath), '_blank');
        } else {
          this.$message.error('视频文件路径无效');
        }
      },
      async handleDownload(row) {
        if (!row.filePath) {
          this.$message.error('无法下载视频，请检查链接是否有效');
          return;
        }

        try {
          const response = await fetch(this.getImgUrl(row.filePath));
          if (!response.ok) throw new Error('网络响应不是OK');

          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = row.videoName || 'video.mp4'; // 设置下载后的文件名，默认为'video.mp4'
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url); // 清理临时URL
        } catch (error) {
          console.error('下载失败:', error);
          this.$message.error('下载失败，请稍后再试');
        }
      },

      getImgUrl(pptPath) {
        let baseUrl = window.location.origin
        var imgData;
        if (pptPath.includes('/ruoyi/')) {
          // 替换路径中的 ruoyi/ 之前的部分
          const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

          if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
            imgData = 'http://127.0.0.1:9215' + finalPath
            // console.log('Final baseUrl:', baseUrl)
          } else {
            imgData = baseUrl + finalPath
          }
        }
        return imgData;

      },
      handleSubmit() {
        this.btnLoading = true
        //如果没有上传文件报请上传文件
        if (this.fileList.length === 0) {
          this.$message.error('请上传案例文件')
          this.btnLoading = false
          return
        }
        if (this.validateForm()) {
          console.log(this.fileList)
          const queryForm = {
            ...this.form,
            fileIds: this.fileList.map(item => item.id),
          }
          console.log(queryForm)
            addVideo(queryForm).then(res => {
              if (res.code === 200) {
                this.$message.success('创建成功')
                this.handleClose()
                this.btnLoading = false
              } else {
                this.btnLoading = false
              }
            }).catch(() => {
              this.btnLoading = false
            })
        } else {
          this.btnLoading = false
        }
      },
      handleClose() {
        this.$refs.form.clearValidate()
        this.resetForm("form");
        this.fileList = []
        this.dialogOpen = false
        this.getList()
      },
      validateForm() {
        let validate
        this.$refs.form.validate((valid) => {
          validate = valid
        })
        return validate
      },
      handleUploadSuccess(res, file,) {
        file.fileIds = res.data.id;
        this.fileList.push({ id: res.data.id });
      },
      handleRemove(file, fileList) {
        const index = this.fileList.findIndex(item => item.id === file.id);
        if (index > -1) {
          this.fileList.splice(index, 1);
        }
      },

    },
  };
</script>
<style lang="scss">
</style>



