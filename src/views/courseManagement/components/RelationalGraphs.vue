<template>
  <div class="tab-content">
    <div id="main" class="main" />
    <div class="relationship-legend">
      <div class="legend-title">关系类型说明</div>
      <div class="legend-item">
        <div class="line-sample solid-line"></div>
        <span>父子关系</span>
      </div>
      <div class="legend-item">
        <div class="line-sample dashed-line"></div>
        <span>关联关系</span>
      </div>
      <div class="legend-item">
        <div class="line-sample dotted-line"></div>
        <span>前后置关系</span>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts';

  export default {
    name: "KnowledgeGraph",
    data() {
      return {
        myChart: null,
        courseName: "",
        graphData: {},
        currentCenter: 'course',
        relationshipStyles: {
          'contains': { type: 'solid', color: '#999' },
          'parent-child': { type: 'solid', color: '#ff0000' },
          'association': { type: 'dashed', color: '#00aa00' },
          'prerequisite': { type: 'dotted', color: '#0000ff' }
        }
      };
    },
    created() {
      this.courseName = this.$router.currentRoute.query && this.$router.currentRoute.query.courseName;
      this.loadGraphData();
    },
    mounted() {
      this.initChart();
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      loadGraphData() {
        if (this.courseName === '城市管理学') {
          this.graphData = this.getUrbanManagementData();
        } else if (this.courseName === '社会保障概论') {
          this.graphData = this.getSocialSecurityData();
        } else {
          this.graphData = this.getDefaultData();
        }
      },
      getUrbanManagementData() {
        return {
          nodes: [
            { id: 'chapter1', name: '城市管理基础', category: 1, symbolSize: 50 },
            { id: 'chapter2', name: '城市规划与管理', category: 1, symbolSize: 50 },
            { id: 'chapter3', name: '城市公共服务', category: 1, symbolSize: 50 },
            { id: 'chapter4', name: '社会保障体系架构', category: 1, symbolSize: 50 },
            { id: 'chapter5', name: '城市治理创新模式', category: 1, symbolSize: 50 },
            { id: 'chapter6', name: '城市可持续发展', category: 1, symbolSize: 50 },
            { id: 'chapter7', name: '智慧城市建设', category: 1, symbolSize: 50 },
            { id: 'chapter8', name: '社区治理与发展', category: 1, symbolSize: 50 },
            { id: 'chapter9', name: '城市应急管理', category: 1, symbolSize: 50 },
            { id: 'chapter10', name: '国际城市管理比较', category: 1, symbolSize: 50 },

            { id: 'chapter1_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter1_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter1_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter2_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter2_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter2_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter3_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter3_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter3_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter4_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter4_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter4_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter5_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter5_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter5_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter6_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter6_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter6_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter7_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter7_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter7_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter8_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter8_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter8_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter9_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter9_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter9_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter10_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter10_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter10_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },



            { id: 'theory1_1', name: '城市管理定义', category: 5, symbolSize: 30 },
            { id: 'theory1_2', name: '管理主体构成', category: 5, symbolSize: 30 },
            { id: 'theory1_3', name: '城市摊贩体现经济活力', category: 5, symbolSize: 30 },
            { id: 'theory1_4', name: '管理需综合考虑内外因素', category: 5, symbolSize: 30 },
            { id: 'theory1_5', name: '国内外管理存在差异化', category: 5, symbolSize: 30 },
            { id: 'theory1_6', name: '管理需创新制度和工具', category: 5, symbolSize: 30 },
            { id: 'innovation1_1', name: '网格化管理', category: 6, symbolSize: 30 },
            { id: 'innovation1_2', name: '应兼顾摊贩权益和城市秩序', category: 6, symbolSize: 30 },
            { id: 'innovation1_3', name: '摈弃粗暴管理，柔性执法', category: 6, symbolSize: 30 },
            { id: 'innovation1_4', name: '统一规划管理公共空间', category: 6, symbolSize: 30 },
            { id: 'innovation1_5', name: '广泛动员与治理体系构建', category: 6, symbolSize: 30 },
            { id: 'innovation1_6', name: '跨部门联合执法', category: 6, symbolSize: 30 },
            { id: 'viewpoint1_1', name: '广泛动员与治理体系构建', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_2', name: '摇号与拍卖', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_3', name: '汽油税与限行', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_4', name: '汽油税与限行', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_5', name: '停车收费', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_6', name: '次优拥堵定价模型', category: 7, symbolSize: 30 },

            { id: 'theory2_1', name: '城市规划原则', category: 5, symbolSize: 30 },
            { id: 'theory2_2', name: '次优拥堵定价模型', category: 5, symbolSize: 30 },
            { id: 'theory2_3', name: '最优拥堵定价模型', category: 5, symbolSize: 30 },
            { id: 'theory2_4', name: '拥堵定价', category: 5, symbolSize: 30 },
            { id: 'theory2_5', name: '交通需求管理', category: 5, symbolSize: 30 },
            { id: 'theory2_6', name: '精准治理的实现途径', category: 5, symbolSize: 30 },
            { id: 'innovation2_1', name: '智慧城市', category: 6, symbolSize: 30 },
            { id: 'innovation2_2', name: '全周期管理的实施与意义', category: 6, symbolSize: 30 },
            { id: 'innovation2_3', name: '城市大脑驱动的应急管理创新', category: 6, symbolSize: 30 },
            { id: 'innovation2_4', name: '城市大脑在应急管理中的优势', category: 6, symbolSize: 30 },
            { id: 'innovation2_5', name: '城市大脑在应急管理中的优势', category: 6, symbolSize: 30 },
            { id: 'innovation2_6', name: '中国城市应急管理的进展', category: 6, symbolSize: 30 },
            { id: 'viewpoint2_1', name: '生态城市', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_2', name: '基于城市大脑的应急管理创新', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_3', name: '城市大脑的内涵与特征', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_4', name: '城市应急管理的基本流程', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_5', name: '城市应急管理的内涵与特征', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_6', name: '城市大脑在应急管理中的创新应用', category: 7, symbolSize: 30 },

            { id: 'theory3_1', name: '公共服务特征', category: 5, symbolSize: 30 },
            { id: 'theory3_2', name: '精准治理的实现路径', category: 5, symbolSize: 30 },
            { id: 'theory3_3', name: '全周期管理的实践与探索', category: 5, symbolSize: 30 },
            { id: 'theory3_4', name: '人工智能在应急管理中的应用', category: 5, symbolSize: 30 },
            { id: 'theory3_5', name: '多源数据融合应用', category: 5, symbolSize: 30 },
            { id: 'theory3_6', name: '多源数据融合应用', category: 5, symbolSize: 30 },
            { id: 'innovation3_1', name: '数字化服务', category: 6, symbolSize: 30 },
            { id: 'innovation3_2', name: '网格化管理升级', category: 6, symbolSize: 30 },
            { id: 'innovation3_3', name: '数字赋能创新', category: 6, symbolSize: 30 },
            { id: 'innovation3_4', name: '智慧城市发展', category: 6, symbolSize: 30 },
            { id: 'innovation3_5', name: '全周期管理', category: 6, symbolSize: 30 },
            { id: 'innovation3_6', name: '技术支撑', category: 6, symbolSize: 30 },
            { id: 'viewpoint3_1', name: '服务均等化', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_2', name: '法律法规', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_3', name: '事后恢复与重建', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_4', name: '应急处置与救援', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_5', name: '应急资源管理', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_6', name: '应急预案', category: 7, symbolSize: 30 },

            { id: 'theory4_1', name: '风险评估', category: 5, symbolSize: 30 },
            { id: 'theory4_2', name: '突发事件管理', category: 5, symbolSize: 30 },
            { id: 'theory4_3', name: '城市应急管理', category: 5, symbolSize: 30 },
            { id: 'theory4_4', name: '公共产品需求测算的主要工具', category: 5, symbolSize: 30 },
            { id: 'theory4_5', name: '公共产品需求的空间分析方法', category: 5, symbolSize: 30 },
            { id: 'theory4_6', name: '公共产品偏好的显示机制', category: 5, symbolSize: 30 },
            { id: 'innovation4_1', name: '政治影响力的增强', category: 6, symbolSize: 30 },
            { id: 'innovation4_2', name: '公共产品需求的研究综述及理论模型', category: 6, symbolSize: 30 },
            { id: 'innovation4_3', name: '政治投票和“用脚投票”的偏好显示', category: 6, symbolSize: 30 },
            { id: 'innovation4_4', name: '公共产品需求的空间匹配分析', category: 6, symbolSize: 30 },
            { id: 'innovation4_5', name: '基于DS模型的公共产品需求分析方法', category: 6, symbolSize: 30 },
            { id: 'innovation4_6', name: 'DS模型', category: 6, symbolSize: 30 },
            { id: 'viewpoint4_1', name: '非政府组织的主角地位', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_2', name: '空间经济分析方法', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_3', name: '萨缪尔森局部均衡模型', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_4', name: '庇古均衡模型', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_5', name: '城市管理的精细化、科学化', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_6', name: '智慧城市管理的技术支撑', category: 7, symbolSize: 30 },

            { id: 'theory5_1', name: '复杂系统理论', category: 5, symbolSize: 30 },
            { id: 'theory5_2', name: '大数据与机器学习在城市管理中的应用', category: 5, symbolSize: 30 },
            { id: 'theory5_3', name: '大数据智慧城市管理', category: 5, symbolSize: 30 },
            { id: 'theory5_4', name: '协作性管理', category: 5, symbolSize: 30 },
            { id: 'theory5_5', name: '公共选择理论', category: 5, symbolSize: 30 },
            { id: 'theory5_6', name: '公共治理理论', category: 5, symbolSize: 30 },
            { id: 'innovation5_1', name: '公共经济学', category: 6, symbolSize: 30 },
            { id: 'innovation5_2', name: 'MASTER分析框架', category: 6, symbolSize: 30 },
            { id: 'innovation5_3', name: '数字技术赋能治理手段智能化', category: 6, symbolSize: 30 },
            { id: 'innovation5_4', name: '城市管理', category: 6, symbolSize: 30 },
            { id: 'innovation5_5', name: '数字技术赋能治理手段智能化', category: 6, symbolSize: 30 },
            { id: 'innovation5_6', name: '规则约束治理策略', category: 6, symbolSize: 30 },
            { id: 'viewpoint5_1', name: '项目制治理结构', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_2', name: '城市社区治理多元主体', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_3', name: '智能化社区治理', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_4', name: '技术赋能治理手段', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_5', name: '规则约束策略', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_6', name: '停车收费', category: 7, symbolSize: 30 },

            { id: 'theory6_1', name: '建设型城市管理系统', category: 5, symbolSize: 30 },
            { id: 'theory6_2', name: '多元共治模式', category: 5, symbolSize: 30 },
            { id: 'theory6_3', name: '技术赋能', category: 5, symbolSize: 30 },
            { id: 'theory6_4', name: '规则约束', category: 5, symbolSize: 30 },
            { id: 'theory6_5', name: '情感治理', category: 5, symbolSize: 30 },
            { id: 'theory6_6', name: '项目制', category: 5, symbolSize: 30 },
            { id: 'innovation6_1', name: '绩效结果应用对更新的作用', category: 6, symbolSize: 30 },
            { id: 'innovation6_2', name: '科层制', category: 6, symbolSize: 30 },
            { id: 'innovation6_3', name: '多元共治', category: 6, symbolSize: 30 },
            { id: 'innovation6_4', name: '城市社区治理', category: 6, symbolSize: 30 },
            { id: 'innovation6_5', name: '技术创新支撑城市管理科学化', category: 6, symbolSize: 30 },
            { id: 'innovation6_6', name: '公共交通与土地利用', category: 6, symbolSize: 30 },
            { id: 'viewpoint6_1', name: '现代城市管理的方法创新', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_2', name: '城市空间动态模型', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_3', name: '城市生态完整性与提高生活质量', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_4', name: '城市生态完整性与提高生活质量', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_5', name: '多样化与城市生态系统平衡', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_6', name: '城市空间战略', category: 7, symbolSize: 30 },

            { id: 'theory7_1', name: '城市管理面临新挑战', category: 5, symbolSize: 30 },
            { id: 'theory7_2', name: '精明增长政策实施与评估', category: 5, symbolSize: 30 },
            { id: 'theory7_3', name: '多元合作治理网络', category: 5, symbolSize: 30 },
            { id: 'theory7_4', name: '交通引导发展模式（TOD）', category: 5, symbolSize: 30 },
            { id: 'theory7_5', name: '精细化的城市空间研究', category: 5, symbolSize: 30 },
            { id: 'theory7_6', name: '一体化动态模型与科学决策', category: 5, symbolSize: 30 },
            { id: 'innovation7_1', name: '大数据驱动的城市规划', category: 6, symbolSize: 30 },
            { id: 'innovation7_2', name: '城市增长边界', category: 6, symbolSize: 30 },
            { id: 'innovation7_3', name: '空间结构规划', category: 6, symbolSize: 30 },
            { id: 'innovation7_4', name: '城市规划实施', category: 6, symbolSize: 30 },
            { id: 'innovation7_5', name: '精明增长', category: 6, symbolSize: 30 },
            { id: 'innovation7_6', name: '新城市主义', category: 6, symbolSize: 30 },
            { id: 'viewpoint7_1', name: '城市管理统称', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_2', name: '新城市主义', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_3', name: '土地空间合理利用', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_4', name: '土地资本化管理', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_5', name: '土地价值捕获方法', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_6', name: '城市土地开发管理', category: 7, symbolSize: 30 },

            { id: 'theory8_1', name: '网络式管理结构的价值取向', category: 5, symbolSize: 30 },
            { id: 'theory8_2', name: '土地空间管理新策略', category: 5, symbolSize: 30 },
            { id: 'theory8_3', name: '土地资本化新模式', category: 5, symbolSize: 30 },
            { id: 'theory8_4', name: '土地价值捕获新工具', category: 5, symbolSize: 30 },
            { id: 'theory8_5', name: '土地空间管理', category: 5, symbolSize: 30 },
            { id: 'theory8_6', name: '土地资本化', category: 5, symbolSize: 30 },
            { id: 'innovation8_1', name: '市场失灵', category: 6, symbolSize: 30 },
            { id: 'innovation8_2', name: '土地价值捕获', category: 6, symbolSize: 30 },
            { id: 'innovation8_3', name: '土地价值捕获', category: 6, symbolSize: 30 },
            { id: 'innovation8_4', name: '城市土地开发', category: 6, symbolSize: 30 },
            { id: 'innovation8_5', name: '加强应急信息通报', category: 6, symbolSize: 30 },
            { id: 'innovation8_6', name: '编制联动应急预案', category: 6, symbolSize: 30 },
            { id: 'viewpoint8_1', name: '建立应急联席会议制度', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_2', name: '整体性治理理论', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_3', name: '签订应急合作协议', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_4', name: '跨区域重大突发事件联防联治策略', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_5', name: '区域公共产品供给的协同合作机制构建', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_6', name: '区域公共产品供给困境的博弈模型分析', category: 7, symbolSize: 30 },

            { id: 'theory9_1', name: '协作性治理理论', category: 5, symbolSize: 30 },
            { id: 'theory9_2', name: '区域公共产品的典型供给模式', category: 5, symbolSize: 30 },
            { id: 'theory9_3', name: '中国城市区域治理模式', category: 5, symbolSize: 30 },
            { id: 'theory9_4', name: '国外城市区域治理的典型机构', category: 5, symbolSize: 30 },
            { id: 'theory9_5', name: '国外城市区域治理的经验与模式', category: 5, symbolSize: 30 },
            { id: 'theory9_6', name: '国内外治理模式与工具', category: 5, symbolSize: 30 },
            { id: 'innovation9_1', name: '城市区域治理理论演进', category: 6, symbolSize: 30 },
            { id: 'innovation9_2', name: '新制度主义理论', category: 6, symbolSize: 30 },
            { id: 'innovation9_3', name: '城市区域治理概念和特征', category: 6, symbolSize: 30 },
            { id: 'innovation9_4', name: '城市区域治理兴起原因', category: 6, symbolSize: 30 },
            { id: 'innovation9_5', name: '跨区域重大突发事件联防联治策略创新', category: 6, symbolSize: 30 },
            { id: 'innovation9_6', name: '区域公共产品供给模式与机制创新', category: 6, symbolSize: 30 },
            { id: 'viewpoint9_1', name: '大数据智慧城市管理', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_2', name: '城市区域治理模式创新', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_3', name: '城市区域治理理论创新', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_4', name: '治理尺度的重构', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_5', name: '对传统地方政府合作困境的反思与超越', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_6', name: '公共问题逐渐突破行政管辖区的刚性约束', category: 7, symbolSize: 30 },

            { id: 'theory10_1', name: '基层社会局部治理体制改革', category: 5, symbolSize: 30 },
            { id: 'theory10_2', name: '区域公共产品的供给模式与机制', category: 5, symbolSize: 30 },
            { id: 'theory10_3', name: '城市区域治理模式、工具及其比较', category: 5, symbolSize: 30 },
            { id: 'theory10_4', name: '新区域主义', category: 5, symbolSize: 30 },
            { id: 'theory10_5', name: '公共选择学派', category: 5, symbolSize: 30 },
            { id: 'theory10_6', name: '传统区域主义', category: 5, symbolSize: 30 },
            { id: 'innovation10_1', name: '公共产品需求函数', category: 6, symbolSize: 30 },
            { id: 'innovation10_2', name: '城市区域治理的演进趋势及特点', category: 6, symbolSize: 30 },
            { id: 'innovation10_3', name: '数字技术赋能治理手段智能化', category: 6, symbolSize: 30 },
            { id: 'innovation10_4', name: '规则约束治理策略', category: 6, symbolSize: 30 },
            { id: 'innovation10_5', name: '摇号与拍卖', category: 6, symbolSize: 30 },
            { id: 'innovation10_6', name: '城市社区治理多元主体', category: 6, symbolSize: 30 },
            { id: 'viewpoint10_1', name: '庇古均衡模型', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_2', name: '智能化社区治理', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_3', name: '整合土地使用与交通规划', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_4', name: '差异化管理', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_5', name: '摊贩自我治理', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_6', name: '多样化与城市生态系统平衡', category: 7, symbolSize: 30 }

          ],
          links: [

            { source: 'chapter1', target: 'chapter1_theory', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_innovation', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_viewpoint', relation: 'contains' },

            { source: 'chapter2', target: 'chapter2_theory', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_innovation', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_viewpoint', relation: 'contains' },

            { source: 'chapter3', target: 'chapter3_theory', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_innovation', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_viewpoint', relation: 'contains' },

            { source: 'chapter4', target: 'chapter4_theory', relation: 'contains' },
            { source: 'chapter4', target: 'chapter4_innovation', relation: 'contains' },
            { source: 'chapter4', target: 'chapter4_viewpoint', relation: 'contains' },

            { source: 'chapter5', target: 'chapter5_theory', relation: 'contains' },
            { source: 'chapter5', target: 'chapter5_innovation', relation: 'contains' },
            { source: 'chapter5', target: 'chapter5_viewpoint', relation: 'contains' },

            { source: 'chapter6', target: 'chapter6_theory', relation: 'contains' },
            { source: 'chapter6', target: 'chapter6_innovation', relation: 'contains' },
            { source: 'chapter7', target: 'chapter6_viewpoint', relation: 'contains' },

            { source: 'chapter7', target: 'chapter7_theory', relation: 'contains' },
            { source: 'chapter7', target: 'chapter7_innovation', relation: 'contains' },
            { source: 'chapter7', target: 'chapter7_viewpoint', relation: 'contains' },

            { source: 'chapter8', target: 'chapter8_theory', relation: 'contains' },
            { source: 'chapter8', target: 'chapter8_innovation', relation: 'contains' },
            { source: 'chapter8', target: 'chapter8_viewpoint', relation: 'contains' },

            { source: 'chapter9', target: 'chapter9_theory', relation: 'contains' },
            { source: 'chapter9', target: 'chapter9_innovation', relation: 'contains' },
            { source: 'chapter9', target: 'chapter9_viewpoint', relation: 'contains' },

            { source: 'chapter10', target: 'chapter10_theory', relation: 'contains' },
            { source: 'chapter10', target: 'chapter10_innovation', relation: 'contains' },
            { source: 'chapter10', target: 'chapter10_viewpoint', relation: 'contains' },


            { source: 'chapter1_theory', target: 'theory1_1', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_2', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_3', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_4', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_5', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_6', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_1', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_2', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_3', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_4', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_5', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_6', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_1', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_2', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_3', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_4', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_5', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_6', relation: 'contains' },

            { source: 'chapter2_theory', target: 'theory2_1', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_2', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_3', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_4', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_5', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_6', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_1', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_2', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_3', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_4', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_5', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_6', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_1', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_2', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_3', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_4', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_5', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_6', relation: 'contains' },

            { source: 'chapter3_theory', target: 'theory3_1', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_2', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_3', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_4', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_5', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_6', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_1', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_2', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_3', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_4', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_5', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_6', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_1', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_2', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_3', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_4', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_5', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_6', relation: 'contains' },

            { source: 'chapter4_theory', target: 'theory4_1', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_2', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_3', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_4', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_5', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_6', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_1', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_2', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_3', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_4', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_5', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_6', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_1', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_2', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_3', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_4', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_5', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_6', relation: 'contains' },

            { source: 'chapter5_theory', target: 'theory5_1', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_2', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_3', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_4', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_5', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_6', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_1', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_2', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_3', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_4', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_5', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_6', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_1', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_2', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_3', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_4', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_5', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_6', relation: 'contains' },

            { source: 'chapter6_theory', target: 'theory6_1', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_2', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_3', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_4', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_5', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_6', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_1', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_2', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_3', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_4', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_5', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_6', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_1', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_2', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_3', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_4', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_5', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_6', relation: 'contains' },

            { source: 'chapter7_theory', target: 'theory7_1', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_2', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_3', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_4', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_5', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_6', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_1', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_2', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_3', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_4', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_5', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_6', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_1', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_2', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_3', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_4', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_5', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_6', relation: 'contains' },

            { source: 'chapter8_theory', target: 'theory8_1', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_2', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_3', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_4', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_5', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_6', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_1', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_2', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_3', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_4', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_5', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_6', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_1', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_2', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_3', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_4', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_5', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_6', relation: 'contains' },

            { source: 'chapter9_theory', target: 'theory9_1', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_2', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_3', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_4', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_5', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_6', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_1', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_2', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_3', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_4', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_5', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_6', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_1', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_2', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_3', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_4', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_5', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_6', relation: 'contains' },

            { source: 'chapter10_theory', target: 'theory10_1', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_2', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_3', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_4', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_5', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_6', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_1', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_2', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_3', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_4', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_5', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_6', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_1', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_2', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_3', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_4', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_5', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_6', relation: 'contains' },




            { source: 'theory1_1', target: 'theory1_2', relation: 'prerequisite' },
            { source: 'theory1_1', target: 'innovation2_1', relation: 'prerequisite' },
            { source: 'innovation1_1', target: 'viewpoint1_1', relation: 'prerequisite' },
            { source: 'innovation1_1', target: 'viewpoint2_1', relation: 'prerequisite' },

            { source: 'theory2_1', target: 'theory2_2', relation: 'association' },
            { source: 'theory2_1', target: 'innovation2_1', relation: 'prerequisite' },
            { source: 'theory2_1', target: 'innovation3_1', relation: 'association' },


            { source: 'theory3_1', target: 'theory3_2', relation: 'association' },
            { source: 'theory3_1', target: 'innovation4_1', relation: 'association' },
            { source: 'innovation4_1', target: 'viewpoint3_1', relation: 'association' },
            { source: 'innovation4_1', target: 'viewpoint4_1', relation: 'prerequisite' },

            { source: 'theory4_1', target: 'theory4_2', relation: 'association' },
            { source: 'theory4_1', target: 'innovation4_1', relation: 'association' },
            { source: 'theory4_1', target: 'innovation5_1', relation: 'parent-child' },

            { source: 'theory5_1', target: 'theory5_2', relation: 'association' },
            { source: 'theory5_1', target: 'innovation5_1', relation: 'association' },
            { source: 'theory5_1', target: 'innovation6_1', relation: 'association' },

            { source: 'theory6_1', target: 'theory6_2', relation: 'association' },
            { source: 'theory6_1', target: 'innovation6_1', relation: 'association' },
            { source: 'theory6_1', target: 'innovation7_1', relation: 'parent-child' },


            { source: 'theory7_1', target: 'theory7_2', relation: 'association' },
            { source: 'theory7_1', target: 'innovation7_1', relation: 'prerequisite' },
            { source: 'theory7_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation8_1', target: 'viewpoint7_1', relation: 'prerequisite' },

            { source: 'theory9_1', target: 'theory7_2', relation: 'association' },
            { source: 'theory9_1', target: 'innovation7_1', relation: 'prerequisite' },
            { source: 'theory9_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation10_1', target: 'viewpoint9_1', relation: 'prerequisite' },
            { source: 'innovation10_1', target: 'viewpoint9_1', relation: 'association' },


            { source: 'theory2_1', target: 'theory7_2', relation: 'parent-child' },
            { source: 'theory4_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation5_1', target: 'viewpoint9_1', relation: 'association' },
            { source: 'innovation6_1', target: 'viewpoint8_1', relation: 'prerequisite' },

            { source: 'theory4_1', target: 'theory2_2', relation: 'association' },
            { source: 'theory8_1', target: 'innovation7_1', relation: 'association' },
            { source: 'theory7_1', target: 'innovation9_1', relation: 'association' },

            { source: 'theory1_1', target: 'theory5_2', relation: 'parent-child' },
            { source: 'theory2_1', target: 'innovation9_1', relation: 'association' },
            { source: 'theory5_2', target: 'innovation10_1', relation: 'prerequisite' },
            { source: 'theory5_3', target: 'innovation10_2', relation: 'association' },
            { source: 'theory5_4', target: 'innovation10_3', relation: 'prerequisite' },
            { source: 'theory4_5', target: 'innovation9_4', relation: 'prerequisite' },
            { source: 'theory2_5', target: 'innovation8_4', relation: 'prerequisite' },
            { source: 'theory6_5', target: 'innovation7_4', relation: 'prerequisite' },
            { source: 'theory1_5', target: 'innovation6_4', relation: 'prerequisite' },
          ],
          categories: [
            { name: '课程' },
            { name: '章节' },
            { name: '基础理论' },
            { name: '创新趋势' },
            { name: '主要观点' },
            { name: '理论知识点' },
            { name: '创新知识点' },
            { name: '观点知识点' }
          ]
        };
      },
      getSocialSecurityData() {
        return {
          nodes: [
            { id: 'chapter1', name: '社会保障概述', category: 1, symbolSize: 50 },
            { id: 'chapter2', name: '社会保障的发展历程', category: 1, symbolSize: 50 },
            { id: 'chapter3', name: '社会保障的理论基础', category: 1, symbolSize: 50 },
            { id: 'chapter4', name: '社会保障模式国际比较', category: 1, symbolSize: 50 },
            { id: 'chapter5', name: '社会保险制度', category: 1, symbolSize: 50 },
            { id: 'chapter6', name: '社会救助与社会福利', category: 1, symbolSize: 50 },
            { id: 'chapter7', name: '社会保障基金管理', category: 1, symbolSize: 50 },
            { id: 'chapter8', name: '社会保障立法与管理体制', category: 1, symbolSize: 50 },
            { id: 'chapter9', name: '社会保障与经济社会发展', category: 1, symbolSize: 50 },
            { id: 'chapter10', name: '中国社会保障的改革与展望', category: 1, symbolSize: 50 },

            { id: 'chapter1_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter1_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter1_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter2_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter2_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter2_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter3_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter3_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter3_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter4_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter4_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter4_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter5_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter5_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter5_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter6_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter6_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter6_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter7_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter7_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter7_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter8_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter8_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter8_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter9_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter9_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter9_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter10_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter10_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter10_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },



            { id: 'theory1_1', name: '社会保障学', category: 5, symbolSize: 30 },
            { id: 'theory1_2', name: '国家干预主义', category: 5, symbolSize: 30 },
            { id: 'theory1_3', name: '新自由主义', category: 5, symbolSize: 30 },
            { id: 'theory1_4', name: '吉登斯“第三条道路”思想', category: 5, symbolSize: 30 },
            { id: 'theory1_5', name: '社会保障制度的改革与发展', category: 5, symbolSize: 30 },
            { id: 'theory1_6', name: '社会保障体系的完善与扩展', category: 5, symbolSize: 30 },
            { id: 'innovation1_1', name: '社会保障管理的信息化与智能化', category: 6, symbolSize: 30 },
            { id: 'innovation1_2', name: '社会保障服务的多元化与个性化', category: 6, symbolSize: 30 },
            { id: 'innovation1_3', name: '社会保障是现代国家文明的重要标志之一', category: 6, symbolSize: 30 },
            { id: 'innovation1_4', name: '社会保障发展史', category: 6, symbolSize: 30 },
            { id: 'innovation1_5', name: '社会保障制度变迁', category: 6, symbolSize: 30 },
            { id: 'innovation1_6', name: '社会保障影响因素', category: 6, symbolSize: 30 },
            { id: 'viewpoint1_1', name: '社会保障项目从普遍原则向选择原则转变', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_2', name: '从“福利国家”向“福利社会”模式转变', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_3', name: '从第一代福利向第二代福利转型', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_4', name: '社会保障发展阶段', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_5', name: '社会保障制度变迁因素', category: 7, symbolSize: 30 },
            { id: 'viewpoint1_6', name: '社会保障国际经验', category: 7, symbolSize: 30 },

            { id: 'theory2_1', name: '社会保障制度变迁规律', category: 5, symbolSize: 30 },
            { id: 'theory2_2', name: '国家干预主义', category: 5, symbolSize: 30 },
            { id: 'theory2_3', name: '新自由主义', category: 5, symbolSize: 30 },
            { id: 'theory2_4', name: '古典自由主义', category: 5, symbolSize: 30 },
            { id: 'theory2_5', name: '现代货币学派', category: 5, symbolSize: 30 },
            { id: 'theory2_6', name: '供给学派', category: 5, symbolSize: 30 },
            { id: 'innovation2_1', name: '公共选择理论', category: 6, symbolSize: 30 },
            { id: 'innovation2_2', name: '“第三条道路”理论', category: 6, symbolSize: 30 },
            { id: 'innovation2_3', name: '国家干预主义', category: 6, symbolSize: 30 },
            { id: 'innovation2_4', name: '从单一政府责任向多元供给主体转变', category: 6, symbolSize: 30 },
            { id: 'innovation2_5', name: '社会保障制度的改革与创新', category: 6, symbolSize: 30 },
            { id: 'innovation2_6', name: '社会保障法制建设', category: 6, symbolSize: 30 },
            { id: 'viewpoint2_1', name: '社会保障法律关系', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_2', name: '社会保障管理原则', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_3', name: '社会保障法的基本内容', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_4', name: '社会保障立法的基本原则', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_5', name: '社会保障管理的定义', category: 7, symbolSize: 30 },
            { id: 'viewpoint2_6', name: '社会保障法律管理的特征', category: 7, symbolSize: 30 },

            { id: 'theory3_1', name: '社会保障管理体制的概念和模式', category: 5, symbolSize: 30 },
            { id: 'theory3_2', name: '社会保障的行政实施阶段', category: 5, symbolSize: 30 },
            { id: 'theory3_3', name: '信息在现代社会保障管理', category: 5, symbolSize: 30 },
            { id: 'theory3_4', name: '社会保障法制建设的完善', category: 5, symbolSize: 30 },
            { id: 'theory3_5', name: '信息化在社会保障管理中的应用', category: 5, symbolSize: 30 },
            { id: 'theory3_6', name: '社会保障管理体制的发展与改革', category: 5, symbolSize: 30 },
            { id: 'innovation3_1', name: '社会养老保险', category: 6, symbolSize: 30 },
            { id: 'innovation3_2', name: '社会养老保险的特征', category: 6, symbolSize: 30 },
            { id: 'innovation3_3', name: '社会养老保险的基本原则', category: 6, symbolSize: 30 },
            { id: 'innovation3_4', name: '社会养老保险的模式', category: 6, symbolSize: 30 },
            { id: 'innovation3_5', name: '社会医疗保险', category: 6, symbolSize: 30 },
            { id: 'innovation3_6', name: '社会保障体系', category: 6, symbolSize: 30 },
            { id: 'viewpoint3_1', name: '医疗保险制度', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_2', name: '社会保险制度', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_3', name: '失业保险', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_4', name: '市场经济', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_5', name: '劳动风险', category: 7, symbolSize: 30 },
            { id: 'viewpoint3_6', name: '扩大失业保障的覆盖范围', category: 7, symbolSize: 30 },

            { id: 'theory4_1', name: '采取差别费率', category: 5, symbolSize: 30 },
            { id: 'theory4_2', name: '加大失业保险基金的征缴力度', category: 5, symbolSize: 30 },
            { id: 'theory4_3', name: '完善管理服务和信息化建设', category: 5, symbolSize: 30 },
            { id: 'theory4_4', name: '工伤保险定义', category: 5, symbolSize: 30 },
            { id: 'theory4_5', name: '工伤保险发展阶段', category: 5, symbolSize: 30 },
            { id: 'theory4_6', name: '工伤保险基本原则', category: 5, symbolSize: 30 },
            { id: 'innovation4_1', name: '工伤保险覆盖范围扩大', category: 6, symbolSize: 30 },
            { id: 'innovation4_2', name: '工伤预防与康复重视', category: 6, symbolSize: 30 },
            { id: 'innovation4_3', name: '工伤保险制度完善', category: 6, symbolSize: 30 },
            { id: 'innovation4_4', name: '工伤认定', category: 6, symbolSize: 30 },
            { id: 'innovation4_5', name: '工伤保险重要性', category: 6, symbolSize: 30 },
            { id: 'innovation4_6', name: '工伤预防与康复', category: 6, symbolSize: 30 },
            { id: 'viewpoint4_1', name: '工伤保险基金管理', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_2', name: '社会救助', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_3', name: '社会救助法制化', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_4', name: '多渠道筹资', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_5', name: '社会福利', category: 7, symbolSize: 30 },
            { id: 'viewpoint4_6', name: '社会福利特征', category: 7, symbolSize: 30 },

            { id: 'theory5_1', name: '社会保障立法', category: 5, symbolSize: 30 },
            { id: 'theory5_2', name: '社会保障执法', category: 5, symbolSize: 30 },
            { id: 'theory5_3', name: '社会保障司法', category: 5, symbolSize: 30 },
            { id: 'theory5_4', name: '社会保障守法', category: 5, symbolSize: 30 },
            { id: 'theory5_5', name: '社会保障管理服务一体化', category: 5, symbolSize: 30 },
            { id: 'theory5_6', name: '网格式社会保障公共服务体系', category: 5, symbolSize: 30 },
            { id: 'innovation5_1', name: '社会保障管理服务机构改革', category: 6, symbolSize: 30 },
            { id: 'innovation5_2', name: '社会保障信息系统建设', category: 6, symbolSize: 30 },
            { id: 'innovation5_3', name: '社会保障一站式服务', category: 6, symbolSize: 30 },
            { id: 'innovation5_4', name: '社会保障法治建设', category: 6, symbolSize: 30 },
            { id: 'innovation5_5', name: '社会保障立法原则', category: 6, symbolSize: 30 },
            { id: 'innovation5_6', name: '社会保障执法特征', category: 6, symbolSize: 30 },
            { id: 'viewpoint5_1', name: '社会保障司法制度', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_2', name: '社会保障守法重要性', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_3', name: '社会保障基金储备和待遇水平界定', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_4', name: '社会保障水平界定', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_5', name: '社会保障水平系数分类', category: 7, symbolSize: 30 },
            { id: 'viewpoint5_6', name: '社会保障支出与工资总额关系', category: 7, symbolSize: 30 },

            { id: 'theory6_1', name: '社会保障支出占财政支出比重', category: 5, symbolSize: 30 },
            { id: 'theory6_2', name: '社会保障支出占国内生产总值比重', category: 5, symbolSize: 30 },
            { id: 'theory6_3', name: '社会保障适度性研究', category: 5, symbolSize: 30 },
            { id: 'theory6_4', name: '设定社会保障适度标准', category: 5, symbolSize: 30 },
            { id: 'theory6_5', name: '社会保障适度水平测定模型应用', category: 5, symbolSize: 30 },
            { id: 'theory6_6', name: '社会保障适度和超度效果比较', category: 5, symbolSize: 30 },
            { id: 'innovation6_1', name: '社会保障整体经济效应分析', category: 6, symbolSize: 30 },
            { id: 'innovation6_2', name: '社会保障水平反映资金需求', category: 6, symbolSize: 30 },
            { id: 'innovation6_3', name: '社会保障水平对企业影响', category: 6, symbolSize: 30 },
            { id: 'innovation6_4', name: '社会保障水平与国民经济发展相关', category: 6, symbolSize: 30 },
            { id: 'innovation6_5', name: '社会保障水平影响生活质量', category: 6, symbolSize: 30 },
            { id: 'innovation6_6', name: '社会保障水平是体系运行指示器', category: 6, symbolSize: 30 },
            { id: 'viewpoint6_1', name: '社会保障支出范围与经济发展水平相关', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_2', name: '社会保障适度水平标准与测定', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_3', name: '社会保障制度', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_4', name: '优抚安置政策', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_5', name: '社会保障制度的完善', category: 7, symbolSize: 30 },
            { id: 'viewpoint6_6', name: '优抚安置政策的创新', category: 7, symbolSize: 30 },

            { id: 'theory7_1', name: '社会保障的重要性', category: 5, symbolSize: 30 },
            { id: 'theory7_2', name: '优抚安置的必要性', category: 5, symbolSize: 30 },
            { id: 'theory7_3', name: '社会保障制度', category: 5, symbolSize: 30 },
            { id: 'theory7_4', name: '优抚安置政策', category: 5, symbolSize: 30 },
            { id: 'theory7_5', name: '社会保障制度的完善', category: 5, symbolSize: 30 },
            { id: 'theory7_6', name: '优抚安置政策的优化', category: 5, symbolSize: 30 },
            { id: 'innovation7_1', name: '临时救助机制的健全', category: 6, symbolSize: 30 },
            { id: 'innovation7_2', name: '法律援助服务的拓展', category: 6, symbolSize: 30 },
            { id: 'innovation7_3', name: '慈善事业的规范化发展', category: 6, symbolSize: 30 },
            { id: 'innovation7_4', name: '最低生活保障的重要性', category: 6, symbolSize: 30 },
            { id: 'innovation7_5', name: '特困人员的救助需求', category: 6, symbolSize: 30 },
            { id: 'innovation7_6', name: '临时救助的必要性', category: 6, symbolSize: 30 },
            { id: 'viewpoint7_1', name: '法律援助的重要性', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_2', name: '慈善事业的社会功能', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_3', name: '健康风险', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_4', name: '公共卫生的定义与特征', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_5', name: '健康风险管理多元化、个性化', category: 7, symbolSize: 30 },
            { id: 'viewpoint7_6', name: '公共卫生国际合作与全球治理', category: 7, symbolSize: 30 },

            { id: 'theory8_1', name: '医疗保险支付方式改革创新', category: 5, symbolSize: 30 },
            { id: 'theory8_2', name: '生育保险制度完善扩展', category: 5, symbolSize: 30 },
            { id: 'theory8_3', name: '医疗救助与保障体系融合发展', category: 5, symbolSize: 30 },
            { id: 'theory8_4', name: '健康风险具有多重特性', category: 5, symbolSize: 30 },
            { id: 'theory8_5', name: '医疗保险提供经济风险保护', category: 5, symbolSize: 30 },
            { id: 'theory8_6', name: '生育保险有多重功能', category: 5, symbolSize: 30 },
            { id: 'innovation8_1', name: '医疗救助保障贫困人口医疗需求', category: 6, symbolSize: 30 },
            { id: 'innovation8_2', name: '健康风险', category: 6, symbolSize: 30 },
            { id: 'innovation8_3', name: '公共卫生的定义与特征', category: 6, symbolSize: 30 },
            { id: 'innovation8_4', name: '医疗保险的定义、功能和目标', category: 6, symbolSize: 30 },
            { id: 'innovation8_5', name: '生育保险的定义、功能和目标', category: 6, symbolSize: 30 },
            { id: 'innovation8_6', name: '医疗救助的定义、功能和目标', category: 6, symbolSize: 30 },
            { id: 'viewpoint8_1', name: '健康风险管理多元化和个性化', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_2', name: '公共卫生国际合作与全球治理', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_3', name: '生育保险制度完善扩展', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_4', name: '医疗救助与医保体系融合', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_5', name: '健康风险具有多重特性。', category: 7, symbolSize: 30 },
            { id: 'viewpoint8_6', name: '公共卫生旨在改善环境、预防疾病。', category: 7, symbolSize: 30 },

            { id: 'theory9_1', name: '医疗保险提供经济风险保护。', category: 5, symbolSize: 30 },
            { id: 'theory9_2', name: '生育保险有多重功能。', category: 5, symbolSize: 30 },
            { id: 'theory9_3', name: '医疗救助保障贫困人口医疗需求。', category: 5, symbolSize: 30 },
            { id: 'theory9_4', name: '职业风险', category: 5, symbolSize: 30 },
            { id: 'theory9_5', name: '工伤风险', category: 5, symbolSize: 30 },
            { id: 'theory9_6', name: '失业风险', category: 5, symbolSize: 30 },
            { id: 'innovation9_1', name: '职业风险与工业化发展密切相关', category: 6, symbolSize: 30 },
            { id: 'innovation9_2', name: '工伤保险和失业保险是应对机制', category: 6, symbolSize: 30 },
            { id: 'innovation9_3', name: '失业保险保障生活并促进就业', category: 6, symbolSize: 30 },
            { id: 'innovation9_4', name: '职业风险管理多元化', category: 6, symbolSize: 30 },
            { id: 'innovation9_5', name: '失业保险制度完善', category: 6, symbolSize: 30 },
            { id: 'innovation9_6', name: '公共就业服务提升', category: 6, symbolSize: 30 },
            { id: 'viewpoint9_1', name: '职业风险概念界定', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_2', name: '职业风险应对机制', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_3', name: '工伤预防和康复的重视', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_4', name: '失业保险制度的改革', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_5', name: '公共就业服务和职业教育培训的加强', category: 7, symbolSize: 30 },
            { id: 'viewpoint9_6', name: '工伤与工伤风险特征', category: 7, symbolSize: 30 },

            { id: 'theory10_1', name: '失业及其类型', category: 5, symbolSize: 30 },
            { id: 'theory10_2', name: '工伤保险特征与功能', category: 5, symbolSize: 30 },
            { id: 'theory10_3', name: '失业保险制度产生原因', category: 5, symbolSize: 30 },
            { id: 'theory10_4', name: '失业保险制度构成要件', category: 5, symbolSize: 30 },
            { id: 'theory10_5', name: '老残风险保障', category: 5, symbolSize: 30 },
            { id: 'theory10_6', name: '老年风险', category: 5, symbolSize: 30 },
            { id: 'innovation10_1', name: '残疾风险', category: 6, symbolSize: 30 },
            { id: 'innovation10_2', name: '人口老龄化', category: 6, symbolSize: 30 },
            { id: 'innovation10_3', name: '社会分工协作', category: 6, symbolSize: 30 },
            { id: 'innovation10_4', name: '老残保障基本理念', category: 6, symbolSize: 30 },
            { id: 'innovation10_5', name: '老残保障意义', category: 6, symbolSize: 30 },
            { id: 'innovation10_6', name: '老残风险保障制度完善', category: 6, symbolSize: 30 },
            { id: 'viewpoint10_1', name: '老残护理服务模式创新', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_2', name: '老年福利制度普遍化', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_3', name: '残疾人福利制度模式转变', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_4', name: '老残保障分类多样', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_5', name: '老残护理服务模式多样', category: 7, symbolSize: 30 },
            { id: 'viewpoint10_6', name: '老年福利特点普遍、多样', category: 7, symbolSize: 30 }

          ],
          links: [

            { source: 'chapter1', target: 'chapter1_theory', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_innovation', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_viewpoint', relation: 'contains' },

            { source: 'chapter2', target: 'chapter2_theory', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_innovation', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_viewpoint', relation: 'contains' },

            { source: 'chapter3', target: 'chapter3_theory', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_innovation', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_viewpoint', relation: 'contains' },

            { source: 'chapter4', target: 'chapter4_theory', relation: 'contains' },
            { source: 'chapter4', target: 'chapter4_innovation', relation: 'contains' },
            { source: 'chapter4', target: 'chapter4_viewpoint', relation: 'contains' },

            { source: 'chapter5', target: 'chapter5_theory', relation: 'contains' },
            { source: 'chapter5', target: 'chapter5_innovation', relation: 'contains' },
            { source: 'chapter5', target: 'chapter5_viewpoint', relation: 'contains' },

            { source: 'chapter6', target: 'chapter6_theory', relation: 'contains' },
            { source: 'chapter6', target: 'chapter6_innovation', relation: 'contains' },
            { source: 'chapter7', target: 'chapter6_viewpoint', relation: 'contains' },

            { source: 'chapter7', target: 'chapter7_theory', relation: 'contains' },
            { source: 'chapter7', target: 'chapter7_innovation', relation: 'contains' },
            { source: 'chapter7', target: 'chapter7_viewpoint', relation: 'contains' },

            { source: 'chapter8', target: 'chapter8_theory', relation: 'contains' },
            { source: 'chapter8', target: 'chapter8_innovation', relation: 'contains' },
            { source: 'chapter8', target: 'chapter8_viewpoint', relation: 'contains' },

            { source: 'chapter9', target: 'chapter9_theory', relation: 'contains' },
            { source: 'chapter9', target: 'chapter9_innovation', relation: 'contains' },
            { source: 'chapter9', target: 'chapter9_viewpoint', relation: 'contains' },

            { source: 'chapter10', target: 'chapter10_theory', relation: 'contains' },
            { source: 'chapter10', target: 'chapter10_innovation', relation: 'contains' },
            { source: 'chapter10', target: 'chapter10_viewpoint', relation: 'contains' },


            { source: 'chapter1_theory', target: 'theory1_1', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_2', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_3', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_4', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_5', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_6', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_1', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_2', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_3', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_4', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_5', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_6', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_1', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_2', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_3', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_4', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_5', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_6', relation: 'contains' },

            { source: 'chapter2_theory', target: 'theory2_1', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_2', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_3', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_4', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_5', relation: 'contains' },
            { source: 'chapter2_theory', target: 'theory2_6', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_1', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_2', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_3', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_4', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_5', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_6', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_1', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_2', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_3', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_4', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_5', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_6', relation: 'contains' },

            { source: 'chapter3_theory', target: 'theory3_1', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_2', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_3', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_4', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_5', relation: 'contains' },
            { source: 'chapter3_theory', target: 'theory3_6', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_1', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_2', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_3', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_4', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_5', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_6', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_1', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_2', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_3', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_4', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_5', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_6', relation: 'contains' },

            { source: 'chapter4_theory', target: 'theory4_1', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_2', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_3', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_4', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_5', relation: 'contains' },
            { source: 'chapter4_theory', target: 'theory4_6', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_1', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_2', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_3', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_4', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_5', relation: 'contains' },
            { source: 'chapter4_innovation', target: 'innovation4_6', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_1', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_2', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_3', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_4', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_5', relation: 'contains' },
            { source: 'chapter4_viewpoint', target: 'viewpoint4_6', relation: 'contains' },

            { source: 'chapter5_theory', target: 'theory5_1', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_2', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_3', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_4', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_5', relation: 'contains' },
            { source: 'chapter5_theory', target: 'theory5_6', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_1', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_2', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_3', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_4', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_5', relation: 'contains' },
            { source: 'chapter5_innovation', target: 'innovation5_6', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_1', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_2', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_3', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_4', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_5', relation: 'contains' },
            { source: 'chapter5_viewpoint', target: 'viewpoint5_6', relation: 'contains' },

            { source: 'chapter6_theory', target: 'theory6_1', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_2', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_3', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_4', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_5', relation: 'contains' },
            { source: 'chapter6_theory', target: 'theory6_6', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_1', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_2', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_3', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_4', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_5', relation: 'contains' },
            { source: 'chapter6_innovation', target: 'innovation6_6', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_1', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_2', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_3', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_4', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_5', relation: 'contains' },
            { source: 'chapter6_viewpoint', target: 'viewpoint6_6', relation: 'contains' },

            { source: 'chapter7_theory', target: 'theory7_1', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_2', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_3', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_4', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_5', relation: 'contains' },
            { source: 'chapter7_theory', target: 'theory7_6', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_1', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_2', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_3', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_4', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_5', relation: 'contains' },
            { source: 'chapter7_innovation', target: 'innovation7_6', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_1', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_2', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_3', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_4', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_5', relation: 'contains' },
            { source: 'chapter7_viewpoint', target: 'viewpoint7_6', relation: 'contains' },

            { source: 'chapter8_theory', target: 'theory8_1', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_2', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_3', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_4', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_5', relation: 'contains' },
            { source: 'chapter8_theory', target: 'theory8_6', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_1', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_2', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_3', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_4', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_5', relation: 'contains' },
            { source: 'chapter8_innovation', target: 'innovation8_6', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_1', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_2', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_3', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_4', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_5', relation: 'contains' },
            { source: 'chapter8_viewpoint', target: 'viewpoint8_6', relation: 'contains' },

            { source: 'chapter9_theory', target: 'theory9_1', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_2', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_3', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_4', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_5', relation: 'contains' },
            { source: 'chapter9_theory', target: 'theory9_6', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_1', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_2', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_3', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_4', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_5', relation: 'contains' },
            { source: 'chapter9_innovation', target: 'innovation9_6', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_1', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_2', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_3', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_4', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_5', relation: 'contains' },
            { source: 'chapter9_viewpoint', target: 'viewpoint9_6', relation: 'contains' },

            { source: 'chapter10_theory', target: 'theory10_1', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_2', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_3', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_4', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_5', relation: 'contains' },
            { source: 'chapter10_theory', target: 'theory10_6', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_1', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_2', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_3', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_4', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_5', relation: 'contains' },
            { source: 'chapter10_innovation', target: 'innovation10_6', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_1', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_2', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_3', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_4', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_5', relation: 'contains' },
            { source: 'chapter10_viewpoint', target: 'viewpoint10_6', relation: 'contains' },




            { source: 'theory1_1', target: 'theory1_2', relation: 'prerequisite' },
            { source: 'theory1_1', target: 'innovation2_1', relation: 'prerequisite' },
            { source: 'innovation1_1', target: 'viewpoint1_1', relation: 'prerequisite' },
            { source: 'innovation1_1', target: 'viewpoint2_1', relation: 'prerequisite' },

            { source: 'theory2_1', target: 'theory2_2', relation: 'association' },
            { source: 'theory2_1', target: 'innovation2_1', relation: 'prerequisite' },
            { source: 'theory2_1', target: 'innovation3_1', relation: 'association' },


            { source: 'theory3_1', target: 'theory3_2', relation: 'association' },
            { source: 'theory3_1', target: 'innovation4_1', relation: 'association' },
            { source: 'innovation4_1', target: 'viewpoint3_1', relation: 'association' },
            { source: 'innovation4_1', target: 'viewpoint4_1', relation: 'prerequisite' },

            { source: 'theory4_1', target: 'theory4_2', relation: 'association' },
            { source: 'theory4_1', target: 'innovation4_1', relation: 'association' },
            { source: 'theory4_1', target: 'innovation5_1', relation: 'parent-child' },

            { source: 'theory5_1', target: 'theory5_2', relation: 'association' },
            { source: 'theory5_1', target: 'innovation5_1', relation: 'association' },
            { source: 'theory5_1', target: 'innovation6_1', relation: 'association' },

            { source: 'theory6_1', target: 'theory6_2', relation: 'association' },
            { source: 'theory6_1', target: 'innovation6_1', relation: 'association' },
            { source: 'theory6_1', target: 'innovation7_1', relation: 'parent-child' },


            { source: 'theory7_1', target: 'theory7_2', relation: 'association' },
            { source: 'theory7_1', target: 'innovation7_1', relation: 'prerequisite' },
            { source: 'theory7_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation8_1', target: 'viewpoint7_1', relation: 'prerequisite' },

            { source: 'theory9_1', target: 'theory7_2', relation: 'association' },
            { source: 'theory9_1', target: 'innovation7_1', relation: 'prerequisite' },
            { source: 'theory9_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation10_1', target: 'viewpoint9_1', relation: 'prerequisite' },
            { source: 'innovation10_1', target: 'viewpoint9_1', relation: 'association' },


            { source: 'theory2_1', target: 'theory7_2', relation: 'parent-child' },
            { source: 'theory4_1', target: 'innovation8_1', relation: 'association' },
            { source: 'innovation5_1', target: 'viewpoint9_1', relation: 'association' },
            { source: 'innovation6_1', target: 'viewpoint8_1', relation: 'prerequisite' },

            { source: 'theory4_1', target: 'theory2_2', relation: 'association' },
            { source: 'theory8_1', target: 'innovation7_1', relation: 'association' },
            { source: 'theory7_1', target: 'innovation9_1', relation: 'association' },

            { source: 'theory1_1', target: 'theory5_2', relation: 'parent-child' },
            { source: 'theory2_1', target: 'innovation9_1', relation: 'association' },
            { source: 'theory5_2', target: 'innovation10_1', relation: 'prerequisite' },
            { source: 'theory5_3', target: 'innovation10_2', relation: 'association' },
            { source: 'theory5_4', target: 'innovation10_3', relation: 'prerequisite' },
            { source: 'theory4_5', target: 'innovation9_4', relation: 'prerequisite' },
            { source: 'theory2_5', target: 'innovation8_4', relation: 'prerequisite' },
            { source: 'theory6_5', target: 'innovation7_4', relation: 'prerequisite' },
            { source: 'theory1_5', target: 'innovation6_4', relation: 'prerequisite' },
          ],
          categories: [
            { name: '课程' },
            { name: '章节' },
            { name: '基础理论' },
            { name: '创新趋势' },
            { name: '主要观点' },
            { name: '理论知识点' },
            { name: '创新知识点' },
            { name: '观点知识点' }
          ]
        };
      },
      getDefaultData() {
        return {
          nodes: [
            { id: 'chapter1', name: '第一章', category: 1, symbolSize: 50 },
            { id: 'chapter2', name: '第二章', category: 1, symbolSize: 50 },
            { id: 'chapter3', name: '第三章', category: 1, symbolSize: 50 },

            { id: 'chapter1_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter1_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter1_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter2_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter2_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter2_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'chapter3_theory', name: '基础理论', category: 2, symbolSize: 40 },
            { id: 'chapter3_innovation', name: '创新趋势', category: 3, symbolSize: 40 },
            { id: 'chapter3_viewpoint', name: '主要观点', category: 4, symbolSize: 40 },

            { id: 'theory1_1', name: '理论知识点1', category: 5, symbolSize: 30 },
            { id: 'theory1_2', name: '理论知识点2', category: 5, symbolSize: 30 },
            { id: 'innovation1_1', name: '创新知识点1', category: 6, symbolSize: 30 },
            { id: 'viewpoint1_1', name: '观点知识点1', category: 7, symbolSize: 30 },

            { id: 'theory2_1', name: '理论知识点3', category: 5, symbolSize: 30 },
            { id: 'innovation2_1', name: '创新知识点2', category: 6, symbolSize: 30 },
            { id: 'viewpoint2_1', name: '观点知识点2', category: 7, symbolSize: 30 },

            { id: 'theory3_1', name: '理论知识点4', category: 5, symbolSize: 30 },
            { id: 'innovation3_1', name: '创新知识点3', category: 6, symbolSize: 30 },
            { id: 'viewpoint3_1', name: '观点知识点3', category: 7, symbolSize: 30 }
          ],
          links: [

            { source: 'chapter1', target: 'chapter1_theory', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_innovation', relation: 'contains' },
            { source: 'chapter1', target: 'chapter1_viewpoint', relation: 'contains' },

            { source: 'chapter2', target: 'chapter2_theory', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_innovation', relation: 'contains' },
            { source: 'chapter2', target: 'chapter2_viewpoint', relation: 'contains' },

            { source: 'chapter3', target: 'chapter3_theory', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_innovation', relation: 'contains' },
            { source: 'chapter3', target: 'chapter3_viewpoint', relation: 'contains' },

            { source: 'chapter1_theory', target: 'theory1_1', relation: 'contains' },
            { source: 'chapter1_theory', target: 'theory1_2', relation: 'contains' },
            { source: 'chapter1_innovation', target: 'innovation1_1', relation: 'contains' },
            { source: 'chapter1_viewpoint', target: 'viewpoint1_1', relation: 'contains' },

            { source: 'chapter2_theory', target: 'theory2_1', relation: 'contains' },
            { source: 'chapter2_innovation', target: 'innovation2_1', relation: 'contains' },
            { source: 'chapter2_viewpoint', target: 'viewpoint2_1', relation: 'contains' },

            { source: 'chapter3_theory', target: 'theory3_1', relation: 'contains' },
            { source: 'chapter3_innovation', target: 'innovation3_1', relation: 'contains' },
            { source: 'chapter3_viewpoint', target: 'viewpoint3_1', relation: 'contains' },

            { source: 'theory1_1', target: 'theory1_2', relation: 'parent-child' },
            { source: 'theory1_1', target: 'innovation1_1', relation: 'association' },
            { source: 'innovation2_1', target: 'viewpoint2_1', relation: 'prerequisite' },
          ],
          categories: [
            { name: '课程' },
            { name: '章节' },
            { name: '基础理论' },
            { name: '创新趋势' },
            { name: '主要观点' },
            { name: '理论知识点' },
            { name: '创新知识点' },
            { name: '观点知识点' }
          ]
        };
      },
      initChart() {
        const chartDom = document.getElementById('main');
        this.myChart = echarts.init(chartDom);

        // 处理连线样式
        const styledLinks = this.graphData.links.map(link => {
          const style = this.relationshipStyles[link.relation] || this.relationshipStyles.contains;
          return {
            ...link,
            lineStyle: {
              type: style.type,
              color: style.color,
              width: 2,
              curveness: 0.1
            }
          };
        });

        // 1. 组织节点数据（过滤掉课程节点）
        const nodes = JSON.parse(JSON.stringify(this.graphData.nodes))
          .filter(node => node.id !== 'course');
        const links = JSON.parse(JSON.stringify(styledLinks))
          .filter(link => link.source !== 'course' && link.target !== 'course');

        // 2. 按章节分组
        const chapters = nodes.filter(n => n.category === 1);
        const chapterGroups = chapters.map(chapter => {
          const chapterId = chapter.id;
          return {
            chapter,
            categories: nodes.filter(n =>
              n.id.startsWith(chapterId) && n.category >= 2 && n.category <= 4
            ),
            knowledge: nodes.filter(n =>
              n.id.startsWith(chapterId) && n.category >= 5
            )
          };
        });

        // 3. 计算章节环形布局
        const groupRadius = 200; // 章节组中心到原点的距离
        const groupAngleStep = (2 * Math.PI) / chapterGroups.length;

        // 4. 为每个章节组设置位置
        chapterGroups.forEach((group, groupIndex) => {
          // 章节组中心位置
          const groupAngle = groupAngleStep * groupIndex;
          const groupCenterX = groupRadius * Math.cos(groupAngle);
          const groupCenterY = groupRadius * Math.sin(groupAngle);

          // 章节节点位置（组中心）
          group.chapter.x = groupCenterX;
          group.chapter.y = groupCenterY;
          group.chapter.fixed = false;

          // 分类节点位置（围绕章节的小环形）
          const categoryRadius = 80;
          const categoryAngleStep = (2 * Math.PI) / group.categories.length;

          group.categories.forEach((category, catIndex) => {
            const angle = categoryAngleStep * catIndex;
            category.x = groupCenterX + categoryRadius * Math.cos(angle);
            category.y = groupCenterY + categoryRadius * Math.sin(angle);
            category.fixed = false;
          });

          // 知识点位置（围绕各自分类）
          group.knowledge.forEach(knowledge => {
            const categoryId = knowledge.id.split('_').slice(0, 2).join('_');
            const category = group.categories.find(c => c.id === categoryId);
            if (category) {
              const radius = 50 + Math.random() * 20;
              const angle = Math.random() * 2 * Math.PI;
              knowledge.x = category.x + radius * Math.cos(angle);
              knowledge.y = category.y + radius * Math.sin(angle);
              knowledge.fixed = false;
            }
          });
        });

        // 5. 配置图表选项
        const option = {
          tooltip: {},
          series: [{
            name: '知识图谱',
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: 800,        // 增大排斥力，防止节点重叠
              edgeLength: 200,       // 边长增加，拉开距离
              gravity: 0.05,         // 稍微增强吸引力，避免漂太远
              friction: 0.7,         // 减缓运动速度，提高稳定性
              layoutAnimation: true,

              // 根据关系强弱动态设置边的强度
              edgeStrength: (edge) => {
                const source = edge.data.source;
                const target = edge.data.target;

                // 如果是父子关系（如章节 → 分类），加强连接
                if (
                  source.startsWith('chapter') &&
                  (target.includes('_theory') || target.includes('_innovation') || target.includes('_viewpoint'))
                ) {
                  return 0.9; // 强连接
                }

                // 如果是分类 → 知识点，稍微弱一点
                if (
                  (source.includes('_theory') || source.includes('_innovation') || source.includes('_viewpoint')) &&
                  !target.startsWith('chapter')
                ) {
                  return 0.6; // 中等连接
                }

                // 其他关系较弱
                return 0.3;
              },

              // 自定义节点引力，让章节更强地吸引其它节点
              nodeStrength: (node) => {
                if (node.data.category === 1) {
                  return -80; // 章节节点具有更强的吸引力（负值表示吸引力）
                }
                return -40; // 其它节点吸引力稍弱
              }
            },
            data: nodes,
            links: links,
            categories: this.graphData.categories.filter(c => c.name !== '课程'),
            roam: true,
            label: {
              show: true,
              position: 'right',
              formatter: params => {
                const maxLength = 8;
                return params.data.name.length > maxLength
                  ? params.data.name.substring(0, maxLength) + '...'
                  : params.data.name;
              },
              fontSize: 12
            },
            lineStyle: {
              opacity: 0.9,
              width: 1.5,
              curveness: 0.1
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 2.5
              }
            },
            draggable: true,
            symbolSize: (value, params) => params.data.symbolSize,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            }
          }]
        };

        // 6. 渲染图表
        this.myChart.setOption(option);

        // 7. 添加交互事件
        this.myChart.on('click', params => {
          if (params.dataType === 'node' && params.data.category === 1) {
            this.recenterGraph(params.data.id);
          }
        });
      },
      recenterGraph(centerId) {
        if (this.currentCenter === centerId) return;

        this.currentCenter = centerId;

        const newNodes = JSON.parse(JSON.stringify(this.graphData.nodes));
        const centerNode = newNodes.find(n => n.id === centerId);

        newNodes.forEach(node => {
          if (node.id === centerId) {
            node.fixed = true;
            node.x = 0;
            node.y = 0;
            node.symbolSize = 70;
          } else {
            node.fixed = false;
            if (node.category === 1) {
              node.symbolSize = 50;
            } else {
              node.symbolSize = node.category >= 5 ? 30 : 40;
            }
          }
        });

        this.myChart.setOption({
          series: [{
            data: newNodes
          }]
        });
      },
      handleResize() {
        if (this.myChart) {
          this.myChart.resize();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tab-content {
    padding: 0;
    margin: 0 auto;
    width: 100%;
    height: calc(100vh - 84px);
    background: #f5f7fa;
    position: relative;
  }

  .main {
    width: 100%;
    height: 100%;
  }

  .relationship-legend {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;

    .legend-title {
      font-weight: bold;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      margin: 5px 0;

      .line-sample {
        width: 40px;
        height: 2px;
        margin-right: 8px;

        &.solid-line {
          border-top: 2px solid #747474;
        }

        &.dashed-line {
          border-top: 2px dashed #00aa00;
        }

        &.dotted-line {
          border-top: 2px dotted #0000ff;
        }
      }

      span {
        font-size: 13px;
      }
    }
  }
</style>
