<!-- 教案 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="教案名称" prop="lessonPlanName">
        <el-input v-model="queryParams.lessonPlanName" placeholder="请输入教案名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="teachingPlanList">
      <el-table-column label="教案名称" prop="lessonPlanName" min-width="150" />
      <el-table-column label="文件名称" prop="fileOriginName" min-width="150">
        <template slot-scope="scope">
             <el-link type="primary" @click="handleDownLoadUrl(scope.row)" :disabled=disabledConfirmDownload>
               {{ scope.row.fileOriginName }}
             </el-link>
        </template>
      </el-table-column>>
      <el-table-column label="创建人" prop="createByName" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="600px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="100px">
        <el-form-item label="教案名称" prop="lessonPlanName">
          <el-input v-model="form.lessonPlanName" />
        </el-form-item>
        <el-form-item label="教案" prop="keyword">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers"
            :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept=".pptx,.ppt,.pdf,.doc,.docx,.txt,.mp4"
            :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持ppt,pdf,doc,docx,txt,mp4文件上传,仅限一个文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMaterialList,
  addTeacherPlan,
  delMaterials,
} from "@/api/courseManagement/courseManagement.js";
import { getToken } from "@/utils/auth";
import {throttle} from "lodash";
export default {
  name: "TeachingPlanList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      teachingPlanList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: '1',
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        studentClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.id,
      },
      title: '创建教案',
      form: {
        type: '1',
        studentCourseId: this.$route.query && this.$route.query.id,
      },
      rules: {
        lessonPlanName: [
          { required: true, message: '请输入教案名称', trigger: ['blur', 'change'] },
        ],
      },
      dialogOpen: false,
      btnLoading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'courseManagement' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      disabledConfirmDownload: false
    };
  },
  watch: {
    // 监视搜索词变化
    "$route.query.id": {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.studentCourseId = newValue
          this.queryParams.studentCourseId = newValue
          this.getList()
        }
      },
    },
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getMaterialList(this.queryParams).then((response) => {
        this.teachingPlanList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialogOpen = true;
      this.title = '创建教案';
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delMaterials(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm()) {
        if (this.form.fileObjectName && this.form.fileObjectName != '') {
          addTeacherPlan(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('创建成功')
              this.handleClose()
              this.btnLoading = false
            } else {
              this.btnLoading = false
            }
          }).catch(() => {
            this.btnLoading = false
          })
        } else {
          this.$message.warning('请上传教案')
          this.btnLoading = false
        }
      } else {

        this.btnLoading = false
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      this.form = {
        type: '1',
        studentCourseId: this.$route.query && this.$route.query.id,
      }
      this.resetForm("form");
      this.fileList = []
      this.dialogOpen = false
      this.getList()
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleUploadSuccess(res, file) {
      this.form.fileObjectName = res.data.id
      this.fileList.push(file)
    },
    handleRemove(file, fileList) {
      this.form.fileObjectName = null
      this.fileList = []
    },

    handleDownLoadUrl(row) {
      let filePathUrl = row.filePathUrl;
      let name = row.fileOriginName;
      this.disabledConfirmDownload = true
      this.handleDownLoad2(filePathUrl, name)
    },
    async handleDownLoad2(fiePathUrl, name) {
      let notification = null // 用于存储通知实例
      try {
        const response = await fetch(fiePathUrl)
        if (!response.ok) {
          this.disabledConfirmDownload = false
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const contentLength = +response.headers.get('Content-Length')

        let receivedLength = 0 // 当前接收到的字节长度
        const chunks = [] // 用来保存接收到的数据块

        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          chunks.push(value)
          receivedLength += value.length

          // 你可以在这里更新下载进度显示
          // console.log(`Received ${((receivedLength / contentLength) * 100).toFixed(2)}%`);
          // 计算下载百分比
          const percent = ((receivedLength / contentLength) * 100).toFixed(2)
          // 实时更新通知内容
          notification = this.updateDownloadNotification(percent, null, notification)
        }

        // 将接收到的数据块合并为一个 Blob
        const blob = new Blob(chunks)
        const url = window.URL.createObjectURL(blob)

        // 创建一个隐藏的链接元素进行下载
        setTimeout(() => {
          const elink = document.createElement('a')
          elink.href = url
          elink.download = name
          document.body.appendChild(elink)
          elink.click()
          document.body.removeChild(elink)
          // 释放 URL 对象
          window.URL.revokeObjectURL(url)
          this.disabledConfirmDownload = false
        }, 800)

        // 下载完成后更新通知内容并关闭通知
        if (notification != null) {
          notification.title = '下载完成'
          notification.message = '文件已成功下载'
          notification.type = 'success'
          setTimeout(() => {
            this.$notify.closeAll()
          }, 3000)
        }

      } catch (error) {
        this.disabledConfirmDownload = false
        console.error('Download error:', error)

        // 出现错误时更新通知内容
        if (notification != null) {
          notification.title = '下载失败'
          notification.message = '文件下载时出现错误，请稍后再试。'
          notification.type = 'error'
        }

        setTimeout(() => {
          this.$notify.closeAll()
        }, 4000)
      }
    },
    updateDownloadNotification: throttle(function (percent, message, notification = null) {
      if (!notification) {
        // 如果没有现有通知，创建一个新通知
        return this.$notify({
          title: '下载中',
          message: `已下载 ${percent}%`,
          duration: 0,  // 设置为0，防止自动关闭
          type: 'info'
        });
      } else {
        // 更新现有通知
        notification.message = message || `已下载 ${percent}%`;
        return notification;
      }
    }, 500), // 每500ms更新一次
  },
};
</script>
<style lang="scss">
</style>



