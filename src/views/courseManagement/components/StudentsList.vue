<!-- 学生 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="学号" prop="studentId">
        <el-input v-model="queryParams.studentId" placeholder="请输入学号" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="studentName">
        <el-input v-model="queryParams.studentName" placeholder="请输入姓名" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="班级" prop="studentClass">
        <el-input v-model="queryParams.studentClass" placeholder="请输入班级" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="courseManagementList">
      <el-table-column label="课程名称" prop="courseName" :show-overflow-tooltip="true" min-width="140" />
      <el-table-column label="学期" prop="term" :show-overflow-tooltip="true" min-width="120">
        <template slot-scope="scope">
          <span>{{ termChange(scope.row.term) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="必修/选修" prop="courseType" min-width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.courseType=='0'?'必修':'选修' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学号" prop="studentId" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="姓名" prop="studentName" min-width="100">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleAnalyse(scope.row)">
            {{ scope.row.studentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="班级" prop="studentClass" :show-overflow-tooltip="true" min-width="180" />
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { getCourseInfo, } from "@/api/courseManagement/courseManagement.js";
export default {
  name: "StudentsList",
  dicts: ["semester"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程管理表格数据
      courseManagementList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        // studentClass: this.$route.query && this.$route.query.studentClass,
        id: this.$route.query && this.$route.query.id,
      },
    };
  },
  watch: {
    // 监视搜索词变化
    "$route.query.id": {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.id = newValue
          this.queryParams.id = newValue
          this.getList()
        }
      },
    },
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询课程管理列表 */
    getList() {
      this.loading = true;
      getCourseInfo(this.queryParams).then((response) => {
        this.courseManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    termChange(term) {
      return this.selectDictLabelByVal(
        this.dict.type.semester,
        term
      );
    },
    // 跳转课程分析
    handleAnalyse(row) {
      this.$router.push({
        path: "/courseManagement/courseAnalysis",
        query: {
          gradeFlag: false,
          courseName: row.courseName,
          studentName: row.studentName,
          id: row.id,
          studentId: row.studentId
        },
      });
    }
  },
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
