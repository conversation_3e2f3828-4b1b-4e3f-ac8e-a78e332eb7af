<!-- 讨论 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="discussList">
      <el-table-column label="标题" prop="title" min-width="120" :show-overflow-tooltip="true" />
      <el-table-column label="内容" prop="content" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="创建人" prop="sendName" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="800px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input type="textarea" :rows="5" v-model="form.content" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDiscussList,
  addDiscuss,
  delDiscuss,
} from "@/api/courseManagement/courseManagement.js";
export default {
  name: "DiscussList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      discussList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        studentClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.id,
      },
      title: '创建讨论',
      form: {
        studentCourseId: this.$route.query && this.$route.query.id,
      },
      rules: {
        title: [
          { required: true, message: '请输入讨论标题', trigger: ['blur', 'change'] },
        ],
        content: [
          { required: true, message: '请输入讨论内容', trigger: ['blur', 'change'] },
        ],
      },
      dialogOpen: false,
      btnLoading: false,
    };
  },
  watch: {
    // 监视搜索词变化
    "$route.query.id": {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.studentCourseId = newValue
          this.queryParams.studentCourseId = newValue
          this.getList()
        }
      },
    },
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getDiscussList(this.queryParams).then((response) => {
        this.discussList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialogOpen = true;
      this.title = '创建讨论';
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delDiscuss(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm()) {
        addDiscuss(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('创建成功')
            this.handleClose()
            this.btnLoading = false
          } else {
            this.btnLoading = false
          }
        }).catch(() => {
          this.btnLoading = false
        })
      } else {
        this.btnLoading = false
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      this.form = {
        studentCourseId: this.$route.query && this.$route.query.id,
      }
      this.resetForm("form");
      this.dialogOpen = false
      this.getList()
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/courseManagement/discussion",
        query: { topicId: row.id },
      });
    },
  },
};
</script>
<style lang="scss">
.ck-input {
  width: 300px;
}
</style>



