<!-- 签到 -->
<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">发起签到</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="signInList">
      <el-table-column label="课程" prop="courseName" min-width="150" />
      <el-table-column label="班级" prop="signClass" min-width="150" />
      <el-table-column label="开始时间" align="center" prop="startTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="600px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="80px">
        <el-form-item label="课程" prop="courseName">
          <el-input class="ck-input" v-model="form.courseName" disabled />
        </el-form-item>
        <el-form-item label="学期" prop="term">
          <el-select class="ck-input" v-model=" form.term" filterable placeholder="请选择学期" disabled>
            <el-option v-for="dict in dict.type.semester" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级" prop="studentClass">
          <el-input class="ck-input" v-model="form.studentClass" disabled />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <div style="width:100%">
            <el-date-picker v-model="form.startTime" type="datetime" placeholder="签到开始时间" style="width:100%"
              value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item label="时效" prop="validity">
          <el-input class="ck-input" v-model="form.validity">
            <template slot="append">分钟</template>
          </el-input>
        </el-form-item>
        <el-form-item label="类型" prop="signType">
          <el-radio-group v-model="form.signType">
            <el-radio :label="0">普通</el-radio>
            <el-radio :label="1">拍照</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="签到详情" :visible.sync="signInListOpen" width="1200px" append-to-body
      :before-close="signInListClose">
      <el-table v-loading="loading" :data="signInInfoList">
        <el-table-column label="学号" prop="studentId" min-width="150" />
        <el-table-column label="姓名" prop="studentName" min-width="150" />
        <el-table-column label="是否签到" align="center" prop="isSign" min-width="150">
          <template slot-scope="scope">
            <span> {{scope.row.isSign==1?'已签到':'未签到'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="签到时间" align="center" prop="signTime" min-width="150">
          <template slot-scope="scope">
            <span> {{scope.row.signTime==null?'-':scope.row.signTime}}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="infoTotal" :page.sync="infoQueryParams.pageNum"
        :limit.sync="infoQueryParams.pageSize" @pagination="getSignInHisInfo" />
    </el-dialog>
  </div>
</template>

<script>
import {
  getSignInList,
  addSignIn,
  getSignInHisInfo
} from "@/api/courseManagement/courseManagement.js";
export default {
  name: "SignIn",
  dicts: ["semester"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      signInList: [],
      signInInfoList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        signClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.id,
      },
      title: '发起签到',
      form: {
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        studentClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.id,
        signType: 0
      },
      rules: {
        startTime: [
          { required: true, message: '请选择签到开始时间', trigger: ['blur', 'change'] },

        ],
        validity: [
          { required: true, message: '请输入时效', trigger: ['blur', 'change'] },
          {
            pattern: /^[0-9]*$/,
            message: '请输入数字值',
            trigger: ['blur', 'change']
          },
        ],
      },
      infoQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      infoTotal: 0,
      dialogOpen: false,
      btnLoading: false,
      signInListOpen: false
    };
  },
  watch: {
    // 监视搜索词变化
    "$route.query.id": {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.studentCourseId = newValue
          this.queryParams.studentCourseId = newValue
          this.getList()
        }
      },
    },
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getSignInList(this.queryParams).then((response) => {
        this.signInList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialogOpen = true;
      this.title = '发起签到';
    },
    handleSubmit() {
      this.btnLoading = true
      this.form.signClassList = [this.form.studentClass]
      if (this.validateForm()) {
        addSignIn(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('发起签到成功')
            this.handleClose()
            this.btnLoading = false
          } else {
            this.btnLoading = false
          }
        }).catch(() => {
          this.btnLoading = false
        })
      } else {

        this.btnLoading = false
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      this.form = {
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        studentClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.id,
        signType: 0
      }
      this.resetForm("form");
      this.fileList = []
      this.dialogOpen = false
      this.getList()
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleReview(row) {
      this.infoQueryParams.courseName = row.courseName;
      this.infoQueryParams.courseType = row.courseType;
      this.infoQueryParams.term = row.term;
      this.infoQueryParams.signClass = row.signClass;
      this.infoQueryParams.id = row.id;
      this.getSignInHisInfo()
      this.signInListOpen = true

    },
    getSignInHisInfo() {
      console.log(this.infoQueryParams)
      getSignInHisInfo(this.infoQueryParams).then(res => {
        this.signInInfoList = res.rows
        this.infoTotal = res.total;
      })

    },
    signInListClose() {
      this.signInInfoList = []

      this.signInListOpen = false
    }
  },
};
</script>
<style lang="scss">
.ck-input {
  width: 100%;
}
</style>



