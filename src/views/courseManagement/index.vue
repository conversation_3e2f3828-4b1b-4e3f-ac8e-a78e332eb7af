<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建课程</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddCourse">创建课程</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="courseManagementList" row-key="id"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column label="课程 / 学期 / 班级" min-width="250">
        <template slot-scope="scope">
          <!-- 根据层级显示不同内容 -->
          <span v-if="!scope.row.term && !scope.row.studentClass">{{ scope.row.courseName }}</span>
          <span v-else-if="scope.row.term && !scope.row.studentClass">{{  termChange(scope.row.term) }}</span>
          <span v-else-if="scope.row.studentClass">{{ scope.row.studentClass }} ({{ scope.row.courseType === '0' ? '必修' : '选修' }})</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- 课程层级 -->
          <div v-if="!scope.row.term && !scope.row.studentClass">
            <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)">添加班级</el-button>
            <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleDetails(scope.row)">详情</el-button>
            <!--            一键直接删除的按钮-->
<!--            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>-->

            <!-- 只有当课程没有子项（学期）时显示删除按钮 -->
            <el-button v-if="!scope.row.children || scope.row.children.length === 0" size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
          </div>
          <!-- 学期层级：不显示任何操作按钮 -->
          <div v-else-if="scope.row.term && !scope.row.studentClass"></div>
          <!-- 班级层级 -->
          <div v-else-if="scope.row.studentClass">
            <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-share" @click="handleAnalyse(scope.row)">课程分析</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增课程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程" prop="courseName">
        <el-input class="ck-input" v-model="form.courseName" placeholder="请输入课程" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmint">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteManagement,
  getCourseManagementList,
  deleteCourse,
  deleteCourses,
  addCourseName
} from "@/api/courseManagement/courseManagement.js";
export default {
  name: "CourseManagement",
  dicts: ["semester"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程管理表格数据
      courseManagementList: [],
      open: false,
      // 弹出层标题
      title: "",
      form: {},
      rules: {
        courseName: [
          { required: true, message: '请输入课程', trigger: ['blur', 'change'] },
        ],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  // activated() {
  //   this.getList();
  // },
  methods: {
    /** 查询课程管理列表 */
    getList() {
      this.loading = true;
      getCourseManagementList(this.queryParams).then((response) => {
        this.courseManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.$router.push({
        path: "/courseManagement/addCourse",
        query: { courseName: row.courseName },
      });
    },
    /** 新增课程 */
    handleAddCourse() {
      this.open = true;
      this.title = "新增课程";
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/courseManagement/reviewCourseInfo",
        query: { courseName: row.courseName, courseType: row.courseType, term: row.term, studentClass: row.studentClass, id: row.id },
      });
    },
    /** 直接删除课程操作 */
    handleDelete(row){
      // console.log("row");
       console.log(row);
      //先拼一个对象给后端发过去
      const params = {
        courseName: row.courseName,
      }
      //确认要不要删除使用
      this.$modal
        .confirm("是否确认删除班级？")
        .then(() => {
          return deleteManagement(params); // 确保是 Promise
        })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("删除成功");
            this.reset();
            this.open = false;
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch(() => {
          this.$message.error("删除失败"); // 这里修正
        });
      //返回成功或者失败




    },
    handleDetails(row) {
      this.$router.push({
        path: "/courseManagement/reviewCourseDetails",
        query: { courseName: row.courseName},
      });
    },
    handleSubmint() {
      if (!this.form.courseName || this.form.courseName.trim() === '') {
        this.$message.error('课程名称不能为空');
        console.log("为空")
        return ;
      }
      // 去重校验：判断 courseName 是否已存在于 courseManagementList 中
      const isDuplicate = this.courseManagementList.some(item =>
        item.courseName?.trim() === this.form.courseName
      );

      if (isDuplicate) {
        this.$message.error('课程名称已存在，不能重复添加');
        console.log("重复")
        return;
      }

      addCourseName(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('新增成功')
            this.reset()
            this.open = false
            this.getList()
          }
        }).catch(() => {
          this.$message.error('新增失败')
          this.reset()
          this.open = false
        })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        courseName: null,
      };
      this.resetForm("form");
    },
    /** 删除按钮操作 */
    handleDel(row) {
      const param = {
        courseName: row.courseName,
        courseType: row.courseType,
        term: row.term,
        id: row.id
      }
      if (row.studentClass != null) {
        this.$modal
          .confirm("是否确认删除班级？")
          .then(function () {
            return deleteCourse(param);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      } else {
        this.$modal
          .confirm("是否确认删除课程？")
          .then(function () {
            return deleteCourses(param);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      }
    },
    termChange(term) {
      return this.selectDictLabelByVal(
        this.dict.type.semester,
        term
      );
    },
    // 跳转课程分析
    handleAnalyse(row) {
      this.$router.push({
        path: "/courseManagement/courseAnalysis",
        query: { courseName: row.courseName,
          studentClass: row.studentClass,
          courseClassId: row.id,
          gradeFlag: true,
        },
      });
    }
  },
};
</script>
