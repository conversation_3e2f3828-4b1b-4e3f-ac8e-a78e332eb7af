<template>
  <div class="app-container tab-container">

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="学生" name="students">
        <students-list v-if="activeName=='students'" />
      </el-tab-pane>
      <el-tab-pane label="教案" name="teachingPlan">
        <teaching-plan-list v-if="activeName=='teachingPlan'" />
      </el-tab-pane>
      <el-tab-pane label="资料" name="material">
        <material-list v-if="activeName=='material'" />
      </el-tab-pane>
      <el-tab-pane label="通知" name="message">
        <message-list v-if="activeName=='message'" />
      </el-tab-pane>
      <el-tab-pane label="讨论" name="discuss">
        <discuss-list v-if="activeName=='discuss'" />
      </el-tab-pane>
      <el-tab-pane label="签到" name="signIn">
        <sign-in v-if="activeName=='signIn'" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import StudentsList from './components/StudentsList.vue'
import TeachingPlanList from './components/TeachingPlanList.vue'
import MaterialList from './components/MaterialList.vue'
import MessageList from './components/MessageList.vue'
import DiscussList from './components/DiscussList.vue'
import SignIn from './components/SignIn.vue'

export default {
  name: 'ReviewCourseInfo',
  components: { StudentsList, TeachingPlanList, MaterialList, MessageList, DiscussList, SignIn },
  data() {
    return {
      activeName: 'students',
    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === 'students') {
      } else if (tab.name === 'teacherInfo') {
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.tab-container {
  padding: 0 10px;
}
</style>