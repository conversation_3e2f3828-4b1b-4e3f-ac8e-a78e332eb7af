<template>
  <div class="app-container tab-container">

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="习题" name="exercises">
        <exercises-list v-if="activeName==='exercises'"/>
      </el-tab-pane>
      <el-tab-pane label="案例" name="case">
        <case-list v-if="activeName==='case'"/>
      </el-tab-pane>
      <el-tab-pane label="视频" name="video">
        <video-list v-if="activeName==='video'"/>
      </el-tab-pane>
      <el-tab-pane label="课程统计" name="CourseStatistics">
        <CourseStatistics v-if="activeName==='CourseStatistics'"/>
      </el-tab-pane>
      <el-tab-pane label="课程图谱" name="relational">
        <relational-graphs v-if="activeName=='relational'" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import ExercisesList from './components/ExercisesList.vue'
import CaseList from './components/CaseList.vue'
import VideoList from './components/VideoList.vue'
import CourseStatistics from "@/views/courseManagement/components/CourseStatistics.vue";
import RelationalGraphs from './components/RelationalGraphs.vue'

export default {
  name: 'reviewCourseDetails',
  components: {CourseStatistics, ExercisesList, CaseList, VideoList,RelationalGraphs},
  data() {
    return {
      activeName: 'exercises',
    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === 'exercises') {
      } else if (tab.name === 'teacherInfo') {
      }
    }
  }
};
</script>
<style lang="scss" scoped>
  .tab-container {
    padding: 0 10px;
  }
</style>
