<template>
  <el-card class="box-card">
    <div class="el-dialog-div">
      <el-form :model="form" ref="form" label-position="top">
        <div
          v-for="(item, index) in homeworkQuestions"
          :key="item.id"
          class="question-container"
        >
          <div class="question-header">
            <p>
              <strong>第{{ index + 1 }}题：</strong>{{ item.question }}
              <span v-if="item.questionType == 'single'">（单选）</span>
              <span v-if="item.questionType == 'multiple'">（多选）</span>
              <span v-if="item.questionType == 'blank'">（填空）</span>
              <span v-if="item.questionType == 'shortAnswer'">（简答）</span>
            </p>
          </div>
          <div v-if="item.questionType == 'single'" class="question-content">
            <el-form-item>
              <el-radio-group v-model="form[item.id]" disabled>
                <el-radio
                  v-for="items in item.homeworkQuestionOptions"
                  :key="items.id"
                  :label="items.optionMark"
                >
                  {{ items.optionMark + '. ' + items.optionText }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <span class="answer">参考答案：{{ item.correctAnswer }}</span>
          </div>
          <div v-if="item.questionType == 'multiple'" class="question-content">
            <el-form-item>
              <el-checkbox-group v-model="form[item.id]" disabled>
                <el-checkbox
                  v-for="items in item.homeworkQuestionOptions"
                  :key="items.id"
                  :label="items.optionMark"
                >
                  {{ items.optionMark + '. ' + items.optionText }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <span class="answer">参考答案：{{ item.correctAnswer }}</span>
          </div>
          <div v-if="item.questionType == 'blank'" class="question-content">

            <span class="answer">参考答案：{{ item.correctAnswer }}</span>
          </div>
          <div v-if="item.questionType == 'shortAnswer'" class="question-content">

            <span class="answer">参考答案：{{ item.correctAnswer }}</span>
          </div>
          <div v-if="item.questionType == 'Essay'" class="question-content">

            <span class="answer">参考答案：{{ item.correctAnswer }}</span>
          </div>
        </div>
      </el-form>
    </div>
  </el-card>
</template>

<script>
import { getHomeWork } from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";

export default {
  name: "LookJob",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      open: false,
      title: '作业详情',
      form: {},
      homeworkQuestions: [],
      id: this.$route.query && this.$route.query.id,
      remark: '',
      classList: [],
      isFirstLoad: true, // 标志变量，初始为 true
    };
  },
  created() {
    this.id= this.$route.query && this.$route.query.id
    this.handleHomeWorkReview(this.id);
  },
  beforeDestroy() {
    this.id= ''
  },
  activated() {
    this.id= this.$route.query && this.$route.query.id
    if (!this.isFirstLoad) {
      // 非首次加载时才调用
      this.handleHomeWorkReview(this.id);
    } else {
      this.isFirstLoad = false; // 第一次加载后设置为 false
    }
  },
  methods: {

    /** 查看按钮操作 */
    handleHomeWorkReview(id) {
      getHomeWork(id).then(response => {
        console.log(response)
        this.homeworkQuestions = response.data.homeworkQuestions
        this.homeworkQuestions.map(item => {
          if (item.questionType == 'multiple') {
            item.userAnswer = item.userAnswer.split('、')
          }
          this.$set(this.form, item.id, item.userAnswer)
        })
        this.id = id
      })
    },

  }
};

</script>

<style scoped>
.el-dialog-div {
  padding: 20px;
}
.box-card {
  margin: 20px auto;
  max-width: 800px;
}
.question-container {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #f9f9f9;
}
.question-header {
  margin-bottom: 10px;
  font-size: 16px;
}
.question-content {
  margin-bottom: 10px;
}
.answer {
  display: block;
  margin-top: 5px;
  color: #909399;
  font-size: 14px;
}
</style>
