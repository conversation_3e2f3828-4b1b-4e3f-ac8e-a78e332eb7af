<template>
  <div class="app-container">
    <el-form ref="form" v-model="form" class="ck-form" label-width="160px">
      <el-form-item label="课程名称" prop="lessonName">
        <el-select class="ck-input" v-model="form.lessonName" filterable placeholder="请选择课程">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作业名称" prop="hwName">
        <el-input class="ck-input" v-model="form.hwName"></el-input>
      </el-form-item>
      <el-form-item label="题型" prop="questionType">
        <el-button type="primary" size="mini" style="margin-bottom: 10px;" @click="handleAdd">添加题型</el-button>
        <div v-for="(item,index) in homeworkQuestionReqs" :key="index" style="margin-bottom: 5px;">
          <el-select v-model=" item.questionType" filterable placeholder="请选择题型">
            <el-option v-for="dict in manualIssueType" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue">
            </el-option>
          </el-select>
          <span style="margin-left:20px">数量</span>
          <el-input style="width:100px;margin:0 10px" v-model="item.count"></el-input>
          <el-button type="danger" size="mini" @click="handleDelete(index)">删除</el-button>
        </div>
      </el-form-item>
      <el-form-item label="其他要求" prop="elseDec">
        <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入内容" v-model="form.elseDec">
        </el-input>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box" v-if="!loadingFlag">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
    <el-row class="step-btn-box" v-if="loadingFlag">
      <el-tag style="width:100%"><i class="el-icon-loading" />正在生成题目，请稍等··· </el-tag>
    </el-row>
    <div>生成的作业</div>

    <el-button style="float: right;margin-right:90px;margin-bottom:10px" size="mini" type="primary"
      @click="handleSave">保存</el-button>
    <el-button style="float: right;margin-right:30px;margin-bottom:10px" size="mini" type="primary"
      @click="handleCreate">新增</el-button>
<!--    <el-button style="float: right;margin-right:30px;margin-bottom:10px" size="mini" type="primary"-->
<!--      @click="handlemodifyDialog">修改</el-button>-->
<!--    穿梭框-->
    <div class="transfer-box">
      <el-transfer class="ck-transfer" style="text-align: left; display: inline-block" v-model="homeworkQuestionsKey"
                   :titles="['作业题目', '确认作业题目']" :data="transferData" @change="change">
  <span slot-scope="{ option }">
    <div v-if="option.questionType==='single'" style="white-space:pre-wrap" @click.stop.prevent="handleClick(option)">
      <span style="white-space:pre-wrap">{{ option.label }}</span><br>
      <span style="white-space:pre-wrap" v-for="(item,index) in option.homeworkQuestionOptions"
            :key="index">{{ item.optionMark +'.' + item.optionText}}</span><br>
      <span style="white-space:pre-wrap">答案：{{ option.correctAnswer }}</span>
    </div>
    <div v-if="option.questionType==='multiple'" @click.stop.prevent="handleClick(option)">
      <span style="white-space:pre-wrap">{{ option.label }}</span><br>
      <span style="white-space:pre-wrap" v-for="(item,index) in option.homeworkQuestionOptions"
            :key="index">{{ item.optionMark +'.' + item.optionText}}</span><br>
      <span style="white-space:pre-wrap">答案：{{ option.correctAnswer }}</span>
    </div>
    <div v-if="option.questionType==='blank'" @click.stop.prevent="handleClick(option)">
      <span style="white-space:pre-wrap">{{ option.label }}</span><br>
      <span style="white-space:pre-wrap">答案：{{ option.correctAnswer }}</span>
    </div>
    <div v-if="option.questionType==='shortAnswer'" @click.stop.prevent="handleClick(option)">
      <span style="white-space:pre-wrap">{{ option.label }}</span><br>
      <span style="white-space:pre-wrap">答案：{{ option.correctAnswer }}</span>
    </div>
    <div v-if="option.questionType==='Essay'" @click.stop.prevent="handleClick(option)">
      <span style="white-space:pre-wrap">{{ option.label }}</span><br>
      <span style="white-space:pre-wrap">答案：{{ option.correctAnswer }}</span>
    </div>
  </span>
        :titles="['作业题目', '确认作业题目']" :data="transferData" @change="change">


      </el-transfer>
    </div>
    <!--    新增题目-->
    <el-dialog title="新增" :visible.sync="dialogOpen">
      <el-form ref="addForm" :model="addForm" label-width="80px">
        <el-form-item label="题型" prop="questionType">
          <el-select v-model=" addForm.questionType" filterable placeholder="请选择题型" @change="changeQuestionType">
            <el-option v-for="dict in dict.type.question_type" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目" prop="question">
          <el-input type="textarea" v-model="addForm.question" :autosize="{ minRows: 1, maxRows: 8}" placeholder="请输入题目"
            maxlength="400" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="选项" prop="question" v-if="questionOptionsShow">
          <el-button type="primary" size="mini" style="margin-bottom: 10px;" @click="handleAddss">添加选项</el-button>
          <div v-for="(item,index) in addForm.homeworkQuestionOptions" :key="index" style="margin-bottom: 5px;">
            <el-input placeholder="请输入内容" v-model="item.optionText" maxlength="100" show-word-limit>
              <template slot="prepend">{{item.optionMark}}</template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="答案" prop="correctAnswer">
          <el-input type="textarea" v-model="addForm.correctAnswer" :autosize="{ minRows: 1, maxRows: 8}"
            placeholder="请输入答案" maxlength="600" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleCreateQuestion">确 定</el-button>
      </div>
    </el-dialog>
<!--    修改题目-->
    <el-dialog title="修改" :visible.sync="modifyDialog">
      <el-form ref="modifyForm" :model="modifyForm" label-width="80px">
        <el-form-item label="题型" prop="questionType">
          <el-select v-model=" modifyForm.questionType" filterable placeholder="请选择题型" @change="changeQuestionType">
            <el-option v-for="dict in dict.type.question_type" :key="dict.value" :label="dict.label"
                       :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目" prop="question">
          <el-input type="textarea" v-model="modifyForm.question" :autosize="{ minRows: 1, maxRows: 8}" placeholder="请输入题目"
                    maxlength="400" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="选项" prop="question" v-if="questionOptionsShow">
          <el-button type="primary" size="mini" style="margin-bottom: 10px;" @click="handleAddss">添加选项</el-button>
          <div v-for="(item,index) in modifyForm.homeworkQuestionOptions" :key="index" style="margin-bottom: 5px;">
            <el-input placeholder="请输入内容" v-model="item.optionText" maxlength="100" show-word-limit>
              <template slot="prepend">{{item.optionMark}}</template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="答案" prop="correctAnswer">
          <el-input type="textarea" v-model="modifyForm.correctAnswer" :autosize="{ minRows: 1, maxRows: 8}"
                    placeholder="请输入答案" maxlength="600" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleModifyQuestion">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createHomeWork, saveHomeWork, getHomeWork, getLesson, putHomeWork } from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";
import {getDicts} from "@/api/system/dict/data";
import modify from "../../modify.vue";
export default {
  name: 'FormContent',
  dicts: ['question_type'],
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 题型
      manualIssueType: [],
      // 题目
      transferData: [

      ],
      // 题目id
      homeworkQuestionsKey: [],
      // 题目
      homeworkQuestionReqs: [{}],
      options: [],
      form: {
      },
      // 作业题目
      homeworkQuestions: [],
      loadingFlag: false,
      dialogOpen: false,
      modifyDialog: false,
      //新增表单
      addForm: {
        questionType: 'single',
        homeworkQuestionOptions: [
          {
            optionMark: 'A',
            optionText: ''
          }
        ]
      },
      //modify修改表单
      modifyForm: {
        id:'',
        question:'',
        questionType: 'single',
        homeworkQuestionOptions: [
          {

            optionMark: 'A',
            optionText: ''
          }
        ],
        correctAnswer: ''

      },
      questionOptionsShow: true
    }
  },
  created() {
    this.getHomeWork()
    this.getLesson()
    this.getDictsData()
  },
  mounted() {

  },
  methods: {
    handleClick(data) {
      //扩展修改表单
      this.modifyForm = { ...this.modifyForm, ...data };
      console.log('题目被点击了！',this.modifyForm);
      console.log("作业题目表",)
      this.modifyDialog = true
       this.handlemodifyDialog
    },
    getDictsData(){
      let dictType = 'question_type'
      getDicts(dictType).then(res=>{
        this.manualIssueType = res.data.filter(item => item.dictValue !== "Essay")
      })
    },
    getHomeWork() {
      if (this.$route.query && this.$route.query.id) {
        getHomeWork(this.$route.query.id).then(res => {
          this.form.id = res.data.id
          this.form.lessonName = res.data.lessonName
          this.form.hwName = res.data.hwName
          this.form.elseDec = res.data.elseDec
          this.homeworkQuestionReqs = res.data.homeworkQuestionReqs ? res.data.homeworkQuestionReqs : []
          this.homeworkQuestions = res.data.homeworkQuestions
          const allData = res.data.homeworkQuestions
          const data = [];
          for (let i = 0; i < allData.length; i++) {
            allData[i].key = i;
            allData[i].label = allData[i].question;
            data.push(allData[i]);
            this.homeworkQuestionsKey.push(i)
          }

          this.transferData = data;
          const priorityOrder = new Map([
            ['single', 1],
            ['multiple', 2],
            ['blank', 3],
            ['shortAnswer', 4],
            ['Essay', 5]
          ])
          this.transferData.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType))

        })
      }
    },
    getLesson() {
      getLesson().then(res => {
        console.log(res)
        if (res.code === 200) {
          const data = [];
          for (let i = 0; i < res.rows.length; i++) {
            const lesson = {
              value: res.rows[i].id,
              label: res.rows[i].courseName
            }
            data.push(lesson);
          }
          this.options = data;
        }
      })
    },
    handleSubmint() {
      const queryForm = {
        ...this.form,
        homeworkQuestionReqs: this.homeworkQuestionReqs
      }

      // 检查课程名称是否存在
      if (!this.form.lessonName) {
        alert('课程名称不能为空');
        this.loadingFlag = false;
        return;
      }


      // 检查题型是否存在
      for (let i = 0; i < this.homeworkQuestionReqs.length; i++) {
        if (!this.homeworkQuestionReqs[i].questionType) {
          alert('请为每个题型选择一个题目类型');
          this.loadingFlag = false;
          return;
        }
        if (!this.homeworkQuestionReqs[i].count) {
          alert('请为每个题目类型选择一个题目数量');
          this.loadingFlag = false;
          return;
        }
      }


      this.loadingFlag = true
      //发送请求获得ai生成的作业
      createHomeWork(queryForm).then(res => {
        if (res.code === 200) {
          const allData = res.data
          const homeworkQuestionsKeyList = []
          //这个是用来解决作业题库的key和作业题库的数据源的key不一致的问题
          for (let i = 0; i < this.homeworkQuestions.length; i++) {
            homeworkQuestionsKeyList.push(i)
          }
          //homeworkQuestionsKey为作业题库的key，transferData为作业题库的数据源
          this.homeworkQuestionsKey = homeworkQuestionsKeyList
          this.transferData = [...this.homeworkQuestions, ...allData]
          console.log('实际上存储的数据是',this.transferData)
          // 为每个作业题库数据源添加key和label
          for (let i = 0; i < this.transferData.length; i++) {
            this.transferData[i].key = i;
            this.transferData[i].questionOrder = i;
            this.transferData[i].label = this.transferData[i].question;
          }
          this.loadingFlag = false
        } else {
          this.loadingFlag = false
        }
      }).catch(() => {
        this.loadingFlag = false
      })
    },
    change(a) {
      const homeworkQuestionList = []
      for (let i = 0; i < a.length; i++) {
        this.transferData.forEach((item) => {
          if (item.key == a[i]) {
            homeworkQuestionList.push(item);
          }
        });

      }
      this.homeworkQuestions = homeworkQuestionList
      const priorityOrder = new Map([
        ['single', 1],
        ['multiple', 2],
        ['blank', 3],
        ['shortAnswer', 4],
        ['Essay', 5]
      ])
      this.transferData.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType))
      this.homeworkQuestions.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType))
    },
    handleSave() {
      const queryForm = {
        ...this.form,
        homeworkQuestionReqs: this.homeworkQuestionReqs,
        homeworkQuestions: this.homeworkQuestions
      }
      if (this.form.id && this.form.id !== '') {
        putHomeWork(queryForm).then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.handleBack()
          }
        })
      } else {
        saveHomeWork(queryForm).then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.handleBack()
          }
        })
      }

    },
    handleAdd() {
      this.homeworkQuestionReqs.push({ questionType: '', count: '' })
    },
    handleDelete(index) {
      this.homeworkQuestionReqs.splice(index, 1)
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push('/explorationCenter6/jobCorrectionCreation')
    },
    // 手动添加题目
    handleCreate() {
      this.dialogOpen = true
    },
    //打开修改的对话框
    handlemodifyDialog()
    {

      this.modifyDialog = true
    },
    handleAddss() {
      var option = String.fromCharCode('A'.charCodeAt(0) + this.addForm.homeworkQuestionOptions.length)
      this.addForm.homeworkQuestionOptions.push({ optionMark: option, optionText: '' })
    },
    changeQuestionType(val) {
      if (val == 'multiple' || val == 'single') {
        this.questionOptionsShow = true
      } else {
        this.questionOptionsShow = false
      }
    },
    //修改作业
    handleModifyQuestion() {
      console.log('执行修改作业');
      const allData = { ...this.modifyForm };
      console.log(allData);

      // 先保存之前的数据，避免直接修改原始数组
      const currentHomeworkQuestions = [...this.homeworkQuestions];
      const currentTransferData = [...this.transferData];
      console.log("名字",this.modifyForm.question,)
      // 修改 transferData 中对应项的数据
      this.transferData = this.transferData.map(item => {
        if (item.key === this.modifyForm.key) {
          return {
            ...item, // 保留原始数据
            label: this.modifyForm.question,
            questionType: this.modifyForm.questionType,
            correctAnswer: this.modifyForm.correctAnswer,
          };
        }
        return item; // 如果没有匹配的项，返回原项
      });

      // 修改 homeworkQuestions 中对应项的数据
      this.homeworkQuestions = this.homeworkQuestions.map(item => {
        if (item.key === this.modifyForm.key) {
          return {
            ...item, // 保留原始数据
            label: this.modifyForm.question,
            questionType: this.modifyForm.questionType,
            correctAnswer: this.modifyForm.correctAnswer,
          };
        }
        return item; // 如果没有匹配的项，返回原项
      });

      // 隐藏修改对话框并重置表单
      this.modifyDialog = false;
      this.resetForm("modifyForm");

      // 初始化新的问题选项
      this.modifyForm.homeworkQuestionOptions = [{ optionMark: 'A', optionText: '' }];
      this.questionOptionsShow = true;

      // 你提到要删除旧的数据，假设是删除修改前的项：
      this.transferData = this.transferData.filter(item => item.key !== this.modifyForm.id);
      this.homeworkQuestions = this.homeworkQuestions.filter(item => item.key !== this.modifyForm.id);

      console.log('修改后的 transferData:', this.transferData);
      console.log('修改后的 homeworkQuestions:', this.homeworkQuestions);
    },
    //根据id来删除内容
    deleteQuestionById(id) {
      console.log("开始删除",id)
      // 删除 homeworkQuestions 和 transferData 中对应 id 的项
      this.homeworkQuestions = this.homeworkQuestions.filter(question => question.key !== id);
      this.transferData = this.transferData.filter(question => question.key !== id);

      // 更新 homeworkQuestionsKey（根据当前删除后的数据）
      this.homeworkQuestionsKey = this.homeworkQuestions.map((question, index) => index);

      // 重新为 homeworkQuestions 和 transferData 中的项赋予 key 和 questionOrder
      this.transferData.forEach((item, index) => {
        item.key = index;
        item.questionOrder = index;
        item.label = item.question;  // 可以根据实际情况设置 label
      });

      // 对 transferData 和 homeworkQuestions 进行排序
      const priorityOrder = new Map([
        ['single', 1],
        ['multiple', 2],
        ['blank', 3],
        ['shortAnswer', 4]
      ]);

      this.transferData.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType));
      this.homeworkQuestions.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType));

      console.log("删除后更新的 homeworkQuestions:", this.homeworkQuestions);
      console.log("删除后更新的 transferData:", this.transferData);
    },
    //新增题目
    handleCreateQuestion() {
      const homeworkQuestionsKeyList = []
      const allData = { ... this.addForm }
      console.log(allData)
      for (let i = 0; i < this.homeworkQuestions.length; i++) {
        homeworkQuestionsKeyList.push(i)
      }
      this.homeworkQuestionsKey = homeworkQuestionsKeyList
      this.transferData = [...this.homeworkQuestions, allData]
      for (let i = 0; i < this.transferData.length; i++) {
        this.transferData[i].key = i;
        this.transferData[i].questionOrder = i;
        this.transferData[i].label = this.transferData[i].question;
      }
      this.homeworkQuestionsKey.push(this.transferData.length - 1)
      this.homeworkQuestions.push(allData)
      const priorityOrder = new Map([
        ['single', 1],
        ['multiple', 2],
        ['blank', 3],
        ['shortAnswer', 4]
      ])
      this.transferData.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType))
      this.homeworkQuestions.sort((a, b) => priorityOrder.get(a.questionType) - priorityOrder.get(b.questionType))
      this.dialogOpen = false
      this.resetForm("addForm");
      this.addForm.homeworkQuestionOptions = [{ optionMark: 'A', optionText: '' }]
      this.questionOptionsShow = true
    }

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
.transfer-box {
  width: 100%;
  text-align: center;
}
.ck-transfer {
  width: 100%;
  margin: auto;
}
</style>
<style>
.ck-transfer .el-transfer-panel {
  width: 42%;
  height: 500px;
}
.ck-transfer .el-transfer-panel__body {
  height: 460px;
}
.ck-transfer .el-transfer-panel__list {
  height: 460px;
}
.ck-transfer .el-transfer-panel__item {
  height: auto;
  min-height: 30px;
}
</style>

