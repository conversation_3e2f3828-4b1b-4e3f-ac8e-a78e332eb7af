<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程" prop="lessonName">
        <el-input v-model="queryParams.lessonName" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建作业</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="作业名称" prop="hwName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="课程名称" prop="lessonName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="发布状态" align="center" prop="publishStatus" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.publishStatus==1 ? '已发布' : '未发布'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-position" @click="handlePublish(scope.row)">布置作业</el-button>
          <el-button size="mini" type="text" icon="el-icon-tickets"
            @click="handleCorrectJob(scope.row)">作业批改</el-button>
          <el-button :disabled="scope.row.publishStatus==1" size="mini" type="text" icon="el-icon-edit"
            @click="handleEdit(scope.row)">编辑</el-button>
          <el-button v-if="scope.row.publishStatus == 1"  size="mini" type="text" icon="el-icon-edit"
                     @click="handleLook(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="选择班级" :visible.sync="dialogVisible">
      <el-form :model="form">

        <el-form-item label="已布置班级" v-if="optionsPublish">
<!--          <el-cascader class="ck-input" disabled v-model="optionsPublish" :options="options" :props="props">-->
<!--          </el-cascader>-->

          <el-table
            :data="tableData"
            height="260"
            style="width: 100%; max-height: 200px; overflow-y: auto;" >
            <el-table-column
              prop="allName"
              label="班级"
              width="500">
              <template slot-scope="scope">
                <span>{{ scope.row.univerName }} - {{ scope.row.colleName }} - {{ scope.row.majorName }} - {{ scope.row.className }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="cutoffTime"
              label="截至日期"
              width="180">
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="所属院系班级">
          <el-cascader class="ck-input" v-model="form.classIds" :options="options" :props="props">
          </el-cascader>
        </el-form-item>

        <el-form-item label="开始时间">
          <el-date-picker readonly
            v-model="form.startTime"
            type="datetime"
            placeholder="选择开始时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="form.cutoffTime"
            type="datetime"
            placeholder="选择截止时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled-date="disabledPastDates"
            @change="validateDate"
          >
          </el-date-picker>
        </el-form-item>

      </el-form>
      <el-alert v-if="errorMessage" :title="errorMessage" type="error" show-icon />
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHomeworkList,
  delHomework,
  getUniversityll,
  publishHomeWork,
  selectClaByHId,
} from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";

export default {
  name: "JobCorrectionCreation",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lessonName: undefined,
      },
      dialogVisible: false,
      form: {
        cutoffTime: '', // 初始化为空字符串或正确的日期格式
        startTime: this.formatDate(new Date())
      },
      props: { multiple: true, emitPath: false },
      options: [],
      optionsPublish: ["1","3","45","134"],
      homeworkId: '',
      errorMessage: "" ,// 存储错误信息
      tableData: [{
        allName: '班级1',
        cutoffTime: '时间1',
      }, {
        allName: '班级1',
        cutoffTime: '时间2',
      },]
    };
  },
  created() {
    this.getList();
    this.getUniversityll()
  },
  activated() {
    this.getList();
    this.getUniversity()
  },
  methods: {
    // 格式化日期为 yyyy-MM-dd HH:mm:ss
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    validateDate() {
      const now = new Date();
      if (this.form.cutoffTime && new Date(this.form.cutoffTime) < now) {
        this.errorMessage = "选择的时间不能是过去的时间！";
        this.form.cutoffTime = null; // 清空无效选择
      } else {
        this.errorMessage = ""; // 清空错误信息
      }
    },
    // 禁止选择过去的日期
    disabledPastDates(date) {
      const now = new Date();
      return date < now; // 禁止当前时间之前的日期和时间
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      getHomeworkList(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getUniversity() {
      getUniversityll().then((res) => {
        this.options = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
              return {
                value: item.id,
                label: item.name,
                children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                      return {
                        value: item.id,
                        label: item.name
                      };
                    }) : null
                  };
                }) : null
              };
            }) : null
          };
        });
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: "/jobCorrectionCreation/addJob",
      });
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.$router.push({
        path: "/jobCorrectionCreation/updateJob",
        query: { id: row.id },
      });
    },
    handleLook(row) {
      this.$router.push({
        path: "/jobCorrectionCreation/lookJob",
        query: { id: row.id },
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      if (row.publishStatus == 0) {
        this.$modal
          .confirm("是否确认删除作业？")
          .then(function () {
            return delHomework(row.id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      } else {
        this.$modal
          .confirm("作业已发布，是否确认删除作业？")
          .then(function () {
            return delHomework(row.id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      }
    },
    /**布置作业 */
    handlePublish(row) {
      this.form.startTime = this.formatDate(new Date())
      const query={hmId: row.id}
      console.log(query)
      selectClaByHId(query).then((res) => {
        console.log(res.data)
        this.tableData = res.data
        // res.data.forEach(item => {
        //   item.allName = res.data.univerName + "-" + res.data.colleName + "-" + res.data.majorName + "-" + res.data.className
        // })
        // this.optionsPublish=res.data
      });

      this.dialogVisible = true
      this.homeworkId = row.id
    },
    handleClose() {
      this.dialogVisible = false
      this.form = {}
    },
    handleSubmit() {
      if (this.form.classIds === undefined || this.form.classIds.length <= 0) {
        this.$modal.msgError("请选择班级");
        return
      }
      if (this.form.cutoffTime === "" || this.form.cutoffTime === undefined){
        this.$modal.msgError("请选择截止时间");
        return;
      }
      if (this.form.startTime === "" || this.form.startTime === undefined) {
        this.form.startTime = this.formatDate(new Date())
      }

      const params = { id: this.homeworkId, cutoffTime : this.form.cutoffTime,...this.form };
      this.$modal
        .confirm("是否确认布置作业？")
        .then(function () {
          return publishHomeWork(params);
        })
        .then(() => {
          this.handleClose()
          this.getList();
          this.$modal.msgSuccess("布置成功");
        })
        .catch(() => { });
    },
    handleCorrectJob(row) {
      this.$router.push({
        path: "/jobCorrectionCreation/correctJob",
        query: { id: row.id },
      });
    },

  },
};
</script>
<style lang="scss" scoped>
.ck-input {
  width: 100%;
}
</style>
