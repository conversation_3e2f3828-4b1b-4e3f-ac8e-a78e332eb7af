<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="班级" prop="lessonName">
        <el-select v-model="queryParams.classId">
          <el-option v-for="item in classList" :key="item.id" :label="item.majorName + '/' +item.className"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="提交状态" prop="commitStatus">
        <el-select v-model="queryParams.commitStatus" placeholder="请选择">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList" border>
      <el-table-column type="index" />
      <el-table-column label="学生姓名" prop="nickName" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="班级" prop="response" min-width="250">
        <template slot-scope="scope">{{ scope.row.majorName + scope.row.className}}
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="response" min-width="150">
        <template slot-scope="scope">{{ scope.row.startTime}}
        </template>
      </el-table-column>
      <el-table-column label="截止时间" prop="response" min-width="150">
        <template slot-scope="scope">{{ scope.row.cutoffTime}}
        </template>
      </el-table-column>
      <el-table-column label="是否提交" prop="commitStatus" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.commitStatus == 0 ? '未提交' : (scope.row.commitStatus == 1 ? '已提交' : '过期未提交') }}
        </template>
      </el-table-column>
      <el-table-column label="是否批改" prop="correctStatus" min-width="100">
        <template slot-scope="scope">{{ scope.row.correctStatus == 0 ? '未批改' : '已批改'}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button :disabled="scope.row.correctStatus == 1 || scope.row.commitStatus != 1" size="mini" type="text" icon="el-icon-edit"
            @click="handleReview(scope.row)">批改</el-button>
          <el-button :disabled="scope.row.commitStatus != 1" size="mini" type="text" icon="el-icon-edit"
                      @click="openCutOffTime(scope.row)">重写</el-button>
          <el-button v-if="scope.row.commitStatus == 3" size="mini" type="text" icon="el-icon-edit"
                     @click="openCutOffTime(scope.row)">延期</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <div class="el-dialog-div">
        <el-form :model="form" ref="form">
          <div v-for="(item, index) in homeworkQuestions" :key="item.id">
            <div>
              <p style="font-weight: 500">
                第{{ index + 1 }}题：{{ item.question
              }}
                <span v-if="item.questionType == 'single'">（单选）</span>
                <span v-if="item.questionType == 'multiple'">（多选）</span>
                <span v-if="item.questionType == 'blank'">（填空）</span>
                <span v-if="item.questionType == 'shortAnswer'">（简答）</span>
                <span v-if="item.questionType == 'Essay'">（论述）</span>
              </p>
            </div>
            <div v-if="item.questionType == 'single'">
              <el-form-item label="">
                <el-radio-group v-model="form[item.id]" disabled>
                  <el-radio v-for="items in item.homeworkQuestionOptionList" :key="items.id"
                    :label="items.optionMark">{{items.optionMark+'.'+items.optionText}}</el-radio>
                </el-radio-group> <br>
                <span>参考答案：{{item.correctAnswer}}</span>
              </el-form-item>
            </div>
            <div v-if="item.questionType == 'multiple'">
              <el-form-item label="">
                <el-checkbox-group v-model="form[item.id]" disabled>
                  <el-checkbox v-for="items in item.homeworkQuestionOptionList" :key="items.id"
                    :label="items.optionMark">{{items.optionMark+'.'+items.optionText}}</el-checkbox>
                </el-checkbox-group>
                <span>参考答案：{{item.correctAnswer}}</span>
              </el-form-item>
            </div>
            <div v-if="item.questionType == 'blank'">
              <el-form-item label="">
                <el-input v-model="form[item.id]" disabled></el-input>
                <span>参考答案：{{item.correctAnswer}}</span>
              </el-form-item>
            </div>
            <div v-if="item.questionType == 'shortAnswer'">
              <el-form-item label="">
                <el-input type="textarea" v-model="form[item.id]" disabled></el-input>
                <span>参考答案：{{item.correctAnswer}}</span>
              </el-form-item>
            </div>
            <div v-if="item.questionType == 'Essay'">
              <el-form-item label="">
                <el-input type="textarea" v-model="form[item.id]" disabled></el-input>
                <span>参考答案：{{item.correctAnswer}}</span>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="text-align: left;margin-bottom: 5px;">
          <div>评语:</div>
          <el-input type="textarea" v-model="remark" />
        </div>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>


    <el-dialog title="截止时间更改" :visible.sync="CutOffTimeMark">
      <el-form :model="cutfrom">
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="cutfrom.cutoffTime"
            type="datetime"
            placeholder="选择截止时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled-date="disabledPastDates"
            @change="validateDate"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <el-alert v-if="errorMessage" :title="errorMessage" type="error" show-icon />


      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleAddCutOffTime">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCorrectList, getStudentHwDetali, getClassList, comment,addCutOffTime } from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";

export default {
  name: "CorrectJob",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        commitStatus:1
      },
      open: false,
      title: '作业批改',
      form: {},
      homeworkQuestions: [],
      id: '',
      remark: '',
      classList: [],
      statusOptions: [
        { value: 2, label: '全部' },
        { value: 1, label: '已提交' },
        { value: 0, label: '未提交' },
        { value: 3, label: '过期未提交' },
      ],
      cutfrom:{},
      CutOffTimeMark:false,
      CutOffTimeId:"",
      errorMessage: "" ,// 存储错误信息
    };
  },
  created() {
    this.getList();
    this.getClassList()
  },
  activated() {
    this.getList();
    this.getClassList()
  },
  methods: {

    handleClose() {
      this.CutOffTimeMark = false
      this.cutfrom = {}
    },
    validateDate() {
      const now = new Date();
      if (this.cutfrom.cutoffTime && new Date(this.cutfrom.cutoffTime) < now) {
        this.errorMessage = "选择的时间不能是过去的时间！";
        this.cutfrom.cutoffTime = null; // 清空无效选择
      } else {
        this.errorMessage = ""; // 清空错误信息
      }
    },
    // 禁止选择过去的日期
    disabledPastDates(date) {
      const now = new Date();
      return date < now; // 禁止当前时间之前的日期和时间
    },

    /** 查询列表 */
    getList() {
      this.queryParams.hmId = this.$router.currentRoute.query && this.$router.currentRoute.query.id
      this.loading = true;
      getCorrectList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /**查询班级 */
    getClassList() {
      getClassList(this.$router.currentRoute.query && this.$router.currentRoute.query.id).then(res => {
        this.classList = res.data
      })
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.open = true
      this.remark = ''
      getStudentHwDetali(row).then(response => {
        this.homeworkQuestions = response.data
        this.homeworkQuestions.map(item => {
          if (item.questionType == 'multiple') {
            item.userAnswer = item.userAnswer.split('、')
          }
          this.$set(this.form, item.id, item.userAnswer)
        })
        this.id = row.id
      })
    },
    openCutOffTime(row){
      this.CutOffTimeMark=true;
      this.CutOffTimeId=row.id;
    },
    handleAddCutOffTime(){
      if (!this.cutfrom.cutoffTime) {
        this.$message.warning("请先选择有效的截止时间");
        return;
      }

      const data={
        id:this.CutOffTimeId,
        cutoffTime: this.cutfrom.cutoffTime
      }
      addCutOffTime(data).then(res=>{
        console.log(res);
        if (res.code == "200"){
          this.CutOffTimeMark=false
          this.$modal.msgSuccess("延长成功");
          this.getList();
        }else {
          this.$modal.msgSuccess("延长失败");
        }

      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    submit() {
      comment({ id: this.id, remark: this.remark }).then(res => {
        if (res.code === 200) {
          this.$message.success('提交成功')
          this.open = false;
          this.getList()
        }

      })
    }
  }
};
</script>
<style scoped>
.el-dialog-div {
  height: 60vh;
  overflow: auto;
}
</style>
