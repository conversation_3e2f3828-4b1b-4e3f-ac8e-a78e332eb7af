<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公式名字" prop="imgName">
        <el-input
          v-model="queryParams.imgName"
          placeholder="请输入公式名字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-if="haveAdd===1"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="formulaList" @selection-change="handleSelectionChange">
      <el-table-column label="公式名字" align="center" prop="imgName" />
      <el-table-column label="公式图片" align="center" prop="imgPath" >
        <template slot-scope="scope" >
          <div  @click="handleLook(scope.row)">
              <img  :src="scope.row.imageUrl" alt="" style="max-width: 80px; height: 80px;" >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="公式说明" align="center" prop="imgExplain" />
<!--      <el-table-column label="图片关键词" align="center" prop="imgKeyword" />-->
      <el-table-column label="中英文" align="center" prop="imgMark" >
        <template slot-scope="scope">
          {{ scope.row.imgMark == 0 ? "中文" : "英文"}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isCreateUser ===0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-if="scope.row.isCreateUser ===0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公式图片对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公式名字" prop="imgName" filterable >
          <el-input v-model="form.imgName" placeholder="请输入公式名字" :disabled="flag"/>
        </el-form-item>
<!--        <el-form-item label="图片path" prop="imgPath">-->
<!--          <el-input v-model="form.imgPath" placeholder="请输入图片path" />-->
<!--        </el-form-item>-->
        <el-form-item label="公式图片上传" prop="imgPath">
          <el-upload class="upload-demo ck-input" drag :action="uploadPng" multiple
                     :data="uploadPngData" :headers="headers" :on-success="handleUploadPngSuccess" :on-remove="handleRemovePng"
                     :file-list="pngFileList" accept=".png,.jpg" :on-preview="handlePreviewPng"  :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持图片上传,仅能上传一个文件</div>
          </el-upload>
        </el-form-item>
<!--        <el-form-item label="图片关键词" prop="imgPath">-->
<!--          <el-input v-model="form.imgKeyword" placeholder="请输入图片path" />-->
<!--        </el-form-item>-->
        <el-form-item label="公式说明" prop="imgExplain">
          <el-input v-model="form.imgExplain" placeholder="请输入图片说明" />
        </el-form-item>
        <el-form-item label="中英文" prop="imgMark">
          <el-select v-model="imgMark" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="预览公式图片" :visible.sync="lookImages" width="500px" append-to-body>
      <img  :src="imageUrl" alt="" style="max-width: 100%; height: auto;">
    </el-dialog>

<!--    <el-dialog-->
<!--      title="预览公式图片"-->
<!--      :visible.sync="lookImages"-->
<!--      width="auto"-->
<!--      append-to-body-->
<!--      :modal="false"-->
<!--      @close="resetDialog"-->
<!--    >-->
<!--      <div-->
<!--        class="dialog-content"-->
<!--        @mousedown="onMouseDown"-->
<!--        ref="dialogContent"-->
<!--      >-->
<!--        <img :src="imageUrl" alt="" style="max-width: 100%; height: auto;" />-->
<!--        <div class="resizer" @mousedown.stop="onMouseDownResize"></div>-->
<!--      </div>-->
<!--    </el-dialog>-->

  </div>
</template>

<script>
import { listFormula, getFormula, delFormula, addFormula, updateFormula ,getImageByPath,checkHaveAdd} from "@/api/formula/formula.js";
import { getToken } from "@/utils/auth";

export default {
  name: "Formula",
  data() {
    return {
      uploadPng: process.env.VUE_APP_BASE_API + "/file/file/upload",
      uploadPngData: { modeltype: 'formula' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公式图片表格数据
      formulaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        imgName: null,
        imgPath: null,
        imgExplain: null,
        imgMark: null
      },
      // 表单参数
      form: {
        imgPath:"",
        fileId:"",
      },
      // 表单校验
      rules: {
      },
      pngFileList:[],
      lookImages:false,
      imageUrl:"",
      options: [
        { value: '中文', label: '中文' },
        { value: '英文', label: '英文' },
      ],
      flag: false,
      imgMark: '中文',
      isDragging: false,
      isResizing: false,
      startX: 0,
      startY: 0,
      startWidth: 0,
      startHeight: 0,
      haveAdd:0,
    };
  },
  created() {
    this.checkHaveAdd();
    this.getList();
  },
  watch:{
    open(newVal,oldVal){
      if (!newVal){
        this.flag=false
        this.imgMark='中文';
        if (this.pngFileList.length > 0) {
          this.pngFileList.forEach(file => {
            this.handleRemovePng(file);
          });
        }
        this.pngFileList = [];
      }
    }
  },
  methods: {



    onMouseDown(event) {
      this.isDragging = true;
      this.startX = event.clientX;
      this.startY = event.clientY;

      document.addEventListener('mousemove', this.onMouseMove);
      document.addEventListener('mouseup', this.onMouseUp);
    },
    onMouseMove(event) {
      if (this.isDragging) {
        const dx = event.clientX - this.startX;
        const dy = event.clientY - this.startY;
        const dialog = this.$refs.dialogContent;

        dialog.style.transform = `translate(${dx}px, ${dy}px)`;
      }
    },
    onMouseUp() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onMouseMove);
      document.removeEventListener('mouseup', this.onMouseUp);
    },
    onMouseDownResize(event) {
      this.isResizing = true;
      this.startX = event.clientX;
      this.startY = event.clientY;
      this.startWidth = this.$refs.dialogContent.clientWidth;
      this.startHeight = this.$refs.dialogContent.clientHeight;

      document.addEventListener('mousemove', this.onMouseResize);
      document.addEventListener('mouseup', this.onMouseUpResize);
    },
    onMouseResize(event) {
      if (this.isResizing) {
        const width = this.startWidth + (event.clientX - this.startX);
        const height = this.startHeight + (event.clientY - this.startY);
        const dialog = this.$refs.dialogContent;

        dialog.style.width = `${width}px`;
        dialog.style.height = `${height}px`;
      }
    },
    onMouseUpResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.onMouseResize);
      document.removeEventListener('mouseup', this.onMouseUpResize);
    },
    resetDialog() {
      // 这里可以重置对话框位置和大小
      this.$refs.dialogContent.style.transform = 'translate(0, 0)';
      this.$refs.dialogContent.style.width = '500px'; // 默认宽度
      this.$refs.dialogContent.style.height = 'auto'; // 默认高度
    },



    //============================================================================

    handleLook(row){
      this.lookImages=true;
      this.imageUrl=row.imageUrl
    },

    handleUploadPngSuccess(res, file){
      this.form.imgPath=res.data.preview;
      this.form.fileId=res.data.id;
    },
    handleRemovePng(file, fileList){
      this.form.imgPath="";
      this.form.fileId="";
      this.pngFileLis=[];
    },

    handlePreviewPng(file, fileList){

    },
    /** 查询公式图片列表 */
    getList2() {
      this.loading = true;
      listFormula(this.queryParams).then(response => {
        this.formulaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getList() {
      this.loading = true;
      listFormula(this.queryParams)
        .then(response => {
          this.total = response.total;
          const promises = response.rows.map(record => {
            const imgPath = record.imgPath;
            // 使用 lookImage2 返回的 Promise 获取图像的 Base64
            return this.lookImage(imgPath).then(base64data => {
              // 将 Base64 数据作为 imageUrl 添加到 record
              record.imageUrl = base64data;
              return record;
            });
          });

          // 使用 Promise.all 确保所有异步操作完成后再更新 scenarioList
          return Promise.all(promises);
        })
        .then(scenarioListWithImages => {
          this.formulaList = scenarioListWithImages;
          this.loading = false;
        })
        .catch(error => {
          console.error("加载列表失败", error);
          this.loading = false;
        });
    },

    lookImage(path) {
      const imgPath = {path:path};
      return getImageByPath(imgPath)
        .then(blob => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);

            reader.onloadend = () => {
              resolve(reader.result); // 返回 base64 字符串
            };

            reader.onerror = (error) => {
              console.error("图像加载失败", error);
              reject(error);
            };
          });
        });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        imgName: null,
        imgPath: null,
        imgExplain: null,
        imgKeyword: null,
        imgMark: null,
        fileId:null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      checkHaveAdd().then(response => {
       if (response.msg == 0){
         this.$message.warning('没有权限')
         return
       }else {
         this.reset();
         this.open = true;
         this.title = "添加公式图片";
       }
      });

    },
    checkHaveAdd(){
      checkHaveAdd().then(response => {
        if (response.msg == 0){
         this.haveAdd=0
        }else {
          this.haveAdd=1
        }
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.flag=true;
      const id = row.id || this.ids
      getFormula(id).then(response => {
        this.form = response.data;
        this.imgMark=response.data.imgMark == 0 ? "中文": "英文",
        this.open = true;
        this.title = "修改公式图片";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {

          if (this.form.imgName == null || this.form.imgName == "" ) {
            this.$message.warning('请输入公式图片名字')
            return
          }
          if (this.form.imgPath == null || this.form.imgPath == "" ) {
            this.$message.warning('请重新上传公式图片')
            return
          }
          if (this.imgMark =="中文"){
            this.form.imgMark=0;
          }
          if (this.imgMark =="英文"){
            this.form.imgMark=1;
          }
          console.log(this.form.fileId)
          if (this.form.id != null) {
            console.log(this.form.fileId)
            updateFormula(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.handleRemovePng()
            });
          } else {
            console.log(this.form.fileId)
            addFormula(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.handleRemovePng()
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除公式图片编号为"' + ids + '"的数据项？').then(function() {
        return delFormula(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('test/formula/export', {
        ...this.queryParams
      }, `formula_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.dialog-content {
  position: relative;
  cursor: move; /* 鼠标样式为可移动 */
}

.resizer {
  width: 10px;
  height: 10px;
  background: gray;
  position: absolute;
  right: 0;
  bottom: 0;
  cursor: nwse-resize; /* 改变鼠标样式为调整大小 */
}
</style>
