<template>
  <div class="big-container">
    <!-- 表单区域 -->
    <div class="question-form">
      <el-form ref="questionForm" :model="form" :rules="rules" label-position="top" label-width="100%">
        <!-- 分类下拉列表 -->
        <el-form inline>
          <el-form-item>
            <template slot="label">
              <span style="color: red">*</span> 问题类型：
            </template>
            <el-select v-model="form.type" placeholder="请选择问题类型">
              <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 问题描述： -->
        <el-form-item label="问题描述：" prop="description">
          <div class="upload-line">
            <el-upload class="inline-upload" :action="uploadUrl" :data="uploadData" :headers="headers"
              :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept="image/*"
              :disabled="flag == 'review'" :before-upload="beforeUploadImage" list-type="picture">
              <el-button size="small" icon="el-icon-upload">添加图片</el-button> <!-- 上传按钮 -->
            </el-upload>

            <!-- 图片添加说明 -->
            <span class="upload-tip">图片支持格式：jpg/jpeg/png/bmp/gif，每张不超过10M</span>
          </div>
          <!-- 文本表单 -->
          <el-input type="textarea" :rows="6" placeholder="请输入问题描述" v-model="form.description" maxlength="2000"
          show-word-limit  />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </el-form-item>
      </el-form>

      <!-- 预览图片窗口 -->
      <el-dialog :visible.sync="previewVisible" width="60%" center>
        <img :src="previewImage" alt="预览图片" style="width: 100%" />
      </el-dialog>
    </div>

    <!-- 表单区域 -->
    <div class="app-container ck-container">
      <!-- 历史发布 + 筛选器 -->
      <div class="ck-message-wrapper">
        <div class="filter-bar" style="margin-left: 60px;">
          <el-form inline>
            <el-form-item label="留言类型">
              <el-select v-model="queryParams.type" placeholder="请选择问题类型" @change="handleFilterChange">
                <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="ck-message">
        <div class="ck-message-item" v-for="(item, index) in list" :key="index">
          <div class="ck-message-item-title">{{ item.nickName }}</div>
          <div class="ck-message-item-content">{{ item.message }}</div>
          <!-- 图片展示 -->
          <div class="ck-message-item-images" v-if="item.img && item.img.length">
            <!-- 分割字符串并传递给 :preview-src-list -->
            <el-image v-for="(img, index) in item.img.split(',')" :key="index" :src="img.trim()" fit="cover"
              style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
              :preview-src-list="item.img.split(',').map(img => img.trim())" preview-teleported />
          </div>
          <div class="ck-message-item-time">{{ item.createTime }}</div>
          <div class="ck-message-item-btn">
            <el-button type="text" size="mini" @click="handleResponse(item, 'reply', 'form')">回复</el-button>
            <el-button type="text" size="mini" v-if="item.deleteFlag"
              @click="handleDel(item, 'reply', 'form')">删除</el-button>
          </div>

          <div class="ck-response-item" v-for="(items, index) in item.sMsgReplyList1" :key="index">
            <span class="ck-response-item-title">{{ items.replyUserName }}</span>
            <span class="ck-response-item-content">回复</span>
            <span class="ck-response-item-title">{{ items.lastReplyUname }}</span>
            <span class="ck-response-item-content">{{ items.replyMsg }}</span>

<!----------------------------------------- 留言历史区回复图片1 -------------------------------------------------->
<!-- <div class="ck-message-item-images" v-if="item.sMsgReplyList1 && item.sMsgReplyList1.length">
  <div v-for="(sMsgReply1, Index) in item.sMsgReplyList1" :key="Index">
      <div class="message-item">
        <div class="message-images">
          <el-image 
            v-for="(img, Index) in sMsgReply1.img.split(',')" 
            :key="Index" 
            :src="img.trim()"
            fit="cover" 
            style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
            preview-teleported>
          </el-image>
        </div>
    </div>
  </div>
</div> -->




            <span class="ck-response-item-time">{{ items.createTime }}</span>
            <el-button type="text" size="mini" @click="handleResponse(items, 'replyreply', 'form')">回复</el-button>
            <el-button type="text" size="mini" v-if="items.deleteFlag"
              @click="handleDel(items, 'replyreply', 'form')">删除</el-button>

          </div>

          <!-- 更多 -->
          <div class="ck-response-item-btn">
            <el-button type="text" size="mini" @click="handleMore(item)">更多</el-button>
          </div>
        </div>

        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </div>

      <!-- 回复弹窗 -->
      <el-dialog title="回复" :visible.sync="dialogFormVisible">
        <!-- 回复添加图片 -->

         <!-- <div class="upload-line">
            <el-upload class="inline-upload" :action="uploadUrl" :data="uploadData" :headers="headers"
              :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept="image/*"
              :disabled="flag == 'review'" :before-upload="beforeUploadImage" list-type="picture">
              <el-button size="small" icon="el-icon-upload">添加图片</el-button> 
         </el-upload>          
            <span class="upload-tip">图片支持格式：jpg/jpeg/png/bmp/gif，每张不超过10M</span>
          </div> -->

          
        <el-form :model="form">
          <el-form-item label="回复内容">
            <el-input v-model="form.replyMsg" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" maxlength="2000" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="留言详情" class="dialogClass" :visible.sync="dialogResponseVisible">
        <div>
          <div v-for="(item, index) in infoList" :key="index">
            <div class="ck-message-item-title">{{ item.nickName }}</div>
            <div class="ck-message-item-content">{{ item.message }}</div>

           <!-- 详情页留言图片 -->
          <div class="ck-message-item-images" v-if="item.img && item.img.length">
           
            <el-image v-for="(img, index) in item.img.split(',')" :key="index" :src="img.trim()" fit="cover"
              style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
              :preview-src-list="item.img.split(',').map(img => img.trim())" preview-teleported />
          </div>


            <div class="ck-message-item-time">{{ item.createTime }}</div>
            <div class="ck-message-item-btn">
              <el-button type="text" size="mini" @click="handleResponse(item, 'reply', 'dialog')">回复</el-button>
              <el-button type="text" size="mini" v-if="item.deleteFlag"
                @click="handleDel(item, 'reply', 'dialog')">删除</el-button>
            </div>

            <div class="ck-response-item" v-for="(reply, index) in item.sMsgReplyList1" :key="index">
              <span class="ck-response-item-title">{{ reply.replyUserName }}</span>
              <span class="ck-response-item-content">回复</span>
              <span class="ck-response-item-title">{{ reply.lastReplyUname }}</span>
              <span class="ck-response-item-content">{{ reply.replyMsg }}</span>
               
              
<!----------------------------------------- 留言历史区回复图片2 -------------------------------------------------->
<!-- <div class="ck-message-item-images" v-if="item.sMsgReplyList1 && item.sMsgReplyList1.length">
  <div v-for="(sMsgReply1, Index) in item.sMsgReplyList1" :key="Index">
    <div v-if="sMsgReply1.img">
      <div class="message-item">
        <div class="message-images">
          <el-image 
            v-for="(img, imgIndex) in sMsgReply1.img.split(',')" 
            :key="imgIndex" 
            :src="img.trim()"
            fit="cover" 
            style="width: 100px; height: 100px; margin-right: 10px; border-radius: 4px"
            preview-teleported>
          </el-image>
        </div>
      </div>
    </div>
  </div>
</div> -->

              <span class="ck-response-item-time">{{ reply.createTime }}</span>
              <el-button type="text" size="mini" @click="handleResponse(reply, 'replyreply', 'dialog')">回复</el-button>
              <el-button type="text" size="mini" v-if="reply.deleteFlag"
                @click="handleDel(reply, 'replyreply', 'dialog')">删除</el-button>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'; // 获取 token
import {
  getMessageList,
  addMessage,
  addReply,
  getMessage,
  delMessage,
  delReply
} from '@/api/fanKui/fankui.js'
export default {
  name: "MessageBoard",
  data() {
    return {
     
      flag: "",
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/uploadPath", // 图片上传接口
      uploadData: { 
        modeltype: 'imageUpload' 

      },  // 上传时传递的数据，适应图片上传
      headers: {
        Authorization: "Bearer " + getToken(), // 使用 token 认证
      },
      fileList: [],  // 用于存储上传的文件列表
      previewVisible: false, // 控制图片预览窗口的显示
      previewImage: '',  // 图片预览地址
      rules: {
        description: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
        type: [{ required: true, message: '请选择问题类型', trigger: 'change' }]
      },
      types: [
        { value: '1', label: '程序错误' },
        { value: '2', label: '界面问题' },
        { value: '3', label: '性能问题' },
        { value: '4', label: '其他问题' }
      ],
      message: '',
      list: [],
      infoList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type:''
      },
      dialogFormVisible: false,
      form: {
        description: '',
        type: '',
        replyMsg: '',
        msgId: '',
        lastReplyUid: '',
        lastReplyUname: '',
        lastReplyId: '',
        i:[],
        img: '',
        imageUrls: [],  // 用于存储上传的图片 URL 数组
      },
      previewVisible: false,
      previewImage: '',
      dialogResponseVisible: false,
      dialogFlag: false,
      SpeechDraftId: [] // 初始化为空数组
    };
  },

  created() {
    this.getList();
  },
  methods: {
    // 上传前检查图片格式和大小
    beforeUploadImage(file) {
      const isImage = file.type.startsWith('image/');
      const isSmallEnough = file.size / 1024 / 1024 < 10;  // 限制图片大小小于 10MB

      if (!isImage) {
        this.$message.error('只能上传图片文件');
      }

      if (!isSmallEnough) {
        this.$message.error('图片大小不能超过 10MB');
      }

      return isImage && isSmallEnough;  // 只有符合条件的文件才能上传
    },

    handleUploadSuccess(response, file, fileList) {
  if (!Array.isArray(fileList)) {
    this.fileList = [];
  }

  let baseUrl = window.location.origin;
  let imgData;

  try {
    const imageUrl = response.data.SpeechDraftPath;
    const speechDraftId = response.data.SpeechDraftId;

    // 确保 this.form.i 已初始化为数组
    if (!Array.isArray(this.form.i)) {
      this.$set(this.form, 'i', []);  // Vue 响应式赋值
    }

    try {
      this.form.i.push(speechDraftId);
      console.log("标识id:", this.form.i);
    } catch (e) {
      console.error("推送 SpeechDraftId 时出错:", e);
    }

    const i = imageUrl.replace("imageUpload", "temp");
    console.log("上传后的文件路径:", i);

    if (i.includes("/ruoyi/")) {
      const finalPath = i.replace(/.*?\/ruoyi\//, "/home/<USER>/");

      if (baseUrl.includes("192.168") || baseUrl.includes("localhost") || baseUrl.includes("127.0")) {
        imgData = "http://127.0.0.1:9215" + finalPath;
      } else {
        imgData = baseUrl + finalPath;
      }
    }

    // 确保 this.form.imageUrls 已初始化为数组
    if (!Array.isArray(this.form.imageUrls)) {
      this.$set(this.form, 'imageUrls', []);
    }

    try {
      this.form.imageUrls.push(imgData);
      console.log("拼接后的图片路径:", this.form.imageUrls);
    } catch (e) {
      console.error("推送图片路径时出错:", e);
    }

    const cachedImages = this.form.imageUrls;

    try {
      this.form.img = cachedImages.join(",");
      console.log("img 字符串路径:", this.form.img);
    } catch (e) {
      console.error("拼接 img 路径时出错:", e);
    }

  } catch (e) {
    console.error("处理上传成功回调时发生错误:", e);
  }
}
,

    // 删除文件的回调
    handleRemove(file, fileList) {
      console.log('删除的文件:', file);  // 打印删除的文件信息
      this.$message.success('文件已删除');
    },

    // 表单提交
    submitForm() {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          // 提交数据到后端，包含图片 URL
          addMessage({
            message: this .form.description,
            img: this.form.img, // 提交图片 URL
            i:this.form.i,
            deleteFlag: true,
            type: this.form.type, // 如果需要提交类型，可以取消注释
          }).then(res => {
            if (res.code == 200) {
              this.$message({
                message: '留言成功',
                type: 'success'
              });
              this.getList(); // 获取最新的留言
            }
          });
          console.log(this.form.type); // 打印类型
          console.log(this.form.img); // 打印描述
          // 清空表单
          this.form.description = '';  // 清空描述字段
          this.form.type = '';  // 清空类型字段
          this.fileList = [];  // 清空上传的文件列表
          this.form.i = [];  // 清空图片路径
          this.form.imageUrls = [];  // 清空图片 URL 数组
          this.handleClose();

          this.$message.success('发布成功！');
        } else {
          this.$message.error('请完善表单信息');
        }
      });
    },



    //--------------------------------------------------------------------------------------------------
    handleFilterChange() {
      this.queryParams.pageNum = 1;  // 重置到第一页
      console.log("筛选条件变化了:", this.queryParams.type); // 你可以在这里调试
      this.getList();  // 重新获取留言列表
    },


    // 获取留言列表
    getList() {
      getMessageList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.list = res.rows;
          this.total = res.total;
          console.log("LIST");
          console.log(res.rows);
          console.log(res.total);
        }
      });
    },

    // 回复操作
    handleResponse(item, type, dialog) {
      this.dialogFormVisible = true;
      this.dialogFlag = dialog === "dialog" ? true : false;
      if (type === 'reply') {
        this.form.msgId = item.msgId;
        this.form.lastReplyUid = item.msgUserId;
        this.form.lastReplyUname = item.nickName;
      } else {
        this.form.msgId = item.msgId;
        this.form.lastReplyId = item.replyId;
        this.form.lastReplyUid = item.replyUserid;
        this.form.lastReplyUname = item.replyUserName;
      }
    },

    // 关闭回复弹窗
    handleClose() {
      this.dialogFormVisible = false;
      this.form = { replyMsg: '' };
    },

    // 提交回复
    handleSubmit() {
      addReply(this.form).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '回复成功',
            type: 'success'
          });
          this.getList();
          this.fileList = [];  // 清空上传的文件列表
          this.form.i = [];  // 清空图片路径
          this.form.imageUrls = [];  // 清空图片 URL 数组
          this.form.description = '';  // 清空描述字段
          this.handleClose();

        }
      });
    },

    // 删除留言或回复
    handleDel(row, type, dialog) {
      this.dialogFlag = dialog === "dialog" ? true : false;
      this.$modal.confirm("是否确认删除此留言？").then(() => {
        if (type === 'reply') {
          delMessage(row.msgId).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          });
        } else {
          if (this.dialogFlag) {
            delReply(row.replyId).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            });
          } else {
            delReply(row.replyId).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            });
          }
        }
      });
    },

    // 查看留言详情
    handleMore(item) {
      this.dialogResponseVisible = true;
      getMessage(item.msgId).then(res => {
        if (res.code == 200) {
          this.infoList = res.data;
        }
      });
    }
  }
};

</script>




<style lang="scss" scoped>
// 大盒子
.big-container {
  background-color: #dbe9f740;
  width: 100%;
  height: auto;
  overflow: hidden;
}

//小盒子1
.question-form {
  padding: 20px;
  max-width: 1450px;
  margin: auto;
  margin-top: 30px;
  // border-bottom: 1px solid #e8e8e8;
  // background-color:  #dbe9f740;
}

.upload-line {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.inline-upload {
  margin-right: 10px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.ck-message-item-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

// 小盒子2
.ck-container {
  padding: 0;
  height: auto;
}

.ck-content {
  width: 100%;
  padding: 20px 10%;
  height: 160px;
}

.ck-message {
  width: 100%;
  padding: 20px 10%;
}

.ck-message-item {
  width: 100%;

}

.ck-message-item-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.ck-message-item-content {
  font-size: 14px;
  color: #666;
  padding: 10px;
}

.ck-message-item-time {
  font-size: 12px;
  color: #666;
  padding: 0 10px;
}

.ck-message-item-btn {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.ck-response-item {
  width: 100%;
  padding: 10px;
}

.ck-response-item-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
}

.ck-response-item-content {
  font-size: 12px;
  color: #666;
  padding: 10px;
}

.ck-response-item-time {
  font-size: 10px;
  color: #666;
  padding: 0 10px;
}

.ck-response-item-btn {
  width: 100%;
  display: flex;
  justify-content: center;
}

.dialogClass .el-dialog__body {
  padding: 10px 20px;
}

.pagination-container {
  height: 50px;
}
</style>
