<template>
  <div class="app-container">
    <div class="ck-form">
      <el-descriptions title="基本信息">
        <el-descriptions-item label="任务名称">{{dataProcessInfo.etlTaskName}}</el-descriptions-item>
        <el-descriptions-item label="处理前数据集">{{dataProcessInfo.sourceDatasetStrName}}</el-descriptions-item>
        <el-descriptions-item label="处理后数据集">{{dataProcessInfo.destDatasetStrName}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="异常清洗配置">
        <el-descriptions-item
          label="移除不可见字符">{{translate(dataProcessInfo.removeInvisibleCharacter)}}</el-descriptions-item>
        <el-descriptions-item
          label="规范化空格">{{translate(dataProcessInfo.replaceUniformWhitespace)}}</el-descriptions-item>
        <el-descriptions-item
          label="去除乱码">{{translate(dataProcessInfo.removeNonMeaningCharacters)}}</el-descriptions-item>
        <el-descriptions-item
          label="繁体转简体">{{translate(dataProcessInfo.replaceTraditionalChineseToSimplified)}}</el-descriptions-item>
        <el-descriptions-item label="去除网页标识符">{{translate(dataProcessInfo.removeWebIdentifiers)}}</el-descriptions-item>
        <el-descriptions-item label="去除表情">{{translate(dataProcessInfo.removeEmoji)}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="过滤配置">
        <el-descriptions-item label="检查文档的词数目">{{translate(dataProcessInfo.numberWords)}}
          {{dataProcessInfo.numberWords?(dataProcessInfo.numberMinRange +'——'+ dataProcessInfo.numberMaxRange):''}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的字重复率">{{translate(dataProcessInfo.characterRepetitionRemoval)}}
          {{dataProcessInfo.characterRepetitionNumber}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的词重复率">{{translate(dataProcessInfo.wordRepetitionRemoval)}}
          {{dataProcessInfo.wordRepetitionNumber}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的特殊字符率">{{translate(dataProcessInfo.specialcharacters)}}
          {{dataProcessInfo.specialNumber}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的色情暴力词率">{{translate(dataProcessInfo.flaggedWords)}}
          {{dataProcessInfo.flaggedNumber}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的语言概率">{{translate(dataProcessInfo.langId)}}
          {{dataProcessInfo.langNumber}}</el-descriptions-item>
        <el-descriptions-item label="检查文档的困惑度">{{translate(dataProcessInfo.perplexity)}}
          {{dataProcessInfo.perplexityNumber}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="去重配置">
        <el-descriptions-item label="simhash-operator">{{translate(dataProcessInfo.simhashOperator)}}
          {{dataProcessInfo.simhashOperatorNumber}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="去隐私配置">
        <el-descriptions-item label="去除Email">{{translate(dataProcessInfo.replaceEmails)}}</el-descriptions-item>
        <el-descriptions-item label="去除IP地址">{{translate(dataProcessInfo.replaceIp)}}</el-descriptions-item>
        <el-descriptions-item label="去除数字">{{translate(dataProcessInfo.replaceIdentifier)}}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
import { getDataProcessInfo } from "@/api/dataSet/dataProcessing.js";
export default {
  name: "ReviewDataProcessing",
  data() {
    return {
      // 遮罩层
      loading: true,
      id: '',
      dataProcessInfo: {}
    };
  },
  activated() {
    this.getInfo()
  },
  methods: {
    /** 查询数据集详情 */
    getInfo() {
      this.id = this.$route.query && this.$route.query.id
      this.loading = true;
      getDataProcessInfo(this.id).then(response => {
        this.dataProcessInfo = response.data;
        this.loading = false;
      });
    },
    translate(type) {
      return type ? '是' : '否'
    }
  }
};
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
</style>

