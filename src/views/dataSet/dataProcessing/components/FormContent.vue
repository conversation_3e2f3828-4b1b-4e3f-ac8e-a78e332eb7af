<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <div class="ck-form-item_title">基本信息</div>
      <el-form-item label="任务名称" prop="etlTaskName">
        <el-input class="ck-input" v-model="form.etlTaskName" placeholder="请输入任务名称" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="处理前数据集" prop="sourceDatasetStrId">
        <el-select class="ck-input" v-model="form.sourceDatasetStrId" placeholder="请选择处理前数据集" @change="handleChange">
          <el-option v-for="item in dataSetOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理后数据集" prop="destDatasetStrName">
        <el-input class="ck-input" v-model="form.destDatasetStrName" placeholder="请输入处理后数据集" maxlength="50"
          show-word-limit />
      </el-form-item>
      <div class="ck-form-item_title">异常清洗配置</div>
      <el-form-item label="移除不可见字符" prop="removeInvisibleCharacter">
        <el-switch v-model="form.removeInvisibleCharacter" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="规范化空格" prop="replaceUniformWhitespace">
        <el-switch v-model="form.replaceUniformWhitespace" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="去除乱码" prop="removeNonMeaningCharacters">
        <el-switch v-model="form.removeNonMeaningCharacters" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="繁体转简体" prop="replaceTraditionalChineseToSimplified">
        <el-switch v-model="form.replaceTraditionalChineseToSimplified" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="去除网页标识符" prop="removeWebIdentifiers">
        <el-switch v-model="form.removeWebIdentifiers" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="去除表情" prop="removeEmoji">
        <el-switch v-model="form.removeEmoji" active-text="开" inactive-text="关" />
      </el-form-item>
      <div class="ck-form-item_title">过滤配置</div>
      <el-form-item label="检查文档的词数目" prop="numberWords">
        <el-switch v-model="form.numberWords" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number ck-input-number-right " v-model="form.numberMinRange"
          controls-position="right" />
        ——
        <el-input-number class="ck-input-number" v-model="form.numberMaxRange" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的字重复率" prop="characterRepetitionRemoval">
        <el-switch v-model="form.characterRepetitionRemoval" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.characterRepetitionNumber" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的词重复率" prop="wordRepetitionRemoval">
        <el-switch v-model="form.wordRepetitionRemoval" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.wordRepetitionNumber" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的特殊字符率" prop="specialcharacters">
        <el-switch v-model="form.specialcharacters" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.specialNumber" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的色情暴力词率" prop="flaggedWords">
        <el-switch v-model="form.flaggedWords" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.flaggedNumber" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的语言概率" prop="langId">
        <el-switch v-model="form.langId" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.langNumber" controls-position="right" />
      </el-form-item>
      <el-form-item label="检查文档的困惑度" prop="perplexity">
        <el-switch v-model="form.perplexity" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.perplexityNumber" controls-position="right" />
      </el-form-item>
      <div class="ck-form-item_title">去重配置</div>
      <el-form-item label="simhash-operator" prop="simhashOperator">
        <el-switch v-model="form.simhashOperator" active-text="开" inactive-text="关" />
        <el-input-number class="ck-input-number" v-model="form.simhashOperatorNumber" controls-position="right" />
      </el-form-item>
      <div class="ck-form-item_title">去隐私配置</div>
      <el-form-item label="去除Email" prop="replaceEmails">
        <el-switch v-model="form.replaceEmails" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="去除IP地址" prop="replaceIp">
        <el-switch v-model="form.replaceIp" active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="去除数字" prop="replaceIdentifier">
        <el-switch v-model="form.replaceIdentifier" active-text="开" inactive-text="关" />
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { addDataProcess, getAllDataSet } from "@/api/dataSet/dataProcessing.js";
export default {
  name: 'FormContent',
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        etlTaskName: '',
        sourceDatasetStrId: '',
        sourceDatasetStrName: '',
        destDatasetStrName: '',
        removeInvisibleCharacter: true,
        replaceUniformWhitespace: true,
        removeNonMeaningCharacters: true,
        replaceTraditionalChineseToSimplified: true,
        removeWebIdentifiers: true,
        removeEmoji: true,
        numberWords: true,
        numberMinRange: 0,
        numberMaxRange: 10000,
        characterRepetitionRemoval: true,
        characterRepetitionNumber: 0.2,
        wordRepetitionRemoval: true,
        wordRepetitionNumber: 0.96,
        specialcharacters: true,
        specialNumber: 0.3,
        flaggedWords: true,
        flaggedNumber: 0.001,
        langId: true,
        langNumber: 0.85,
        perplexity: true,
        perplexityNumber: 1110,
        simhashOperator: true,
        simhashOperatorNumber: 4,
        replaceEmails: true,
        replaceIp: true,
        replaceIdentifier: true,
        menuRouting: ''
      },
      rules: {
        etlTaskName: [
          { required: true, message: '请输入任务名称', trigger: ['blur', 'change'] },
        ],
        sourceDatasetStrId: [
          { required: true, message: '请输入处理前数据集', trigger: ['blur', 'change'] },
        ],
        destDatasetStrName: [
          { required: true, message: '请输入处理后数据集名称', trigger: ['blur', 'change'] },
        ],
      },
      dataSetOptions: [],
    }
  },
  created() {
    this.getAllDataSet()
  },
  methods: {
    getAllDataSet() {
      getAllDataSet(this.$route.query.menuRouting).then(res => {
        const dataSetOptions = []
        if (res.data && res.data.length > 0) {
          res.data.forEach(item => {
            dataSetOptions.push({
              label: item.groupName,
              value: item.id,
              projectType: item.projectType
            })
          })
        }
        this.dataSetOptions = dataSetOptions
      })
      this.form.sourceDatasetStrId = this.$route.query && this.$route.query.id && Number(this.$route.query.id)
      this.form.sourceDatasetStrName = this.$route.query && this.$route.query.name
      this.form.projectType = this.$route.query && this.$route.query.projectType
      this.form.menuRouting = this.$route.query && this.$route.query.menuRouting
    },
    handleSubmint() {
      console.log(this.form)
      addDataProcess(this.form).then(res => {
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.handleBack()
        }
      })
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push(this.$route.query.menuRouting)
    },
    handleChange(sourceDatasetStrId) {
      const sourceDatasetStrName = this.dataSetOptions.filter(item => item.value == sourceDatasetStrId)
      this.form.sourceDatasetStrName = sourceDatasetStrName.length > 0 && sourceDatasetStrName[0].label
      this.form.projectType = sourceDatasetStrName.length > 0 && sourceDatasetStrName[0].projectType
    },

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.ck-form-item_title {
  margin: 0;
  padding: 0;
  height: 26px;
  line-height: 26px;
  font-size: 15px;
  font-weight: normal;
  position: relative;
  padding-left: 10px;
  display: flex;
  color: #333;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  &::before {
    width: 4px;
    height: 16px;
    display: block;
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #1682e6;
    margin: auto;
  }
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.ck-input-number {
  margin-left: 20px;
}
.ck-input-number-right {
  margin-right: 20px;
}
</style>

