<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="etlTaskName">
        <el-input v-model="queryParams.etlTaskName" placeholder="请输入任务名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建任务</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataProcessingList">
      <el-table-column label="任务名称" prop="etlTaskName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="清洗状态" prop="processStatus" min-width="100">
        <template slot-scope="scope">
          <span>{{processStatusChange(scope.row.processStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="清洗方式" prop="entityType" min-width="100">
        <template slot-scope="scope">
          <span>{{entityTypeChange(scope.row.entityType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="清洗前数据集" prop="sourceDatasetStrName" min-width="100" />
      <el-table-column label="清洗后数据集" prop="destDatasetStrName" min-width="100" />
      <el-table-column label="开始时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-video-pause"
            @click="handletermination(scope.row)">终止任务</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getDataProcessList,
  delDataProcess
} from "@/api/dataSet/dataProcessing.js";
export default {
  name: "DataProcessing",
  dicts: ['process_status', 'entity_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据集管理表格数据
      dataProcessingList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        etlTaskName: undefined,
        menuRouting: this.$route.path,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList()
  },
  methods: {
    /** 查询数据集管理列表 */
    getList() {
      this.loading = true;
      getDataProcessList(this.queryParams).then(response => {
        this.dataProcessingList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      var path=this.getSecondSlash(this.$route.path)+'/addDataProcessing';
      var params=this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: { 'menuRouting': this.$route.path }
      })
    },
    /** 查看按钮操作 */
    handleReview(row) {
      var path=this.getSecondSlash(this.$route.path)+'/reviewDataProcessing';
      var params=this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: { 'id': row.id }
      })
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal.confirm('是否确认删除数据集？').then(function () {
        return delDataProcess(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 字符串处理 */
    getSecondSlash(str) {
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },
    extractContent(str) {
      // 找到第二个和第三个 '/' 的位置
      let index1 = str.indexOf('/', str.indexOf('/') + 1); // 第二个 '/'
      let index2 = str.indexOf('/', index1 + 1); // 第三个 '/'

      // 提取这两个索引之间的子字符串
      if (index1 !== -1 && index2 !== -1) {
        return str.substring(index1 + 1, index2);
      } else {
        return null; // 如果没有找到符合要求的 '/'，返回null或其他默认值
      }
    },
    /**终止 */
    handletermination() {

    },
    processStatusChange(processStatus) {
      return this.selectDictLabelByVal(this.dict.type.process_status, processStatus)
    },
    entityTypeChange(entityType) {
      return this.selectDictLabelByVal(this.dict.type.entity_type, entityType)
    },
  }
};
</script>
