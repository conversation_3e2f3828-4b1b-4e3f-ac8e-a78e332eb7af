<template>
  <div class="app-container">
     <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
       <el-form-item label="接口名称" prop="groupName">
        <el-input v-model="queryParams.groupName" placeholder="请输入接口名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建接口</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="dataSetList">
      <el-table-column label="接口名称" prop="name" :show-overflow-tooltip="true" min-width="180" />
     <!-- <el-table-column label="接口代码" prop="code" min-width="100"/>-->
      <el-table-column label="简介" prop="nj" min-width="100"/>
      <!-- <el-table-column label="科目" prop="km" min-width="100"/>
      <el-table-column label="学期" prop="xq" min-width="100"/> -->
      <el-table-column label="更新时间" align="gxsj" prop="gxsj" min-width="150"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!--<el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>-->
          <el-button size="mini" type="text" icon="el-icon-download" >参数详情</el-button>
         <!-- <el-button size="mini" type="text" icon="el-icon-delete" >删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination   v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 定义上传图片的对话框 -->
    <el-dialog title="上传图片" :visible.sync="upload.open">
      <el-upload ref="upload" :headers="upload.headers" :action="getUploadUrl()" :disabled="upload.isUploading"
        :data="{ dataId: getDataId(), imageNumber: upload.imageNumber }" :on-success="handleSuccess"
        :on-error="handleError" :before-upload="beforeUpload">
        <el-button slot="trigger" @click="submitUpload">选择文件</el-button>
      </el-upload>

      <div style="margin-top: 56px;">
        <el-form>
          <el-form-item label="图片编号:">
            <el-input placeholder="请输入图片编号" v-model="upload.imageNumber" style="width: 550px;"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取消</el-button>
        <!--      <el-button type="primary" @click="submitUpload">上传</el-button>-->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDataSetList,
  delDataSet,
  release,
} from "@/api/dataSet/dataSetManagement.js";
import { getToken } from "@/utils/auth";

export default {
  name: "DataSetManagement",
  dicts: ["import_progress", "release_status", "project_type"],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 50,
      // 数据集管理表格数据
      dataSetList: [
        {name:"Lite-128K",code:"001",nj:"Lite系列轻量级模型，适用于快速推理任务。",gxsj:"2024-10-23"},
        {name:"character-fiction-8k",code:"002",nj:"角色分析的小规模模型.",gxsj:"2024-10-23"},
        {name:"Tiny-8k",code:"003",nj:"极小尺寸模型，适合资源受限设备。",gxsj:"2024-10-23"},
        {name:"Tiny-128k",code:"004",nj:"Tiny系列中的较大容量模型，兼顾性能与效率。",gxsj:"2024-10-23"},
        {name:"chatGlm",code:"005",nj:"对话式AI模型，擅长人机交互。",gxsj:"2024-10-23"},
        {name:"xlnet-base",code:"006",nj:"基础XLNet模型，用于多种NLP任务.",gxsj:"2024-10-23"},
        {name:"deberta-v3-cn",code:"007",nj:"具有较强的对话问答、内容创作生成等能力。",gxsj:"2024-10-23"},
        {name:"chatGlm-2.0",code:"008",nj:"对话式AI模型，增强对话能力。",gxsj:"2024-10-23"},
        {name:"LIama-2-13B",code:"009",nj:"大规模模型，拥有强大计算力。",gxsj:"2024-10-23"},
        {name:"chatglm-lipr",code:"0010",nj:"特别优化的聊天机器人模型，提升响应速度。",gxsj:"2024-10-23"},
      ],
      //上传图片弹窗显隐
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        dataId: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/test/storage/upload",
        imageNumber: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
        menuRouting: this.$route.path,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询数据集管理列表 */
    // getList() {
    //   this.loading = true;
    //   getDataSetList(this.queryParams).then((response) => {
    //     this.dataSetList = response.rows;
    //     this.total = response.total;
    //     this.loading = false;
    //   });
    // },
    getExplicitImplicit() {
      return this.getSecondSlash(this.$route.path) === '/dataSet';
    },
    submitUpload() {
      console.log(this.upload.imageNumber);
      // 检查图片编号是否已填写
      if (this.upload.imageNumber == '' || this.upload.imageNumber.trim() === '') {
        this.$message.error('请先填写图片编号！');
        return; // 阻止进一步执行
      }

      this.$refs.upload.submit(); // 触发Element Upload组件的上传
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleTraining":
          this.handleTraining(row);
          break;
        case "showUploadDialog":
          this.showUploadDialog(row);
          break;
        case "viewPicture":
          this.viewPicture(row);
          break;
        case "handleDel":
          this.handleDel(row);
          break;
        case "handlePublish":
          this.handlePublish(row);
          break;
        case "handleConduct":
          this.handleConduct(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      var path = this.getSecondSlash(this.$route.path) + '/addDataSet';
      var params = this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: { 'menuRouting': this.$route.path }
      });
    },
    /** 查看按钮操作 */
    handleReview(row) {
      var path = this.getSecondSlash(this.$route.path) + '/reviewDataSet';
      var params = this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: { id: row.id },
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除数据集？")
        .then(function () {
          return delDataSet(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport(row) {
      this.download(
        "test/dataSet/fileDownload",
        {
          id: row.id,
        },
        `数据集.jsonl`
      );
    },
    /**训练,跳转训练任务新增 */
    handleTraining(row) {
      var path = this.getSecondSlash(this.$route.path) + '/addTrainingMission';
      var params = this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: { datasetId: row.datasetId, groupName: row.groupName, 'menuRouting': this.$route.path },
      });
    },
    /**图片详情 */
    viewPicture(row) {
      this.$router.push({
        path: '/dataSet/pictureDetails',
        query: { dataId: row.id },
      });
    },
    /**上传图片 */
    showUploadDialog(row) {
      this.upload.open = true;
      this.upload.dataId = row.id;
    },
    // 图片上传成功处理
    handleSuccess(response, file) {
      if (response.code === 500) {
        this.$message.error('图片编号已存在');
      } else if (response.code !== 200) { // 或者其他成功的状态码
        // 处理其他非预期的code情况
        this.$message.error('上传过程中发生错误，请重试');
      } else {
        this.$message.success('图片上传成功！');
      }

      // 根据需要处理响应
      this.upload.open = false;
    },
    // 图片上传失败处理
    handleError(err, file) {
      this.$message.error('图片上传失败！');
      console.error(err);
    },
    // 上传前的校验，可以在这里限制文件类型、大小等
    beforeUpload(file) {
      console.log(this.upload.imageNumber);
      if (!this.upload.imageNumber || this.upload.imageNumber.trim() === '') {
        this.$message.error('请先填写图片编号！');
        return false; // 阻止文件上传
      }
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        this.$message.error('只能上传图片！');
        return false;
      }
      // 可以根据需要添加更多校验逻辑
      return true;
    },
    // 点击“上传”按钮时触发，如果需要额外处理可以在这里写
    getUploadUrl() {
      // return this.upload.url + '?dataId=' + this.upload.dataId;
      return this.upload.url;
    },
    getDataId() {
      return this.upload.dataId;
    },
    /**发布 */
    handlePublish(row) {
      if (row.entityCount < 100) {
        this.$message.error("数据集实体数量必须大于等于100才能发布");
        return;
      } else {
        this.$modal
          .confirm("是否确认发布数据集？")
          .then(function () {
            const params = { id: row.id, groupName: row.groupName };
            return release(params);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("发布已提交");
          })
          .catch(() => { });
      }
    },
    /**清洗，跳转数据集处理 */
    handleConduct(row) {
      var path = this.getSecondSlash(this.$route.path) + '/addDataProcessing';
      var params = this.extractContent(this.$route.path);
      this.$router.push({
        path: path,
        params: { dynamicPart: params },
        query: {
          id: row.id,
          name: row.groupName,
          projectType: row.projectType,
          'menuRouting': this.$route.path
        },
      });
    },
    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },
    extractContent(str) {
      // 找到第二个和第三个 '/' 的位置
      let index1 = str.indexOf('/', str.indexOf('/') + 1); // 第二个 '/'
      let index2 = str.indexOf('/', index1 + 1); // 第三个 '/'

      // 提取这两个索引之间的子字符串
      if (index1 !== -1 && index2 !== -1) {
        return str.substring(index1 + 1, index2);
      } else {
        return null; // 如果没有找到符合要求的 '/'，返回null或其他默认值
      }
    },
    importProgressChange(importProgress) {
      return this.selectDictLabelByVal(
        this.dict.type.import_progress,
        importProgress
      );
    },
    releaseStatusChange(releaseStatus) {
      return this.selectDictLabelByVal(
        this.dict.type.release_status,
        releaseStatus
      );
    },
    releaseProjectType(projectType) {
      const projectTypeString = projectType.toString();
      return this.selectDictLabelByVal(
        this.dict.type.project_type,
        projectTypeString
      );
    },
  },
};
</script>
