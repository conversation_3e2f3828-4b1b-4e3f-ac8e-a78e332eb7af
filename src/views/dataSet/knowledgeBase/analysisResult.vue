<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <!--      <el-form-item label="知识点类别" prop="knowledgeCategory">-->
      <!--        <el-input v-model="queryParams.knowledgeCategory" placeholder="请输入知识点类别" clearable style="width: 240px"-->
      <!--          @keyup.enter.native="handleQuery" />-->
      <!--      </el-form-item>-->
      <el-form-item label="理论知识" prop="category">
        <el-input v-model="queryParams.category" placeholder="请输入理论知识" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="章节" prop="chapter">
        <el-input v-model="queryParams.chapter" placeholder="请输入章节" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right;">
        <el-button v-if="flag=='edit'" type="primary" icon="el-icon-finished" size="mini"
          @click="handleSubmitResults">提交解析结果</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="flag=='edit'">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5" v-if="flag=='edit'">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="analysisList" class="research-group-table" border>
      <!--      <el-table-column label="知识点类别" prop="knowledgeCategory" min-width="80" />-->
      <el-table-column label="理论知识" prop="category" min-width="80" />
      <el-table-column label="关键词" prop="keyword" min-width="300" />
      <el-table-column label="章节" prop="chapter" min-width="300" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="flag=='edit'">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="1000px" append-to-body>
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="100px">
        <!--        <el-form-item label="知识点类别" prop="knowledgeCategory">-->
        <!--          <el-input class="ck-input" v-model="form.knowledgeCategory" placeholder="请输入知识点类别" />-->
        <!--        </el-form-item>-->
        <el-form-item label="理论知识" prop="category">
          <el-input class="ck-input" v-model="form.category" placeholder="请输入理论知识" />
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input type="textarea" autosize v-model="form.keyword" placeholder="请输入关键词" maxlength="100"
            show-word-limit />
        </el-form-item>
        <el-form-item label="章节" prop="chapter">
          <el-input class="ck-input" v-model="form.chapter" placeholder="请输入章节" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="提交解析结果" :visible.sync="submitResultsDialogOpen" width="600px" append-to-body>
      <el-form ref="resultForm" :model="resultForm" class="ck-form" :rules="rules" label-position="left"
        label-width="100px">
        <!--        <el-form-item label="学院名称" prop="colleName">-->
        <!--          <el-input v-model="resultForm.colleName" placeholder="请输入学院名称" />-->
        <!--        </el-form-item>-->
        <el-form-item label="出版社" prop="publishingHouse">
          <el-input v-model="resultForm.publishingHouse" placeholder="请输入出版社" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="resultForm.author" placeholder="请输入作者" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog" :loading="submitResultLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmitResult" :loading="submitResultLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-form>
        <el-form-item label="规则">
          <el-select v-model="isCover" placeholder="请选择您的规则" style="width: 80%">
            <el-option v-for="item in coverOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-upload ref="upload" :headers="upload.headers" :action="getUploadUrl()" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm" :loading="upload.btnLoading">确 定</el-button>
        <el-button @click="upload.open = false" :loading="upload.btnLoading">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAnalysisList,
  addAnalysis,
  updateAnalysis,
  delAnalysisr,
  submitAudit
} from "@/api/dataSet/knowledgeBase.js";
import { getToken } from "@/utils/auth";
export default {
  name: "AnalysisResult",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      analysisList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        textbookId: this.$route.query && this.$route.query.id,
      },
      dialogOpen: false,
      title: '修改',
      form: {},
      rules: {
        // knowledgeCategory: [
        //   { required: true, message: '请输入知识点类别', trigger: ['blur', 'change'] },
        // ],
        category: [
          { required: true, message: '请输入理论知识', trigger: ['blur', 'change'] },
        ],
        keyword: [
          { required: true, message: '请输入关键词', trigger: ['blur', 'change'] },
        ],
        colleName: [
          { required: true, message: '请输入学院名称', trigger: ['blur', 'change'] },
        ],
        publishingHouse: [
          { required: true, message: '请输入出版社', trigger: ['blur', 'change'] },
        ],
        author: [
          { required: true, message: '请输入作者', trigger: ['blur', 'change'] },
        ],
      },
      btnLoading: false,
      resultForm: {},
      submitResultsDialogOpen: false,
      submitResultLoading: false,
      flag: this.$route.query && this.$route.query.flag,
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        dataId: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/zhi/analysis/import",
        btnLoading: false,

      },
      isCover: 'N',
      coverOptions: [
        {
          label: '全量上传',
          value: 'N'
        }, {
          label: '增量上传',
          value: 'F'
        }
      ]
    };
  },
  watch: {
    "$route.query.id": {
      immediate: true,
      handler(value) {
        this.queryParams.textbookId = value
        this.getList();
      },
    },
    "$route.query.flag": {
      immediate: true,
      handler(value) {
        this.flag = value
      },
    },
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getAnalysisList(this.queryParams).then((response) => {
        this.analysisList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 新增
    handleAdd() {
      this.form = {
        textbookId: this.queryParams.textbookId
      };
      this.dialogOpen = true;
      this.title = '新增';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = row;
      this.dialogOpen = true;
      this.title = '修改';
    },
    // /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除此解析结果？")
        .then(function () {
          return delAnalysisr(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    //**修改 */
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm()) {
        if (this.form.id) {
          updateAnalysis(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.handleClose()
              this.btnLoading = false
            } else {
              this.btnLoading = false
            }
          }).catch(() => {
            this.btnLoading = false
          })
        } else {
          addAnalysis(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功')
              this.handleClose()
              this.btnLoading = false
            } else {
              this.btnLoading = false
            }
          }).catch(() => {
            this.btnLoading = false
          })
        }
      } else {
        this.btnLoading = false
      }
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleClose() {
      this.dialogOpen = false
      this.$refs.form.clearValidate()
      this.getList()
    },
    //**提交 */
    handleSubmitResults() {
      this.resultForm = {
        //id: this.$route.query && this.$route.query.id,
        textbookId: this.$route.query && this.$route.query.id,
        submitStatus: 'N',
        examineFlag: '1'
      };
      this.submitResultsDialogOpen = true;
    },
    handleSubmitResult() {
      this.submitResultLoading = true
      if (this.validateResultForm()) {
        submitAudit(this.resultForm).then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this.handleCloseDialog()
            this.submitResultLoading = false
            this.flag = 'review'
          } else {
            this.submitResultLoading = false
          }
        }).catch(() => {
          this.submitResultLoading = false
        })
      } else {
        this.submitResultLoading = false
      }
    },
    validateResultForm() {
      let validate
      this.$refs.resultForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleCloseDialog() {
      this.submitResultsDialogOpen = false
      this.$refs.resultForm.clearValidate()
      this.getList()
      this.resetForm("resultForm");
    },
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code == 200) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$message.success('导入成功')
        this.getList();
        this.upload.btnLoading = false
      } else {
        this.upload.btnLoading = false
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
      this.upload.btnLoading = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('zhi/analysis/export', {
        ...this.queryParams
      }, `解析结果_${new Date().getTime()}.xlsx`)
    },
    getUploadUrl() {
      return this.upload.url + '?textbookId=' + this.queryParams.textbookId + '&isCover=' + this.isCover;
    }
  },
};
</script>
<style scoped>
.ck-input {
  width: 300px;
}
</style>
