<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="知识库名称" prop="kbName">
        <el-input v-model="queryParams.kbName" placeholder="请输入知识库名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建知识库</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="baseList">
      <el-table-column label="知识库名称" prop="kbName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="是否开启知识增强" prop="isEnhanced" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.isEnhanced == true ? "是" : "否"}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" min-width="100" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-upload" @click="handleImport(scope.row)">文件导入</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="文件导入" :visible.sync="dialogOpen" width="500px" append-to-body>
      <el-form>
        <el-form-item label="是否开启知识增强" prop="isEnhanced">
          <el-switch v-model="isEnhanced" active-text="是" inactive-text="否" />
        </el-form-item>
      </el-form>
      <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple
        :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept=".doc,.txt,.doc,.pdf">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">支持doc，txt，doc，pdf文件上传</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleImportFile">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBaseList,
  importFile
} from "@/api/dataSet/knowledgeBase.js";
import { getToken } from "@/utils/auth";

export default {
  name: "KnowledgeBase",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      baseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dialogOpen: false,
      kbId: '',
      uploadUrl: process.env.VUE_APP_BASE_API + "/zhi/kbfile/upload", //
      uploadData: { modeltype: 'zsk' },
      headers: {
        Authorization: "Bearer " + getToken(),
        timeout: 300000
      },
      fileList: [],
      isEnhanced: false
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getBaseList(this.queryParams).then((response) => {
        this.baseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: "/knowledgeBase/addKnowledgeBase",
      });
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/knowledgeBase/reviewKnowledgeBase",
        query: { kbId: row.kbId },
      });
    },
    handleImport(row) {
      this.dialogOpen = true
      this.fileList = []
      this.kbId = row.kbId
    },
    handleUploadSuccess(res, file,) {
      if (res.code == 200) {
        file.id = res.data.id;
        this.fileList.push(res.data.id);
      } else {
        this.$message.error(res.msg);
        this.$refs.upload.handleRemove(file);
      }
    },
    handleRemove(file, fileList) {
      const findex = this.fileList.map(f => f.indexOf(file.id));
      if (findex > -1) {
        this.fileList.splice(findex, 1);
      }
    },
    handleImportFile() {
      const queryForm = {
        kbId: this.kbId,
        fileIds: this.fileList,
        isEnhanced: this.isEnhanced
      }
      const loading = this.$loading({
        lock: true,
        text: `正在上传,请稍等...`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      importFile(queryForm).then(res => {
        if (res.code === 200) {
          this.$message.success('导入成功')
          this.dialogOpen = false
          this.fileList = []
          loading.close()
        } else {
          loading.close()
        }
      }).catch(() => {
        loading.close()
      })
    },
    // /** 删除按钮操作 */
    // handleDel(row) {
    //   this.$modal
    //     .confirm("是否确认删除知识库？")
    //     .then(function () {
    //       return delBase(row.id);
    //     })
    //     .then(() => {
    //       this.getList();
    //       this.$modal.msgSuccess("删除成功");
    //     })
    //     .catch(() => { });
    // },

  },
};
</script>
