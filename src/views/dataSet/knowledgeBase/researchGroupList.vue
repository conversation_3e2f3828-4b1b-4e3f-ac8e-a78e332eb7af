<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="教师姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入教师姓名" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8" v-if="checkPermi(['create:base:createCourse']) ||isPersonCharge">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">添加</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="researchGroupList" class="research-group-table">
      <el-table-column label="教师ID" prop="teacherId" min-width="60" />
      <el-table-column label="教师姓名" prop="name" min-width="60" />
      <el-table-column label="学校" prop="univerName" min-width="80" />
      <el-table-column label="学院" prop="colleName" min-width="80" />
      <el-table-column label="性别" prop="sex" min-width="50">
        <template slot-scope="scope">
          {{scope.row.sex == '0'? '男':'女'}}
        </template>
      </el-table-column>
      <el-table-column label="职称" prop="title" min-width="60" />
      <el-table-column label="学历" prop="education" min-width="60" />

      <el-table-column label="是否为负责人" prop="isPersonCharge" min-width="100">
        <template slot-scope="scope">
          {{scope.row.isPersonCharge == '1'? '是':'否'}}
        </template>
      </el-table-column>
      <el-table-column label="研究方向" prop="researchDirection" min-width="180">
        <template slot-scope="scope">
          <el-popover placement="right" width="400" trigger="hover">
            {{ scope.row.researchDirection }}
            <el-button type="text" size="small" slot="reference" style="color: #606266;">
              {{scope.row.researchDirection&&scope.row.researchDirection.length>20? scope.row.researchDirection.substr(0,20) + "..." : scope.row.researchDirection}}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="研究成果" prop="kbName" min-width="180">
        <template slot-scope="scope">
          <el-popover placement="right" width="400" trigger="hover"
            v-if="scope.row.teacherFindingsList&&scope.row.teacherFindingsList.length > 0">
            <el-table :data="scope.row.teacherFindingsList" :show-header="false">
              <el-table-column type="index" />
              <el-table-column min-width="300" property="researchFindings" label="研究成果"></el-table-column>
            </el-table>
            <el-button type="text" size="small" slot="reference" style="color: #606266;">
              {{ scope.row.teacherFindingsList[0].researchFindings&&scope.row.teacherFindingsList[0].researchFindings.length>20?scope.row.teacherFindingsList[0].researchFindings.substr(0,20) + "...":scope.row.teacherFindingsList[0].researchFindings }}</el-button>
          </el-popover>
          <span v-else>{{ '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-if="checkPermi(['create:base:createCourse'])||(isPersonCharge&&scope.row.isPersonCharge=='1'&&scope.row.isOneself=='T')||(isPersonCharge&&scope.row.isPersonCharge!='1')||(!isPersonCharge&&scope.row.isOneself=='T')">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)"
            v-if="checkPermi(['create:base:createCourse'])||(isPersonCharge&&scope.row.isPersonCharge!='1')">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="1300px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form teacher-form" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="6">
            <el-form-item label="教师姓名" prop="name">
              <el-select class="ck-input" v-model="form.name" filterable remote reserve-keyword placeholder="请输入教师姓名"
                :remote-method="remoteMethod" :loading="selectLoading" @change="teacherChange"
                :disabled="form.isOneself=='T'">
                <el-option v-for="item in options" :key="item.teacherId" :label="item.teacherName"
                  :value="item.teacherId">
                  <span style="margin-right: 5px;">{{ item.teacherName }}</span>
                  <span style="margin-right: 5px;"> {{ item.teacherId }}</span>
                  <span style=" margin-right: 5px;">{{ item.univerName }}</span>
                  <span style="margin-right: 5px;">{{ item.colleName }} </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span=" 6">
            <el-form-item label="教师ID" prop="teacherId">
              <el-input class="ck-input" v-model="form.teacherId" readonly placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学校" prop="univerName">
              <el-input class="ck-input" v-model="form.univerName" readonly placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学院" prop="colleName">
              <el-input class="ck-input" v-model="form.colleName" readonly placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item ref="radio" label="性别" prop="sex">
              <el-radio-group v-model="form.sex">
                <el-radio label="0">男</el-radio>
                <el-radio label="1">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="职称" prop="title">
              <el-input class="ck-input" v-model="form.title" placeholder="请输入职称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学历" prop="education">
              <el-input class="ck-input" v-model="form.education" placeholder="请输入学历" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否负责人" prop="isPersonCharge">
              <el-select class="ck-input" v-model="form.isPersonCharge" :disabled="isPersonCharge">
                <el-option v-for="dict in dict.type.is_person_charge" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="研究方向" prop="researchDirection">
              <el-input type="textarea" autosize v-model="form.researchDirection" placeholder="请输入研究方向" maxlength="300"
                show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="研究成果">
              <div v-for="(item,index) in teacherFindingsList" :key="index" style="margin-bottom: 5px;">
                <el-input type="textarea" autosize style="width:90%;margin-right:10px" v-model="item.researchFindings"
                  maxlength="500" show-word-limit></el-input>
                <el-button type="danger" style="float: right;" @click="handleDelete(index)">删除</el-button>
              </div>
              <el-button type="primary" plain style="margin-bottom: 10px;width: 100%;border-style:dashed;"
                @click="handleAddList">添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getResearchGroupList,
  addTeacher,
  updateTeacher,
  delTeacher,
  getTeacherinfo
} from "@/api/dataSet/knowledgeBase.js";
import { checkPermi } from "@/utils/permission";
export default {
  name: "ResearchGroupList",
  dicts: ['is_person_charge'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      researchGroupList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        disciplineId: this.$route.query && this.$route.query.disciplineId,
        majorId: this.$route.query && this.$route.query.majorId,
        courseName: this.$route.query && this.$route.query.courseName
      },
      dialogOpen: false,
      title: '添加',
      form: {
        sex: '0',
        disciplineId: this.$route.query && this.$route.query.disciplineId,
        majorId: this.$route.query && this.$route.query.majorId,
        courseName: this.$route.query && this.$route.query.courseName
      },
      teacherFindingsList: [],
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: ['blur', 'change'] },
        ],
        sex: [
          { required: true, message: '请选择性别', trigger: ['blur', 'change'] },
        ],
        // title: [
        //   { required: true, message: '请输入职称', trigger: ['blur', 'change'] },
        // ],
        // education: [
        //   { required: true, message: '请输入学历', trigger: ['blur', 'change'] },
        // ],
        // researchDirection: [
        //   { required: true, message: '请输入研究方向', trigger: ['blur', 'change'] },
        // ]
      },
      isPersonCharge: (this.$route.query && this.$route.query.isPersonCharge == '1') ? true : false,
      btnLoading: false,
      selectLoading: false,
      options: []
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },

  methods: {
    checkPermi,
    /** 查询列表 */
    getList() {
      this.loading = true;
      getResearchGroupList(this.queryParams).then((response) => {
        this.researchGroupList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.form = {
        sex: '0',
        disciplineId: this.$route.query && this.$route.query.disciplineId,
        majorId: this.$route.query && this.$route.query.majorId,
        courseName: this.$route.query && this.$route.query.courseName,
        isPersonCharge: checkPermi(['create:base:createCourse']) ? '1' : '0',
      }
      this.dialogOpen = true;
      this.title = '添加';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = row;
      this.teacherFindingsList = row.teacherFindingsList
      this.dialogOpen = true;
      this.title = '修改';
    },
    // /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除此教师？")
        .then(function () {
          return delTeacher(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm() && this.validateTeacherFindings()) {
        const queryForm = {
          ...this.form,
          teacherFindingsList: this.teacherFindingsList
        }
        if (this.form.id) {
          updateTeacher(queryForm).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.handleClose()
              this.btnLoading = false
            } else {
              this.btnLoading = false
            }
          }).catch(() => {
            this.btnLoading = false
          })
        } else {
          addTeacher(queryForm).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.handleClose()
              this.btnLoading = false
            } else {
              this.btnLoading = false
            }
          }).catch(() => {
            this.btnLoading = false
          })
        }
      } else {
        this.btnLoading = false
      }
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleClose() {
      this.dialogOpen = false
      this.$refs.form.clearValidate()
      this.getList()
      this.resetForm("form");
      this.teacherFindingsList = []
    },
    handleAddList() {
      this.teacherFindingsList.push({ researchFindings: '' })
    },
    handleDelete(index) {
      this.teacherFindingsList.splice(index, 1)
    },
    validateTeacherFindings() {
      var flag = true
      for (var i = 0; i < this.teacherFindingsList.length; i++) {
        if (this.teacherFindingsList[i].researchFindings == '') {
          flag = flag && false
        }
      }
      if (!flag) {
        this.$message.warning('请填写研究成果')
      }
      return flag
    },
    remoteMethod(query) {
      if (query !== '') {
        this.selectLoading = true;
        const queryForm = {
          teacherName: query,
          flag: this.$route.query && this.$route.query.flag
        }
        getTeacherinfo(queryForm).then(res => {
          if (res.code === 200) {
            this.options = res.data
            this.selectLoading = false
          } else {
            this.selectLoading = false
            this.options = [];
          }
        }).catch(() => {
          this.selectLoading = false
          this.options = [];
        })
      } else {
        this.options = [];
      }
    },
    teacherChange(teacherId) {
      const teacherInfo = this.options.filter(item => item.teacherId == teacherId)
      this.form.name = teacherInfo.length > 0 && teacherInfo[0].teacherName
      this.form.teacherId = teacherInfo.length > 0 && teacherInfo[0].teacherId
      this.form.univerName = teacherInfo.length > 0 && teacherInfo[0].univerName
      this.form.univerId = teacherInfo.length > 0 && teacherInfo[0].univerId
      this.form.colleName = teacherInfo.length > 0 && teacherInfo[0].colleName
      this.form.colleId = teacherInfo.length > 0 && teacherInfo[0].colleId
    },
  },
};
</script>
<style scoped>
.ck-input {
  width: 300px;
}
.teacher-form {
  height: calc(100vh - 250px);
  overflow-y: auto;
}
</style>
