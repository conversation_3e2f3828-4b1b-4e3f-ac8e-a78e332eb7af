<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="专业名称" prop="majorName">
        <el-input v-model="queryParams.majorName" placeholder="请输入专业名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" v-if="checkPermi(['create:base:createCourse'])">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建课程</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="courseList">
      <el-table-column label="学院" prop="collegeName" min-width="200" />
      <el-table-column label="专业" prop="majorName" min-width="200" />
      <el-table-column label="课程" prop="courseName" min-width="200">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.courseName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否联盟课" prop="isAllianceCourse" :show-overflow-tooltip="true" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.isAllianceCourse=="N"?'是':'否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-user"
            @click="handleToResearchGroup(scope.row)">课程组</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="创建课程" :visible.sync="dialogOpen" width="800px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="120px">
        <el-form-item label="院系" prop="affiliatedUnit">
          <el-cascader class="ck-input" v-model="form.affiliatedUnit" :options="options" @change="handleChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="课程" prop="courseName">
          <el-input class="ck-input" v-model="form.courseName" placeholder="请输入课程名称" @change="handleChange" />
        </el-form-item>
        <el-form-item label="是否联盟课" prop="isAllianceCourse">
          <el-radio-group v-model="form.isAllianceCourse">
            <el-radio label="N">是</el-radio>
            <el-radio label="F">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="importLoading">取 消</el-button>
        <el-button type="primary" @click="handleAddCourse" :loading="importLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getdCourseList,
  addCourse,
  getUniversity
} from "@/api/dataSet/knowledgeBase.js";
import { checkPermi } from "@/utils/permission";
export default {
  name: "DisciplineList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 课程列表数据
      courseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        disciplineId: this.$route.query && this.$route.query.disciplineId
      },
      total: 0,
      dialogOpen: false,
      importLoading: false,
      options: [],
      form: {
        courseName: '',
        disciplineId: this.$route.query && this.$route.query.disciplineId,
        isAllianceCourse: 'F',
        parseFlag: '1'
      },
      rules: {
        affiliatedUnit: [
          { required: true, message: '请选择院系', trigger: ['blur', 'change'] },
        ],
        courseName: [
          { required: true, message: '请输入课程名称', trigger: ['blur', 'change'] },
        ],
        isAllianceCourse: [
          { required: true, message: '请选择是否联盟课', trigger: ['blur', 'change'] },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getUniversity();
  },
  activated() {
    this.getList();
  },
  methods: {
    checkPermi,
    /** 查询列表 */
    getList() {
      this.loading = true;
      getdCourseList(this.queryParams).then((response) => {
        this.courseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 创建课程 */
    handleAdd() {
      this.dialogOpen = true
    },
    /** 点击课程操作 */
    handleReview(row) {
      this.$router.push({
        path: "/knowledgeBase/reviewKnowledgeBase",
        query: {
          disciplineId: row.disciplineId,
          majorId: row.majorId,
          affiliatedUnit: row.affiliatedUnit,
          courseName: row.courseName,
          isPersonCharge: row.isPersonCharge,
          isAllianceCourse: row.isAllianceCourse, },
      });
    },
    /**课程组 */
    handleToResearchGroup(row) {
      this.$router.push({
        path: "/knowledgeBase/researchGroup",
        query: { disciplineId: row.disciplineId, majorId: row.majorId, courseName: row.courseName, flag: row.isAllianceCourse, isPersonCharge: row.isPersonCharge },
      });
    },
    // 创建课程提交
    handleAddCourse() {
      this.importLoading = true
      if (this.validateForm()) {
        addCourse(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('创建成功')
            this.handleClose()
            this.importLoading = false
          } else {
            this.importLoading = false
          }
        }).catch(() => {
          this.importLoading = false
        })
      } else {
        this.importLoading = false
      }
    },
    // 提交校验
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    // 关闭弹窗
    handleClose() {
      this.dialogOpen = false
      this.getList()
      this.$refs.form.clearValidate()
      this.fileList = []
      this.resetForm("form");
    },
    // 查询课程是否存在
    handleChange() {
      if (this.form.courseName && this.form.courseName.length > 0) {

      }
    },
    // 获取院系
    getUniversity() {
      getUniversity().then((res) => {
        this.options = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
              return {
                value: item.id,
                label: item.name,
                children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                  };
                }) : []
              };
            }) : []
          };
        });
      })
    },
  },
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
.ck-input {
  width: 500px;
}
</style>
