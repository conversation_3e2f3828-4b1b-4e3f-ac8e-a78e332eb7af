<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="教材" prop="fileName">
        <el-input v-model="queryParams.fileName" placeholder="请输入教材名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8" v-if="isPersonCharge">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">添加教程</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="kbfileList" row-key="id"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column label="教材" prop="fileName" min-width="300" />
      <el-table-column label="创建人" prop="nickName" min-width="100" />
      <el-table-column label="解析状态" prop="parseStatus" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.parseFlag == '0'">{{ '-'}}</span>
          <span v-else>{{ parseStatusChange(scope.row.parseStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提交状态" prop="submitStatus" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.parseFlag == '0'">{{ '-'}}</span>
          <span v-else>{{ scope.row.submitStatus=='F'?'未提交':'已提交' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" prop="createTime" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <!-- parseFlag文件类型 ，operateFlag是否为创建人 -->
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-coin"
            v-if="scope.row.parseFlag == '1'&&scope.row.operateFlag == true"
            :disabled="scope.row.submitStatus !== 'F'" @click="handleTo(scope.row)">去解析</el-button>
          <el-button size="mini" type="text" icon="el-icon-tickets" v-if="scope.row.parseFlag == '1'"
            :disabled="scope.row.parseStatus !== 'analysisCompleted'"
            @click="handleReview(scope.row)">查看解析结果</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit"
            v-if="scope.row.parseFlag == '1'&&scope.row.operateFlag == true"
            :disabled="scope.row.parseStatus !== 'analysisCompleted' || scope.row.submitStatus !== 'F'"
            @click="handleEdit(scope.row)">修改解析结果</el-button>
          <el-button size="mini" type="text" icon="el-icon-tickets" v-if="scope.row.parseFlag == '0'"
            @click="handleToKnowledgeData(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
                     v-if="scope.row.operateFlag == true && scope.row.parseStatus !== 'inProgress' "
                     @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 解析对话框 -->
    <el-dialog title="知识点解析" :visible.sync="dialogVisible" width="60%">
      <el-form>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <div v-for="(item, index) in teacherFindingsList" :key="index" style="display: flex; align-items: flex-start; margin-bottom: 15px;">
                <!-- 章节名称 -->
                <el-col :span="10">
                  <div style="width: 100%;">
                    <el-input placeholder="请输入章节名称" v-model="item.chapter" style="width: 100%; margin-right: 10px;"></el-input>
                    <!-- 提示信息放置在输入框下方 -->
                    <div class="el-upload__tip" style="margin-top: 5px; width: 100%;">
                      支持上传的文件类型为：pdf,txt,docx，单个文件大小限制为20MB
                    </div>
                  </div>
                </el-col>
                <!-- 文件上传 -->
                <el-tooltip class="item" effect="light" content="最大20MB" placement="top-start">
                  <el-col :span="10" style="margin-left: 10px;">
                    <el-upload
                      class="upload-demo"
                      :action="uploadUrl"
                      :on-success="handleUploadSuccess.bind(null, index)"
                      :data="getUploadData(index)"
                      :before-remove="beforeRemove"
                      :file-list="item.fileList"
                      :headers="headers"
                      accept=".pdf,.txt,.docx"
                      :file-size-limit="20 * 1024 * 1024"
                      :before-upload="beforeUpload"
                      style="width: 100%;"
                      :limit="1">
                      <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                  </el-col>
                </el-tooltip>
                <!-- 删除按钮 -->
                <el-col :span="4">
                  <el-button type="danger" style="float: right;" @click="handleDelete(index)">删除</el-button>
                </el-col>
              </div>
              <el-button type="primary" plain style="margin-bottom: 10px;width: 100%;border-style:dashed;" @click="handleAddList">添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getKbfile,
  analyze,
  initiateParsing,
  delKnowledgeData,
} from "@/api/dataSet/knowledgeBase.js";
import { getToken } from "@/utils/auth";
import Cookies from "js-cookie";
export default {
  name: "ReviewKnowledgeBase",
  dicts: ["parse_status"],
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'jx1' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // fileId: ,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      textbookId:"",
      // 表格数据
      kbfileList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      isPersonCharge: (this.$route.query && this.$route.query.isPersonCharge == '1') ? true : false,
      dialogVisible: false,
      teacherFindingsList: [
        // 示例数据
        { chapter: '', researchFindings: '', fileList: [] }
      ],
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.queryParams.disciplineId = this.$route.query && this.$route.query.disciplineId
      this.queryParams.majorId = this.$route.query && this.$route.query.majorId
      this.queryParams.courseName = this.$route.query && this.$route.query.courseName
      this.loading = true;
      getKbfile(this.queryParams).then((response) => {
        this.kbfileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    getUploadData(index) {
      // 合并 index 和 uploadData
      return { ...this.uploadData, index };
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleToAnalyze(row) {
      this.$modal
        .confirm("是否确认解析此数据？")
        .then(function () {
          return analyze(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("正在解析，请稍后查看解析结果");
        })
        .catch(() => { });
    },
    // 查看解析结果
    handleReview(row) {
      this.$router.push({
        path: "/knowledgeBase/analysisResult",
        query: { id: row.id, flag: 'review', submitStatus: row.submitStatus },
      });
    },
    // 修改解析结果
    handleEdit(row) {
      this.$router.push({
        path: "/knowledgeBase/analysisResult",
        query: { id: row.id, flag: 'edit' },
      });
    },
    parseStatusChange(parseStatus) {
      return this.selectDictLabelByVal(
        this.dict.type.parse_status,
        parseStatus
      );
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: "/knowledgeBase/addKnowledgeBase",
        query: {
          disciplineId: this.$route.query && this.$route.query.disciplineId, majorId: this.$route.query && this.$route.query.majorId, affiliatedUnit: this.$route.query && this.$route.query.affiliatedUnit, courseName: this.$route.query && this.$route.query.courseName, isAllianceCourse:
            this.$route.query && this.$route.query.isAllianceCourse
        },

      });
    },
    // 详情
    handleToKnowledgeData(row) {
      this.$router.push({
        path: "/knowledgeBase/knowledgeData",
        query: { id: row.id, },
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认该教材？")
        .then(function () {
          return delKnowledgeData(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    beforeUpload(file) {
      //console.log(file.status)
      //.xlsx,.pdf,.docx
      // 允许的文件类型
      const allowedTypes = [
        'application/pdf', // PDF 文件
        'text/plain',      // TXT 文件
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX 文件
      ];
      const isAllowedType = allowedTypes.includes(file.type);

      if (!isAllowedType) {
        this.$message.error('仅支持上传pdf,txt,docx文件！');
        return false; // 阻止上传
      }

      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error('文件大小超过20MB，无法上传！');
        return false; // 阻止文件上传
      }
      return isLt20M;
    },

    handleTo(row) {
      this.textbookId = row.id;
      this.dialogVisible = true;
      // 如果需要初始化数据，可以在打开对话框时进行
    },
    handleAddList() {
      if (this.teacherFindingsList.length >= 5) {
        this.$message.warning('一次性最多5个'); // 使用 Element Plus 的提示框
        return;
      }
      this.teacherFindingsList.push({ chapter: '', researchFindings: '', fileList: [] });
    },
    handleDelete(index) {
      this.teacherFindingsList.splice(index, 1);
    },
    // handleUploadSuccess(response, file, fileList, index) {
    //   this.teacherFindingsList[index].fileList = fileList;
    // },
    // handleUploadSuccess(res, file,index) {
    //   console.log(res.data.id);
    //   console.log(this.teacherFindingsList);
    //   console.log(index);
    //   this.teacherFindingsList[index].fileList.push(res.data.id);
    //
    // },
    handleUploadSuccess(index, res, file, fileList) {

      if (index !== undefined && this.teacherFindingsList[index]) {
        const fileItem = { ...file, id: res.data.id };
        this.teacherFindingsList[index].fileList.push(fileItem);
      } else {
        console.error('Invalid index:', index);
      }
    },
    beforeRemove(file, fileList, index) {
      //console.log(file.status)
      if (file.status === 'success') {
        return this.$confirm(`确定移除 ${file.name}？`);
      }
      //console.log(file.status)
      if (file.status === 'ready') {
        return true;
      }
      return false; // 对未成功的文件，不允许移除
    },
    submitForm() {
      const param = {
        id: this.textbookId,
        language: Cookies.get("voiceType"),
        textbookKeywordAnalysisList: this.teacherFindingsList.map(item => ({
          chapter: item.chapter,
          fileId: item.fileList[0].id
        })),

      };
      // 提交表单逻辑
      initiateParsing(param).then(() => {
        this.getList();
        this.$modal.msgSuccess("正在解析，请稍后查看解析结果");
      })
      this.dialogVisible = false;
    },
    cancel(){
      this.teacherFindingsList = [
        // 示例数据
        { chapter: '', researchFindings: '', fileList: [] }
      ];
      this.dialogVisible = false;
    }
  },
};
</script>
