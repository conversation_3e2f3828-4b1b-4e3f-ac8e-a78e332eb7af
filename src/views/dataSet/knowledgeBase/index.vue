<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="baseList">
      <el-table-column label="知识库名称" prop="kbName" :show-overflow-tooltip="true" min-width="180">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.kbName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否开启知识增强" prop="isEnhanced" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.isEnhanced == true ? "是" : "否"}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="创建人" prop="createBy" min-width="100" /> -->
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getBaseList,
} from "@/api/dataSet/knowledgeBase.js";

export default {
  name: "KnowledgeBase",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      baseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getBaseList(this.queryParams).then((response) => {
        this.baseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/knowledgeBase/discipline",
        query: { disciplineId: row.id },
      });
    },
  },
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
