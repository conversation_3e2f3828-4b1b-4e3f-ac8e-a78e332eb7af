<template>
  <div class="app-container ck-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="mapping-tabs">
      <el-tab-pane label="统计图" name="statisticalChart">
        <satistical-chart v-if="activeName === 'statisticalChart'" />
      </el-tab-pane>
      <el-tab-pane label="关系图" name="relationalGraph">
        <relational-graph v-if="activeName === 'relationalGraph'" />
      </el-tab-pane>
      <el-tab-pane label="思维导图" name="mindMap">
        <mind-map v-if="activeName === 'mindMap'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SatisticalChart from './components/SatisticalChart.vue'
import RelationalGraph from './components/RelationalGraphs.vue'
import MindMap from './components/MindMap.vue'
export default {
  name: "MappingKnowledgeDomain",
  components: { SatisticalChart, RelationalGraph, MindMap },

  data() {
    return {
      activeName: 'statisticalChart',

    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === 'statisticalChart') {
      } else if (tab.name === 'relationalGraph') {
      } else if (tab.name === 'mindMap') {
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.ck-container {
  padding: 0 10px;
  margin: 0 auto;
  width: 100%;
}
</style>
<style lang="scss" >
.mapping-tabs {
  .el-tabs__header {
    margin: 0;
  }
}
</style>
