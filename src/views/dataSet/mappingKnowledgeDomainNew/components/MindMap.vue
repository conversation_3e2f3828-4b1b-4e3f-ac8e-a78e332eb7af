 
<template>
  <div class="tab-content">
    <G6Tree v-if="treedata.name" :treedata="treedata"></G6Tree>
  </div>
</template>
<script>
import { getMindMapping } from "@/api/dataSet/knowledgeBase.js";
import G6Tree from "./G6Tree.vue";
export default {
  components: {
    G6Tree,
  },
  data() {
    return {
      treedata: {},
    };
  },
  created() {
    this.getMindMapping()
  },
  methods: {
    getMindMapping() {
      getMindMapping().then((res) => {
        if (res.code == 200) {
          this.treedata = res.data;
          this.treedata = this.expandFirstTwoLevels(res.data)
          console.log(this.treedata, 'treedata')
        }
      });

    },
    expandFirstTwoLevels(data, level = 0) {
      if (level < 2) {
        data.collapsed = false;
        if (data.children) {
          data.children.forEach(child => this.expandFirstTwoLevels(child, level + 1));
        }
        return data
      } else if (level >= 2) {
        data.collapsed = true; // 默认折叠第三层及更深的节点
        if (data.children) {
          data.children.forEach(child => this.expandFirstTwoLevels(child, level + 1));
        }
        return data

      }
    },
  },
};
</script>
<style lang="scss" scoped>
.tab-content {
  padding: 0;
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 124px);
  background: url(../../../../assets/images/mapBack.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
</style>