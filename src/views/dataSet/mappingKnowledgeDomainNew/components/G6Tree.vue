<template>
  <div>
    <div id="container" class="container" />
  </div>
</template>
 
<script>
import G6 from '@antv/g6';

export default {
  data() {
    return {
      graph: undefined,
    };
  },
  props: {
    treedata: {
      type: Object
    }
  },
  watch: {
    "treedata": {
      handler: function (cur, old) {
        if (cur != old) {
          this.refresh();
        }
      },
      deep: true,	//对象内部的属性监听，也叫深度监听
      // immediate:true
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.treedata.name) {
        this.showChart(this.treedata);
        this.$forceUpdate()
      }
    })

  },
  methods: {
    refresh() {
      if (this.graph) {
        //销毁方法
        this.graph.destroy();
      }
      //重新实例化
      this.showChart();
      this.$forceUpdate()
    },
    showChart() {
      const that = this
      G6.registerNode(
        "tree-node",
        {
          drawShape: function drawShape(cfg, group) {
            const rect = group.addShape("rect", {
              // 节点框样式
              attrs: {
                fill: "#d1dce3",//节点框背景色
                // stroke: "red",
                x: 0,
                y: 0,
                width: 1,
                height: 1,
                radius: [12],
              },
              name: "rect-shape",
            });
            const content = cfg.name;
            const text = group.addShape("text", {
              // 节点文字样式
              attrs: {
                text: content,
                x: 0,
                y: 0,
                textAlign: "left",
                textBaseline: "middle",
                fill: "#12588d",//节点框内白色文字
                overFlow: "hidden",
                textOverflow: "ellipsis",
              },
              name: "text-shape",
            });
            const bbox = text.getBBox();
            const hasChildren = cfg.children && cfg.children.length > 0;
            rect.attr({
              x: -20,
              y: -15,
              width: 170, //节点框的宽度
              height: 27,
              textAlign: "left"
            });
            // 文字的位置
            text.attr({
              //   x: -bbox.width / 2 + 15,
              //   y: 2,
              x: 0,
              y: 0,
            });
            //   展开折叠图标样式
            if (hasChildren) {
              const direction = cfg.direction;
              group.addShape("marker", {
                attrs: {
                  //   x: bbox.width / 2 + 24,
                  //   y: 0,
                  x: direction == 'left' ? -20 : 150,
                  y: 0,
                  r: 6,
                  symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                  stroke: "#fff",
                  fill: "#17cfa3",//绿色展开按钮
                  lineWidth: 1,
                },
                name: "collapse-icon",
              });
            }
            return rect;
          },
          update: (cfg, item) => {
            const group = item.getContainer();
            const icon = group.find((e) => e.get("name") === "collapse-icon");
            icon.attr(
              "symbol",
              cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse
            );
          },
        },
        "single-node"
      );

      const container = document.getElementById("container");
      const width = container.scrollWidth;
      const height = container.scrollHeight || 500;
      console.log(width, height, container, container.scrollHeight, 'container.scrollHeight');
      const graph = new G6.TreeGraph({
        container: "container",
        width,
        height,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                const data = item.get("model");
                graph.updateItem(item, {
                  collapsed,
                });
                data.collapsed = !collapsed;
                return true;
              },
            },
            "drag-canvas",
            "zoom-canvas",
          ],
        },
        defaultNode: {
          type: "tree-node",
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
        },
        // 节点连接线样式
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            stroke: "#305fcbfc",
          },
        },
        layout: {
          type: "compactBox",
          direction: "H",
          // 判断节点向左展开还是向右展开
          getSide: (d) => {
            if (d.data.direction == 'right') {
              return 'right';
            }
            return 'left';
          },
          getId: function getId(d) {
            return d.id + "";
          },
          getHeight: function getHeight() {
            return 16;
          },
          // 节点框和线的宽度
          getWidth: function getWidth() {
            return 70;
          },
          getVGap: function getVGap() {
            return 20;
          },
          getHGap: function getHGap() {
            return 60;
          },
        },
      });
      this.graph = graph
      graph.data(this.treedata);
      graph.render();
      graph.fitCenter();

      graph.on('node:click', (evt) => {
        const clickedNode = evt.item;
        const model = clickedNode.getModel();
        // 只展开或折叠当前节点，而不影响其子节点的状态
        const newCollapsedState = !model.collapsed;
        // 更新节点的 collapsed 状态
        graph.updateItem(clickedNode, { collapsed: newCollapsedState });

        // 如果是展开操作，确保子节点保持折叠状态
        if (!newCollapsedState && clickedNode.get('children')) {
          clickedNode.get('children').forEach(child => {
            graph.updateItem(child, { collapsed: true }); // 确保子节点保持折叠
          });
        }

        // 刷新布局以反映更改
        graph.refreshLayout();
      });
      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(container.scrollWidth, container.scrollHeight);
        };
    },
  },
};
</script>
 
<style scoped>
.container {
  width: "100%";
  height: calc(100vh - 124px);
}
#nodeDetails {
  list-style: none;
  background-color: blue;
}
#nodeDetails > li {
  padding: 5px 0;
  text-align: left;
  background-color: #ebeefd;
}
::v-deep .g6-component-tooltip {
  background-color: #ccc;
}
.nodeClass {
  padding: 10px;
}
</style>