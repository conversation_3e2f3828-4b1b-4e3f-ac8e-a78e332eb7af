<template>
  <div class="tab-content">
    <el-form :model="queryForm" ref="queryForm" class="selectForm" :inline="true">
      <el-form-item label="关键词" prop="keyword">
        <el-select v-model="queryForm.keywordValue" style="width: 150px;margin-right: 5px;">
          <el-option v-for="dict in dict.type.knowledge_type_new" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
        <el-input v-model="queryForm.keyword" placeholder="请输入关键词" clearable style="width: 240px"
          @keyup.enter.native="getCount" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getCount">搜索</el-button>
      </el-form-item>
    </el-form>
    <div id="main" class="main" />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getKnowledgeMappingNew } from "@/api/dataSet/knowledgeBase.js";
import academic_discipline from "@/assets/symbolImg/academic_discipline.png"
import new_liberal_arts from "@/assets/symbolImg/new_liberal_arts.png"
import colle_name from "@/assets/symbolImg/colle_name.png"
import major_name from "@/assets/symbolImg/major_name.png"
import course from "@/assets/symbolImg/course.png"
import textbook from "@/assets/symbolImg/textbook.png"
import keyword from "@/assets/symbolImg/keyword.png"
import category from "@/assets/symbolImg/category.png"
import father_node from "@/assets/symbolImg/father_node.png"
import master_node from "@/assets/symbolImg/master_node.png"

export default {
  name: "MindMap",
  dicts: ['knowledge_type_new'],
  data() {
    return {
      keywordInit: '',
      keyword: '',
      queryForm: {
        keyword: '新文科',
        keywordValue: 'new_liberal_arts'
      },
      graph: {
        categories: [
          {
            "name": "学科",
            "value": "academic_discipline",
          },
          {
            "name": "新文科 ",
            "value": "new_liberal_arts",
          },
          {
            "name": "一级学科",
            "value": "colle_name",
          },
          {
            "name": "二级学科",
            "value": "major_name",
          },
          {
            "name": "课程",
            "value": "course",
          },
          {
            "name": "教材",
            "value": "textbook",
          },
          {
            "name": "知识点",
            "value": "keyword",
          },
          {
            "name": "理论知识",
            "value": "category",
          },
        ]
      },
      myChart: null,
    };
  },
  created() {
  },
  mounted() {
    this.getCount()

  },
  methods: {
    getCount() {
      if ((!this.queryForm.parentKeywordValue || this.queryForm.parentKeywordValue.length == 0) && this.queryForm.keywordValue != 'new_liberal_arts') {
        this.queryForm.parentKeywordValue = 'new_liberal_arts'
        this.queryForm.parentKeyword = '新文科'
      } else if ((!this.queryForm.parentKeywordValue || this.queryForm.parentKeywordValue.length == 0) && this.queryForm.keywordValue == 'new_liberal_arts') {
        this.queryForm.keyword = '新文科'
      }
      getKnowledgeMappingNew(this.queryForm).then(res => {
        this.queryForm.parentKeywordValue = null
        this.queryForm.parentKeyword = null
        this.queryForm.parentFlag = null
        this.graph.nodes = res.data.literatrueNodesData.map(item => {
          var categoryIndex
          this.graph.categories.map(function (a, i) {
            if (a.value == item.keywordValue) {
              categoryIndex = i;
            }
          })
          return {
            id: item.id.toString(),
            category: categoryIndex,
            name: item.name,
            symbol: item.hostFlag == '1' ? 'image://' + master_node : item.parentFlag == '1' ? 'image://' + father_node : item.keywordValue == 'academic_discipline' ? 'image://' + academic_discipline : item.keywordValue == 'new_liberal_arts' ? 'image://' + new_liberal_arts : item.keywordValue == 'new_liberal_arts' ? 'image://' + new_liberal_arts :
              item.keywordValue == 'colle_name' ? 'image://' + colle_name : item.keywordValue == 'major_name' ? 'image://' + major_name : item.keywordValue == 'course' ? 'image://' + course : item.keywordValue == 'textbook' ? 'image://' + textbook : item.keywordValue == 'keyword' ? 'image://' + keyword : 'image://' + category,
            symbolSize: item.gradeFlag == 1 ? 76 : item.gradeFlag == 2 ? 55 : 43,
            value: item.count,
            keywordValue: item.keywordValue,
            onclickFlag: item.onclickFlag,
            parentFlag: item.parentFlag,
            gradeFlag: item.gradeFlag - 1,
            label: {
              show: true
            }
          }
        })
        this.graph.links = res.data.literatrueLinks.map(item => {
          return {
            source: item.source.toString(),
            target: item.target.toString()
          }
        })
        this.initChart();
      })
    },
    initChart() {
      var chartDom = document.getElementById('main');
      this.myChart = echarts.init(chartDom);
      var option;
      this.myChart.showLoading();
      this.myChart.hideLoading();
      function ellipsis(str, maxLength) {
        return str.length > maxLength ? str.slice(0, maxLength) + '...' : str;
      }
      this.graph.nodes.forEach(function (node) {
        node.tooltip = {
          show: node.name.length > 10 ? true : false,
        };
      });
      option = {
        title: {
          text: '知识图谱',
          subtext: 'Default layout',
          top: 'bottom',
          left: 'right'
        },
        tooltip: {
          formatter: params => {
            if (params.dataType == "node") {
              let dataStr = `<div style="display:flex;justify-content:space-between;align-items:center">
              <span style="display:block;width:10px;height:10px;background-color:${params.color};"></span>
              <p style="font-weight:bold;margin:5px">${params.name}</p></div>`;
              return dataStr;
            }
            if (params.dataType == "edge") {
              var linkName = params.name;
              var nameString = linkName.split(">");
              var linkNameFirst = nameString[0].trim();
              var linkNameLast = nameString[1].trim();
              var dataList = [];
              for (var j = 0; j < this.graph.nodes.length; j++) {
                if (linkNameFirst == this.graph.nodes[j].id || linkNameLast == this.graph.nodes[j].id) {
                  dataList.push(this.graph.nodes[j]);
                }
              }
              return '<span style="padding-left:5px;height:30px;line-height:30px;display: inline-block;font-size: 14px;">' + dataList[0].name + '->' + dataList[1].name + '</span>';
            }
          }
        },
        legend: {
          show: true,
          type: 'plain',
          left: '20',
          bottom: '120',
          width: 50,
          height: 500,
          padding: 5,
          textStyle: {
            fontSize: 14,//字体大小
            color: '#393939'//字体颜色
          },
          data: [
            {
              name: '学科',
              value: "academic_discipline",
              icon: 'image://' + academic_discipline
            },
            {
              name: '新文科',
              value: "new_liberal_arts",
              icon: 'image://' + new_liberal_arts
            },
            {
              name: '一级学科',
              value: "colle_name",
              icon: 'image://' + colle_name
            },
            {
              name: '二级学科',
              value: "major_name",
              icon: 'image://' + major_name
            },
            {
              name: '课程',
              value: "course",
              icon: 'image://' + course
            },
            {
              name: '教材',
              value: "textbook",
              icon: 'image://' + textbook
            },
            {
              name: '知识点',
              value: "keyword",
              icon: 'image://' + keyword
            },
            {
              name: '理论知识',
              value: "category",
              icon: 'image://' + category
            },
          ]
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDuration: 2000,
        // color: ['#3158A5', '#4069A7', '#4565BB', '#4285C4', '#297BD3', '#5D8EE1', '#6188E2', '#8FB5E4'],
        series: [
          {
            name: this.queryForm.keyword,
            type: 'graph',
            legendHoverLink: true,
            layout: 'force',
            force: {
              gravity: 0.1,
              repulsion: 1000,
              edgeLength: [200, 420],
              layoutAnimation: true,
              friction: 1,

            },
            data: this.graph.nodes,
            links: this.graph.links,
            categories: this.graph.categories,
            roam: true,
            label: {
              position: 'right',
              formatter: '{b}',
              fontSize: 18,
              formatter: function (params) {
                const maxLength = 10;
                const labelText = params.data.name;
                return ellipsis(labelText, maxLength);
              }
            },
            lineStyle: {
              color: 'source',
              curveness: 0.2,
              normal: {
                opacity: 0.4,
                width: 2,
                // type: 'solid' // 直线
                type: 'dashed', // 虚线,
                color: '#666666'
              }
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 10
              }
            }
          }
        ]
      };
      this.myChart.setOption(option);
      const _that = this
      this.myChart.off('click');
      this.myChart.on('click', function (param) {
        if (param.dataType == 'node' && param.data.onclickFlag == true) {
          _that.clickNode(param)
        } else {
        }
      })
      option && this.myChart.setOption(option);
    },
    clickNode(param) {
      this.queryForm.parentKeywordValue = this.queryForm.keywordValue
      this.queryForm.parentKeyword = this.queryForm.keyword
      this.queryForm.keywordValue = param.data.keywordValue
      this.queryForm.keyword = param.name
      this.queryForm.parentFlag = param.data.parentFlag
      this.getCount()
    },
  },


};
</script>
<style lang="scss" scoped>
.tab-content {
  padding: 0;
  padding-top: 10px;
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 124px);
  background: url(../../../../assets/images/mapBack.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.main {
  width: 100%;
  height: calc(100% - 50px);
}
.selectForm {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
