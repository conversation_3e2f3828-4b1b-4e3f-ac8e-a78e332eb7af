<template>
  <div class="tab-content">
    <div class="top-box">
      <div class="top-content top-content-l" v-if="mapList.disciplineConstruction && mapList.disciplineConstruction ">
        <el-row>
          <el-col :span="24" style="display: flex;margin-top: 10px;">
            <div style="line-height: 30px;width:80px;">学科建设：</div>
            <div
              style="height: 30px;background-color: #7cd6cf;width: 45%;line-height: 30px;display: flex;justify-content: flex-end;padding-right: 10px;border-radius: 4px;">
              {{ mapList.disciplineConstruction }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="display: flex;margin-top: 10px;">
            <div style="line-height: 30px;width:80px;">课程体系：</div>
            <div
              style="height: 30px;background-color: #63b2ee;width: 65%;line-height: 30px;display: flex;justify-content: flex-end;padding-right: 10px;">
              {{mapList.courseSystem}}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="display: flex;margin-top: 10px;">
            <div style="line-height: 30px;width:80px;">知 识 点：</div>
            <div
              style="height: 30px;background-color: #7898e1;width: 88%;line-height: 30px;display: flex;justify-content: flex-end;padding-right: 10px;">
              {{ mapList.knowledgePoint }}</div>
          </el-col>
        </el-row>
      </div>
      <div class="top-content top-content-r" v-if="countObj.coursesNumber &&countObj.coursesNumber != 0">
        <el-row type="flex" justify="space-around" style="margin-top: 20px;">
          <el-col :span="6">
            <div class="count-num">
              {{countObj.coursesNumber}}
            </div>
            <div class="count-title">课程</div>
          </el-col>
          <el-col :span="6">
            <div class="count-num">

              {{countObj.textbooksNumber}}
            </div>
            <div class="count-title">教材</div>
          </el-col><el-col :span="6">
            <div class="count-num">

              {{countObj.knowledgeNumber}}
            </div>
            <div class="count-title">知识点</div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="bottom-box">
      <div class="yAxisChart" id="yAxisChart"></div>
      <div class="yAxisChart2" id="yAxisChart2"></div>
    </div>

  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getSatisticalChart, getCount, getNumber } from "@/api/dataSet/knowledgeBase.js";

export default {
  name: "SatisticalChart",
  data() {
    return {
      xAxisChart: null,
      yAxisChart: null,
      productSalesList: [],
      mapList: {},
      countObj: {},
      colorList: { '知 识 点': '#7898e1', '课程体系': '#63b2ee', '学科建设': '#7cd6cf' },
      number: [],
      particular: []
    };
  },
  created() {
    this.getSatisticalChartData()
    this.getCount()
    this.getNumber()
  },
  methods: {
    format(percentage) {
      return percentage !== 0 ? '333' : `${percentage}%`;
    },
    getCount() {
      getCount().then((res) => {
        if (res.code === 200) {
          this.countObj.coursesNumber = res.data.coursesNumber
          this.countObj.textbooksNumber = res.data.textbooksNumber
          this.countObj.knowledgeNumber = res.data.knowledgeNumber
        }
      })
    },
    getNumber() {
      getNumber().then((res) => {
        if (res.code === 200) {
          this.number = res.data.number
          this.particular = res.data.particular
          this.initLineChart()

        }
      })
    },
    getSatisticalChartData() {
      getSatisticalChart().then((res) => {
        if (res.code === 200) {
          this.productSalesList = res.data.productSalesList
          var mapList = {}
          res.data.mapList.map(item => {
            if (item.name === '学科建设') {
              mapList.disciplineConstruction = item.value;
            } else if (item.name === '课程体系') {
              mapList.courseSystem = item.value;
            } else {
              mapList.knowledgePoint = item.value;
            }
          })
        }
        this.mapList = mapList
        this.intYAxisChart()
      })
    },
    // intXAxisChart() {
    //   var chartDom = document.getElementById('xAxisChart');
    //   this.xAxisChart = echarts.init(chartDom);
    //   var option;
    //   option = {
    //     xAxis: {
    //       type: 'value'
    //     },

    //     tooltip: {
    //       // trigger: 'item', // axis显示该列下所有坐标轴对应数据，item只显示该点数据
    //       // axisPointer: { // 坐标轴指示器，坐标轴触发有效
    //       //   type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
    //       // },
    //       formatter: function (params) {
    //         var res = params.name == "知 识 点" ? `<div>
    //       <div
    //         style='display: inline-block;
    //         width:  10px;
    //         height: 20px;
    //         color: ${params.color}'
    //         >●</div>
    //       <span>${params.value + ' 千条'}</span>
    //     </div>
    //   `: `<div>
    //       <div
    //         style='display: inline-block;
    //         width:  10px;
    //         height: 20px;
    //         color: ${params.color}'
    //         >●</div>
    //       <span>${params.value + ' 条'}</span>
    //     </div>
    //   `
    //         return params.name + res
    //       }

    //     },
    //     yAxis: {
    //       type: 'category',
    //       data: this.mapListTitle,
    //     },
    //     series: [
    //       {
    //         data: this.mapListValue,
    //         type: 'bar'
    //       }
    //     ]
    //   };

    //   option && this.xAxisChart.setOption(option);

    // },
    intYAxisChart() {
      var chartDom = document.getElementById('yAxisChart');
      this.yAxisChart = echarts.init(chartDom);
      var option;
      option = {
        legend: {},
        tooltip: {
          formatter: function (params) {
            var res = params.seriesName == "学科建设" ? `<div>
          <div
            style='display: inline-block;
            width:  10px;
            height: 20px;
            color: ${params.color}'
            >●</div>
          <span>${params.seriesName + ' ' + params.value[1] + ' 条'}</span>
        </div>
      `: params.seriesName == "课程体系" ? `<div>
          <div
            style='display: inline-block;
            width:  10px;
            height: 20px;
            color: ${params.color}'
            >●</div>
          <span>${params.seriesName + ' ' + params.value[2] + ' 条'}</span>
        </div>
      `: `<div>
          <div
            style='display: inline-block;
            width:  10px;
            height: 20px;
            color: ${params.color}'
            >●</div>
          <span>${params.seriesName + ' ' + params.value[3] + ' 百条'}</span>
        </div>
      `
            return params.name + res
          }

        },
        dataset: {
          source: this.productSalesList
        },
        xAxis: { type: 'category' },
        yAxis: {},
        series: [{
          type: 'bar',
          itemStyle: {
            normal: {
              color: '#7cd6cf',   //柱状颜色
            }
          }
        },
        {
          type: 'bar',
          barGap: '0%',
          itemStyle: {
            normal: {
              color: '#63b2ee',   //柱状颜色
            }
          }
        },
        {
          type: 'bar',
          itemStyle: {
            normal: {
              color: '#7898e1',   //柱状颜色
            }
          }
        }]
      };

      option && this.yAxisChart.setOption(option);

    },
    initLineChart() {
      var chartDom = document.getElementById('yAxisChart2');
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        title: {
          text: '知识点'
        },
        xAxis: {
          type: 'category',
          data: this.particular
        },
        yAxis: {
          type: 'value',
          name: '数量'
        },
        tooltip: {
        },
        series: [
          {
            name: '知识点数量',
            data: this.number,
            type: 'line'
          }
        ]
      };
      option && myChart.setOption(option);

    }
  },

};
</script>
<style lang="scss" scoped>
.tab-content {
  padding: 0 30px;
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 140px);
}
.top-box {
  width: 100%;
  height: 135px;
  display: flex;
  margin: 10px 0;
}
.top-content {
  padding: 0 10px;
  height: 100%;
  border: 1px solid #c1c1c1;
  box-shadow: inset 0px 0px 15px 2px #c1c1c1;
  border-radius: 20px;
  margin: 0 10px;
}
.top-content-l {
  width: 70%;
}
.top-content-r {
  width: 30%;
}
.count-num {
  text-align: center;
  font-size: 40px;
  color: #015391;
  font-weight: 800;
  font-family: "微软雅黑", Microsoft YaHei;
}
.count-title {
  text-align: center;
  font-size: 24px;
  color: #3785c1;
  font-weight: 600;
  font-family: "微软雅黑", Microsoft YaHei;
}
.bottom-box {
  width: 100%;
  height: calc(100% - 145px);
  display: flex;
  border: 1px solid #c1c1c1;
  box-shadow: inset 0px 0px 15px 2px #c1c1c1;
  border-radius: 20px;
}
.yAxisChart {
  padding: 10px;
  width: 50%;
  height: 100%;
  // border: 1px solid #c1c1c1;
  // box-shadow: inset 0px 0px 15px 2px #c1c1c1;
  // border-radius: 20px;
}
.yAxisChart2 {
  padding: 10px;
  width: 50%;
  height: 100%;
  // border: 1px solid #c1c1c1;
  // box-shadow: inset 0px 0px 15px 2px #c1c1c1;
  // border-radius: 20px;
}
</style>
