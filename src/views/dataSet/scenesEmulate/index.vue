<template>
  <div class="app-container ck-container ">
    <!-- <img :src="literatureImg" alt="literatureImg" style="width:100%;height:100%" /> -->
    <div class="start-box">
      <el-button type="primary" id="startButton" class="start-button" @click="start">开始模拟</el-button>
      <el-button style="margin: 5px;" size="mini" round :icon="playFlag?'el-icon-bell':'el-icon-close-notification'"
         @click="autoPlayAudio">
         {{playFlag ?  '手动播放': '自动播放'}}
      </el-button>
    </div>
    <div class="ask-box" style="display: none;">
      <el-button type="primary" size="mini" icon="el-icon-close" style="right:10px;position: absolute;top:5px;" @click="closeAskBox" />
      <el-input v-model="content" type="textarea" @blur="saveEditedContent" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" id="askBox"  :autosize="{ minRows: 6, maxRows: 6}" maxlength="1500"
      @keydown.native="handleKeyCode($event)" show-word-limit style="top:30px;" />
      <el-button type="primary" id="askButton" class="ask-button" size="mini" round icon="el-icon-s-promotion" @click="ask" />
      <el-button class="talk-button" size="mini" round
                :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'"
                @click="voiceASRType === 1 ? baiduASR() : translation()">
                {{translationFlag ? '停止' : '语音'}}
              </el-button>
    </div>
    <div class="answer-box" style="display: none;">
      <div class="answer">
        {{ dialogueResult.content }}
      </div>
      <div style="width: 100%;position: absolute;bottom: 0px;right: 10px;">
        <el-button type="primary" style="float: right;margin: 5px;" size="mini" round @click="stopAsk">结束</el-button>
        <el-button type="primary" style="float: right;margin: 5px;" size="mini" round @click="continueAsk">继续提问</el-button>
        <el-button style="margin: 5px;" size="mini" round :icon="isplaying?'el-icon-bell':'el-icon-close-notification'"
          @click="toggleAudio">
          {{isplaying ?  '停止': '播放'}}
        </el-button>
        <!-- <el-button style="float: right;margin: 5px;" size="mini" round @click="pause">停止播放</el-button>
        <el-button style="float: right;margin: 5px;" size="mini" round
          @click="play(dialogueResult.content)">开始播放</el-button> -->

          <!-- <el-button style="float: right;margin: 5px;" size="mini" round
                    @click="voiceType === 1 ? stopAudio() : pause()">停止播放
                  </el-button>
                  <el-button style="float: right;margin: 5px;" size="mini" round
                    @click="voiceType === 1 ? playAudio(dialogueResult.content) : play(dialogueResult.content)">开始播放
                  </el-button> -->
      </div>
    </div>
    <audio :src="computedAudioSrc"  controls :playsinline="true" style="display: none;" ref="audio" @ended="continuePlay">
            您的浏览器不支持音频播放。
    </audio>
  </div>
</template>

<script>
// import literatureImg from "@/assets/images/literatrue.png";
import {
  getCount
} from "@/api/literature/literature.js";
import IatRecorder from '@/assets/js/IatRecorder.js'
import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
import { AudioRecorder } from "@/assets/js/BaiDuASR.js";
import {
  getDialogueList,
  getPromptList,
  addDialogue,
  updateDialogue,
  delDialogue,
  getDialogue,
  getId,
  getBaiDuToken,
} from "@/api/explorationCenter/experience.js";
import { getToken } from "@/utils/auth";
import { getScenarioBgS } from "@/api/applicationScenario/scenario.js";
import speechRecognitionService from '@/utils/speechRecognitionService';
import { eventBus } from '@/utils/eventBus';

import {
  getUserVoiceRole,
  DBTTS,
  getSystemTTSChoose,
} from "@/api/system/voiceRole.js";
import qs from 'qs'
import axios from "axios";
import Cookies from "js-cookie";

const iatRecorder = new IatRecorder('zh_cn', 'mandarin', '2c12e69c')////
const ttsRecorder = new TtsRecorder();
export default {
  name: "Literature",
  dicts: ['call_type','system_websocket_param','user_voiceasr_choose','user_voice_choose'],
  data() {
    return {
      // literatureImg: literatureImg,
      // 遮罩层
      loading: false,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        title: '养老服务',
      },
      invocation: "model",
      menuRouting: "",
      dialogueResult: {content:'正在回答，请稍等···'},
      dialogueNum: true,
      loadSendBtn: false,
      content: "",
      id: "",
      routePath: this.$route.path,
      playFlag : false, // 自动播放
      isplaying: false, // 停止？播放
      translationFlag: false, // 语音标示
      audioArr : [],
      segments : [],
      audioSrc : "",
      currentIndex : 0,
      token : "",
      per : "1",
      speed: 5, // 语速
      pitch: 5, // 语调
      volum: 5,  // 音量
      pid: '',
      AsrintervalId: null,
      baiduASRParam:{
        appId: '',
        appKey: '',
        dev_pid: '',
        name: this.$route.path
      },
      //routePath: "/explorationCenter9/she/scenesEmulate",
      contenMark:0,
      finalText: "", // 累加的最终结果
      timer: null, // 定时器
      voiceASRType: 1, // 识别服务商
      voiceType: 1, // 语音合成服务商
      isStopFinsh: true,
      isPlayFinish: false,
      userVoiceChoose: "",// 语音合成服务商
      isAudioLoading: false,
    };
  },
  created() {
    this.getVoiceRole();
    //document.body.style.backgroundImage = `url(${this.$route.params && this.$route.params.imageUrl})`;
    this.getBgById(this.$route.params.id);
    this.initRecorder();
    this.getSystemTTSChoose();
  },
  beforeDestroy() {
    //关闭页面前停止
    this.clearAudio();
    document.body.style.backgroundImage = `url()`;
  },
  computed :{
    computedAudioSrc() {
      return this.audioArr[this.currentIndex];
    },
  },
  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.isplaying) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play();
          },
          { once: true }
        );
      }
    },
  },
  mounted(){
    setTimeout(() => {
      this.getVoiceASRChoose()
    }, 1500);
  },
  methods: {

    // 获取系统语音合成选择
    getSystemTTSChoose(){
      getSystemTTSChoose().then(res => {
        this.userVoiceChoose = res.msg;
      })

    },
    // 获取语音识别调用
    getVoiceASRChoose() {
      // 通用函数：根据字典值设置类型
      const setTypeFromDict = (dict, key) => {
        dict.forEach(item => {
          if (item.label === "baidu") {
            this[key] = 1;
          } else if (item.label === "xfSpark") {
            this[key] = 0;
          }
        });
      };
      // 设置 voiceASRType
      setTypeFromDict(this.dict.type.user_voiceasr_choose, "voiceASRType");
      // 设置 voiceType
      setTypeFromDict(this.dict.type.user_voice_choose, "voiceType");
    },
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/addLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const reader = response.body.getReader();
      let done = false;
      try {
        let s = "";
        let imageUrls = []; // 新增数组用于存储图片URL

        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
          } else {
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueResult.content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性

            });
          }
        }
      } finally {
        reader.releaseLock();
      }
    },

    getImgUrl(pptPath) {
      let baseUrl = window.location.origin
      var imgData;
      if (pptPath.includes('/ruoyi/')) {
        // 替换路径中的 ruoyi/ 之前的部分
        const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

        if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
          imgData = 'http://127.0.0.1:9215' + finalPath
          // console.log('Final baseUrl:', baseUrl)
        } else {
          imgData = baseUrl + finalPath
        }
      }
      return imgData;

    },
    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueResult.content = cleanedContent;

            });
          }
        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    getBgById(id){
      getScenarioBgS(id).then(blob => {
         // URL.createObjectURL(blob); // 将 URL 绑定到 data 属性上
        document.body.style.backgroundImage = `url(${URL.createObjectURL(blob)})`;
        document.body.style.backgroundSize = "100% 100%";
        document.body.style.backgroundRepeat = "no-repeat"; // 防止图片平铺
        document.body.style.backgroundPosition = "center";
      }).catch(error => {
        console.error("图像加载失败", error);
      });
    },


    //停止播放
    async stopAudio() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false;
        this.$refs.audio.pause();
        // this.isplaying = false;
        this.isPlayFinish = true;
        this.clearAudio();
      } else {
        setTimeout(() => {
          this.isStopFinsh = true;
        }, 500);
      }
    },


    //封装异步  文本转音频   播放音频
    async playAudio(content) {
      this.clearAudio()

      //提前获取token 并赋值
      const res = await getBaiDuToken();
      this.token = res.token;

      this.isPlayFinish = false
      this.textToAudio2(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isplaying = true;
      this.isStopFinsh = true
      //this.isplaying = true;
    },
    start() {
      const startBox = document.querySelector(".start-box");
      startBox.style.visibility = "hidden";
      const askBox = document.querySelector(".ask-box");
      askBox.style.display = "block";
    },
    // 获取发言人
    getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
        })
      },
    // autoPlay() {
    //   if(this.playFlag) {
    //     this.playFlag = !this.playFlag;
    //     this.pause();
    //   } else {
    //     this.playFlag = !this.playFlag;
    //   }
    // },
    autoPlayAudio() {
      this.playFlag = !this.playFlag;
      this.isplaying = !this.isplaying;
    // 一段音频
    if(this.audioSrc){
        this.$refs.audio.play();
    }

},
continuePlay() {
      this.currentIndex++;
      if (this.currentIndex < this.audioArr.length) {
        setTimeout(() => {
          this.$refs.audio.load();
          this.$refs.audio.play().catch((error) => {
            console.error('Failed to play:', error);
          });
        }, 100);
      } else {
        this.isplaying = false; // 结束播放，重置 autoplay
      }
    },
    // 播放暂停
toggleAudio() {
  if (this.isplaying) {
      // 停止播放逻辑
      this.handleAudioControl()
    } else {
      // 开始播放逻辑
      this.synthesizeVoice(this.dialogueResult.content)
    }
      // 切换播放状态
      this.isplaying = !this.isplaying;
    },
    // 添加一个统一的语音合成方法
    synthesizeVoice(content) {
      // 停止当前正在播放的音频
      this.stopAudio();

      // 根据 userVoiceChoose 选择不同的语音合成方法
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.playAudio(content);
          break;
        case "xfSpark": // 讯飞语音
          this.play(content);
          break;
        case "doubao": // 豆包语音
          this.playAudioFromUrl(content);
          break;
        default:
          // 默认使用百度语音
          this.playAudio(content);
          break;
      }
    },
    // 统一的暂停方法
    handleAudioControl() {
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.stopAudio();
          break;
        case "xfSpark": // 讯飞语音
          this.pause();
          break;
        case "doubao": // 豆包语音
          this.stopAudio();
          break;
        default:
          // 默认使用百度语音
          this.stopAudio();
          break;
      }
    },
    play(content) {
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: content,
        // 角色
        voiceName: this.per,
        // 语速
        speed: this.speed,
        // 音量
        voice: this.volume,
        // 音高
        pitch: this.pitch,
      });
      ttsRecorder.start();
    },

    pause() {
      ttsRecorder.stop();
    },
    initRecorder(){
      speechRecognitionService.initRecorder((blob, encTime) => {
        if (speechRecognitionService.websocket && speechRecognitionService.isRecording) {
          speechRecognitionService.websocket.send(blob);
        }
      });
    },
    baiduASROptions(){
      const type = Cookies.get("voiceType")
      if(type === 'CN'){
        this.baiduASRParam.dev_pid = 1537
      }else if(type === 'EN'){
        this.baiduASRParam.dev_pid = 1737
      }else{
        this.baiduASRParam.dev_pid = 1537
      }
      this.dict.type.system_websocket_param.forEach(item => {
        console.log(item)
        if(item.label === 'appId'){
          this.baiduASRParam.appId = Number(item.value)
        }
        if(item.label === 'appKey'){
          this.baiduASRParam.appKey = item.value
        }
      })
    },
    closeASR(){
      this.translationFlag = false;
      this.finalText = "";
      if(this.voiceASRType === 1){
        speechRecognitionService.closeWebsocket();
        eventBus.$off(this.baiduASRParam.name);
        if (this.timer) {
          clearTimeout(this.timer); // 清除定时器
          this.timer = null;
        }
        return;
      }
      iatRecorder.stop();
    },
    baiduASR(){
      this.baiduASROptions();
      if(this.translationFlag){
        this.closeASR();
      }else{
        this.translationFlag = true;
        speechRecognitionService.startSpeechRecognition(error => {
        if (error) {
          this.$message.error('麦克风未打开！');
          switch (error.message || error.name) {
            case 'PERMISSION_DENIED':
            case 'PermissionDeniedError':
              console.info('用户拒绝提供信息。');
              break;
            case 'NOT_SUPPORTED_ERROR':
            case 'NotSupportedError':
              console.info('浏览器不支持硬件设备。');
              break;
            case 'MANDATORY_UNSATISFIED_ERROR':
            case 'MandatoryUnsatisfiedError':
              console.info('无法发现指定的硬件设备。');
              break;
            default:
              console.info('无法打开麦克风。异常信息:' + (error.code || error.name));
              break;
          }
        }
      }, this.baiduASRParam);
      eventBus.$on(this.baiduASRParam.name, (result) => {
        console.log("识别结果:", result);

        if (result.type === "MID_TEXT"){
          this.content = this.finalText + result.text;
        }
        if (result.type === "FIN_TEXT"){
          console.log("最终结果:", this.finalText);
           // 累加最终结果
          this.finalText += result.text;
          this.content = this.finalText;
          console.log("最终结果:", this.content);
        }
      });
      // 设置 60 秒后自动关闭录音
      this.timer = setTimeout(() => {
        speechRecognitionService.closeWebsocket(); // 调用关闭方法
        eventBus.$off(this.baiduASRParam.name) // 清除监听器
        this.translationFlag = false; // 更新标识
        this.finalText = "";
        this.$message.info('录音已自动停止。');
      }, 60000);
      }
    },

    saveEditedContent() {
      // 用户编辑完成后同步最终内容
      this.finalText = this.content;
      console.log("保存用户编辑后的结果:", this.finalText);
    },


    translation() {
      if (this.translationFlag) {
        iatRecorder.stop();
        this.translationFlag = false;
      } else {
        iatRecorder.start()
        this.translationFlag = true;
        iatRecorder.onTextChange = (text) => {
          let inputText = text;
          this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
          console.log(this.content);
        };
      }
    },
    async getId() {
      await getId()
        .then((res) => {
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    async ask() {
      if(this.translationFlag){
        this.closeASR();
      } // 关闭语音输入
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }
      const askBox = document.querySelector(".ask-box");
      const answerBox = document.querySelector(".answer-box");
      askBox.style.display = "none";
      answerBox.style.display = "block";

      if (this.dialogueNum) {
        await this.getId();
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          id: this.id,
          language: Cookies.get("voiceType"),
          content: this.content,
          //menuRouting: this.getSecondSlash(this.routePath) + "/",
          menuRouting: this.routePath + "/",
        };

        this.token = getBaiDuToken().then(res => {
          this.token = res.token;
        }).catch(error => {
          console.error('Failed to get token:', error);
        });

        this.content = ''
        await this.addDialogue2(param).catch(error => {
          console.error('添加对话时出错:', error);
        });
      } else {
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          language: Cookies.get("voiceType"),
          id: this.id,
          menuRouting: this.routePath + "/",
        };

        this.content = ''
        this.updateDialogue2(param)
          .catch(error => console.error('Error:', error));
      }

    },

    closeAskBox() {
      const askBox = document.querySelector(".ask-box");
      const startBox = document.querySelector(".start-box");
      askBox.style.display = "none";
      startBox.style.visibility = "visible";

      this.content = "";
      this.dialogueNum = true;
      this.dialogueResult.content = "正在回答，请稍等···";
    },
    continueAsk() {
      const askBox = document.querySelector(".ask-box");
      const answerBox = document.querySelector(".answer-box");
      askBox.style.display = "block";
      answerBox.style.display = "none";

      this.dialogueResult.content = "正在回答，请稍等···";

      //停止当前播放的语音
      this.clearAudio();
      this.audioArr = []
    },
    stopAsk() {
      const answerBox = document.querySelector(".answer-box");
      answerBox.style.display = "none";
      const startBox = document.querySelector(".start-box");
      startBox.style.visibility = "visible";
      this.dialogueNum = true;

      this.dialogueResult.content = "正在回答，请稍等···";

      //停止当前播放的语音
      this.clearAudio();
      this.audioArr = []
    },
    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },
    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    },
    splitTextByPunctuation(text,maxLength){
      const punctuation = /[。！？；]/;
      const secondaryPunctuation  = /[ ， ]/;
      let result = []
      let currentSegment = ""
    while(text.length > 0){
        // 正则表达式匹配字符
        let match = punctuation.exec(text);
        // 如果匹配到字符
        if(match){
            let segment = text.slice(0,match.index+1);
            if(segment.length <= maxLength){
                text = text.slice(match.index+1);
                result.push(segment.trim())
            }else{
                while(segment.length > maxLength){
                    let secondaryMatch = secondaryPunctuation.exec(segment);
                    if (secondaryMatch && secondaryMatch.index < maxLength) {
                        let subSegment = segment.slice(0, secondaryMatch.index + 1);
                        if (subSegment.length <= maxLength) {
                            result.push(subSegment.trim());
                            segment = segment.slice(secondaryMatch.index + 1);
                        } else {
                            result.push(segment.slice(0, maxLength).trim());
                            segment = segment.slice(maxLength);
                        }
                    }else {
                        result.push(segment.slice(0, maxLength).trim());
                        segment = segment.slice(maxLength);
                    }
                }
                if (segment.length > 0) {
                    result.push(segment.trim());
                }
                text = text.slice(match.index + 1);
            }
        }else{
          while (text.length > maxLength) {
                result.push(text.slice(0, maxLength).trim());
                text = text.slice(maxLength);
            }
            if (text.length > 0) {
                result.push(text.trim());
                text = "";
            }
        }
    }
    // 最后剩一个片段，将当前片段添加到结果数组中
    if(currentSegment.length > 0){
        result.push(currentSegment.trim())
    }
    return result
},
async playAudioFromUrl(content) {
      if (this.isPlayAudio && !this.txtToImage) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true
      this.isPlayFinish = false
      this.synthesizeSpeech(content)
      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
    },
// 豆包语音合成方法，使其接收内容参数
synthesizeSpeech(content) {
      this.segments = this.splitTextByPunctuation(content, 55);
      this.isPlayFinish = false;
      let Index = 0; // 添加一个索引来跟踪当前处理的段落
      const processSegment = () => {
        if (Index >= this.segments.length || this.isPlayFinish) {
          return; // 如果处理完所有段落或者标记为完成，则停止处理
        }
        const text = this.segments[Index].trim();
        if (text === "") {
          Index++;
          processSegment(); // 如果文本为空，跳过当前段落
          return;
        }

        if (!this.isAudioLoading) {
          this.isAudioLoading = true; // 设置音频正在加载的标志

          const params = {
            content: text,
            voiceType: this.per,
            speed: this.speed,
          };

          DBTTS(params).then((response) => {
            if (response.data && response.data.audioData) {
              const audioBase64 = response.data.audioData;
              const binaryString = window.atob(audioBase64);
              const len = binaryString.length;
              const bytes = new Uint8Array(len);
              for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              const audioBlob = new Blob([bytes], { type: 'audio/mp3' });
              const audioUrl = URL.createObjectURL(audioBlob);
              this.audioArr.push(audioUrl); // 将生成的音频 URL 添加到播放列表

              Index++; // 移动到下一个段落
              this.isAudioLoading = false; // 重置音频加载标志
              processSegment(); // 递归调用处理下一个段落
            } else {
              throw new Error("网络不佳请稍后再试");
            }
          }).catch(error => {
            this.$message.error(`网络不佳请稍后再试: ${error.message}`);
            console.error("语音合成请求失败:", error);
            Index++;
            this.isAudioLoading = false;
            processSegment(); // 即使出错也继续处理下一个段落
          });
        }
      };

      processSegment(); // 开始处理第一个段落
    },
async textToAudio2 (text, token) {
  // console.log(this.dialogueResult.content)
    this.segments = this.splitTextByPunctuation(text,55)
    for(const text of this.segments){
        const option = {
            tex: text,
            tok: token,
            cuid: `${Math.floor(Math.random() * 1000000)}`,
            ctp: "1",
            lan: "zh",
            per: this.per,
            spd: this.speed,
            pit: this.pitch,
            vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            responseType: "blob",
        });
        if (this.isPlayFinish) {
          this.clearAudio()
          return;
        }
        // console.log(res.data)
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
    }
},
clearAudio() {
        this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
        this.currentIndex = 0
        this.audioArr = []
    },
  },

};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  //background-image: url("../../../assets/images/scenesEmulate_bg.jpg");
  background-size: 100% 200%;
 // background-size: cover; /* 让背景图片充满背景 */
 // background-repeat: no-repeat; /* 防止背景图片重复 */
  .start-box {
    width: 200px;
    height: 100px;
    bottom: 10px;
    right: 10px;
    border-radius: 150px 100px;
    position: absolute;
    background: rgba($color: #fff, $alpha: 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    flex-direction: column;
  }
  .ask-box {
    width: 400px;
    height: 220px;
    background: #fff;
    border-radius: 5px;
    border: #757575 1px solid;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -150px;
    margin-left: -180px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    .ask-button {
      position: absolute;
      bottom: 8px;
      right: 10px;
      margin: auto;
    }
    .talk-button {
      position: absolute;
      bottom: 8px;
      right: 60px;
      margin: auto;
    }
  }
  .answer-box {
    width: 400px;
    height: 200px;
    position: absolute;
    top: 50px;
    left: 100px;
    border-radius: 5px;
    border: #757575 1px solid;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    text-indent: 2em;//设置span首行缩进
    font-size: 13px;
    .answer {
      width: 100%;
      height: 150px;
      overflow: auto;
    }
  }

}
</style>
