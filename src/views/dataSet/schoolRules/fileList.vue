<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleLoading"
        >刷新</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="schoolfileList" @selection-change="handleSelectionChange">
      <el-table-column label="文件id" align="center" prop="fileId" />
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--          >修改</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库文件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="上传文件" prop="deiDesc">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl"
                     :data="uploadData" :headers="headers" :on-success="handleUploadSuccess" :on-remove="handleRemove"
                     :file-list="fileList" accept=".pdf" :on-preview="handlePreview"  :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip" v-if="flag != 'review'">支持pdf文件上传,仅能上传一个文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listSchoolfile, getSchoolfile, delSchoolfile, addSchoolfile, updateSchoolfile, ruleOrAdd,initDocument} from "@/api/dataSet/dataMake";

export default {
  name: "Schoolfile",
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload",
      uploadData: { modeltype: 'ppt' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 知识库文件表格数据
      schoolfileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileId: null,
        fileName: null,
        kbId: null,
        filePath: null,
        indexingStatus: this.$route.query && this.$route.query.kbName,
      },
      kbName:this.$route.query && this.$route.query.kbName,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    this.queryParams.kbName="";
  },
  activated() {
    this.queryParams.kbName=this.$route.query && this.$route.query.kbName
    this.getList();
  },
  methods: {

    handleLoading(){
      this.loading = true;
      const fileLoading={
        kbName:this.$route.query && this.$route.query.kbName
      }
      initDocument(fileLoading).then(response => {
        this.$modal.msgSuccess("刷新成功");
        this.loading = false;
        this.getList();

      });

    },

    handleUploadSuccess(res, file) {
      this.form.id = res.data.id;
      this.form.name = res.data.name;
      this.form.size = res.data.size;
      this.form.preview = res.data.preview;
      this.form.kbName =this.$route.query && this.$route.query.kbName;
    },
    handleRemove(file, fileList) {
      this.fileList=[]
      this.form.preview = undefined
    },
    handlePreview(){},
    /** 查询知识库文件列表 */
    getList() {
      this.loading = true;
      listSchoolfile(this.queryParams).then(response => {
        this.schoolfileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        fileId: null,
        fileName: null,
        kbId: null,
        filePath: null,
        createTime: null,
        indexingStatus: this.$route.query && this.$route.query.kbName,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加知识库文件";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        const fileVo={
          id:this.form.id ,
          name:this.form.name ,
          size:this.form.size,
          preview:this.form.preview,
          kbName:this.form.kbName ,
        }
        ruleOrAdd(fileVo).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除知识库文件编号为"' + ids + '"的数据项？').then(function() {
        return delSchoolfile(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  }
};
</script>
