<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="知识库名称" prop="kbName">
        <el-input
          v-model="queryParams.kbName"
          placeholder="请输入知识库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="schoolList" @selection-change="handleSelectionChange">
      <el-table-column label="知识库名称" align="center" prop="kbName" >
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.kbName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否开启知识增强" align="center" prop="isEnhanced" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import { listSchool } from "@/api/dataSet/dataMake";

export default {
  name: "School",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 知识库信息表格数据
      schoolList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        kbId: null,
        kbName: null,
        isCustomProcessRule: null,
        customProcessRule: null,
        isEnhanced: null,
        menuRouting: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询知识库信息列表 */
    getList() {
      this.loading = true;
      listSchool(this.queryParams).then(response => {
        this.schoolList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        kbId: null,
        kbName: null,
        isCustomProcessRule: null,
        customProcessRule: null,
        isEnhanced: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        menuRouting: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleReview(row) {
      this.$router.push({
        path: "/dataSet/schoolFile",
        query: { kbName: row.kbName },
      });
    },


  }
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
