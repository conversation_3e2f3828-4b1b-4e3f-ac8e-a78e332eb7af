<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="临时数据集名称" prop="groupName">
        <el-input v-model="queryParams.groupName" placeholder="请输入临时数据集名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建数据集</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataSetList">
      <el-table-column label="临时数据集名称" prop="groupName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="导入状态" prop="importProgress" min-width="100">
        <template slot-scope="scope">
          <span>{{importProgressChange(scope.row.importProgress) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" prop="dataType" min-width="100">
        <template slot-scope="scope">
          <span>{{scope.row.dataType?scope.row.dataType:'-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据量" prop="entityCount" min-width="100">
        <template slot-scope="scope">
          <span>{{scope.row.entityCount? scope.row.entityCount:'-'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间"  prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="150" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleExport(scope.row)">导出</el-button>
          <el-button size="mini" type="text" icon="el-icon-upload2" @click="handleImport(scope.row)">导入</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />


    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :headers="upload.headers"
        :action="getUploadUrl()"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-form label-width="50px">
        <el-form-item label="规则">
          <el-select v-model="rulesId" placeholder="请选择您的规则"  style="width: 80%">
            <el-option
              v-for="item in rulesNameOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getDataSetList,
    delDataSet,
    getRulesName,
    exportData
  } from "@/api/generateDataSet/generateDataSet.js";
  import { getToken } from "@/utils/auth";
  export default {
    name: "DataSetManagement",
    dicts: ['import_progress', 'release_status'],

    data() {

      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 数据集管理表格数据
        dataSetList: [],
        rulesId:'',
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          groupName: undefined,
          menuRouting: this.$route.path,
        },
        rulesNameOptions: [],
        upload: {
          // 是否显示弹出层
          open: false,
          // 弹出层标题
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          dataId:0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/test/temporary/extract"
        },
      };

    },
    created() {
      this.getList();
    },
    activated() {
      this.getList()
    },
    methods: {
      /** 查询数据集管理列表 */
      getList() {
        this.loading = true;
        getDataSetList(this.queryParams).then(response => {
            this.dataSetList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 更多操作触发
      handleCommand(command, row) {
        switch (command) {
          case "handleTraining":
            this.handleTraining(row);
            break;
          case "handleDel":
            this.handleDel(row);
            break;
          case "handlePublish":
            this.handlePublish(row);
            break;
          case "handleConduct":
            this.handleConduct(row);
            break;
          default:
            break;
        }
      },

      /** 新增按钮操作 */
      handleAdd() {
        var path=this.getSecondSlash(this.$route.path)+'/addGenerateDataSet';
        this.$router.push({
          path: path,
          query: { 'menuRouting': this.$route.path }
        })
      },
      /** 查看按钮操作 */
      handleReview(row) {
        var path=this.getSecondSlash(this.$route.path)+'/reviewGenerateDataSet';
        this.$router.push({
          path: path,
          query: { 'id': row.id }
        })
      },
      /** 删除按钮操作 */
      handleDel(row) {
        this.$modal.confirm('是否确认删除数据集？').then(function () {
          return delDataSet(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      },
      /** 字符串处理 */
      getSecondSlash(str) {
        var lastSlashIndex = str.lastIndexOf("/");

        // 如果找到了'/'
        if (lastSlashIndex !== -1) {
          // 提取从开始到'/'前的内容
          return str.substring(0, lastSlashIndex);
        } else {
          // 如果没有找到'/'，返回原字符串
          return str;
        }
      },
      isSummaryAppearingOnce(content) {
        const searchString = "prompt";
        const regex = new RegExp(searchString, 'g');
        const matches = content.match(regex);
        return matches ? matches.length === 1 : false;
      },
      handleExport: function (row) {
        exportData(row.id).then(response => {
          // 创建Blob对象
          const re=JSON.stringify(response);
          if (this.isSummaryAppearingOnce(re)){
            const blob = new Blob([re], {type: "application/octet-stream"});
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = '数据集.json';
            link.click();
          }else {
            const blob = new Blob([response], {type: "application/octet-stream"});
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = '数据集.json';
            link.click();
          }

        }).catch()
      },
      //导入
      handleImport(row){
        this.upload.title = "导入";
        this.upload.open = true;
        this.upload.dataId=row.id;
        this.getRulesName();
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
        this.getList();
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      },
      getRulesName(){
        getRulesName().then((res)=>{
          this.rulesNameOptions=res.data.map((item) => {
            return {
              value: item.rulesId,
              label: item.rulesName,
              id: item.rulesId,
            };
          });
        })
      },

      importProgressChange(importProgress) {
        return this.selectDictLabelByVal(this.dict.type.import_progress, importProgress)
      },
      releaseStatusChange(releaseStatus) {
        return this.selectDictLabelByVal(this.dict.type.release_status, releaseStatus)
      },
      getUploadUrl() {
        return this.upload.url + '?dataId=' + this.upload.dataId + '&rulesId=' + this.rulesId;
      }
    }
  };
</script>
