<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="imageList" border>
      <el-table-column label="图片索引" prop="id" v-if="false" :show-overflow-tooltip="true" min-width="100" />
      <el-table-column label="图片编号" prop="imageNumber" :show-overflow-tooltip="true" min-width="100" />
      <el-table-column label="图片名称" prop="fileName" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="图片" min-width="500">
        <template slot-scope="scope">
          <img v-if="scope.row.imageUrl" :src="scope.row.imageUrl" alt="图片" style="max-width: 100%; height: auto;">
          <!-- 可以添加加载中或错误提示 -->
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />
  </div>
</template>

<script>
  import { getImageList,delImageProcess } from "@/api/dataSet/pictureDetails.js";

  export default {
    name: "ReviewDataSet",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 图片详情表格数据
        imageList: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          dataId: ''
        },
        open: false,
        title: '数据集详情',
        form: {}
      };
    },
    activated() {
      this.getList()
    },
    methods: {
      /** 查询数据集详情 */
      getList() {
        this.queryParams.dataId = this.$router.currentRoute.query && this.$router.currentRoute.query.dataId
        this.loading = true;
        getImageList(this.queryParams).then(response => {
            this.imageList = response.rows;
            this.total = response.total;
            this.loading = false;
          // 遍历列表，将字节串转换为 Base64
          for (let i = 0; i < this.imageList.length; i++) {
            const imageData = `data:image/jpeg;base64,${this.imageList[i].processing}`;
            this.$set(this.imageList, i, { ...this.imageList[i], imageUrl: imageData });
          }
          }
        );
      },
      /** 删除按钮操作 */
      handleDel(row) {
        this.$modal.confirm('是否确认删除图片？').then(function () {
          return delImageProcess(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      },
    }
  };
</script>
