<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="dataSetInfoList" border>
      <el-table-column type="index" />
      <el-table-column label="Prompt" prop="prompt" :show-overflow-tooltip="true" min-width="500" />
      <el-table-column label="Response" prop="response" min-width="250" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleReview(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form">
        <el-form-item label="Prompt" prop="nickName">
          <el-input v-model="form.prompt" type="textarea" readonly autosize />
        </el-form-item>
        <el-form-item label="Response">
          <el-input v-model="form.response" type="textarea" readonly autosize />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getdataSet } from "@/api/dataSet/dataSetManagement.js";

export default {
  name: "ReviewDataSet",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据集详情表格数据
      dataSetInfoList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dataId: ''
      },
      open: false,
      title: '数据集详情',
      form: {}
    };
  },
  activated() {
    this.getList()
  },
  methods: {
    /** 查询数据集详情 */
    getList() {
      console.log(this.$router)
      this.queryParams.dataId = this.$router.currentRoute.query && this.$router.currentRoute.query.id
      this.loading = true;
      getdataSet(this.queryParams).then(response => {
        this.dataSetInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.open = true
      this.form = row
    },
  }
};
</script>