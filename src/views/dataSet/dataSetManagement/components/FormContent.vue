<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="课程" prop="courseName">
        <el-select class="ck-input" v-model="form.courseName" placeholder="请选择课程">
          <el-option v-for="item in classOptions" :key="item.id" :label="item.courseName" :value="item.courseName">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据集名称" prop="groupName">
        <el-input class="ck-input" v-model="form.groupName" placeholder="请输入数据集名称" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="数据类型" prop="projectType">
        <el-button-group>
          <el-button size="mini" :class="this.form.projectType==20?'is-choose':''"
            @click="handleChange(20)">Prompt+Response</el-button>
          <!-- <el-button>纯文本</el-button>
          <el-button>Prompt+Chosen+Rejected</el-button>
          <el-button>Prompt+多Response排序</el-button>
          <el-button>Prompt集</el-button>
          <el-button>Prompt+图片</el-button> -->
        </el-button-group>
      </el-form-item>
      <!-- <el-form-item label="FAQ挖掘" prop="deiAlias">
        <el-switch v-model="form.value1" active-text="关" inactive-text="开">
        </el-switch>
      </el-form-item>
      <el-form-item label="选择服务" prop="deiBusCal">
        <el-select class="ck-input" v-model="form.value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择应用" prop="deiBusCal">
        <el-select class="ck-input" v-model="form.value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="保存位置" prop="deiAlias">
        <el-radio-group v-model="form.radio">
          <el-radio :label="3"> 对象存储BOS </el-radio>
          <el-radio :label="6"> 平台共享存储 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="保存Bucket地址" prop="deiBusCal">
        <el-select class="ck-input" v-model="form.value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="保存文件夹地址" prop="deiBusCal">
        <el-select class="ck-input" v-model="form.value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="导入方式" prop="deiBusCal">
        <el-select class="ck-input" v-model="form.value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="上传文件" prop="deiDesc">
        <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple
          :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" accept=".jsonl,.xlsx">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持jsonl、xlsx件上传</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { addDataSet, cheackChargeClass } from "@/api/dataSet/dataSetManagement.js";
export default {
  name: 'FormContent',
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'hk1' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      form: {
        groupName: '',
        projectType: 20
      },
      rules: {
        courseName: [
          { required: true, message: '请选择课程', trigger: ['blur', 'change'] },

        ],
        groupName: [
          { required: true, message: '请输入数据集名称', trigger: ['blur', 'change'] },
          {
            pattern: /^(?!_)[\u4e00-\u9fa5A-Za-z0-9_]{1,50}$/,
            message: '数据集名称只能包含中文、英文、数字、下划线，且长度在1到50个字符之间，不能以下划线开头',
            trigger: ['blur', 'change']
          }
        ],
      },
      classOptions: []
    }
  },
  created() {
    this.cheackChargeClass()
  },
  methods: {
    // getCodeFormat(rule, value, callback) {
    //   if (value) {
    //     var reg = /^dei_[a-zA-Z0-9_]+$/
    //     if (reg.test(value) === false) {
    //       callback(new Error('支持中文、英文、数字、下划线(_)，50个字符以内，不能以下划线为开头'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // },
    // 查询当前登陆人是负责人的课程
    cheackChargeClass() {
      cheackChargeClass({ isPersonCharge: '1' }).then(res => {
        this.classOptions = res.data
      })
    },
    handleUploadSuccess(res, file,) {
      file.id = res.data.id;
      this.fileList.push(res.data.id);

    },
    handleRemove(file, fileList) {
      const findex = this.fileList.map(f => f.indexOf(file.id));
      if (findex > -1) {
        this.fileList.splice(findex, 1);
      }
    },
    handleSubmint() {
      if (this.validateForm()) {
        const queryForm = {
          ...this.form,
          fileIds: this.fileList,
          menuRouting: this.$route.query && this.$route.query.menuRouting
        }
        addDataSet(queryForm).then(res => {
          if (res.code === 200) {
            this.$message.success('新增成功')
            this.handleBack()
          }
        })
      } else {
        this.loading = false
      }
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push(this.$route.query.menuRouting)
    },
    handleChange(type) {
      this.form.projectType = type
    }

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
</style>

