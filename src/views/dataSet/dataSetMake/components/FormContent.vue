<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">

      <el-form-item label="上传文件" prop="deiDesc">
        <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple
          :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList"  :accept="'.docx,.txt,.pdf'"
                   :file-size-limit="20 * 1024 * 1024"  :before-upload="beforeUpload" :limit="1">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip" v-if="flag != 'review'">支持docx、txt、pdf上传,限制20MB,仅支持单文件上传</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { addDataMake } from "@/api/dataSet/dataMake.js";
export default {
  name: 'FormContent',
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'mk1' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
    }
  },
  methods: {

    handleUploadSuccess(res, file,) {
      file.id = res.data.id;
      this.fileList.push(res.data.id);

    },
    handleRemove(file, fileList) {
      const findex = this.fileList.map(f => f.indexOf(file.id));
      if (findex > -1) {
        this.fileList.splice(findex, 1);
      }
    },
    handleSubmint() {
      const queryForm = {
        fileIds: this.fileList,
        menuRouting: this.$route.query && this.$route.query.menuRouting
      }
      addDataMake(queryForm).then(res => {
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.handleBack()
        }
      })
    },
    beforeUpload(file) {
      // 定义允许的文件类型
      const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
      const fileType = file.type;
      const isValidType = allowedTypes.includes(fileType);

      // 检查文件类型
      if (!isValidType) {
        this.$message.error('上传文件只能是 pdf, docx, txt 格式!');
        return false;
      }
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!');
      }
      return isLt20M;
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push(this.$route.query.menuRouting)
    },
    handleChange(type) {
      this.form.projectType = type
    }

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
</style>

