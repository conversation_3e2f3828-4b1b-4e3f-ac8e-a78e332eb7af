<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="数据集名称" prop="groupName">
        <el-input class="ck-input" v-model="form.groupName" placeholder="请输入数据集名称" maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="文件名称" prop="fileName">
        {{ fileName }}
      </el-form-item>
      <el-form-item label="数据量" prop="totalNumber">
       {{ totalNumber }}
      </el-form-item>
      

    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>

import { poductionToDataSet } from "@/api/dataSet/dataSetManagement.js";

export default {
  name: 'makeDataSet',
  data() {
    return {
      form: {
        groupName: '',
        projectType: 20,
        totalNumber: null,
        fileName: ''
      },
      rules: {
        groupName: [
          { required: true, message: '请输入数据集名称', trigger: ['blur', 'change'] },
          // { validator: this.getCodeFormat, trigger: ['blur', 'change'] },
        ],
      },
    }
  },
  created(){
    this.totalNumber = this.$route.query.totalNumber;
    this.fileName = this.$route.query.fileName;

    // this.totalNumber = this.$route.params.totalNumber;
    // this.fileName = this.$route.params.fileName;
    
    // console.log(this.$route.params)
    // console.log(this.$route)
    // console.log(this.$route.query)
  },
  methods: {
  
    handleSubmint() {
  
      const data={
          id: this.$route.query.id,
          menuRouting: this.$route.query.menuRouting,
          dataSetName: this.form.groupName
      }
     
      poductionToDataSet(data).then((response) => {
        console.log(response)
        if (response.code === 200) {
          this.$message.success('制作成功')
          this.handleBack()
        }
      });

    },
  
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push(this.$route.query.menuRouting)
    },
    handleChange(type) {
      this.form.projectType = type
    }

  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
</style>

