<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="dataMakeList" border>
      <el-table-column type="index" />
      <el-table-column label="Prompt" prop="problem" :show-overflow-tooltip="true" min-width="500" />
      <el-table-column label="Response" prop="answer" min-width="250" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleReview(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form">
        <el-form-item label="Prompt" prop="problem">
          <el-input v-model="form.problem" type="textarea" readonly autosize />
        </el-form-item>
        <el-form-item label="Response">
          <el-input v-model="form.answer" type="textarea" readonly autosize />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getDataMake,delDataGenerate } from "@/api/dataSet/dataMake.js";

export default {
  name: "ReviewDataMake",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据集详情表格数据
      dataMakeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: undefined
      },
      open: false,
      title: '详情',
      form: {}
    };
  },
  activated() {
    this.getList()
  },
  methods: {
    /** 查询数据集详情 */
    getList() {

      this.queryParams.taskId = this.$router.currentRoute.query && this.$router.currentRoute.query.id
      this.loading = true;
      console.log(this.queryParams)
      getDataMake(this.queryParams).then(response => {
        this.dataMakeList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.open = true
      this.form = row
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delDataGenerate(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  }
};
</script>
