<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="dataMakeList" border>
      <el-table-column type="index" />
      <el-table-column label="Prompt" prop="problem" :show-overflow-tooltip="true" min-width="500" />
      <el-table-column label="Response" prop="answer" min-width="250" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
<!--    对话弹窗-->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="formData" :model="formData">
        <el-form-item label="Prompt" prop="problem">
          <el-input v-model="formData.problem" type="textarea"  autosize />
        </el-form-item>
        <el-form-item label="Response">
          <el-input v-model="formData.answer" type="textarea"  autosize />
        </el-form-item>
      </el-form>
      <div style="text-align: center;">
        <el-button type="primary" @click="handleSubmint()">保存</el-button>
        <el-button type="primary" @click="open = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDataMake,delDataGenerate,editDataMake } from "@/api/dataSet/dataMake.js";

export default {
  name: "ReviewDataMake",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据集详情表格数据
      dataMakeList: [],
      // 对话框
      formData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: undefined
      },
      open: false,
      title: '详情',
      form: {}
    };
  },
  created() {
    console.log("初始化")
    this.getList()
  },
  methods: {
    handleSubmint(){
      console.log("提交")
      const index = this.dataMakeList.findIndex(item => item.id === this.formData.id);
      if (index !== -1) {
        // 使用 Vue.set() 确保数据变更被 Vue 监听到
        this.$set(this.dataMakeList, index, { ...this.formData });
      }
      console.log("id",index)
      this.open = false;
      //发送更新请求
      editDataMake(this.formData).then(response => {
        console.log("结果",response)
      })

    },
    /** 查询数据集详情 */
    getList() {

      this.queryParams.taskId = this.$router.currentRoute.query && this.$router.currentRoute.query.id
      this.loading = true;
      console.log(this.queryParams)
      getDataMake(this.queryParams).then(response => {
        this.dataMakeList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      console.log(row)
      this.open = true
      this.formData = { ...row }; // 复制数据，不直接修改原数据
      console.log(this.formData)
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delDataGenerate(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  }
};
</script>
