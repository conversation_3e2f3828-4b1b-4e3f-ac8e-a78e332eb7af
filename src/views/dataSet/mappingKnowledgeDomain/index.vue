<template>
  <div class="app-container ck-container">
    <el-form :model="queryForm" ref="queryForm" class="selectForm" :inline="true">
      <el-form-item label="关键词" prop="keyword">
        <el-select v-model="queryForm.keywordValue" style="width: 150px;margin-right: 5px;">
          <el-option v-for="dict in dict.type.knowledge_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
        <el-input v-model="queryForm.keyword" placeholder="请输入关键词" clearable style="width: 240px"
          @keyup.enter.native="getCount" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getCount">搜索</el-button>
      </el-form-item>
    </el-form>
    <div id="main" class="main" />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getKb } from "@/api/dataSet/knowledgeBase.js";
export default {
  name: "MappingKnowledgeDomain",
  dicts: ['knowledge_type'],
  data() {
    return {
      keywordInit: '',
      keyword: '',
      queryForm: {
        keyword: '新文科',
        keywordValue: 'academic_discipline'
      },
      graph: {
        categories: [
          {
            "name": "A"
          },
          {
            "name": "B"
          },
          {
            "name": "C"
          },
          {
            "name": "D"
          },
          {
            "name": "E"
          },
          {
            "name": "F"
          },
        ]
      }
    };
  },
  created() {

  },
  mounted() {
    this.getCount()

  },
  methods: {
    getCount() {
      getKb(this.queryForm).then(res => {
        this.graph.nodes = res.data.literatrueNodesData.map(item => {
          return {
            id: item.id.toString(),
            category: item.gradeFlag - 1,
            name: item.name,
            symbolSize: item.gradeFlag == 1 ? 40 : item.gradeFlag == 2 ? 35 : 25,
            value: item.count,
            keywordValue: item.keywordValue,
            label: {
              show: true
            }
          }
        })
        this.graph.links = res.data.literatrueLinks.map(item => {
          return {
            source: item.source.toString(),
            target: item.target.toString()
          }
        })
        this.initChart();

      })
    },
    initChart() {
      var chartDom = document.getElementById('main');
      var myChart = echarts.init(chartDom);
      var option;
      myChart.showLoading();
      myChart.hideLoading();
      this.graph.nodes.forEach(function (node) {
        node.tooltip = {
          show: node.category !== 0 && node.category !== 1
        };
      });
      option = {
        title: {
          text: '知识图谱',
          subtext: 'Default layout',
          top: 'bottom',
          left: 'right'
        },
        tooltip: {},
        legend: [
          {
            // selectedMode: 'single',
            data: this.graph.categories.map(function (a) {
              return a.name;
            })
          }
        ],
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDuration: 2000,
        series: [
          {
            name: this.queryForm.keyword,
            type: 'graph',
            legendHoverLink: true,
            layout: 'force',
            force: {
              gravity: 0.1,
              repulsion: 1000,
              edgeLength: [200, 420],
              layoutAnimation: true,
              friction: 1,

            },
            data: this.graph.nodes,
            links: this.graph.links,
            categories: this.graph.categories,
            roam: true,
            label: {
              position: 'right',
              formatter: '{b}',
            },
            lineStyle: {
              color: 'source',
              curveness: 0.2
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 10
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      const _that = this
      myChart.off('click');
      myChart.on('click', function (param) {
        if (param.dataType == 'node' && param.data.category !== 0 && param.data.category !== 1) {
          _that.clickNode(param)
        } else {
        }
      })
      option && myChart.setOption(option);
    },
    clickNode(param) {
      this.queryForm.keywordValue = param.data.keywordValue
      this.queryForm.keyword = param.name
      this.getCount()
    },
  },

};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  background-color: #030035; // '#404a59',
}
.main {
  width: 100%;
  height: calc(100% - 50px);
}
.selectForm {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
