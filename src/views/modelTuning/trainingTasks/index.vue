<template>
  <div class="app-container">

    <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
      <el-table-column label="模型名称" prop="modelName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="模型描述" prop="description" min-width="100" />
      <el-table-column label="发布状态" prop="state" min-width="100" />
      <el-table-column label="基础模型" prop="trainType" min-width="100" />
      <el-table-column label="最新完成训练任务" prop="taskName" min-width="100" />
      <el-table-column label="更新时间" align="center" prop="updateTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >发布</el-button>
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--          >导出</el-button>-->
<!--          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">-->
<!--            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>-->
<!--            <el-dropdown-menu slot="dropdown">-->
<!--              <el-dropdown-item command="handleDataScope" icon="el-icon-circle-check">去训练</el-dropdown-item>-->
<!--              <el-dropdown-item command="handleAuthUser" icon="el-icon-user">删除数据集</el-dropdown-item>-->
<!--              <el-dropdown-item command="handleAuthUser" icon="el-icon-user">发布</el-dropdown-item>-->
<!--            </el-dropdown-menu>-->
<!--          </el-dropdown>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getModelList } from "@/api/modelTuning/trainingTasks";

export default {
  name: "DataSetManagement",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据集管理表格数据
      modelList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      getModelList(this.queryParams).then(response => {
          this.modelList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    // handleQuery() {
    //   this.queryParams.pageNum = 1;
    //   this.getList();
    // },
    /** 重置按钮操作 */
    // resetQuery() {
    //   this.dateRange = [];
    //   this.resetForm("queryForm");
    //   this.handleQuery();
    // },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roleId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.$router.push({
    //     path: '/dataSet/addDataSet'
    //   })
    // },
    /** 发布按钮操作 */
    handleUpdate(row) {
      // this.$router.push({
      //   path: '/dataSet/updateData',
      //   query: { 'groupId': row.groupId }
      // })
    },
    /** 删除按钮操作 */
    // handleDelete(row) {
    //   const roleIds = row.roleId || this.ids;
    //   this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
    //     return delRole(roleIds);
    //   }).then(() => {
    //     this.getList();
    //     this.$modal.msgSuccess("删除成功");
    //   }).catch(() => {});
    // },
    /** 导出按钮操作 */
    // handleExport(){
    //   this.download('system/role/export', {
    //     ...this.queryParams
    //   }, `role_${new Date().getTime()}.xlsx`)
    // }
  }
};
</script>
