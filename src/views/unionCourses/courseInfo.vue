<template>
  <div class="app-container">

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="教师信息" name="teacherInfo">
        <el-table v-loading="loading" :data="teacherInfoList" class="research-group-table">
          <el-table-column label="教师ID" prop="teacherId" min-width="60" />
          <el-table-column label="教师姓名" prop="name" min-width="60" />
          <el-table-column label="学校" prop="univerName" min-width="80" />
          <el-table-column label="学院" prop="colleName" min-width="80" />
          <el-table-column label="性别" prop="sex" min-width="50">
            <template slot-scope="scope">
              {{scope.row.sex == '0'? '男':'女'}}
            </template>
          </el-table-column>
          <el-table-column label="职称" prop="title" min-width="60" />
          <el-table-column label="学历" prop="education" min-width="60" />

          <el-table-column label="是否为负责人" prop="isPersonCharge" min-width="100">
            <template slot-scope="scope">
              {{scope.row.isPersonCharge == '1'? '是':'否'}}
            </template>
          </el-table-column>
          <el-table-column label="研究方向" prop="researchDirection" min-width="180">
            <template slot-scope="scope">
              <el-popover placement="right" width="400" trigger="hover">
                {{ scope.row.researchDirection }}
                <el-button type="text" size="small" slot="reference" style="color: #606266;">
                  {{scope.row.researchDirection&&scope.row.researchDirection.length>20? scope.row.researchDirection.substr(0,20) + "..." : scope.row.researchDirection}}</el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="研究成果" prop="kbName" min-width="180">
            <template slot-scope="scope">
              <el-popover placement="right" width="400" trigger="hover"
                v-if="scope.row.teacherFindingsList&&scope.row.teacherFindingsList.length > 0">
                <el-table :data="scope.row.teacherFindingsList" :show-header="false">
                  <el-table-column type="index" />
                  <el-table-column min-width="300" property="researchFindings" label="研究成果"></el-table-column>
                </el-table>
                <el-button type="text" size="small" slot="reference" style="color: #606266;">
                  {{ scope.row.teacherFindingsList[0].researchFindings&&scope.row.teacherFindingsList[0].researchFindings.length>20?scope.row.teacherFindingsList[0].researchFindings.substr(0,20) + "...":scope.row.teacherFindingsList[0].researchFindings }}</el-button>
              </el-popover>
              <span v-else>{{ '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="teacherTotal > 0" :total="teacherTotal" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getTeacherInfoList" />
      </el-tab-pane>
      <el-tab-pane label="教材信息" name="textbookInfo">
        <el-table v-loading="loading" :data="textbookInfoList">
          <el-table-column label="教材" prop="fileName" min-width="300" />
          <el-table-column label="创建人" prop="createByNickname" min-width="100" />
          <el-table-column label="创建人学校" prop="createByUniverName" min-width="100" />
          <el-table-column label="上传时间" prop="createTime" min-width="120">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="textTotal > 0" :total="textTotal" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getTextbookInfoList" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import { getTeacherInfoList, getTextbookInfoList } from "@/api/unionCourses/unionCourses.js";

export default {
  data() {
    return {
      loading: false,
      activeName: 'teacherInfo',
      teacherInfoList: [],
      teacherTotal: 0,
      textbookInfoList: [],
      textTotal: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: this.$route.query && this.$route.query.id
      }
    };
  },
  created() {
    this.getTeacherInfoList();
  },
  activated() {
    this.getTeacherInfoList();
  },
  methods: {
    getTeacherInfoList() {
      this.loading = true
      getTeacherInfoList(this.queryParams).then(response => {
        this.teacherInfoList = response.rows
        this.teacherTotal = response.total
        this.loading = false
      })
    },
    getTextbookInfoList() {
      this.loading = true
      getTextbookInfoList(this.queryParams).then(response => {
        this.textbookInfoList = response.rows
        this.textTotal = response.total
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
    },
    handleClick(tab, event) {
      if (tab.name === 'textbookInfo') {
        this.resetQuery()
        this.getTextbookInfoList()
      } else if (tab.name === 'teacherInfo') {
        this.resetQuery()
        this.getTeacherInfoList()
      }
    }
  }
};
</script>