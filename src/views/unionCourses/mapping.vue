<template>
  <div class="app-container ck-container">
    <div id="main" class="main" />
    <!-- 查看详情 -->
    <right-drawer class="details-drawer" :show-drawer.sync="show">
      <teachers-or-materials-details v-if="detailsDrawer" ref="teachersOrMaterialsDetails" slot="drawer-content"
        :tableNameFieldName="tableNameFieldName" :keywordValue="keywordValue" :keywordLabel="keywordLabel"
        :id="queryForm.id" />
    </right-drawer>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getMapping } from "@/api/unionCourses/unionCourses.js";
import teachersOrMaterialsDetails from './teachersOrMaterialsDetails.vue'

export default {
  name: "MappingKnowledgeDomain",
  dicts: ['knowledge_type'],
  components: { teachersOrMaterialsDetails },
  data() {
    return {
      show: false,
      detailsDrawer: false, // 点击详情
      tableNameFieldName: '',
      keywordLabel: '',
      keywordValue: '',
      queryForm: {
        id: ''
      },
      graph: {
        categories: [
          {
            "name": "A"
          },
          {
            "name": "B"
          },
          {
            "name": "C"
          },
          {
            "name": "D"
          },
          {
            "name": "E"
          },
          {
            "name": "F"
          },
        ]
      }
    };
  },
  created() {

  },
  mounted() {
    this.getCount()

  },
  methods: {
    getCount() {
      this.queryForm.id = this.$route.query && this.$route.query.id
      getMapping(this.queryForm).then(res => {
        this.graph.nodes = res.data.nodesDatasVoList.map(item => {
          return {
            id: item.id.toString(),
            category: item.gradeFlag - 1,
            name: item.name,
            symbolSize: item.gradeFlag == 1 ? 40 : item.gradeFlag == 2 ? 35 : 25,
            keywordValue: item.keywordValue,
            onclickFlag: item.onclickFlag,
            tableNameFieldName: item.tableNameFieldName,
            label: {
              show: true
            }
          }
        })
        this.graph.links = res.data.nodeDataLinksVoList.map(item => {
          return {
            source: item.source.toString(),
            target: item.target.toString()
          }
        })
        this.initChart();

      })
    },
    initChart() {
      var chartDom = document.getElementById('main');
      var myChart = echarts.init(chartDom);
      var option;
      myChart.showLoading();
      myChart.hideLoading();
      this.graph.nodes.forEach(function (node) {
        node.tooltip = {
          show: node.category !== 0 && node.category !== 1
        };
      });
      option = {
        title: {
          text: '课程联盟图谱',
          subtext: 'Default layout',
          top: 'bottom',
          left: 'right'
        },
        tooltip: {
          formatter: function (x) {
            return x.data.name;//设置提示框的内容和格式 节点和边都显示name属性
          }
        },
        legend: [
          {
            // selectedMode: 'single',
            data: this.graph.categories.map(function (a) {
              return a.name;
            })
          }
        ],
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDuration: 2000,
        series: [
          {
            name: this.queryForm.keyword,
            type: 'graph',
            legendHoverLink: true,
            layout: 'force',
            force: {
              gravity: 0.1,
              repulsion: 1000,
              edgeLength: [200, 420],
              layoutAnimation: true,
              friction: 1,

            },
            data: this.graph.nodes,
            links: this.graph.links,
            categories: this.graph.categories,
            roam: true,
            label: {
              position: 'right',
              formatter: '{b}',
            },
            lineStyle: {
              color: 'source',
              curveness: 0.2
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 10
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      const _that = this
      myChart.off('click');
      myChart.on('click', function (param) {
        if (param.dataType == 'node' && param.data.onclickFlag === true) {
          _that.clickNode(param)
        } else {
        }
      })
      option && myChart.setOption(option);
    },
    clickNode(param) {
      this.tableNameFieldName = param.data.tableNameFieldName
      this.keywordLabel = param.name
      this.keywordValue = param.data.keywordValue
      this.show = true
      this.detailsDrawer = true
    },
  },

};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  background-color: #030035; // '#404a59',
}
.main {
  width: 100%;
  height: 100%;
}
.selectForm {
  display: flex;
  justify-content: center;
  align-items: center;
}
.details-drawer.isShow {
  width: 800px !important;
}
</style>
