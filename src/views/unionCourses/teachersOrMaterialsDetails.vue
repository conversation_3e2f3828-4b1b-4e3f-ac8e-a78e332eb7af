<template>
  <div class="drawer-details_container" style="height: 100%;padding: 10px;">
    <el-descriptions class="margin-top" title="教材信息" :column="2" border v-if="tableNameFieldName!='s_ambit_teacher:id'">
      <el-descriptions-item label="教材名称" :span="2">{{bookInfo.fileName}}</el-descriptions-item>
      <el-descriptions-item label="课程名称" :span="2">{{bookInfo.courseName}}</el-descriptions-item>
      <el-descriptions-item label="创建人姓名">{{bookInfo.createByNickname}}</el-descriptions-item>
      <el-descriptions-item label="创建人学校">{{bookInfo.createByUniverName}}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions class="margin-top" title="教师信息" :column="3" border v-if="tableNameFieldName=='s_ambit_teacher:id'">
      <el-descriptions-item label="教师姓名">{{teacherInfo.name}}</el-descriptions-item>
      <el-descriptions-item label="教师ID">{{teacherInfo.teacherId}}</el-descriptions-item>
      <el-descriptions-item label="性别">{{teacherInfo.sex == '0'? '男':'女'}}</el-descriptions-item>
      <el-descriptions-item label="学校">{{teacherInfo.univerName}}</el-descriptions-item>
      <el-descriptions-item label="学院">{{teacherInfo.colleName}}</el-descriptions-item>
      <el-descriptions-item label="职称">{{teacherInfo.title}}</el-descriptions-item>
      <el-descriptions-item label="学历">{{teacherInfo.education}}</el-descriptions-item>
      <el-descriptions-item label="是否为负责人"
        :span="2">{{teacherInfo.isPersonCharge == '1'? '是':'否'}}</el-descriptions-item>
      <el-descriptions-item label="研究方向" :span="3">{{teacherInfo.researchDirection}}</el-descriptions-item>
      <el-descriptions-item label="研究成果" :span="3">{{teacherInfo.researchFindings}}</el-descriptions-item>
    </el-descriptions>

  </div>
</template>
<script>
import { getTeacherInfo, getBookInfo } from "@/api/unionCourses/unionCourses.js";
export default {
  name: 'AtiDetails',
  props: {
    id: {
      default: '',
      type: String
    },
    tableNameFieldName: {
      default: '',
      type: String
    },
    keywordLabel: {
      default: '',
      type: String
    },
    keywordValue: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      teacherInfo: {},
      bookInfo: {},
      loading: false
    }
  },
  watch: {
    tableNameFieldName(val) {
      this.getThesis()
    },
    keywordLabel(val) {
      this.getThesis()
    },
    keywordValue(val) {
      this.getThesis()
    },
    id(val) {
      this.getThesis()
    },
  },
  created() {
    this.getThesis()
  },
  methods: {
    getThesis() {
      this.loading = true
      const paramData = {
        id: this.id,
        tableNameFieldName: this.tableNameFieldName,
        keywordLabel: this.keywordLabel,
        keywordValue: this.keywordValue
      }
      if (this.tableNameFieldName === 's_ambit_teacher:id') {
        getTeacherInfo(paramData).then(res => {
          this.teacherInfo = res.data
          this.loading = false
        })
      } else {
        getBookInfo(paramData).then(res => {
          this.bookInfo = res.data
          this.loading = false
        })
      }
    },
    handleDownload(row) {
      this.download('test/kbfile/fileDownloadPost', {
        titleId: row.titleId
      }, `${row.title}.pdf`)
    },

  }
}
</script>
