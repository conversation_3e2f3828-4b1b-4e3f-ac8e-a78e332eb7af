<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="联盟名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入联盟名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-if="checkPermi(['unionCourses:info:add'])">新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="unionCoursesList">
      <el-table-column label="联盟名称" align="left" prop="name" />
      <el-table-column label="课程名称" align="left" prop="courseName">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.courseName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联盟学校" align="left" prop="schoolNames" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-share" @click="handleToMapping(scope.row)">图谱</el-button>
          <el-button v-if="scope.row.createBy.toString() === loginUserData.id.toString()" size="mini" type="text"
            icon="el-icon-edit" @click="handleUpdate(scope.row)">修改
          </el-button>
          <el-button v-if="scope.row.createBy.toString() === loginUserData.id.toString()" size="mini" type="text"
            icon="el-icon-delete" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改课程联盟对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="联盟名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入联盟名称" />
        </el-form-item>
        <el-form-item label="课程名字" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课程名字" />
        </el-form-item>

        <el-form-item label="联盟学校" prop="schoolIdAray">
          <el-select style="width: 100%;" v-model="form.schoolIdAray" multiple placeholder="请选择联盟学校">
            <el-option v-for="item in schoolOptions" :key="item.key" :label="item.label" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUnionCourses,
  getUnionCourses,
  delUnionCourses,
  addUnionCourses,
  updateUnionCourses, getUniversityAll
} from '@/api/unionCourses/unionCourses'
import { checkPermi } from "@/utils/permission";
export default {
  name: 'UnionCourses',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程联盟表格数据
      unionCoursesList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        courseName: null
      },
      // 表单参数
      form: {
        schoolIdAray: []
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '联盟名称不能为空', trigger: ['blur', 'change'] }
        ],
        courseName: [
          { required: true, message: '课程名称不能为空', trigger: ['blur', 'change'] }
        ],
        schoolIdAray: [
          { required: true, message: '联盟学校不能为空', trigger: ['blur', 'change'] }
        ]
      },
      // 学校选项
      schoolOptions: [],
      // 当前登录用户
      loginUserData: {}
    }
  },
  created() {
    // 获取列表
    this.getList()
    // 获取学校数据
    this.getUniversityAll()
    this.loginUserData = this.$store.state.user
  },

  methods: {
    checkPermi,
    /** 查询课程联盟列表 */
    getList() {
      this.loading = true
      listUnionCourses(this.queryParams).then(response => {
        this.unionCoursesList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '创建课程联盟'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = { ...row }
      this.open = true
      this.title = '修改课程联盟'
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUnionCourses(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.handleClose()
            })
          } else {
            addUnionCourses(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.handleClose()

            })
          }
        }
      })
    },
    /** 关闭/取消 */
    handleClose() {
      this.form = { schoolIdAray: [] }
      this.$refs.form.clearValidate()
      this.resetForm("form");
      this.open = false
      this.getList()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除联盟课名称为"' + row.name + '"的数据项？').then(function () {
        return delUnionCourses(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /* 获取所有的学校列表**/
    getUniversityAll() {
      getUniversityAll().then(res => {
        this.schoolOptions = res.data
      })
    },
    /** 查询课程信息列表 */
    handleReview(row) {
      this.$router.push({
        path: "/unionCourse/courseInfo",
        query: { id: row.id },
      });
    },
    /** 查询图谱 */
    handleToMapping(row) {
      this.$router.push({
        path: "/unionCourse/mapping",
        query: { id: row.id },
      });
    },
  }
}
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
