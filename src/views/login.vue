<template>
  <div :class="prodFLag === true ? 'login' : 'login-test'">
    <button @click="jumpToVideoPage" class="video-button">视频演示</button>
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" style="position: relative;">
      <!--      <h3 class="title">-->
      <!--        <img src="@/assets/icons/loing.png" style="position: absolute; top: 0; left: 0; width: 100%; object-fit: cover;">-->

      <!--        <img src="@/assets/icons/aicaidmx2.png" style="width: 150px; height: 50px;">-->
      <!--      </h3>-->
      <h3 class="title">
        <img :src="prodFLag === true ? url1: url2"
          style="position: absolute;top: -1px;left: -1px;width:502px;height: auto;object-fit: cover;z-index: 1;border-radius:7px 7px 0px 0px;">

        <img :src="prodFLag === true ? url3: url4"
          style="position: relative; z-index: 2; width: 250px; height: 100px; margin-top: 88px;">
      </h3>
      <el-form-item prop="username" style="margin-left: 55px;">
        <el-input v-model="loginForm.username" class="input-with-select" type="text" auto-complete="off"
          placeholder="用户名">
          <svg-icon slot="prepend" icon-class="user" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />

        </el-input>
      </el-form-item>
      <el-form-item prop="password" style="margin-left: 55px;">
        <el-input v-model="loginForm.password" class="input-with-select" type="password" auto-complete="off"
          placeholder="密码" @keyup.enter.native="handleLogin">
          <svg-icon slot="prepend" icon-class="password" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled" class="flex-center" style="margin-left: 55px;">
        <el-input v-model="loginForm.code" class="input-with-select" auto-complete="off" placeholder="验证码"
          style="width: 43%" @keyup.enter.native="handleLogin">
          <svg-icon slot="prepend" icon-class="validCode" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 0px 55px">记住密码</el-checkbox>
      <el-form-item prop="haveRead" style="margin:0px 0px 25px 55px;">
        <el-checkbox-group v-model="loginForm.haveRead">
          <el-checkbox label="haveRead">已阅读并同意
            <a href="/policy/privacy-policy.html" target="_blank" rel="noopener noreferrer">隐私政策</a> 和 <a
              href="/policy/user-agreement.html" target="_blank" rel="noopener noreferrer">用户协议</a></el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item style=" width: 100%; align-items: center;">
        <el-button :loading="loading" size="medium" type="primary"
          style="width: 30%;margin-bottom: 10px;margin-left: 155px;border-radius: 20px;background: #5a9dd8;"
          @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="text-align: right; margin-left: auto;" v-if="register">
          <!--          <router-link class="link-type" :to="'/externalCertification'">立即注册</router-link>-->
          <span style="margin-right: 10px;"></span>
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
          <span style="margin-right: 10px;"></span>
          <router-link class="link-type" :to="'/modify'">忘记密码?</router-link>
          <span style="margin-right: 10px;"></span>
          <el-button @click="drawer = true" type="text" style="margin-left: 16px;font-size: 16px;z-index: initial"
            size="47%">
            注册流程
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <el-drawer title="我是标题" :visible.sync="drawer" :with-header="false">
      <iframe :src="'/注册认证.pdf'" style="position:absolute;" width="100%" height="100%"></iframe>
    </el-drawer>
    <!--  底部  -->
    <div class="el-login-footer">
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank"><span>{{ icpNum }}</span></a><br />
      <span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;客户电话: 0531-88809809</span><br />
      <span>&emsp;&emsp;&emsp;&emsp;176-1006-0813</span>
    </div>

    <el-dialog :class="prodFLag === true ? 'introduction-dialog' : 'informn-dialog'"
      :title="prodFLag === true ? '入口介绍' : '通知'" :visible.sync="dialogVisible" width="900px" :showClose="false">
      <div style="width:570px;margin-left: auto;" v-if="prodFLag">{{ introduction }}</div>
      <div v-if="!prodFLag">
        <span>尊敬的山财大用户：</span><br />
        <span style="display:block;text-indent:2em;">您好！</span>
        <span style="display:block;text-indent:2em;">为了提供更加稳定、高效的服务体验，自2024年11月13日起"AI"才专有大模型应用平台网站域名升级调整为：
          <a href="https://www.aicaipap.com" target="_blank"><span
              style="color: #409EFF;">www.aicaipap.com。</span></a></span>
        <span style="display:block;text-indent:2em;">此次域名变更不会影响您的用户数据。感谢您的支持理解，我们继续为您提供优质服务！</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeIntroductionDialog" round>{{'关闭'+' '+ dialogVisibleTimes+'s' }}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";

export default {
  name: "Login",
  data() {
    return {
      drawer: false,
      codeUrl: "",
      loginForm: {
        username: undefined,
        password: undefined,
        rememberMe: false,
        code: "",
        uuid: "",
        haveRead: [],
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的用户名" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
        haveRead: [{ required: true, trigger: ["blur", "change"], message: "请勾选'已阅读并同意隐私政策和大模型服务协议'" }],
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: true,
      modify: true,
      redirect: undefined,
      dialogVisible: false,
      dialogVisibleTimes: 10,
      dialogVisibleTime: undefined,
      icpNum: "鲁ICP备2022016453号",
      prodFLag: false,
      introduction: '欢迎使用山东财经大学新文科教育教学专有大模型应用平台——“AI才”!它由山东财经大学与百度智能云、山东银瑞合作研发，集“知识数字化、教学助手、学智会友、研究工坊、训习园地和智能提升”等功能于一体，旨在为每一位学生提供个性化的支持和关怀。期待“AI才”成为您在新文科教育教学领域中的得力助手。',
      url1: require('@/assets/icons/loing.png'),
      url2: require('@/assets/icons/loing-test.jpg'),
      url3: require('@/assets/icons/aicaidmx3.png'),
      url4: require('@/assets/icons/jy-test.png'),

    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCode();
    this.getCookie();
    this.changedialogVisible()
    this.prodFLag = window.location.hostname === 'www.papaicai.com' ? false : true
    this.icpNum = window.location.hostname === 'www.papaicai.com' ? '鲁ICP备2022016453号-2' :
      window.location.hostname === 'www.aicaipap.com' ? '鲁ICP备2022016453号-3' :
        window.location.hostname === 'aicai.sdufe.edu.cn' ? '鲁ICP备05001933号-2' : ''
  },
  methods: {
    jumpToVideoPage() {
      this.$router.push({ name: 'VideoPlayer' });
    },
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        haveRead: []
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {
              });
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        }
      });
    },
    changedialogVisible() {
      this.dialogVisible = true
      this.dialogVisibleTime = setInterval(() => {
        this.dialogVisibleTimes--
        if (this.dialogVisibleTimes == 0) {
          this.dialogVisible = false
          clearInterval(this.dialogVisibleTime);
        }
      }, 1000);
    },
    closeIntroductionDialog() {
      this.dialogVisible = false
      clearInterval(this.dialogVisibleTime);
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.flex-center {
  align-items: center; /* 垂直居中对齐 */
}

.login-code {
  /*margin-left: 10px; !* 根据需要调整间距 *!*/
}

.input-with-select .el-input-group__prepend {
  border-radius: 20px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background: #6dade2;
  width: 34px;
}

.input-with-select .el-input__inner {
  border-radius: 20px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background: #5a9dd8;
}

.login {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login(1).jpg");
  background-size: cover;
}
.login-test {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-test.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 500px;
  padding: 25px 25px 5px 25px;
  margin-right: 100px;

  .el-input {
    height: 38px;
    width: 350px;
    // margin-left: 55px;
    /*margin-right: 65px;*/
    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 10px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 80px;
  line-height: 20px;
  position: fixed;
  bottom: 0;
  /*width: 100%;*/
  right: 50%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

.video-button {
  position: fixed; /* 固定定位，使其相对于浏览器窗口定位 */
  bottom: 20px; /* 距离底部的距离，按需调整 */
  left: 20px; /* 距离左侧的距离，按需调整 */
  background-color: transparent; /* 绿色背景 */
  color: white; /* 文字颜色为白色 */
  padding: 10px 20px; /* 按钮内边距，按需调整 */
  border: none; /* 无边框 */
  cursor: pointer; /* 鼠标悬停时指针变为手型 */
  border-radius: 5px; /* 圆角，可选 */
  font-size: 14px; /* 字体大小，按需调整 */
  z-index: 1000; /* 确保按钮在最上层，避免被其他元素遮挡 */
}

.informn-dialog .el-dialog {
  margin-top: 25vh !important;
  background: rgba(194, 229, 250, 0.9);
}

.informn-dialog .el-dialog__header {
  display: none;
}
.informn-dialog .el-dialog__body {
  padding: 30px 40px;
  font-size: 20px;
  line-height: 46px;
  letter-spacing: 2px;
  text-align: justify;
  background-color: rgb(255, 255, 255, 0);
  color: #3e3f41;
}
.informn-dialog .el-dialog__footer {
  padding: 20px;
  background-color: rgb(255, 255, 255, 0);
  text-align: center;
  .el-button {
    background-color: rgb(255, 255, 255, 0);
  }
}

.introduction-dialog .el-dialog {
  margin-top: 25vh !important;
  background: url("../assets/images/introduction_back.png") no-repeat;
  background-size: 100% 100%;
}
.introduction-dialog .el-dialog__header {
  display: none;
}
.introduction-dialog .el-dialog__body {
  padding: 30px 40px;
  font-size: 28px;
  line-height: 46px;
  letter-spacing: 2px;
  text-align: justify;
  background-color: rgb(255, 255, 255, 0);
  color: #3e3f41;
}
.introduction-dialog .el-dialog__footer {
  padding: 20px;
  background-color: rgb(255, 255, 255, 0);
  text-align: center;
  .el-button {
    background-color: rgb(255, 255, 255, 0);
  }
}
a {
  color: #398dee;
  text-decoration: none;
}

a:hover {
  color: #398dee;
  text-decoration: underline;
}
a:focus {
  color: #398dee;
  text-decoration: underline;
}
</style>
