<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <div class="ck-form-item_title">基本信息</div>
      <el-form-item label="任务名称" prop="taskName">
        <el-input class="ck-input" v-model="form.taskName" placeholder="请输入任务名称" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="选择数据集" prop="datasetId">
        <el-select class="ck-input" v-model="form.datasetId" placeholder="请选择数据集" @change="handleChange">
          <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务描述" prop="description">
        <el-input v-model="form.description" type="textarea" autosize />
      </el-form-item>
      <div class="ck-form-item_title">参数配置</div>
      <el-form-item label="迭代轮次" prop="epoch">
        <template slot="label">
          <span>迭代轮次&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              迭代轮次（Epoch），控制训练过程中的迭代轮数。可以根据数据规模适当调整Epoch大小，若用1000条数据SFT，Epoch推荐为10。若用10000条数据SFT，Epoch推荐为2。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-input-number :min="1" :max="50" class="ck-input" v-model="form.epoch" controls-position="right" />
      </el-form-item>
      <el-form-item label="学习率" prop="learningRate">
        <template slot="label">
          <span>学习率&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              学习率（LearningRate）是在梯度下降的过程中更新权重时的超参数，过高会导致模型难以收敛，过低则会导致模型收敛速度过慢，平台已给出默认推荐值，可根据经验调整。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-input-number :min="0.00003" :max="0.001" class="ck-input" v-model="form.learningRate" controls-position="right" />
      </el-form-item>
      <el-form-item label="序列长度" prop="maxSeqlen">
        <template slot="label">
          <span>序列长度&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              单条数据的长度，单位为token。如果数据集中每条数据的长度（输入）都在4096 tokens 以内，建议选择4096，针对短序列可以达到更优的训练效果。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-select class="ck-input" v-model="form.maxSeqlen" placeholder="请选择序列长度">
          <el-option v-for="dict in dict.type.max_seqlen" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="保存日志间隔" prop="loggingSteps">
        <template slot="label">
          <span>保存日志间隔&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              日志保存间隔步数。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-input-number :min="1" :max="50" class="ck-input" v-model="form.loggingSteps" controls-position="right" />
      </el-form-item>
      <el-form-item label="预热比例" prop="warmupRatio">
        <template slot="label">
          <span>预热比例&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              学习率预热的步数占比。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-input-number :min="0.01" :max="0.5"  class="ck-input" v-model="form.warmupRatio" controls-position="right" />
      </el-form-item>
      <el-form-item label="正则化系数" prop="weightDecay">
        <template slot="label">
          <span>正则化系数&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              正则化系数（Weight_decay），用于防止模型对训练数据过拟合。但系数过大，可能导致欠拟合。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-input-number :min="0.0001" :max="0.1" class="ck-input" v-model="form.weightDecay" controls-position="right" />
      </el-form-item>
      <el-form-item label="LoRA 策略中的秩" prop="loraRank">
        <template slot="label">
          <span>LoRA 策略中的秩&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              LoRA秩大小。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-select class="ck-input" v-model="form.loraRank" placeholder="请选择LoRA策略中的秩">
          <el-option v-for="dict in dict.type.lora_rank" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="LoRA所有线性层" prop="loraAllLinear">
        <template slot="label">
          <span>LoRA所有线性层&emsp;</span>
          <el-tooltip placement="top-start" effect="light">
            <div slot="content">
              是否将 LoRA 策略应用在所有linear层。
            </div>
            <span><i class="el-icon-question" /></span>
          </el-tooltip>
        </template>
        <el-select class="ck-input" v-model="form.loraAllLinear" placeholder="请选择LoRA所有线性层">
          <el-option v-for="dict in dict.type.lora_all_linear" :key="dict.value" :label="dict.label"
            :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { addTraining, getDataSetPublished } from "@/api/modelFineTuning/trainingMission.js";
export default {
  name: 'FormContent',
  dicts: ['max_seqlen', 'lora_rank', 'lora_all_linear'],
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        datasetId: '',
        groupName: '',
        epoch:1,
        maxSeqlen:4096,
        loggingSteps:1,
        warmupRatio:0.1,
        weightDecay:0.0001,
        loraRank:8,
        loraAllLinear:"True",
        learningRate:0.0003,
        menuRouting:  this.$route.query && this.$route.query.menuRouting,
      },
      rules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: ['blur', 'change'] },
        ],
      },
      groupOptions: [] //数据集
    }
  },
  created() {
    this.getDataSetPublished()
  },
  methods: {
    getDataSetPublished() {
      getDataSetPublished(this.$route.query.menuRouting).then(res => {
        const groupOptions = []
        if (res.data && res.data.length > 0) {
          res.data.forEach(item => {
            groupOptions.push({
              label: item.groupName,
              value: item.datasetId
            })
          })
        }
        this.groupOptions = groupOptions
      })
      this.form.datasetId = this.$route.query && this.$route.query.datasetId ;
      this.form.groupName =  this.$route.query && this.$route.query.groupName
    },
    handleSubmint() {
      addTraining(this.form).then(res => {
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.handleBack()
        }
      })
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push(this.$route.query.menuRouting)
    },
    handleChange(datasetId) {
      const groupName = this.groupOptions.filter(item => item.value == datasetId)
      this.form.groupName = groupName.length > 0 && groupName[0].label
    },
  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.ck-form-item_title {
  margin: 0;
  padding: 0;
  height: 26px;
  line-height: 26px;
  font-size: 15px;
  font-weight: normal;
  position: relative;
  padding-left: 10px;
  display: flex;
  color: #333;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  &::before {
    width: 4px;
    height: 16px;
    display: block;
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #1682e6;
    margin: auto;
  }
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
</style>

