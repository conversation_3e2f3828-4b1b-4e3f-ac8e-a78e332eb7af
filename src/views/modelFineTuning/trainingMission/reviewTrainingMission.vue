<template>
  <div class="app-container">
    <div class="ck-form">
      <el-descriptions title="基本信息">
        <el-descriptions-item label="任务名称">{{trainingInfo.taskName}}</el-descriptions-item>
        <el-descriptions-item label="数据集">{{trainingInfo.groupName}}</el-descriptions-item>
        <el-descriptions-item label="任务描述">{{trainingInfo.description}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="参数配置">
        <el-descriptions-item label="迭代轮次">{{trainingInfo.epoch}}</el-descriptions-item>
        <el-descriptions-item label="学习率">{{trainingInfo.learningRate}}</el-descriptions-item>
        <el-descriptions-item label="序列长度">{{trainingInfo.maxSeqlen}}</el-descriptions-item>
        <el-descriptions-item label="保存日志间隔">{{trainingInfo.loggingSteps}}</el-descriptions-item>
        <el-descriptions-item label="预热比例">{{trainingInfo.warmupRatio}}</el-descriptions-item>
        <el-descriptions-item label="正则化系数">{{trainingInfo.weightDecay}}</el-descriptions-item>
        <el-descriptions-item label="策略中的秩">{{trainingInfo.loraRank}}</el-descriptions-item>
        <el-descriptions-item label="LoRA所有线性层">{{trainingInfo.loraAllLinear}}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
import { getTraining } from "@/api/modelFineTuning/trainingMission.js";
export default {
  name: "ReviewDataProcessing",
  data() {
    return {
      // 遮罩层
      loading: true,
      trainingInfo: {},
      id: '',
    };
  },
  created() {
    this.getInfo()
  },
  methods: {
    /** 查询详情 */
    getInfo() {
      this.id = this.$route.query && this.$route.query.id
      this.loading = true;
      getTraining(this.id).then(response => {
        this.trainingInfo = response.data;
        this.loading = false;
      });
    },
  }
};
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
</style>