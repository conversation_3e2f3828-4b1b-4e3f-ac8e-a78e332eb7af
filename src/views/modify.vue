<template>
  <div :class="prodFLag === true ? 'register' : 'register-test'">
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form"
      style="position: relative;">
      <!--      <h3 class="title">注册</h3>-->
      <h3 class="title">
        <img :src="prodFLag === true ? url1: url2"
          style="position: absolute;top: -1px;left: -1px;width:502px;height: auto;object-fit: cover;z-index: 1;border-radius:7px 7px 0px 0px;">

        <img :src="prodFLag === true ? url3: url4"
          style="position: relative; z-index: 2; width: 250px; height: 100px; margin-top: 88px;">
      </h3>
      <el-form-item prop="username">
        <el-input v-model="registerForm.username" type="text" auto-complete="off" placeholder="用户名"
          class="input-with-select">
          <svg-icon slot="prepend" icon-class="user" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="registerForm.password" type="password" auto-complete="off" placeholder="密码"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="password" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="off" placeholder="确认密码"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="password" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <!--      <el-form-item prop="code" v-if="captchaEnabled">-->
      <!--        <el-input-->
      <!--          v-model="registerForm.code"-->
      <!--          auto-complete="off"-->
      <!--          placeholder="验证码"-->
      <!--          style="width: 43%"-->
      <!--          @keyup.enter.native="handleRegister"-->
      <!--          class="input-with-select"-->
      <!--        >-->
      <!--        </el-input>-->
      <!--      </el-form-item>-->
      <!-- <el-form-item prop="phonenumber">
        <el-input v-model="registerForm.phonenumber" type="text" auto-complete="off" placeholder="手机号"
          class="input-with-select">
          <svg-icon slot="prepend" icon-class="phone" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item> -->
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input v-model="registerForm.code" auto-complete="off" placeholder="请输入验证码" style="width: 43%"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="validCode" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
        <el-button :disabled="isSendingCode || countdown > 0" :loading="isSendingCode"
          style="width: 25%; margin-left: 5%" @click="sendSmsCode">
          {{ countdown > 0 ? `${countdown} 秒后重试` : '发送验证码' }}
        </el-button>
      </el-form-item>

      <el-form-item style="width: 100%;align-items: center;">
        <el-button :loading="loading" size="medium" type="primary"
          style="width: 30%;margin-bottom: 10px;margin-left: 155px;border-radius: 20px;background: #5a9dd8;"
          @click.native.prevent="handleRegister">
          <span v-if="!loading">修改密码</span>
          <span v-else>修 改 中...</span>
        </el-button>
        <div style="text-align: right; margin-left: auto;">
          <router-link class="link-type" :to="'/login'">使用已有账户登录</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank"><span>{{ icpNum }}</span></a><br />
      <span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;客户电话: 0531-88809809</span><br />
      <span>&emsp;&emsp;&emsp;&emsp;176-1006-0813</span>
    </div>
  </div>
</template>

<script>
import { send, revise } from "@/api/login";
import { encrypt, decrypt } from "@/api/system/rsaUtil";

export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      codeUrl: "",
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        code: "",
        uuid: "",
        icpNum: "鲁ICP备2022016453号",
        prodFLag: false,
      },
      registerRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的用户名" },
          // {
          //   min: 11,
          //   max: 11,
          //   message: "手机号长度必须为11位",
          //   trigger: "blur",
          // },
          // {
          //   pattern: /^1[3-9]\d{9}$/, // 正则表达式，匹配中国手机号格式
          //   message: "请输入正确的手机号格式",
          //   trigger: "blur",
          // },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
          {
            min: 8, // 修改最小长度为8
            max: 12,
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, // 添加正则表达式确保包含大小写字母和数字
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
        // phonenumber: [
        //   { required: true, trigger: "blur", message: "请输入您的手机号" },
        //   {
        //     min: 11,
        //     max: 11,
        //     message: "手机号长度必须为11位",
        //     trigger: "blur",
        //   },
        //   {
        //     pattern: /^1[3-9]\d{9}$/, // 正则表达式，匹配中国手机号格式
        //     message: "请输入正确的手机号格式",
        //     trigger: "blur",
        //   },
        // ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      captchaEnabled: true,
      isSendingCode: false, // 标记验证码是否正在发送中
      countdown: 0, // 发送验证码后的倒计时计数
      url1: require('@/assets/icons/loing.png'),
      url2: require('@/assets/icons/loing-test.jpg'),
      url3: require('@/assets/icons/aicaidmx3.png'),
      url4: require('@/assets/icons/jy-test.png'),
    };
  },
  created() {
    this.prodFLag = window.location.hostname === 'www.papaicai.com' ? false : true
    this.icpNum = window.location.hostname === 'www.papaicai.com' ? '鲁ICP备2022016453号-2' :
      window.location.hostname ==='www.aicaipap.com'? '鲁ICP备2022016453号-3':
        window.location.hostname ==='aicai.sdufe.edu.cn'? '鲁ICP备05001933号-2':''
  },

  methods: {
    sendSmsCode() {
      if (this.isSendingCode || this.countdown > 0) {
        return; // 如果正在发送中或倒计时未结束，直接返回
      }
      // const phoneRegex = /^1[3-9]\d{9}$/; // 简单的中国大陆手机号正则表达式
      // if (!phoneRegex.test(this.registerForm.phonenumber)) {
      //   this.$message.error('请输入有效的手机号码');
      //   return;
      // }
      this.isSendingCode = true; // 开始发送，禁用按钮并显示加载状态
      var priKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu';
      const param = {
        userName: this.registerForm.username,
        dataNow: encrypt(Date.now().toString(), priKey),
      };
      send(param)
        .then((res) => {
          this.countdown = 60;
        }
        );
      // 发送成功后，开始倒计时
      this.startCountdown();
    },
    startCountdown() {
      const timer = setInterval(() => {
        if (this.countdown === 0) {
          clearInterval(timer);
          this.isSendingCode = false; // 发送完成，重置状态
        } else {
          this.countdown--; // 倒计时减1
        }
      }, 1000); // 每秒减少
    },
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          var priKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu';
          const param = {
            userName: this.registerForm.username,
            password: this.registerForm.password,
            code: this.registerForm.code,
            dataNow: encrypt(Date.now().toString(), priKey),
          };
          revise(param)
            .then((res) => {
              const username = this.registerForm.username;
              this.$alert(
                "<font color='red'>恭喜你，您的账号 " +
                username +
                " 密码修改成功！</font>",
                "系统提示",
                {
                  dangerouslyUseHTMLString: true,
                  type: "success",
                }
              )
                .then(() => {
                  this.$router.push("/login");
                })
                .catch(() => { });
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.input-with-select .el-input-group__prepend {
  border-radius: 20px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background: #6dade2;
  width: 34px;
}

.input-with-select .el-input__inner {
  border-radius: 20px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background: #5a9dd8;
}
.register {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login(1).jpg");
  background-size: cover;
}
.register-test {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-test.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 500px;
  padding: 25px 25px 5px 25px;
  margin-right: 100px;
  .el-input {
    height: 38px;
    width: 350px;
    margin-left: 55px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.register-code-img {
  height: 38px;
}
.el-login-footer {
  height: 80px;
  line-height: 20px;
  position: fixed;
  bottom: 0;
  /*width: 100%;*/
  right: 50%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
