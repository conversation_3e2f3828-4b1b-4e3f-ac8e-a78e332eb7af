<template>
  <div class="drawer-details_container" style="height: 100%;">
    <el-table v-loading="loading" :data="thesisList">
      <el-table-column label="论文名称" prop="title" min-width="380" />
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { getThesis } from "@/api/literature/literature.js";
export default {
  name: 'AtiDetails',
  props: {
    keywordInit: {
      default: '',
      type: String
    },
    keyword: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      thesisList: [],
      loading: false
    }
  },
  watch: {
    keywordInit(val) {
      this.getThesis()
    },
    keyword(val) {
      this.getThesis()
    },
  },
  created() {
    this.getThesis()
  },
  methods: {
    getThesis() {
      this.loading = true
      const paramData = {
        keywordInit: this.keywordInit,
        keyword: this.keyword,
      }
      getThesis(paramData).then(res => {
        this.thesisList = res.rows
        this.loading = false
      })
    },
    handleDownload(row) {
      this.download('test/kbfile/fileDownloadPost', {
        titleId: row.titleId
      }, `${row.title}.pdf`)
    },

  }
}
</script>
