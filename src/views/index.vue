<template>
  <div class="main">
    <div class="image-container" :style="{ 'background':' url('+backgroundImg+')'}">
      <!-- <img src="@/assets/logo/ShanCaiPAAPlogo.png" alt="山财公共管理logo"
        :style="{ transform: 'translate(-3em, 2.5em) scale(0.75) ' }" /> -->
    </div>
    <div class="swiper">
      <div class="swiper-wrapper">
        <swiper :options="swiperOptions">
          <swiper-slide v-for="(item, index) in swiperItems" :key="index" style="width:100%;height:70%;top:-100px">
            <img :src="item.image" alt="logo"
              style="width:115%;height:auto;transform:translate(-7em, 2em) scale(0.8)" />
            <!--            <div class="text-container">{{ item.text }}</div>-->
          </swiper-slide>
          <!--          <swiper-slide style="width:100%;height:100%">-->
          <!--            <img :src="flowChartImage" alt="logo"-->
          <!--              style="width:115%;height:46%;transform:translate(-7em, 2em) scale(0.8)" />-->
          <!--          </swiper-slide>-->
        </swiper>
        <div class="swiper-button-prev" v-if="true"></div>
        <div class="swiper-button-next" v-if="true"></div>
      </div>
      <div class="swiper-pagination"></div>
    </div>
  </div>
</template>

<script>
import { getAllRotationChart } from "@/api/rotationChart/rotationChart.js";
import { getLogos } from "@/api/logoConfiguration/logoConfiguration.js";
export default {
  data() {
    return {
      swiperItems: [],
      swiperOptions: {
        type: 'carousel',
        loop: true,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false, // 用户操作后是否继续自动播放
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true, // 是否允许点击分页器切换
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
      },
      // flowChartImage: flowChartImage,
      backgroundImg: '',
    };
  },
  created() {
    this.getList();
    this.getLogoImgs();
  },
  methods: {
    /** 查询列表 */
    getList() {
      getAllRotationChart().then((response) => {
        if (response.length > 0) {
          let imgArr = []
          this.swiperItems = response.map(item => {
            imgArr.push({ image: item })
          })
          this.swiperItems = imgArr
        }
      });
    },
    /** 查询logo */
    getLogoImgs() {
      getLogos(1).then(response => {
        this.backgroundImg = response[0]
      });
    },
  }
};
</script>

<style scoped>
.main {
  width: auto;
  height: 80em;
}

.swiper {
  position: absolute;
  width: 95%;
  height: 100%;
  top: 12em;
  right: 1.5em;
  padding-right: 2em;
  padding-left: 1em;
}

.image-container {
  position: absolute;
  width: 100%;
  height: 8em;
  /* background-color: rgb(64, 158, 255); */
  /* background: url("~@/assets/logo/title.jpg"); */
  background-size: 100% 100% !important;
}

.text-container {
  position: absolute;
  white-space: pre-line;
  padding: 20px;
  top: 6.5em;
  right: 1.1em;
  left: 41em;
  font-size: 1em;
  line-height: 1.5em;
  letter-spacing: 0.12em;
  text-indent: 2.25em;
}

.swiper-pagination {
  position: absolute;
  top: 38em;
  left: 50%;
}

.swiper-button-prev {
  position: absolute;
  top: 22em;
}

.swiper-button-next {
  position: absolute;
  top: 22em;
}
</style>
