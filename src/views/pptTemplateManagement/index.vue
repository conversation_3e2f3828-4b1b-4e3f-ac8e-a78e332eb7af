<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名" prop="zipFullName">
        <el-input
          v-model="queryParams.zipFullName"
          placeholder="压缩包文件名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="value" prop="tValue">
        <el-input
          v-model="queryParams.tValue"
          placeholder="压缩包文件名value"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="lable" prop="tLabel">
        <el-input
          v-model="queryParams.tLabel"
          placeholder="压缩包文件名lable"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--        >导出-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table highlight-selection-row v-loading="loading" :data="pptTemplateManagementList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
<!--      <el-table-column label="预览" align="center" prop="previewList" min-width="230">-->
<!--        <template v-slot="scope">-->
<!--          <div v-if="scope.row.previewList && scope.row.previewList.length > 0">-->
<!--            &lt;!&ndash; 如果是其他类型，遍历 previewList 展示图片 &ndash;&gt;-->
<!--            <el-carousel height="200px" :indicator-position="scope.row.previewList.length > 9 ? 'none' : ''">-->
<!--              <el-carousel-item v-for="(item,index) in scope.row.previewList" :key="index">-->
<!--                <el-image :src="item" fit="contain" :preview-src-list="scope.row.previewList">-->
<!--                </el-image>-->
<!--              </el-carousel-item>-->
<!--            </el-carousel>-->
<!--          </div>-->
<!--          <div v-else style="height: 70px; display: flex; justify-content: center; align-items: center;">-->
<!--            &lt;!&ndash; 如果没有图片数据，显示加载中，保持高度一致 &ndash;&gt;-->
<!--            <i v-if="scope.row.value!=='auto'" class="el-icon-loading" style="font-size: 24px;"></i>-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="名称" align="center" prop="zipFullName" min-width="100%">
        <template v-slot="scope">
          <el-popover trigger="hover" placement="left">
            <p>{{ scope.row.zipFullName }}</p>
            <div slot="reference" class="no-wrapper">
              <el-link type="primary" @click="handleClickScopeRowZipFullName(scope.row)">
                <span class="ellipsis">{{ scope.row.zipFullName }}</span>
              </el-link>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="文件名value" align="center" prop="tValue"/>
      <el-table-column label="文件名lable" align="center" prop="tLabel"/>
      <el-table-column label="序号" align="center" prop="sort" min-width="60" sortable>
        <template v-slot="scope">
          <el-link type="info">{{ scope.row.sort }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="路径地址" align="center" prop="path">
        <template v-slot="scope">
          <el-popover trigger="hover" placement="left">
            <p>{{ scope.row.path }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag class="clickable-tag"
                      ref="clickableTag"
                      effect="plain"
                      :type="scope.row.isDisabled ? 'info' : 'success'"
                      @click="handleClickScopeElTag(scope.row, scope.$index)"
              >{{ scope.row.path }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="解压地址" align="center" prop="unzipFolder">
        <template v-slot="scope">
          <el-popover trigger="hover" placement="left">
            <p>{{ scope.row.unzipFolder }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag class="clickable-tag"
                      ref="clickableTag"
                      effect="plain"
                      type="info"
              >{{ scope.row.unzipFolder }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 20, 30, 50,100]"
      @pagination="getList"
    />

    <!-- 添加或修改ppt模板管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="压缩包value" prop="tValue">
          <el-input v-model="form.tValue" placeholder="请输入模板压缩包文件名value" maxlength="30"
                    @input="handleInputTValue" :disabled="updateDisabled"/>
        </el-form-item>
        <el-form-item label="压缩包label" prop="tLabel">
          <el-input v-model="form.tLabel" placeholder="请输入模板压缩包文件名label" maxlength="20"
                    @input="handleInputTLabel" :disabled="updateDisabled"/>
        </el-form-item>
        <el-form-item label="压缩包文件名">
          <el-input :value="form.zipFullName" placeholder="格式：value-label" readonly :disabled="updateDisabled"/>
        </el-form-item>
        <el-row v-if="!updateDisabled">
          <el-col :span="24">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" :precision="2" :step="1.0" :max="999999"
                               style="width: 100%;"></el-input-number>
            </el-form-item>
          </el-col>
<!--          <el-col :span="10">-->
<!--            <el-form-item label="生成预览" prop="sort">-->
<!--              <el-switch-->
<!--                v-model="form.generatePreview"-->
<!--                active-text="是"-->
<!--                inactive-text="否">-->
<!--              </el-switch>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
        <el-row v-else>
          <el-col :span="24">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" :precision="2" :step="1.0" :max="999999"
                               style="width: 100%;"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="文件上传" prop="path" v-if="!updateDisabled">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" multiple
                     :data="uploadDataLabel" :headers="headers" :on-success="handleUploadSuccess"
                     :on-remove="handleRemoveData"
                     :file-list="fileList" accept=".zip" :limit="1" :on-preview="handlePreview">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">仅支持zip上传,仅能上传一个文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listPptTemplateManagement,
  getPptTemplateManagement,
  delPptTemplateManagement,
  addPptTemplateManagement,
  updatePptTemplateManagement, unzip
} from '@/api/pptTemplateManagement/pptTemplateManagement'
import {getToken} from '@/utils/auth'
import {downLoad} from '@/api/intellectSmartPpt/intellectSmartPpt'
import {Loading} from 'element-ui'
import drawingDefault from '@/utils/generator/drawingDefault'

export default {
  name: 'PptTemplateManagement',
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/file/upload', // 上传的图片服务器地址
      uploadDataLabel: {modeltype: 'pptTemplate'},
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ppt模板管理表格数据
      pptTemplateManagementList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tValue: null,
        tLabel: null,
        path: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tValue: [
          {required: true, message: '请输入模板压缩包文件名value', trigger: ['blur', 'change', 'input']},
          {pattern: /^[A-Za-z0-9]+$/, message: 'tValue只能包含英文字符', trigger: ['blur', 'change', 'input']}
        ],
        tLabel: [
          {required: true, message: '请输入模板压缩包文件名label', trigger: ['blur', 'change', 'input']},
          {
            pattern: /^[\u4e00-\u9fa5A-Za-z0-9]+$/,
            message: 'tLabel只能包含中文或英文字符',
            trigger: ['blur', 'change', 'input']
          }
        ],
        zipFullName: [{required: true, message: '文件名不能为空', trigger: ['blur', 'change', 'input']}
        ],
        path: [{required: true, message: '上传文件不能为空', trigger: ['blur', 'change', 'input']}
        ]
      },
      loadingInstance: null, // 用于保存加载框实例
      updateDisabled: false, // 修改的时候禁用组件的标志
      lastClickTime: 0 // 记录上次点击时间
    }
  },
  created() {
    this.getList()
  }
  ,
  computed: {}
  ,
  methods: {
    handleUploadSuccess(res, file) {
      console.log('upload')
      console.log(res)
      this.form.path = res.data.preview

      const fileWithoutExtension = res.data.name.replace(/\.[^/.]+$/, '')
      // 分割文件名
      const parts = fileWithoutExtension.split('-')
      if (parts.length >= 2) {
        if (this.form.tValue == null || this.form.tValue == '') {
          this.form.tValue = parts[0]
          // 手动触发输入事件
          this.handleInputTValue()
        }
        if (this.form.tLabel == null || this.form.tLabel == '') {
          this.form.tLabel = parts[1]
          // 手动触发输入事件
          this.handleInputTLabel()
        }
      }
    }
    ,
    handleRemoveData(file, fileList) {
      this.form.path = ''
    }
    ,
    /** 查询ppt模板管理列表 */
    getList() {
      this.loading = true
      listPptTemplateManagement(this.queryParams).then(response => {
        this.pptTemplateManagementList = response.rows
        this.total = response.total
        this.loading = false
      })
    }
    ,
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    }
    ,
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tValue: null,
        tLabel: null,
        path: null,
        zipFullName: null,
        unzipFolder: null,
        sort: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        generatePreview: false
      }
      this.resetForm('form')
    }
    ,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    }
    ,
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
    ,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    }
    ,
    /** 新增按钮操作 */
    handleAdd() {
      this.updateDisabled = false
      this.reset()
      this.fileList = []
      this.open = true
      this.title = '添加PPT模板管理'
    }
    ,
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.updateDisabled = true
      this.reset()
      this.fileList = []
      const id = row.id || this.ids
      getPptTemplateManagement(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改PPT模板管理'
      })
    }
    ,
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.openFullScreen()
          if (this.form.id != null) {
            updatePptTemplateManagement(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
              this.closeFullScreen()
            }).catch(() => {
              this.closeFullScreen()
            })
          } else {
            addPptTemplateManagement(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
              this.closeFullScreen()
            }).catch(() => {
              this.closeFullScreen()
            })
          }
        }
      })
    }
    ,
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除ppt模板管理编号为"' + ids + '"的数据项？').then(function () {
        return delPptTemplateManagement(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    }
    ,
    /** 导出按钮操作 */
    handleExport() {
      this.download('ppt/pptTemplateManagement/export', {
        ...this.queryParams
      }, `pptTemplateManagement_${new Date().getTime()}.xlsx`)
    }
    ,

    /**
     * 输入 tValue 时，拼接新的 zipFullName
     */
    handleInputTValue() {
      // 当输入 tValue 时，拼接新的 zipFullName
      this.form.zipFullName = `${this.form.tValue}-${this.form.tLabel || ''}`.trim()
    }
    ,
    /**
     * 输入 tLabel 时，拼接新的 zipFullName
     */
    handleInputTLabel() {
      // 当输入 tLabel 时，拼接新的 zipFullName
      this.form.zipFullName = `${this.form.tValue}-${this.form.tLabel}`.trim()
    }
    ,
    /**
     * 处理点击的行
     * @param row
     */
    handleClickScopeRowZipFullName(row) {
      // 路由到模板详细页面
      this.$router.push({
        path: '/system/pptTemplateManagementDetail',
        query: {
          templateManagementId: row.id
        }
      })
    }
    ,

    /**
     * 事件触发防抖 判断点击是否在指定时间内
     * @param limit 限制时间（毫秒），默认为 1500 毫秒（1.5 秒）
     * @returns {boolean} true 可以继续执行，false 不能继续执行
     */
    isClickWithinLimit(limit = 1500) {
      const currentTime = new Date().getTime()
      const timeDiff = currentTime - this.lastClickTime

      // 如果点击时间差小于指定的限制时间（默认1秒）
      if (timeDiff < limit) {
        this.$notify({
          title: '提示',
          message: '点击频繁，请稍等！',
          type: 'warning',
          duration: 1500
        })
        return false // 在时间限制内，禁止继续执行
      } else {
        // 更新上次点击的时间戳
        this.lastClickTime = currentTime // 这里在用户成功点击后更新
        return true // 超过时间限制，可以继续执行
      }
    }
    ,
    /**
     * 点击路径地址
     * @param row 行
     * @param index 索引
     */
    handleClickScopeElTag(row, index) {
      console.log(row)
      console.log(index)
      if (!this.isClickWithinLimit()) {
        console.log('触发防抖')
        return // 如果不能继续执行，则返回
      }
      this.$set(row, 'isDisabled', true) // 禁用标签
      this.$notify({
        title: '开始下载',
        message: '正在下载中',
        type: 'success',
        duration: 0
      })

      // 请求参数
      const data = {
        'filePath': row.path,
        'fileName': row.zipFullName
      }

      // 调用方法下载
      downLoad(data).then(res => {
        if (res) {
          console.log(res)
          const link = document.createElement('a')
          link.download = row.zipFullName + '.zip'
          link.style.display = 'none'
          const blob = new Blob([res])
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          setTimeout(() => {
            link.click()
            document.body.removeChild(link)
            this.$notify.closeAll()
            this.$set(row, 'isDisabled', false) // 恢复标签
          }, 100)
        }
      }).catch(error => {
        this.$notify({
          title: '下载失败',
          message: '文件下载时出现错误，请稍后再试。',
          type: 'error',
          duration: 3000
        })
        setTimeout(() => {
          this.$notify.closeAll()
          this.$set(row, 'isDisabled', false) // 恢复标签
        }, 3000)
      }).finally(
      )
    },

    // 对上传的压缩包进行解压
    handleUnzip() {
      unzip(this.form).then(res => {
        this.$message.success('解压完成')
      })
    }
    ,

    // 打开全屏加载框
    openFullScreen() {
      this.loadingInstance = this.$loading({
        lock: true,
        text: '任务处理中,请耐心等待',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)' // 设置为白色背景，透明度为0.7
      })
    }
    ,
    // 关闭全屏加载框
    closeFullScreen() {
      if (this.loadingInstance) {
        this.loadingInstance.close() // 关闭加载框
        this.loadingInstance = null // 清空加载实例
      }
    }
    ,

    handlePreview(data) {
      console.log(data)
    }
  }
}
</script>

<style scoped>
.clickable-tag {
  cursor: pointer; /* 鼠标悬浮时显示小手 */
}

.disabled-tag {
  background-color: #d3d3d3 !important; /* 禁用状态为灰色背景 */
  cursor: not-allowed; /* 禁用状态鼠标样式 */
}

.no-wrapper {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>
