<template>
  <div class="app-container ck-container">
    <!-- 左侧栏 -->
    <div class="sidebar" @mouseenter="showText = true" @mouseleave="showText = false">
      <!-- 展开时显示的盒子列 -->
      <div class="box-list" v-if="showText">
        <div class="box" v-for="(box, index) in pptImageThumbnailList" :key="index" :class="{'active': box.isActive}"
          @click="selectBox(index)"
          @mouseenter="hoverBox(index)"
          @mouseleave="removeHover(index)">
          <img :src="box.image" alt="Box Image" />
          <span style="margin-top: 10px;">{{ index+1 }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧栏 -->
    <div class="sidebarRight" @click.stop="toggleRightSidebar">
      <div class="rightBox-list" v-show="showRightSidebar">
        <el-button type="primary" id="startButton" class="start-button"
                   :title="hasStarted ? '结束' : '开始'" @click.stop="start" :disabled="dialogueFlag">
          <img class="action-img" :src="require(`@/assets/icons/start.png`)" alt="">
        </el-button>

        <el-button type="primary" id="pauseButton" class="pause-button" :title="pauseFlag ? '继续' : '暂停'"
                   @click.stop="pauseAudio"
                   v-show="hasStarted" :disabled="dialogueFlag">
          <img class="action-img" :src="pauseFlag ? require(`@/assets/icons/video-pause.png`) : require(`@/assets/icons/video-play.png`)" alt="">
        </el-button>
        <el-button type="success" id="dialogueButton" class="dialogue-button"
                   @click.stop="startDialogue"
                   :title="dialogueFlag ? '结束问答' : '课堂问答'">
          <img class="action-img" :src="require(`@/assets/icons/QA.png`)" alt="">
        </el-button>
        <el-button
          class="dialogue-button"
          :title="showHuman ? '隐藏' : '展示'"
          @click.stop="showHuman = !showHuman">
          <img class="action-img" :src="require(`@/assets/icons/digitalHuman.png`)" alt="">
        </el-button>
      </div>
    </div>

    <!-- 数字人展示 -->
    <div class="imgboxHuman" v-show="showHuman">
      <img class="img" :src="defaultImage" alt="default image" v-show="!imgListData || imgListData.length === 0"/>
      <img class="img" :src="item || defaultImage" alt="default image" v-for="(item, index) in imgListData" :key="index"
           v-show="v===index?true:false">
    </div>
    <div class="PPTcontent">
      <div v-if="showPPTStyle==='img'" class="pptbox1" id="pptbox">
        <div class="pptImgDataBox1">
          <img :src="pptImgData" alt=""/>
        </div>

        <div class="subtitlesStyles1">
          <div class="subtitle-content1">
            {{ subtitles }}
          </div>
        </div>
      </div>

      <div v-if="showPPTStyle==='iframe'" class="pptbox" id="pptbox">
        <iframe :src="src" ref="iframe" class="ppt-iframe"></iframe>
        <div class="subtitlesStyles">
          <div class="subtitle-content">
            {{ subtitles }}
          </div>
        </div>
      </div>
    </div>
    <!-- 课堂问答 -->
    <el-drawer style="font-size: 16px;" size="45%"
               :visible.sync="drawerRigth" direction="ltr" :before-close="handleCloseRightDrawer" :modal="false">
      <div class="app-container ck-container2">

        <div class="right-container">
          <div class="answer-box" id="answer-box" ref="scrollContainer">
            <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
                    justify="center">
              <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;"
                   alt=""/>
              <div v-if="item.issue == 'assistant'" style="width:100%;">
                <div
                  style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
                  {{ item.content }}
                  <img v-if="item.imageUrl" :src="item.imageUrl" alt="Dialogue Image"
                       style="max-width: 100%; height: auto;">
                  <div style="width: 100%">
                    <el-button style="float: right;margin: 5px;" size="mini" round
                    @click="handleAudioControl()">停止播放
                  </el-button>
                  <el-button style="float: right;margin: 5px;" size="mini" round
                  @click="synthesizeVoice(item.content)">开始播放
                  </el-button>
                    <el-button style="float: right;margin: 5px;;padding-top:5px;padding-bottom: 5px;" size="mini" round
                               @click="likeOrStomp(item,index,2)">
                      <svg-icon :icon-class="item.likeStomp=='2'?'bad':'thumbs-down'" class-name="card-panel-icon"/>
                    </el-button>
                    <el-button style="float: right;margin: 5px;padding-top:5px;padding-bottom: 5px;" size="mini" round
                               @click="likeOrStomp(item,index,1)">
                      <svg-icon :icon-class="item.likeStomp=='1'?'good':'thumbs-up'" class-name="card-panel-icon"/>
                    </el-button>
                  </div>
                </div>

                <div style="position:absolute; bottom:-23px; left:5px;"> <!-- 调整bottom值以靠近内容框 -->
                  <!-- 重新生成按钮 -->
                  <el-button v-if="!loadSendBtn && index == dialogueList.length-1" size="mini" type="text"
                             class="copy-button" @click="handleRegen(index)"
                             style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 40px;">
                    重新生成
                  </el-button>
                </div>


              </div>
              <div v-if="item.issue == 'user'" style="width:100%;">
                <div
                  style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
                  {{ item.content }}
                </div>
              </div>
              <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;"/>
            </el-row>
          </div>

          <div class="ask-box">
            <div
              style="border:1px solid #a4a4a4;padding: 10px 10px;background-color: #fff;border-radius: 12px ;">
              <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'"
                          placement="top">
              <el-button style="margin: 5px;" size="mini" round
                :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'"
                @click="voiceASRType === 1 ? baiduASR() : translation()">
                {{translationFlag ? '停止' : '语音'}}
              </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="light" :content="playFlagRight ?  '点击停止播放': '点击播放'"
                          placement="top">
                <el-button style="margin: 5px;" size="mini" round @click="autoPlayRight">
                  <svg-icon :icon-class="playFlagRight ?  'voice': 'voiceClose'"/>
                  {{ playFlagRight ? ' 停播' : ' 播放' }}
                </el-button>
              </el-tooltip>
              <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
                        show-word-limit @keydown.native="handleKeyCode($event)" @blur="saveEditedContent"
                        placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行"/>
            </div>
            <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"
                       @click="ask" :loading="loadSendBtn"/>
          </div>
        </div>
        <audio :src="computedAudioSrcRigth" controls :playsinline="true" style="display: none;" ref="audioRigth"
               @ended="continuePlayRight"></audio>
      </div>
    </el-drawer>


    <el-dialog title="课堂小测试"
               style="font-size: 35px; margin-top: 10vh;" size="55%"
               :visible.sync="drawer"
               :show-close="false"
               :direction="this.direction"
               :wrapperClosable="false"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               :before-close="handleBeforeClose"
    >
      <div class="demo-drawer__content">
        <!-- 显示错误提示 -->
        <el-alert v-if="questionErrorMsg" :title="questionErrorMsg" type="error" show-icon style="margin-bottom: 20px;"></el-alert>
        <el-form v-model="examination">

          <el-form-item>
            <span style="font-size: 24px; margin-left: 40px;">{{ checkShow ? "[多选]" : "[单选]" }}  {{ examination.question }}</span>
          </el-form-item>

          <el-form-item>
            <el-radio-group
              v-show="this.radioShow"
              v-model="examination.checkList"
              class="radio-group-horizontal"
            >
              <el-radio v-for="(item, index) in examination.options" :key="index" :label="item.id"
                        class="radio-item">
                {{ item.id }} {{ item.value }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-checkbox-group
              v-show="this.checkShow"
              v-model="examination.checkList"
              class="radio-group-horizontal">
              <el-checkbox v-for="item in examination.options" :key="item.id" :label="item.id"
                           class="radio-item">
                {{ item.id }} {{ item.value }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

        </el-form>
        <div class="demo-drawer__footer" style="margin-right: 10px;">
          <el-button type="primary" @click="getAnswerCorrectness()" >确定</el-button>
        </div>
      </div>
    </el-dialog>


    <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio"
           @ended="continuePlayIsPause"></audio>
    <!-- 问答音频 -->
    <!-- <audio :src="computedAudioSrc2" controls :playsinline="true" style="display: none;" ref="audio2" @ended="continuePlay2"></audio> -->
  </div>

</template>

<script>
import '../../../public/ppt/css/pptxjs.css'
import '../../../public/ppt/css/nv.d3.min.css'
import thesisDetails from './thesisDetails.vue'

const ttsRecorder = new TtsRecorder()
const iatRecorder = new IatRecorder('zh_cn', 'mandarin')
import asks from '@/assets/logo/asks.png'
import user from '@/assets/logo/user.png'
import TtsRecorder from '@/assets/js/tts_xunfei/audio.js'
import IatRecorder from '@/assets/js/IatRecorder.js'
import { AudioRecorder } from '@/assets/js/BaiDuASR.js'
import { getToken } from '@/utils/auth'
import speechRecognitionService from '@/utils/speechRecognitionService';
import SpeechWakeUp from '@/utils/voiceweakup/SpeechWakeUp.js';
import { eventBus } from '@/utils/eventBus';
import {
  getDialogueList,
  getDialogue,
  addDialogue,
  updateDialogue,
  updateDialoguelikeStomp,
  updateDiogloe,
  getpptInfo,
  getDigitalHumanSpeech,
  updateProgress,
  getppt,
  getRelativePath,
  getBaiDuToken,
  getInitialPlatById,
  getProblem,
  checkAnwer,
  getImageThumbnail,
  getId
} from '@/api/explorationCenter/experience.js'
import {
  splitTextByPunctuation
} from '@/assets/js/common.js'
import {
  getUserVoiceRole,
  DBTTS,
  getSystemTTSChoose,
} from "@/api/system/voiceRole.js";
import { mapState, mapActions } from 'vuex';
import axios from 'axios'
import qs from 'qs'
import Cookies from 'js-cookie'

export default {
  name: 'wisdomSchools',
  components: { thesisDetails },
  dicts: ['call_type','system_websocket_param','user_voiceasr_choose','user_voice_choose','sys_class_wakeup',],
  data() {
    return {
      v: 0,
      pptIndex: 1,
      currentMotionIndex: 0,
      curMotionMaxIndex: 0,
      imglist: [],
      imgListData: [],
      defaultImage: '', /// 数字人初始形象
      pptContent: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      src: '/ppt/pptx.html',
      iframeWin: {},
      pptPageDiv: '',
      //是否正在播放语音
      isplaying: false,
      currentPageContent: '',
      //是否开始播放ppt
      hasStarted: false,
      //ppt是否播放完成
      completeFlag: true,
      //当前ppt页是否正在播放
      curPageIsPlaying: false,
      intervalId_img: null,
      intervalId_ppt: null,
      intervalId_text: null,
      //动作播放速度
      interval_time: 0,
      //无动作说话循环次数
      nomotion_order: 0,
      //无动作说话最大循环次数
      nomotion_max_order: 6,
      //当前动作是否播放完成
      curMotionIsDone: false,
      asks: asks,
      user: user,
      invocation: 'model',
      hisList: [],
      menuRouting: '',
      dialogueResult: { content: '正在回答，请稍等···' },
      dialogueNum: true,
      loadSendBtn: false,
      content: '',
      id: '',
      routePath: this.$route.path,
      playFlag: true,
      translationFlag: false,
      dialogueFlag: false,
      contentArray: [],
      isLastSegment: false,
      pauseFlag: false,
      curSegmentIndex: 0,
      pptInfo: {},
      ppts: '',
      pptSingleImg: '',
      pptImgData: '',
      showPPTStyle: 'img', // img | iframe
      videoPageIndexesArray: [],
      goPages: '',
      pptPath: '',
      presentationAllpage: 0,//ppt总页数
      currentPage: 0,//ppt看过的最大页数
      getRelativePathParam: {
        figureId: this.$route.query.figureId,
        size: '1215 x 2160',
        // beforeAnalysisContent: ''
        // 课件id + 页码
        presentationId: this.$route.query.presentationId, // 课件id
        pageIndex: '' // 页码
      },
      getImageThumbnailParam: {
        presentationId: this.$route.query.presentationId
      },
      showText: false, // 左侧抽屉
      showRightSidebar: true, // 右侧抽屉
      // 查询初始形象参数
      initialPlatParam: {
        figureId: this.$route.query.figureId
      },
      arr: [], // 接收数组数据
      pptContentAction: [], // ppt动作
      pathArr: [], // ppt动作路径
      interval: 155, // 图片切换的时间间隔（毫秒）
      token: '',
      per: '1',
      speed: 5,
      pitch: 5,
      volume: 5,  // 音量
      pid: '',
      AsrintervalId: null, // 语音计时器
      audioArr: [],
      currentIndex: 0,
      isPlayAudio: false,
      audioArr2: [], // 问答音频数组
      currentvoiceIndex: 0, // 当前问答播放的音频索引
      contentArray2: [],  // 问答拆分文字数组
      initPath: '',
      show: false,
      detailsDrawer: false, // 提问
      questionErrorMsg: '', // 存储错误信息
      // keywordInit: '',
      // keyword: '',
      drawer: false, // 底部抽屉
      drawerRigth: false,
      direction: 'btt',
      examination: {
        problemId: '',
        question: '',
        // 选项
        options: [

          // { id: "A", value: "这是一个正确答案" },
          // { id: "B", value: "这是一个非常只能够却" },
          // { id: "C", value: "这公司汉斯哦" },
          // { id: "D", value: "这是私事那句撒" },
        ],
        checkList: [], // 选中的选项
        answer: ''
      },
      attemptCount: 0, // 错误次数计数器
      maxAttempts: 3, // 最大允许错误次数
      // 校验答案参数
      answerParam: {
        problemId: '',
        subAnwer: ''
      },
      // 获取题参数
      getProblemParam: {
        index: '',
        presentationId: ''
      },
      // 当前页是否有题
      hasProblem: false,
      radioShow: false,
      checkShow: false,
      // 答题是否完成
      answerIsDone: true,
      subtitles: '',// 字幕
      subtitlesArr: [], // 数组
      digitalHumanSaveTableName: '', // 数字人前端存储表名
      imgIdlist: [], // 数字人图片id数组
      subtitlesIndex: 0,
      sentenceAction: 1,
      showHuman: true,
      isProcessing: false, // 控制当前语音任务是否正在处理
      stopv: true, // 停止动作
      // 课堂问答需要使用到的相关参数============================
      dialogueList: [], // 对话列表
      playFlagRight: false, // 问答是否自动播放
      currentIndexRigth: 0, // 当前播放的索引
      pptImageThumbnail: [], // 缩略图
      pptImageThumbnailList: [],
      baiduASRParam:{
        appId: '',
        appKey: '',
        dev_pid: '',
        name: this.$route.path
      },
      pauseTimeout: null,
      intervalId: null, // 定时器 ID
      audioArrRight: [], // 存放所有音频的数组
      isPlayAudioRigth: false,//正在播放
      progress: '', // 学习进度
      progressp: '', // 学习进度
      iconLikeA: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18898 22.1733C4.08737 21.0047 5.00852 20 6.18146 20H10C11.1046 20 12 20.8954 12 22V41C12 42.1046 11.1046 43 10 43H7.83363C6.79622 43 5.93102 42.2068 5.84115 41.1733L4.18898 22.1733Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 21.3745C18 20.5388 18.5194 19.7908 19.2753 19.4345C20.9238 18.6574 23.7329 17.0938 25 14.9805C26.6331 12.2569 26.9411 7.33595 26.9912 6.20878C26.9982 6.05099 26.9937 5.89301 27.0154 5.73656C27.2861 3.78446 31.0543 6.06492 32.5 8.47612C33.2846 9.78471 33.3852 11.504 33.3027 12.8463C33.2144 14.2825 32.7933 15.6699 32.3802 17.0483L31.5 19.9845H42.3569C43.6832 19.9845 44.6421 21.2518 44.2816 22.5281L38.9113 41.5436C38.668 42.4051 37.8818 43 36.9866 43H20C18.8954 43 18 42.1046 18 41V21.3745Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconStompA: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18051 26.8339C4.08334 27.9999 5.00352 29 6.1736 29H10C11.1046 29 12 28.1046 12 27V7C12 5.89543 11.1046 5 10 5H7.84027C6.80009 5 5.93356 5.79733 5.84717 6.83391L4.18051 26.8339Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 26.6255C18 27.4612 18.5194 28.2092 19.2753 28.5655C20.9238 29.3426 23.7329 30.9062 25 33.0195C26.6331 35.7431 26.9411 40.664 26.9912 41.7912C26.9982 41.949 26.9937 42.107 27.0154 42.2634C27.2861 44.2155 31.0543 41.9351 32.5 39.5239C33.2846 38.2153 33.3852 36.496 33.3027 35.1537C33.2144 33.7175 32.7933 32.3301 32.3802 30.9517L31.5 28.0155H42.3569C43.6832 28.0155 44.6421 26.7482 44.2816 25.4719L38.9113 6.45642C38.668 5.5949 37.8818 5 36.9866 5H20C18.8954 5 18 5.89543 18 7V26.6255Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconLikeB: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18898 22.1733C4.08737 21.0047 5.00852 20 6.18146 20H10C11.1046 20 12 20.8954 12 22V41C12 42.1046 11.1046 43 10 43H7.83363C6.79622 43 5.93102 42.2068 5.84115 41.1733L4.18898 22.1733Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 21.3745C18 20.5388 18.5194 19.7908 19.2753 19.4345C20.9238 18.6574 23.7329 17.0938 25 14.9805C26.6331 12.2569 26.9411 7.33595 26.9912 6.20878C26.9982 6.05099 26.9937 5.89301 27.0154 5.73656C27.2861 3.78446 31.0543 6.06492 32.5 8.47612C33.2846 9.78471 33.3852 11.504 33.3027 12.8463C33.2144 14.2825 32.7933 15.6699 32.3802 17.0483L31.5 19.9845H42.3569C43.6832 19.9845 44.6421 21.2518 44.2816 22.5281L38.9113 41.5436C38.668 42.4051 37.8818 43 36.9866 43H20C18.8954 43 18 42.1046 18 41V21.3745Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconStompB: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18051 26.8339C4.08334 27.9999 5.00352 29 6.1736 29H10C11.1046 29 12 28.1046 12 27V7C12 5.89543 11.1046 5 10 5H7.84027C6.80009 5 5.93356 5.79733 5.84717 6.83391L4.18051 26.8339Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 26.6255C18 27.4612 18.5194 28.2092 19.2753 28.5655C20.9238 29.3426 23.7329 30.9062 25 33.0195C26.6331 35.7431 26.9411 40.664 26.9912 41.7912C26.9982 41.949 26.9937 42.107 27.0154 42.2634C27.2861 44.2155 31.0543 41.9351 32.5 39.5239C33.2846 38.2153 33.3852 36.496 33.3027 35.1537C33.2144 33.7175 32.7933 32.3301 32.3802 30.9517L31.5 28.0155H42.3569C43.6832 28.0155 44.6421 26.7482 44.2816 25.4719L38.9113 6.45642C38.668 5.5949 37.8818 5 36.9866 5H20C18.8954 5 18 5.89543 18 7V26.6255Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      contenMark:0,
      finalText: "", // 累加的最终结果
      timer: null, // 定时器
      voiceASRType: 1, // 识别服务商
      voiceType: 1, // 语音合成服务商
      showScoreDialog: false, // 控制得分弹窗的显示
      scoreDialogShown: false, // 跟踪得分弹窗是否已显示过
      userVoiceChoose: "",// 语音合成服务商
      isAudioLoading: false,
      isUnsupported: false,
      selectedLanguage: 'zh-CN',
      autoRestart: true,
      
      audioContext: null,        // 音频上下文
      audioAnalyser: null,       // 音频分析器
      audioDataArray: null,      // 音频数据数组
      silenceTimer: null,        // 静音计时器
      silenceThreshold: 10,      // 静音阈值（0-255之间的值）
      silenceDuration: 3000,     // 静音持续时间（毫秒）
      isMonitoringVolume: false, // 是否正在监控音量
      lastVolumeTime: 0,         // 上次检测到声音的时间
      autoSendEnabled: true      // 是否启用自动发送
    }
  },

  created() {
    // 获取数字初始形象
    this.getInitialPlat()
    this.getDialogueId()
    this.getpptInfo()
    this.getDigitalHumanSpeech()
    this.getDialogueList()
    this.getPlatTableNameById() // 获取数字人存储在那个表
    // this.getBaiDuToken();
    this.getVoiceRole()
    this.getToken()
    this.initRecorder();
    this.getSystemTTSChoose();
    // 获取ppt缩略图
    setTimeout(() => {
      // 这里是需要延迟执行的代码
      this.getPPTThumbnail()
    }, 500) // 1000毫秒后执行，即1秒
    // 获取题目
    // this.getProblem()
    // ppt定时切换的定时器
    this.intervalId_ppt = setInterval(() => {
      //执行条件：已开始播放 且 当前没有播放内容 且 ppt未播放完成 题目回答完
      // console.log(this.hasStarted + "|" + !this.curPageIsPlaying + "|" + !this.completeFlag
      //   + "|" + !this.pauseFlag)
      if (this.hasStarted && !this.curPageIsPlaying && !this.completeFlag
        && !this.pauseFlag && this.answerIsDone
      ) {
        // 当前页有
        // console.log(this.hasProblem)
        if (this.hasProblem) {
          this.answerIsDone = false
          this.drawer = true
          // 如果当前页面已达到最大错误次数，重新播放当前页
          if (this.attemptCount >= this.maxAttempts) {
            this.attemptCount = 0 // 重置错误次数
            this.drawer = false
            this.sendIframeWinpMessage() // 重新播放当前页
          }
        } else {
          // 当前页没有题
          if (this.pptIndex < this.pptInfo.presentationAllpage) {
            //console.log("pptIndex++")
            this.pptIndex++
            // this.pptIndex++;
            this.sendIframeWinpMessage()
            // 获取题目
            this.getProblem()
          }

          const _this = this

          setTimeout(() => {
            if (_this.pptIndex >= _this.pptInfo.presentationAllpage) {
              _this.completeFlag = true
              _this.hasStarted = false
              _this.pptIndex = _this.pptInfo.presentationAllpage
              // _this.v = 0;
            }
          }, 5000)
        }
      }
    }, 2000)

    //  获取记录
    this.handleReviewById(this.id)
  },
  mounted() {
    if (this.showPPTStyle === 'iframe') {
      this.$nextTick(() => {
        this.initializeIframe()
      })
    }
    // 添加 message 事件监听器
    window.addEventListener('message', this.handleVideoPlayMessage, false)
    setTimeout(() => {
      this.getVoiceASRChoose();
      // 注册语音命令
      this.registerSpeechCommands();
    }, 1500);
    
    // 初始化右侧栏样式
    if (this.showRightSidebar) {
      const sidebarRight = document.querySelector('.sidebarRight');
      if (sidebarRight) {
        sidebarRight.classList.add('expanded');
      }
    }
  },
  beforeDestroy() {
    this.updateDialogueId()
    //关闭页面前停止
    this.stop()
    this.clearAudio()
    // 清除定时器
    if (this.intervalId_img) {
      clearInterval(this.intervalId_img)
    }
    if (this.intervalId_ppt) {
      clearInterval(this.intervalId_ppt)
    }
    if (this.intervalId_text) {
      clearInterval(this.intervalId_text)
    }
    // 清除定时器，防止内存泄漏
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
    // 组件销毁时移除监听器
    window.removeEventListener('message', this.handleVideoPlayMessage, false)
  },
  computed: {
    computedAudioSrc() {
      return this.audioArr[this.currentIndex]
    },
    computedAudioSrcRigth() {
      return this.audioArrRight[this.currentIndexRigth]
    },
    // 用于将vuex存储中的状态 (state) 映射到组件的计算属性中
    // speechRecognition 表示vuex中的模块名称 数组是映射的状态名称
    ...mapState('speechRecognition', [
      'isListening',
      'transcript',
      'interimTranscript'
    ])
  },
  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.curPageIsPlaying) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play()
            // this.isplaying = true;
          },
          { once: true }
        )
      }
    },
    computedAudioSrcRigth(newSrc) {
      if (newSrc && this.isPlayAudioRigth && this.playFlagRight) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audioRigth.play()
            // this.isplaying = true;
          },
          { once: true }
        )
      }
    },
    // 监听show属性，控制抽屉的显示或隐藏
    show(newValue) {
      if (newValue) {
        this.detailsDrawer = true
        this.dialogueFlag = true
      } else {
        this.detailsDrawer = false
        this.dialogueFlag = false
      }
    },
    showRightSidebar(newValue) {
      // 获取右侧栏DOM元素
      const sidebarRight = document.querySelector('.sidebarRight');
      if (sidebarRight) {
        if (newValue) {
          sidebarRight.classList.add('expanded');
        } else {
          sidebarRight.classList.remove('expanded');
        }
      }
    },
    dialogueList: {
      handler(newList) {
        this.scrollToBottom() // 每当dialogueList更新时，滚动到底部
      },
      deep: true
    },
    showPPTStyle(newVal, oldVal) {
      if (newVal === 'iframe') {
        this.$nextTick(() => {
          this.initializeIframe() // 初始化 iframe 的操作
        })
      } else if (oldVal === 'iframe') {
        // this.cleanupIframe() // 清理 iframe 的操作
      }
    }
  },

  methods: {
    ...mapActions('speechRecognition', [
      'registerCommands',
      'clearTranscript'
    ]),

    // 语音唤醒
    registerSpeechCommands() {
      if (!this.dict || !this.dict.type || !this.dict.type.sys_class_wakeup) {
        console.warn('字典中未找到唤醒词配置');
        return;
      }
      console.log('从字典中加载唤醒词...');
      // 创建命令对象
      const wakeupCommands = {};
      // 遍历字典中的唤醒词
      this.dict.type.sys_class_wakeup.forEach(item => {
        // 将每个唤醒词注册为命令
        wakeupCommands[item.label] = () => {
          console.log(`检测到唤醒词: ${item.label}`);
          // 根据字典中的值执行不同的方法
          if (item.value === 'pauseAudio') {
            // 暂停 继续
            this.pauseAudio();
          } else if (item.value === 'startDialogue') {
            this.startDialogue();
            if(this.voiceASRType === 1) {
              this.baiduASR();
            } else {
              this.translation();
            }
            // 开始监控麦克风音量
            if (this.autoSendEnabled) {
              this.startVolumeMonitoring();
            }
          }else if(item.value === 'stopDialogue'){
            this.startDialogue();
          }else if (item.value === 'true'){
            this.showHuman = true;
          }else if (item.value === 'false'){
            this.showHuman = false;
          }else if (item.value === 'init'){
            if(this.drawerRigth && this.dialogueFlag){
              if(this.voiceASRType === 1) {
                this.baiduASR();
              } else {
                this.translation();
              }
              // 开始监控麦克风音量
              if (this.autoSendEnabled) {
                this.startVolumeMonitoring();
              }
            }
          }
        };
      });
      
      // 注册所有唤醒词命令
      if (Object.keys(wakeupCommands).length > 0) {
        this.registerCommands({
          context: this.$route.name || 'explorationCenter',
          commands: wakeupCommands
        });
        
        console.log(`成功注册 ${Object.keys(wakeupCommands).length} 个唤醒词`);
      } else {
        console.warn('未找到有效的唤醒词配置');
      }
    },


    // 获取系统语音合成选择
    getSystemTTSChoose(){
      getSystemTTSChoose().then(res => {
        this.userVoiceChoose = res.msg;
      })

    },
    // 获取语音识别调用
    getVoiceASRChoose() {
      // 通用函数：根据字典值设置类型
      const setTypeFromDict = (dict, key) => {
        dict.forEach(item => {
          if (item.label === "baidu") {
            this[key] = 1;
          } else if (item.label === "xfSpark") {
            this[key] = 0;
          }
        });
      };
      // 设置 voiceASRType
      setTypeFromDict(this.dict.type.user_voiceasr_choose, "voiceASRType");
      // 设置 voiceType
      setTypeFromDict(this.dict.type.user_voice_choose, "voiceType");
    },
    // 监听来自iframe的消息 是否ppt中的视频进行了播放
    handleVideoPlayMessage(event) {
      // 这里只是简单示例，确保是来自合法来源（可选）
      // if (event.origin !== "子页面的合法来源") return;

      const data = event.data
      if (data.type === 'video-play') {
        console.log('ppt中的视频开始播放:', data.videoSrc)
        // 当前智慧学堂的播放暂停
        this.pauseFlag = true
        this.$refs.audio.pause()
      } else if (data.type === 'video-paused') {
        console.log('ppt中的视频暂停:', data.videoSrc)
      }
    },
    // 获取ppt缩略图
    getPPTThumbnail() {
      let id = Number(this.getImageThumbnailParam.presentationId)
      getImageThumbnail(id).then(res => {
        if (res.code == 200) {
          // console.log(res.data)
          res.data.forEach(item => {
            const newData = {
              image: item,
              isActive: false
            }
            this.pptImageThumbnail.push(newData)
          })
          if (this.progress < 15) {
            console.log(this.progress)
            // 开始定时移动数据
            this.startMovingThumbnails()
          } else {
            this.pptImageThumbnailList = this.pptImageThumbnail
          }
        }
      })
    },

    startMovingThumbnails() {
      this.intervalId = setInterval(() => {
        if (this.pptImageThumbnail.length > 0) {
          // 移动一个数据
          const nextItem = this.pptImageThumbnail.shift()
          this.pptImageThumbnailList.push(nextItem)
        } else {
          // 停止定时器
          clearInterval(this.intervalId)
          this.intervalId = null
        }
      }, 5000) // 每 5 秒执行一次
    },
    // 重新生成
    handleRegen(index) {
      this.stopAudioRight()
      this.loadSendBtn = true
      let content = this.dialogueList[index - 1].content
      this.dialogueList.push({ content: content, issue: 'user' })
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: 'assistant' })
      this.getHeight()
      const param = {
        invocation: this.invocation,
        //promptId: this.promptId,
        content: this.dialogueList[index - 1].content,
        id: this.id,
        language: Cookies.get('voiceType'),
        menuRouting: this.getSecondSlash(this.$route.path) + '/'
      }

      this.content = ''
      //
      this.updateDialogue2(param)
        .catch(error => console.error('Error:', error))
    },

    // 点赞/点踩
    likeOrStomp(item, index, type) {
      const param = {
        id: item.id,
        likeStomp: type
      }
      updateDialoguelikeStomp(param).then(res => {
        if (res.code == 200) {
          this.dialogueList[index].likeStomp = type
        }
      })
    },

    updateDialoguelike(id, likeStomp) {
      const param = {
        id: id,
        likeStomp: likeStomp == 1 ? 0 : 1
      }
      updateDialoguelikeStomp(param).then((res) => {
        if (res.code === 200) {
          this.handleReview(this.id)
        }
      })
    },

    updateDialogueStomp(id, likeStomp) {
      const param = {
        id: id,
        likeStomp: likeStomp == 2 ? 0 : 2
      }
      updateDialoguelikeStomp(param).then((res) => {
        if (res.code === 200) {
          this.handleReview(this.id)
        }
      })
    },

    handleReview(id) {
      getDialogue(id).then((res) => {
        this.dialogueList = res.data.dialogueDetailsList
        for (let i = 0; i < this.dialogueList.length; i++) {
          if (this.dialogueList[i].processing != null) {
            const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`
            this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrl: imageData })
          }
        }
        this.dialogueNum = false
      })
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container && container.scrollHeight) {
          container.scrollTop = container.scrollHeight
        }

      })
    },

// 高度方法
    getHeight() {
      this.$nextTick(() => {
        var container = document.querySelector('.right-container')
        if (container && container.scrollHeight) {
          container.scrollTop = container.scrollHeight
        }

      })
    },

    getDialogueId() {

      let dialogueId = this.$route.query.dialogueId
      if (dialogueId != null && dialogueId != '') {
        this.id = dialogueId
        this.dialogueNum = false
      }
    },

    handleReviewById(id) {

      if (id != '') {
        getDialogue(id).then((res) => {
          this.id = id
          this.dialogueList = res.data.dialogueDetailsList
          for (let i = 0; i < this.dialogueList.length; i++) {
            if (this.dialogueList[i].processing != null) {
              const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`
              this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrl: imageData })
            }
          }
          this.dialogueNum = false
        })
      } else {
        console.log('id==null')
      }
    },
    handleBeforeClose(done) {
      // 强制用户通过"确定"按钮关闭弹窗
      this.$message.info("请通过点击确定按钮提交答案！");
    },

    // 关闭左侧侧拉框
    handleCloseRightDrawer() {
      this.drawerRigth = !this.drawerRigth
      this.dialogueFlag = !this.dialogueFlag
      this.stopAudioRight()
    },
    // 获取题
    getProblem() {
      this.getProblemParam.index = this.pptIndex
      this.getProblemParam.presentationId = this.$route.query.presentationId
      getProblem(this.getProblemParam).then(res => {
        if (res.code == 200 && res.data != null) {
          this.hasProblem = true
          this.examination.problemId = res.data.id
          this.examination.question = res.data.problem
          this.examination.options = res.data.optionsMap
          const options = Object.keys(res.data.optionsMap).map(key => {
            return {
              id: key,
              value: res.data.optionsMap[key]
            }
          })
          this.examination.options = options
          this.examination.answer = res.data.anwers
          this.examination.checkList = []
          if (res.data.remark === "1") {
            this.radioShow = false
            this.checkShow = true
          } else {
            this.radioShow = true
            this.checkShow = false
          }
        } else {
          this.hasProblem = false
        }
        // console.log(this.hasProblem)
      })
    },
    // 确认答案是否正确
    getAnswerCorrectness() {
      this.questionErrorMsg = ''
      this.answerParam.problemId = this.examination.problemId
      this.answerParam.subAnwer = this.examination.checkList.toString()
      checkAnwer(this.answerParam).then(anwer => {
        // console.log(anwer)
        this.checkAnswerAndProceed(anwer)
      })
    },
    // 播放ppt
    checkAnswerAndProceed(anwer) {
      // getAnswerCorrectness 是一个检查答案是否正确的方法，返回 true/false
      if (anwer) {
        this.pptIndex++ // 播放下一页
        this.attemptCount = 0 // 重置错误次数
        this.answerIsDone = true
        // 获取题目
        this.getProblem()
        this.sendIframeWinpMessage() // 发送播放下一页的消息
        this.drawer = false
      } else {
        this.attemptCount++ // 增加错误次数
        //this.$message.error('答错了，请重新答')
        this.questionErrorMsg = '答错了，请重新答'
        setTimeout(() => {
          this.questionErrorMsg = ''
        }, 1000)

        if (this.attemptCount >= this.maxAttempts) {
          // 如果错误次数达到三次，重新播放当前页
          this.attemptCount = 0
          this.sendIframeWinpMessage() // 重新播放当前页
          this.drawer = false
          this.answerIsDone = true
          this.questionErrorMsg = ''
          this.examination.checkList = []
        }
      }

      // setTimeout(() => {
      //   if (this.pptIndex === this.pptInfo.presentationAllpage) {
      //     this.completeFlag = true;
      //     this.hasStarted = false;
      //   }
      // }, 2000);
    },
    // 获取初始数字人形象
    getInitialPlat() {
      this.initialPlatParam.figureId = Number(this.$route.query.figureId)
      getInitialPlatById(this.initialPlatParam).then(res => {
        this.initPath = res.msg
        this.defaultImage = 'data:image/jpg;base64,' + this.initPath
        // console.log(this.defaultImage)
      })
    },
    // 获取百度token
    getBaiDuToken() {
      this.token = getBaiDuToken().then(res => {
        this.token = res.token
      })
    },
    // 获取发言人
    getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
        })
      },
    play(content) {
      let resText = this.markdownToPlainText(content)
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: resText,
        // 角色
        voiceName: this.per,
        // 语速
        speed: this.speed,
        // 音量
        voice: this.volume,
        // 音高
        pitch: this.pitch,
      });
      ttsRecorder.start();
    },
    pause() {
      ttsRecorder.stop();
    },
    // 通过数字人形象 id 获取数字人数据存储的那个表
    getPlatTableNameById() {
      let id = Number(this.$route.query.figureId)
      const request = indexedDB.open('digitalHuman', 1)
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
        const store = transaction.objectStore('digitalHuman_table_relation')
        // console.log(id)
        const getRequest = store.get(id)
        // console.log(request)
        getRequest.onsuccess = (event) => {
          const result = event.target.result
          if (result) {
            this.digitalHumanSaveTableName = result.data.saveTable
            // console.log('查询结果:', result)
          } else {
            console.log('未找到对应 id 的数据')
          }
        }
      }
    },
    // 获取相对路径  获取对应图片id
    async getRelativePath(pageNum) {
      this.imglist = []
      this.getRelativePathParam.pageIndex = pageNum
      const response = await getRelativePath(this.getRelativePathParam)
      if (response.data != null && response.data != '') {
        this.imgIdlist = response.data.actionList
      }
      await this.getDigitalHumanInfoByIds()
      this.pptPageChange()
    },
    // 获取到数字人id序列后，查询数据库获取数字人信息
    getDigitalHumanInfoByIds() {
      return new Promise((resolve, reject) => {
        let tableName = this.digitalHumanSaveTableName
        const request = indexedDB.open('digitalHuman', 1)

        request.onsuccess = (event) => {
          const db = event.target.result
          const transaction = db.transaction(tableName, 'readwrite')
          const store = transaction.objectStore(tableName)

          let promises = [] // 用于存储所有异步请求的 Promise

          for (let i = 0; i < this.imgIdlist.length; i++) {
            const ids = this.imgIdlist[i].split(',')
            let imgS = []

            ids.forEach(item => {
              let id = Number(item)

              // 将 get 请求封装为 Promise
              const promise = new Promise((resolve, reject) => {
                const getRequest = store.get(id)
                getRequest.onsuccess = (event) => {
                  const result = event.target.result
                  if (result) {
                    imgS.push('data:image/jpg;base64,' + result.base64String)
                  }
                  resolve()
                }
                getRequest.onerror = () => reject()
              })

              promises.push(promise)
            })

            // 等待所有请求完成
            Promise.all(promises)
              .then(() => {
                this.imglist.push(imgS)
                resolve() // 所有数据处理完毕，调用 resolve
              })
              .catch(error => {
                console.error('加载图片时出错', error)
                reject(error)
              })
          }
        }

        request.onerror = (event) => {
          console.error('打开数据库失败:', event.target.error)
          reject(event.target.error)
        }
      })
    },

    getpptInfo() {
      const params = {
        presentationId: this.$route.query && this.$route.query.presentationId
      }
      getpptInfo(params).then((response) => {
        this.pptInfo = response.data
        this.presentationAllpage = this.pptInfo.presentationAllpage
        this.currentPage = this.pptInfo.currentPage ? this.pptInfo.currentPage : 1
        this.pptIndex = this.pptInfo.currentPage ? this.pptInfo.currentPage : 1
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + ((this.currentPage && this.currentPage != this.presentationAllpage) ? this.currentPage : 1) + this.pptInfo.suffix
        this.videoPageIndexesArray = (response.data.videoPageIndexes || '')
          .split(',')
          .filter(item => item) // 过滤掉空字符串项
          .map(Number)

        this.getppt(this.pptPath)
        // this.getPptImgUrl(this.pptPath)
      })
    },
    getPptImgUrl(pptPath) {
      let baseUrl = window.location.origin
      // let baseUrl = 'https://www.papaicai.com/'
      // console.log('Initial baseUrl:', baseUrl)
      // console.log(pptPath)

      // 替换 .pptx 为 .jpg
      const newPath = pptPath.replace('.pptx', '.jpg')

      if (pptPath.includes('/ruoyi/')) {
        // 替换路径中的 ruoyi/ 之前的部分
        const finalPath = newPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

        if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
          this.pptImgData = 'http://127.0.0.1:9215' + finalPath
          // console.log('Final baseUrl:', baseUrl)
        } else {
          this.pptImgData = baseUrl + finalPath
        }
      }

      // else if (baseUrl.includes('https://www')) {
      //   baseUrl = baseUrl + newPath
      //   this.pptImgData = baseUrl
      //   // console.log('www Final baseUrl:', baseUrl)
      // }

      // 返回或使用处理后的 baseUrl

    },
    async getppt(pptPath) {
      this.pptIndex = Number(this.pptIndex)
      // console.log('当前页码', this.pptIndex, typeof this.pptIndex)
      // console.log(this.videoPageIndexesArray)
      // console.log(this.videoPageIndexesArray.includes(this.pptIndex))
      if (this.videoPageIndexesArray.includes(this.pptIndex)) {
        this.showPPTStyle = 'iframe'
        // console.log('页面有视频，需要进行原始 iframe 播放')
        this.pauseFlag = true
        this.$refs.audio.pause()
      } else {
        this.showPPTStyle = 'img'
        // console.log('进行 img 播放')
      }

      if (this.showPPTStyle === 'img') {
        this.pptSingleImg = pptPath.replace('.pptx', '.jpg')
        // getppt(paramss).then((response) => {
        //   this.ppts = response
        //   const blob = new Blob([response], { type: 'image/jpg' })  // 假设图片是PNG格式
        //   const url = URL.createObjectURL(blob)
        //   this.pptImgData = url
        // })
        this.getPptImgUrl(pptPath)
      }
      // 当显示为 iframe 时，获取 ppt 文件流并设置 src
      if (this.showPPTStyle === 'iframe') {
        const paramss = {
          filePath: pptPath
        }
        const response = await getppt(paramss)
        this.ppts = response

      }
    },

    initializeIframe() {
      // console.log('当前ppts')
      // console.log(this.ppts)
      // 检查 iframe 是否存在
      if (this.$refs.iframe && this.$refs.iframe.contentWindow) {
        this.iframeWin = this.$refs.iframe.contentWindow

        // 显示加载中警告消息
        const loadingMessage = this.$message({
          message: '网络较慢，数据加载中，请稍候...',
          type: 'warning',
          duration: 0 // 设置为 0，提示框不会自动关闭
        })

        // 使用 setInterval 循环检查 ppts 是否有数据
        const interval = setInterval(() => {
          // 如果 ppts 已经有数据，调用 cresssated 方法
          if (this.ppts) {
            console.log('PPTS 数据已加载，执行 cresssated')
            if (this.iframeWin && this.iframeWin.cresssated) {
              this.iframeWin.cresssated(this.ppts)
            }
            // 清除定时器，停止循环检查
            clearInterval(interval)
            setTimeout(() => {
              // 关闭加载中提示消息
              loadingMessage.close()
            }, 500)

          } else {
            console.log('等待 PPT 数据...')
          }
        }, 500) // 每 500ms 检查一次
      } else {
        console.log('iframe 未找到或未加载')
      }
    },
    cleanupIframe() {
      this.iframeWin = null // 清理 iframe 的引用
      // 清理其他与 iframe 相关的内容或事件监听器
    },
    // 获取分割后的两个数组
    getDigitalHumanSpeech() {
      const params = {
        presentationId: this.$route.query && this.$route.query.presentationId
      }
      getDigitalHumanSpeech(params).then((response) => {
        this.arr = response.data.speechDrafts
        this.progress = response.data.progress
        this.progressp = response.data.progress
        this.arr.forEach(item => {
          // console.log(item.txtPauses);
          // this.pptContent.push(item.txtSentences)
          // this.pptContentAction.push(item.beforeAnalysisContent) // 获取图片文本数组 （已废弃）
          this.pptContent.push(item.txtPauses)
        })
        // console.log('this.pptContent', this.pptContent)
      })
    },
    start() {
      // console.log('this.pauseFlag')
      // console.log(this.pauseFlag)
      const pptBox = document.querySelector('#pptbox')
      pptBox.style.display = 'block'
      // console.log("start")
      if (!this.hasStarted) {
        // console.log('==========================')
        this.curPageIsPlaying = true
        this.hasStarted = true
        this.completeFlag = false
        this.isLastSegment = false
        this.stopv = true
        this.sendIframeWinpMessage()
      } else {
        this.clearAudio()
        // console.log('==========================2')
        this.hasStarted = false
        this.completeFlag = true
        this.pauseFlag = false
        this.stopv = false
        // this.pptIndex = 1;
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex + this.pptInfo.suffix
        this.getppt(this.pptPath)
      }
    },
    sendIframeWinpMessage() {
      this.subtitlesIndex = 0
      this.sentenceAction = 0
      this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex + this.pptInfo.suffix
      this.getppt(this.pptPath)

      //切换ppt
      console.log(this.pptIndex, 'pptIndex')
      this.updateProgress(this.pptInfo, this.pptIndex)
      //当前动作播放状态
      this.curMotionIsDone = false

      //语音播放ppt内容
      let page = this.pptContent[this.pptIndex - 1]
      if (page) {
        // 获取一页ppt内容
        // this.currentPageContent = this.pptContent[this.pptIndex - 1]
        // 切割文本
        // this.contentArray = splitTextByPunctuation(this.currentPageContent, 55)
        // this.subtitlesArr = this.contentArray
        // pptContent 存放的是一页的中每句话
        this.subtitlesArr = this.pptContent[this.pptIndex - 1]
        this.subtitles = this.subtitlesArr[this.subtitlesIndex].txt
        //this.getRelativePath(this.pptContentAction[this.pptIndex - 1])
        // 传页码
        this.getRelativePath(this.pptIndex)

        this.curPageIsPlaying = true
        this.isLastSegment = false
        this.curSegmentIndex = 0

        // 清除定时器
        if (this.intervalId_text) {
          clearInterval(this.intervalId_text)
        }

        this.intervalId_text = setInterval(() => {
          // console.log("是否有语音正在处理"+this.isProcessing)
          // console.log(this.hasStarted + "|" +this.curPageIsPlaying
          // + "|" +!this.completeFlag + "|" +!this.isLastSegment + "|" +!this.pauseFlag + "|" +!this.isplaying)
          //执行条件：已开始播放 且 正在播放ppt 且 ppt未播放完成 且当前ppt页未播放到最后一句 且 不是暂停状态 且 没有正在播放的语音 且 语音任务没有在处理
          if (this.hasStarted && this.curPageIsPlaying
            && !this.completeFlag && !this.isLastSegment && !this.pauseFlag && !this.isplaying && !this.isProcessing) {
            if (this.curSegmentIndex == this.subtitlesArr.length - 1) {
              this.isLastSegment = true
            }
            console.log(this.isProcessing)
            // console.log(this.subtitlesArr[this.curSegmentIndex])
            // this.play(this.contentArray[this.curSegmentIndex]);
            if (this.subtitlesArr[this.curSegmentIndex].txt != '') {
              this.isProcessing = true
              if(this.userVoiceChoose === 'baidu'){
                this.playAudio(this.subtitlesArr[this.curSegmentIndex].txt).then(() => {
                  this.isProcessing = false
                })
              }else if(this.userVoiceChoose === 'doubao'){
                console.log(this.isProcessing)
                this.synthesizeSpeech(this.subtitlesArr[this.curSegmentIndex].txt).then(() => {
                  this.isProcessing = false
                })
              }

            }
            // this.changeMotion()
            this.curSegmentIndex++
          }
        }, 700)
      }
    },
    // 当ppt翻页的时候 执行初始化
    pptPageChange() {
      const index = 0
      this.initMotion(index)
    },
    // 百度文本转语音
    async playAudio(text) {
      const option = {
        tex: text,
        tok: this.token,
        cuid: `${Math.floor(Math.random() * 1000000)}`,
        ctp: '1',
        lan: 'zh',
        per: this.per,
        spd: this.speed,
        pit: this.pitch,
        vol: this.volume
      }

      const res = await axios.post('https://tsn.baidu.com/text2audio', qs.stringify(option), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        responseType: 'blob'
      })
      // this.audioArr.push(this.audioArrData[index]);
      // console.log(res.data)
      this.audioArr.push(URL.createObjectURL(res.data))
    },
    // 豆包语音合成
    // 豆包语音合成方法，使其接收内容参数
    async synthesizeSpeech(content) {
      const params = {
        content: content,
        voiceType: this.per,
        speed: this.speed,
      };
      try {
        const response = await DBTTS(params);
        if (response.data && response.data.audioData) {
          const audioBase64 = response.data.audioData;
          const binaryString = window.atob(audioBase64);
          const len = binaryString.length;
          const bytes = new Uint8Array(len);
          for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          const audioBlob = new Blob([bytes], { type: 'audio/mp3' });
          const audioUrl = URL.createObjectURL(audioBlob);
          this.audioArr.push(audioUrl); // 将生成的音频 URL 添加到播放列表
          return audioUrl; // 返回成功结果，使Promise正常解析
        } else {
          throw new Error("网络不佳请稍后再试");
        }
      } catch (error) {
        this.$message.error(`网络不佳请稍后再试: ${error.message}`);
        console.error("语音合成请求失败:", error);
        throw error; // 重新抛出错误，这样外部的 then/catch 可以捕获
      }
    },
    // 添加暂停判断
    continuePlayIsPause(){
      const currentSubtitle = this.subtitlesArr[this.subtitlesIndex];
      // 检查是否需要暂停和暂停时间
      const shouldPause = currentSubtitle?.havePause || false;
      const pauseTime = (currentSubtitle?.timeOut || 0) * 1000;
      console.log(shouldPause, pauseTime)
      if (shouldPause) {
        if(pauseTime <= 0){
          // 当 pauseTime <= 0 时，进入一直暂停状态
          this.pauseFlag = true; // 标志当前状态为暂停
          // this.pauseAudio();
          this.$refs.audio.pause(); // 停止当前音频播放
          this.pauseTimeout = true
          return; // 停止后续逻辑
        }
        // 暂停指定时间后播放下一段音频
        this.pauseFlag = true
        this.pauseTimeout = setTimeout(() => {
          this.continuePlay();
          this.pauseFlag = false
          this.pauseTimeout = null; // 清除定时器引用
        }, pauseTime);
      } else {
        // 直接播放下一段音频
        this.continuePlay();
      }
    },


    //每段音频结束后调用此函数播放下一段
    continuePlay() {
      this.sentenceAction++
      this.currentIndex++
      this.subtitlesIndex++
      this.subtitles = this.subtitlesArr[this.subtitlesIndex]?.txt || "";
      // console.log(this.subtitles)
      if (this.currentIndex < this.audioArr.length) {

        this.initMotion(this.sentenceAction)
        setTimeout(() => {
          this.$refs.audio.load()
          this.$refs.audio.play().catch((error) => {
            console.error('Failed to play:', error)
          })
        }, 100)
      } else {
        // this.isplaying = false;
        this.curPageIsPlaying = false
      }
    },
    //初始化动作
    initMotion(index) {
      let max_index = this.imglist[index].length
      this.curMotionMaxIndex = max_index
      this.v = 0
      this.imgListData = this.imglist[index]
      // console.log(this.imgListData)
      this.changeMotion()
    },
    //切换动作
    changeMotion() {
      // console.log("切换动作")
      //清除定时器
      if (this.intervalId_img) {
        clearInterval(this.intervalId_img)
      }
      // console.log(this.curMotionMaxIndex)
      //设置定时器
      this.intervalId_img = setInterval(() => {
        //如果语音正在播放
        if (!this.isplaying) {
          if (this.v >= this.curMotionMaxIndex - 1) {

            this.v = this.curMotionMaxIndex - 1
          } else {
            // this.v++
            if (!this.pauseFlag && this.stopv) {
              this.v++
              // console.log(this.v)
            }
            // console.log(this.imgListData[this.v])
          }
        }
      }, this.interval)
    },
    clearAudio() {
      this.$refs.audio.load() // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.currentvoiceIndex = 0
      this.audioArr = []
    },
    stop() {
      //this.isplaying = false;
      // ttsRecorder.mystop(this);
      // this.v = 0;
      // this.$refs.audio.pause();
      this.curMotionIsDone = true
    },
    pauseAudio() {
      if (!this.pauseFlag) {
        this.pauseFlag = !this.pauseFlag
        this.$refs.audio.pause()
      } else {
        this.pauseFlag = !this.pauseFlag
        // this.$refs.audio.play()
        if (this.pauseTimeout) {
          // 如果存在暂停的定时器，清除它
          clearTimeout(this.pauseTimeout);
          this.pauseTimeout = null;
          this.continuePlay();
        }
        // this.continuePlay();
        this.$refs.audio.play().catch((error) => {
          console.error("Failed to play audio:", error);
        });
      }
    },
    startDialogue() {
      this.drawerRigth = !this.drawerRigth
      // this.detailsDrawer = !this.detailsDrawer
      this.dialogueFlag = !this.dialogueFlag
      if(!this.dialogueFlag){
        this.closeASR()
      }
      this.scrollToBottom()
    },
    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += '\n'
      } else if (event.keyCode == 13) {
        event.preventDefault()
        this.ask()
      }
    },

    initRecorder(){
      speechRecognitionService.initRecorder((blob, encTime) => {
        if (speechRecognitionService.websocket && speechRecognitionService.isRecording) {
          speechRecognitionService.websocket.send(blob);
        }
      });
    },
    baiduASROptions(){
      const type = Cookies.get("voiceType")
      if(type === 'CN'){
        this.baiduASRParam.dev_pid = 1537
      }else if(type === 'EN'){
        this.baiduASRParam.dev_pid = 1737
      }else{
        this.baiduASRParam.dev_pid = 1537
      }
      this.dict.type.system_websocket_param.forEach(item => {
        console.log(item)
        if(item.label === 'appId'){
          this.baiduASRParam.appId = Number(item.value)
        }
        if(item.label === 'appKey'){
          this.baiduASRParam.appKey = item.value
        }
      })
    },
    closeASR(){
      this.translationFlag = false;
      this.finalText = "";
      // 停止音量监控
      this.stopVolumeMonitoring();
      if(this.voiceASRType === 1){
        speechRecognitionService.closeWebsocket();
        eventBus.$off(this.baiduASRParam.name);
        if (this.timer) {
          clearTimeout(this.timer); // 清除定时器
          this.timer = null;
        }
        return;
      }
      iatRecorder.stop();
    },
    baiduASR(){
      this.baiduASROptions();
      if(this.translationFlag){
        this.closeASR();
      }else{
        this.translationFlag = true;
        speechRecognitionService.startSpeechRecognition(error => {
        if (error) {
          this.$message.error('麦克风未打开！');
          switch (error.message || error.name) {
            case 'PERMISSION_DENIED':
            case 'PermissionDeniedError':
              console.info('用户拒绝提供信息。');
              break;
            case 'NOT_SUPPORTED_ERROR':
            case 'NotSupportedError':
              console.info('浏览器不支持硬件设备。');
              break;
            case 'MANDATORY_UNSATISFIED_ERROR':
            case 'MandatoryUnsatisfiedError':
              console.info('无法发现指定的硬件设备。');
              break;
            default:
              console.info('无法打开麦克风。异常信息:' + (error.code || error.name));
              break;
          }
        }
      }, this.baiduASRParam);
      eventBus.$on(this.baiduASRParam.name, (result) => {
        console.log("识别结果:", result);

        if (result.type === "MID_TEXT"){
          this.content = this.finalText + result.text;
        }
        if (result.type === "FIN_TEXT"){
          console.log("最终结果:", this.finalText);
           // 累加最终结果
          this.finalText += result.text;
          this.content = this.finalText;
          console.log("最终结果:", this.content);
        }
      });
      // 设置 60 秒后自动关闭录音
      this.timer = setTimeout(() => {
        speechRecognitionService.closeWebsocket(); // 调用关闭方法
        eventBus.$off(this.baiduASRParam.name) // 清除监听器
        this.translationFlag = false; // 更新标识
        this.finalText = "";
        this.$message.info('录音已自动停止。');
      }, 60000);
      }
    },

    saveEditedContent() {
      // 用户编辑完成后同步最终内容
      this.finalText = this.content;
      console.log("保存用户编辑后的结果:", this.finalText);
    },


    translation() {
      if (this.translationFlag) {
        iatRecorder.stop()
        this.translationFlag = false
      } else {
        let language = ""
        const type = Cookies.get("voiceType")
        if(type === 'CN'){
          language = "zh_cn"
        }else if(type === 'EN'){
          language = "en_us"
        }else{
          language = "zh_cn"
        }
        iatRecorder.setParams({
          language: language
        })
        iatRecorder.start()
        this.translationFlag = true
        iatRecorder.onTextChange = (text) => {
          let inputText = text
          this.content = inputText.substring(0, inputText.length - 1) //文字处理，因为不知道为什么识别输出的后面都带'。'，这个方法是去除字符串最后一位
          // console.log(this.content);
        }
      }
    },

    updateProgress(pptInfo, pptIndex) {
      const param = {
        currentPage: pptIndex,
        presentationAllpage: pptInfo.presentationAllpage,
        presentationId: pptInfo.presentationId,
        dialogueId: this.id
      }
      updateProgress(param).then((res) => {
        if (res.code === 200) {
          this.currentPage = this.currentPage > pptIndex ? this.currentPage : pptIndex
        }
      })
    },

    updateDialogueId() {
      const param = {
        presentationId: this.pptInfo.presentationId,
        dialogueId: this.id
      }
      console.log(this.id)
      updateDiogloe(param).then((res) => {
        if (res.code === 200) {
          this.currentPage = this.currentPage > pptIndex ? this.currentPage : pptIndex
        }
      })
    },
    // 课堂问答方法======================================
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/addLiu', {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + getToken(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      let num1 = 1
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader()
      let done = false
      try {
        this.dialogueList[this.dialogueList.length - 1].content = ''
        let s = ''
        while (!done) {
          const { value, done: isDone } = await reader.read()
          if (isDone) {
            done = true
            this.loadSendBtn = false
            this.dialogueNum = false
            this.getDialogueList()
            if (this.playFlagRight) {
              this.playAudioRigth(s)
            }

            this.handleReview(this.id)
            //获取高度
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value)
            s += str
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str
            })
            this.getHeight()
          }

        }
      } finally {
        reader.releaseLock() // 确保在循环结束时释放锁
      }
    },

    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + getToken(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      let num1 = 1
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader()
      let done = false
      try {
        this.dialogueList[this.dialogueList.length - 1].content = ''
        let s = ''
        while (!done) {
          const { value, done: isDone } = await reader.read()
          if (isDone) {
            done = true
            this.loadSendBtn = false
            this.dialogueNum = false
            if (this.playFlagRight) {
              this.playAudioRigth(s)
            }

            this.handleReview(this.id)
            //获取高度
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value)
            s += str
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str
            })
            //获取高度
            this.getHeight()
          }

        }
      } finally {
        reader.releaseLock() // 确保在循环结束时释放锁
      }
    },

    getDialogueList() {
      const param = {
        menuRouting: this.getSecondSlash(this.$route.path) + '/',
        invocation: this.invocation
      }
      getDialogueList(param).then((res) => {
        this.hisList = res.data
        //获取高度
        this.getHeight()
      })
    },

    // 添加一个统一的语音合成方法
    synthesizeVoice(content) {
      // 停止当前正在播放的音频
      this.stopAudioRight();

      // 根据 userVoiceChoose 选择不同的语音合成方法
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.playAudioRigth(content);
          break;
        case "xfSpark": // 讯飞语音
          this.play(content);
          break;
        case "doubao": // 豆包语音
          this.playAudioFromUrlRigth(content);
          break;
        default:
          // 默认使用百度语音
          this.playAudioRigth(content);
          break;
      }
    },
    // 统一的暂停方法
    handleAudioControl() {
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.stopAudioRight();
          break;
        case "xfSpark": // 讯飞语音
          this.pause();
          break;
        case "doubao": // 豆包语音
          this.stopAudioRight();
          break;
        default:
          // 默认使用百度语音
          this.stopAudioRight();
          break;
      }
    },
    async playAudioFromUrlRigth(content) {
      if (this.isPlayAudio && !this.txtToImage) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true
      this.isPlayFinish = false
      this.synthesizeSpeechRigth(content)
      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
    },
    // 豆包语音合成方法，使其接收内容参数
    synthesizeSpeechRigth(content) {
      const tex = this.markdownToPlainText(content);
      this.segments = this.splitTextByPunctuation(tex, 55);
      this.isPlayFinish = false;
      let Index = 0; // 添加一个索引来跟踪当前处理的段落
      const processSegment = () => {
        if (Index >= this.segments.length || this.isPlayFinish) {
          return; // 如果处理完所有段落或者标记为完成，则停止处理
        }
        const text = this.segments[Index].trim();
        if (text === "") {
          Index++;
          processSegment(); // 如果文本为空，跳过当前段落
          return;
        }

        if (!this.isAudioLoading) {
          this.isAudioLoading = true; // 设置音频正在加载的标志

          const params = {
            content: text,
            voiceType: this.per,
            speed: this.speed,
          };

          DBTTS(params).then((response) => {
            if (response.data && response.data.audioData) {
              const audioBase64 = response.data.audioData;
              const binaryString = window.atob(audioBase64);
              const len = binaryString.length;
              const bytes = new Uint8Array(len);
              for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              const audioBlob = new Blob([bytes], { type: 'audio/mp3' });
              const audioUrl = URL.createObjectURL(audioBlob);
              this.audioArr.push(audioUrl); // 将生成的音频 URL 添加到播放列表

              Index++; // 移动到下一个段落
              this.isAudioLoading = false; // 重置音频加载标志
              processSegment(); // 递归调用处理下一个段落
            } else {
              throw new Error("语音合成失败：未获取到音频数据");
            }
          }).catch(error => {
            this.$message.error(`语音合成请求失败: ${error.message}`);
            console.error("语音合成请求失败:", error);
            Index++;
            this.isAudioLoading = false;
            processSegment(); // 即使出错也继续处理下一个段落
          });
        }
      };

      processSegment(); // 开始处理第一个段落
    },
    // 自动播放
    autoPlayRight() {
      //如果已是自动播放  关闭播放 改变状态
      if (this.playFlagRight) {
        this.playFlagRight = !this.playFlagRight

        this.isPlayAudioRigth = false
        this.$refs.audioRigth.pause()
        //清除音频src []
        this.clearAudioRight()
        // this.isplaying = false
      } else {
        this.playFlagRight = !this.playFlagRight
        this.isPlayAudioRigth = false
      }
    },
    // 封装异步  文本转音频   播放音频
    async playAudioRigth(content) {
      if (this.isPlayAudioRigth) {
        return
      }
      this.clearAudioRight()
      this.isPlayAudioRigth = true

      //提前获取token 并赋值
      const res = await getBaiDuToken()
      this.token = res.token

      this.isPlayFinish = false
      this.textToAudio2Right(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
      //this.isplaying = true;
    },
    // 每段音频结束后调用此函数播放下一段
    continuePlayRight() {
      this.currentIndexRigth++
      if (this.currentIndexRigth < this.audioArrRight.length) {
        setTimeout(() => {
          this.$refs.audioRigth.load()
          this.$refs.audioRigth.play().catch((error) => {
          })
        }, 100)
      } else {
        // this.isplaying = false;
      }
    },
    async getId() {
      await getId()
        .then((res) => {
          // console.log(res);
          if (res.code === 200) {
            this.id = res.data
          }
        })
        .catch((err) => {
          this.loadSendBtn = false
        })
    },
    // 文本转语音  提供文本转语音
    async textToAudio2Right(text, token) {
      const tex = this.markdownToPlainText(text);
      this.segments = this.splitTextByPunctuation(tex, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: '1',
          lan: 'zh',
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        }

        const res = await axios.post('https://tsn.baidu.com/text2audio', qs.stringify(option), {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          responseType: 'blob'
        })
        if (this.isPlayFinish) {
          this.clearAudioRight()
          return
        }
        this.audioArr.push(URL.createObjectURL(res.data))
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    async ask() {
      if(this.translationFlag){
        this.closeASR();
      } // 关闭语音输入
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题')
        return false
      }

      this.loadSendBtn = true
      this.dialogueList.push({ content: this.content, issue: 'user' })
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: 'assistant' })
      // this.dialogueList.push({content: '', issue: "assistant"});
      //获取高度
      this.getHeight()
      //首次发请求
      if (this.dialogueNum) {
        //后端ai询问
        await this.getId()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          //menuRouting: this.getSecondSlash(this.routePath) + "/",
          menuRouting: this.getSecondSlash(this.$route.path) + '/',
          id: this.id,
          language: Cookies.get('voiceType')

        }

        //置空content
        this.content = ''
        //发送请求
        await this.addDialogue2(param)
          .catch(error => console.error('Error:', error))
      } else {
        //
        this.stopAudioRight()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          id: this.id,
          menuRouting: this.getSecondSlash(this.$route.path) + '/',
          language: Cookies.get('voiceType')
        }

        this.content = ''
        //
        this.updateDialogue2(param)
          .catch(error => console.error('Error:', error))
      }

    },
    //获取token
    async getToken() {
      const res = await getBaiDuToken()
      this.token = res.token
    },
    //停止播放
    stopAudioRight() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false
        this.$refs.audio.pause()
        // this.isplaying = false;
        this.isPlayAudioRigth = false
        this.isPlayFinish = true
        this.clearAudioRight()
      } else {
        setTimeout(() => {
          this.isStopFinsh = true
        }, 500)
      }
    },
    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf('/')

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex)
      } else {
        // 如果没有找到'/'，返回原字符串
        return str
      }
    },
    markdownToPlainText(markdown) {
    if (!markdown) return '';

    // 移除 Markdown 标题
    markdown = markdown.replace(/^#+\s(.+)/gm, '$1');

    // 移除 Markdown 图片和链接
    markdown = markdown.replace(/!\[.*?\]\(.*?\)/g, '');
    markdown = markdown.replace(/\[.*?\]\(.*?\)/g, '');

    // 移除 Markdown 粗体和斜体
    markdown = markdown.replace(/\*\*(.*?)\*\*/g, '$1'); // 粗体
    markdown = markdown.replace(/\*(.*?)\*/g, '$1');     // 斜体
    markdown = markdown.replace(/__(.*?)__/g, '$1');     // 粗体
    markdown = markdown.replace(/_(.*?)_/g, '$1');       // 斜体

    // 移除 Markdown 代码块和行内代码
    markdown = markdown.replace(/```[\s\S]*?```/g, '');
    markdown = markdown.replace(/`(.*?)`/g, '$1');

    // 移除 Markdown 分割线
    markdown = markdown.replace(/-{3,}/g, '');

    // 移除 Markdown 列表
    markdown = markdown.replace(/^\s*[-*+]\s+/gm, '');
    markdown = markdown.replace(/^\d+\.\s+/gm, '');

    // 移除 Markdown 引用
    markdown = markdown.replace(/^>\s+/gm, '');

    // 移除 Markdown 表格
    markdown = markdown.replace(/\|.*?\|/g, '');
    markdown = markdown.replace(/-\|/g, '');

    // 移除多余的换行和空格
    markdown = markdown.replace(/\n{2,}/g, '\n');
    markdown = markdown.trim();

    return markdown;
},
// 开始监控麦克风音量
startVolumeMonitoring() {
      if (!navigator.mediaDevices || this.isMonitoringVolume) return;
      
      console.log("开始监控麦克风音量");
      this.isMonitoringVolume = true;
      
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // 请求麦克风权限
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          // 创建媒体流源
          const source = this.audioContext.createMediaStreamSource(stream);
          
          // 创建分析器
          this.audioAnalyser = this.audioContext.createAnalyser();
          this.audioAnalyser.fftSize = 256;
          
          // 连接源到分析器
          source.connect(this.audioAnalyser);
          
          // 创建数据数组
          this.audioDataArray = new Uint8Array(this.audioAnalyser.frequencyBinCount);
          
          // 开始分析音量
          this.analyzeVolume();
          
          // 保存流的引用，以便稍后停止
          this.microphoneStream = stream;
        })
        .catch(err => {
          console.error("获取麦克风权限失败:", err);
          this.isMonitoringVolume = false;
        });
    },
    
    // 停止监控麦克风音量
    stopVolumeMonitoring() {
      if (!this.isMonitoringVolume) return;
      
      console.log("停止监控麦克风音量");
      
      // 清除静音检测定时器
      if (this.silenceTimer) {
        clearTimeout(this.silenceTimer);
        this.silenceTimer = null;
      }
      
      // 停止音频分析
      if (this.requestAnimationId) {
        cancelAnimationFrame(this.requestAnimationId);
        this.requestAnimationId = null;
      }
      
      // 关闭麦克风
      if (this.microphoneStream) {
        this.microphoneStream.getTracks().forEach(track => track.stop());
        this.microphoneStream = null;
      }
      
      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close().catch(e => console.error(e));
        this.audioContext = null;
      }
      
      this.audioAnalyser = null;
      this.audioDataArray = null;
      this.isMonitoringVolume = false;
    },
    
    // 分析麦克风音量
    analyzeVolume() {
      if (!this.isMonitoringVolume || !this.audioAnalyser) return;
      
      // 获取当前音频数据
      this.audioAnalyser.getByteFrequencyData(this.audioDataArray);
      
      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < this.audioDataArray.length; i++) {
        sum += this.audioDataArray[i];
      }
      const average = sum / this.audioDataArray.length;
      
      // 处理音量数据
      this.handleVolumeData(average);
      
      // 继续请求下一帧分析
      this.requestAnimationId = requestAnimationFrame(() => this.analyzeVolume());
    },
    
    // 处理音量数据
    handleVolumeData(volume) {
      const now = Date.now();
      
      // 如果音量超过阈值，认为有人在说话
      if (volume > this.silenceThreshold) {
        // 更新最后声音时间
        this.lastVolumeTime = now;
        
        // 如果有静音定时器，清除它
        if (this.silenceTimer) {
          clearTimeout(this.silenceTimer);
          this.silenceTimer = null;
        }
      } 
      // 如果音量低于阈值且没有静音定时器，开始计时
      else if (!this.silenceTimer && this.lastVolumeTime > 0) {
        // 设置静音定时器
        this.silenceTimer = setTimeout(() => {
          // 如果自动发送功能开启且有内容
          if (this.autoSendEnabled && this.content.trim() !== '') {
            console.log("检测到静音，自动发送内容");
            
            // 停止语音识别和音量监控
            this.closeASR();
            this.stopVolumeMonitoring();
            
            // 发送内容
            this.ask();
          }
          
          this.silenceTimer = null;
        }, this.silenceDuration);
      }
    },
    // 拆分文本
    splitTextByPunctuation(text, maxLength) {

      const punctuation = /[。！？；]/
      const secondaryPunctuation = /[ ， ]/
      let result = []
      let currentSegment = ''
      while (text.length > 0) {
        // 正则表达式匹配字符
        let match = punctuation.exec(text)
        // 如果匹配到字符
        if (match) {
          let segment = text.slice(0, match.index + 1)
          if (segment.length <= maxLength) {
            text = text.slice(match.index + 1)
            result.push(segment.trim())
          } else {
            while (segment.length > maxLength) {
              let secondaryMatch = secondaryPunctuation.exec(segment)
              if (secondaryMatch && secondaryMatch.index < maxLength) {
                let subSegment = segment.slice(0, secondaryMatch.index + 1)
                if (subSegment.length <= maxLength) {
                  result.push(subSegment.trim())
                  segment = segment.slice(secondaryMatch.index + 1)
                } else {
                  result.push(segment.slice(0, maxLength).trim())
                  segment = segment.slice(maxLength)
                }
              } else {
                result.push(segment.slice(0, maxLength).trim())
                segment = segment.slice(maxLength)
              }
            }
            if (segment.length > 0) {
              result.push(segment.trim())
            }
            text = text.slice(match.index + 1)
          }
        } else {
          while (text.length > maxLength) {
            result.push(text.slice(0, maxLength).trim())
            text = text.slice(maxLength)
          }
          if (text.length > 0) {
            result.push(text.trim())
            text = ''
          }
        }
      }
// 最后剩一个片段，将当前片段添加到结果数组中
      if (currentSegment.length > 0) {
        result.push(currentSegment.trim())
      }
      return result
    },
    clearAudioRight() {
      this.$refs.audio.load() // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.audioArr = []
    },
    handleKeyDown(event) {
      if (event.key === 'Enter') {
        this.goPage()
      }
    },
    // 点击图片时切换选中状态
    selectBox(index) {
      this.pptImageThumbnail.forEach((box, i) => {
        if (i === index) {
          box.isActive = !box.isActive // 切换点击图片的状态
          this.goPages = index + 1
          this.goPage()
        } else {
          box.isActive = false // 其他图片取消选中状态
        }
      })
    },
    // 鼠标悬浮时激活该图片
    hoverBox(index) {
      this.pptImageThumbnail.forEach((box, i) => {
        if (i === index) {
          box.isActive = true // 当前悬浮的图片激活
        } else {
          box.isActive = false // 其他图片清除悬浮效果
        }
      })
    },
    // 鼠标移出时移除激活效果
    removeHover(index) {
      // 如果图片没有被点击选中，移除悬浮效果
      if (!this.pptImageThumbnail[index].isActive) {
        this.pptImageThumbnail[index].isActive = false
      }
    },
    goPage() {
      let roundedResult = Math.round((this.progress * this.presentationAllpage) / 100)
      if (this.goPages > roundedResult) {
        this.$message.warning('请勿跳转至未播放的页面')
        return
      } else if (!this.hasStarted) {
        this.pptIndex = this.goPages
        this.curPageIsPlaying = true
        this.hasStarted = true
        this.completeFlag = false
        this.isLastSegment = false
        this.goPages = ''
        this.sendIframeWinpMessage()
        this.getProblem();
      } else {
        this.stop()
        this.clearAudio()
        this.pptIndex = this.goPages
        this.sendIframeWinpMessage()
        this.getProblem();
      }
    },
    toggleRightSidebar() {
      this.showRightSidebar = !this.showRightSidebar;
    }
  }
}
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  background-size: 100% 100%;
  display: flex;
  background-image: url('../../../public/logo/wisdomSchool-bgSC.jpg'); /* 背景图片路径 */

  .ck-container2 {
    margin: 0 auto;
    width: 100%;
    height: calc(100vh - 84px);
    background-size: 100% 100%;
    display: flex;
  }
  .imgbox {
    width: 17%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 100px;
    left: 20px;

    .img {
      height: 100%;
    }
  }

  .imgboxHuman {
    height: calc(63%);
    width: calc(85% * 1 / 4);
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    margin-top: calc(8% - 20px);
    top: 22vh;
    right: 13vh;
    z-index: 240;
  }

  .img {
    height: 100%;
    max-width: 100%;
  }

  .action-img {
    width: 30px;
    height: 30px;
  }
  /* 控制按钮和分页容器 */

  .controls-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    position: relative;
    right: 0px;
    top: calc(70% + 40px); /* 根据图片高度向下偏移20px */
    z-index: 10;
  }

  /* 让 "开始" 和 "暂停" 按钮在同一行 */

  .button-row {
    display: flex;
    flex-direction: row; /* 水平排列按钮 */
    justify-content: center; /* 按钮水平居中 */
    align-items: center; /* 垂直居中 */
    gap: 10px; /* 按钮之间的间距 */
    z-index: 10;
  }

  /* 按钮的样式 */

  .start-button, .pause-button, .dialogue-button {
    margin: 10px 0 0;
    z-index: 10;
    width: 40px;
    height: 40px;
    border: none; /* 去掉边框 */
    background-color: rgb(111, 109, 109); /* 设置背景色 */
    color: white; /* 设置文字和图标颜色 */
    box-shadow: none; /* 去掉默认的阴影效果 */
    border-radius: 50%; /* 圆形按钮 */
    padding: 0; /* 去掉内边距 */
  }

  .start-button:hover, .pause-button:hover, .dialogue-button:hover {
    background-color: #a9aaa9; /* 鼠标悬停时背景色变深 */
  }

  .start-button:active, .pause-button:active, .dialogue-button:active {
    background-color: #c6c8c7; /* 按钮被按下时背景色更深 */
    transform: scale(0.95); /* 按钮被按下时轻微缩小 */
  }


  /* 分页框样式 */

  .pagination {
    margin-top: 10px;
    text-align: center;
    width: 100%;
    margin-left: 20px;
  }

  .pptbox {
    position: absolute;
    height: 100%;
    width: 80%;
    top: 10px;
    right: 20vh;

    // position: relative;
    // height: calc(100% - 20px);
    // width: calc(100% * 3 / 4 + 40px);
    // left: calc(60% * 1 / 20);
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // overflow: hidden;
    // background: #000;
  }

  .goPage {
    // position: absolute; /* 或者 fixed  absolute*/
    // top: 34vw;
    // left: 15vw;
    padding: 0px 0px 0px 40px;
  }

  .ppt-iframe {
    width: 100%; /* 填满容器宽度 */
    height: 100%; /* 填满容器高度 */
    border: none; /* 去掉边框 */
    display: block; /* 确保不出现空隙 */
    margin-left: 8.5vh;
    margin-top: 2vh;
  }

  .pptbox1 {
    position: relative;
    height: calc(100% - 20px);
    width: calc(100% * 3 / 4 + 40px);
    left: calc(60% * 1 / 15);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    margin-top: -6vh;
  }

  .pptImgDataBox1 {
    height: 100%;
    width: 92%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: #000000 solid 1px;
    border-radius: 20px;
    background-color: rgb(255, 255, 255);
  }

  .pptImgDataBox1 img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }


  .right-container {
    background: #dbe9f740;
    flex: 1;
    border-radius: 10px;
    margin-left: 20px;

    .answer-box {
      height: 72%;
      overflow-y: auto;
      border-bottom: 1px solid #e8e8e8;
      padding: 10px;

      .answer {
        width: 100%;
        height: 150px;
        overflow: auto;
      }
    }

    .ask-box {
      height: 28%;
      overflow-y: auto;
      padding: 8px;
      border-radius: 5px;

      .ask-button {
        position: absolute;
        bottom: 8px;
        right: 10px;
        margin: auto;
      }

      .talk-button {
        position: absolute;
        bottom: 8px;
        right: 60px;
        margin: auto;
      }
    }
  }
}

.subtitlesStyles1 {
  position: fixed; /* 相对于 pptbox 定位 */
  bottom: 7vh; /* 距离底部一定距离 */
  width: 50%; /* 宽度为全屏 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  background-color: rgba(0, 0, 0, 0.5); /* 灰色透明背景 */
  padding: 15px 0; /* 上下内边距 */
  margin-left: 0%;
}

.subtitle-content1 {
  color: white; /* 字幕字体颜色为白色 */
  font-size: 18px; /* 字体大小 */
  text-align: center; /* 文字居中 */
  max-width: 90%; /* 限制最大宽度 */
  overflow-wrap: break-word; /* 自动换行以适应较长文本 */
}


.subtitlesStyles {
  position: fixed; /* 相对于 pptbox 定位 */
  bottom: 7vh; /* 距离底部一定距离 */
  width: 49%; /* 宽度为全屏 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  background-color: rgba(0, 0, 0, 0.5); /* 灰色透明背景 */
  padding: 15px 0; /* 上下内边距 */
  margin-left: 6.5%;
}

.svg-icon {
  width: 16px;
  height: 16px;
}

.subtitle-content {
  color: white; /* 字幕字体颜色为白色 */
  font-size: 18px; /* 字体大小 */
  text-align: center; /* 文字居中 */
  max-width: 90%; /* 限制最大宽度 */
  overflow-wrap: break-word; /* 自动换行以适应较长文本 */
}

.PPTcontent {
  flex: 1;
  flex-grow: 1; /* 添加这个属性 */
  margin-left: 8vh;
  padding: 15vh;
  overflow-y: visible;
}

.sidebar {
  position: fixed; /* 或者 fixed  absolute*/
  margin-top: 13vh;
  width: 1vh; /* 默认宽度更小 */
  height: 60vh;
  margin-left: -1.5vh;
  background-color: rgb(131, 128, 128); /* 半透明背景 */
  transition: width 0.3s ease, background-color 0.3s ease;
  overflow: hidden;
  border-radius: 10px; /* 设置圆角，数字可以调整圆角的大小 */
  z-index: 10;
}


.sidebar:hover {
  width: 200px; /* 鼠标悬停时的宽度 */
  background-color: rgb(240, 240, 240); /* 完全不透明 */
}

.box-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  height: 60vh; /* 固定高度 */
  overflow-y: auto; /* 让图片区域可以滚动 */
}

.box-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.box {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  color: rgb(12, 12, 12);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.box.active {
  transform: scale(1.1); /* 放大选中的图片 */
  border: 2px solid #007BFF; /* 添加选中的边框 */
}

.box img {
  width: 200px;
  height: 100px;
  border-radius: 5px;
}

.sidebarRight {
  position: fixed; /* 固定在页面位置 */
  right: 1vh; /* 将导航栏放在页面右侧 */
  top: 15vh;
  width: 1vh; /* 默认收起时的宽度 */
  // height: 30vh;
  // height: auto; /* 自动适配内容 */
  background-color: rgb(64, 65, 64); /* 半透明背景 */
  transition: width 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
  overflow: hidden;
  border-radius: 10px; /* 设置圆角 */
  z-index: 10;
  cursor: pointer; /* 添加指针样式表明可点击 */

  /* 高度动态适应内容，但保留最小高度 */
  height: auto; /* 高度适应内容 */
  min-height: 5vh; /* 设置最小高度，确保收起状态下竖条可见 */
  max-height: 30vh; /* 可选：限制最大高度 */
  overflow-y: auto; /* 可选：如果内容超出盒子，则滚动 */
}

/* 当showRightSidebar为true时的样式 */
.sidebarRight.expanded {
  width: 8vh; /* 展开后的宽度 */
  background-color: rgb(111, 109, 109); /* 完全不透明背景 */
}

/* 移除hover样式 */

.rightBox-list {
  // margin-top: 20px;
  // flex-direction: column;
  // padding: 10px;
  // height: 30vh; /* 固定高度 */
  // overflow-y: auto; /* 让图片区域可以滚动 */

  display: flex;
  flex-direction: column; /* 水平排列按钮 */
  justify-content: center; /* 按钮水平居中 */
  align-items: center; /* 垂直居中 */
  gap: 10px; /* 按钮之间的间距 */
  z-index: 10;
  height: 30vh;
}

.rightBox-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}


.radio-group-horizontal {
  display: block;
  flex-wrap: wrap; /* 如果选项多，支持换行 */
  gap: 20px; /* 控制选项间距 */
  margin-left: 60px;
}

/* 选项样式 */

.demo-drawer__footer {
  display: flex; /* 使用弹性布局 */
  justify-content: flex-end; /* 内容靠右对齐 */
  padding: 10px; /* 为按钮容器添加内边距 */
}

.radio-group-vertical,
.checkbox-group-vertical {
  display: block; /* 弹性布局 */
  flex-direction: column; /* 强制垂直排列 */
  gap: 10px; /* 设置选项之间的间距 */
}

.radio-item,
.checkbox-item {
  display: block; /* 每个选项单独占一行 */
  align-items: center; /* 垂直居中对齐 */
  height: auto; /* 根据内容自动调整高度 */
  margin: 0 0 0 0px; /* 移除多余间距 */
  padding: 5px 0; /* 设置适当的上下间距 */
  line-height: 1.5; /* 调整文字的行高 */
}


</style>

