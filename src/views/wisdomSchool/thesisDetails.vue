<template>
  <div class="app-container ck-container">
    <div class="right-container">
      <div class="answer-box" id="answer-box" ref="scrollContainer">
        <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
                justify="center">
          <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
          <div v-if="item.issue == 'assistant'" style="width:100%;">
            <div
              style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
              <img v-if="item.imageUrl" :src="item.imageUrl" alt="Dialogue Image"
                style="max-width: 100%; height: auto;">
              <div style="width: 100%">
                <el-button style="float: right;margin: 5px;" size="mini" round @click="stopAudio">停止播放</el-button>
                <el-button style="float: right;margin: 5px;" size="mini" round
                  @click="playAudio(item.content)">开始播放</el-button>
              </div>
            </div>
          </div>
          <div v-if="item.issue == 'user'" style="width:100%;">
            <div
              style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
            </div>
          </div>
          <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;" />
        </el-row>
      </div>
      <div class="ask-box">
        <div
          style="border:1px solid #a4a4a4;padding: 10px 10px;background-color: #fff;border-radius: 12px ;">
          <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'" placement="top">
            <el-button style="margin: 5px;" size="mini" round
              :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'" @click="translation">
              {{translationFlag ? '停止' : '语音'}}
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="light" :content="playFlag ?  '点击停止播放': '点击播放'" placement="top">
            <el-button style="margin: 5px;" size="mini" round @click="autoPlay">
              <svg-icon :icon-class="playFlag ?  'voice': 'voiceClose'" />
              {{playFlag ?  ' 停播': ' 播放'}}
            </el-button>
          </el-tooltip>
          <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
            show-word-limit @keydown.native="handleKeyCode($event)" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
        </div>
        <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"
          @click="ask" :loading="loadSendBtn" />
      </div>
    </div>
    <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio" @ended="continuePlay"></audio>
  </div>



</template>

<script>
import logoImg from "@/assets/logo/logo1.png";
import asks from "@/assets/logo/asks.png";
import user from "@/assets/logo/user.png";
import IatRecorder from '@/assets/js/IatRecorder.js'
import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
import axios from "axios";
import qs from "qs";
import {getToken} from "@/utils/auth";
import {
  getDialogueList,
  getPromptList,
  addDialogue,
  updateDialogue,
  delDialogue,
  getDialogue,
  getBaiDuToken,
  getId,
} from "@/api/explorationCenter/experience.js";
import {
  getUserVoiceRole,
} from "@/api/system/voiceRole.js";

const iatRecorder = new IatRecorder('en_us', 'mandarin', '5f27b6a9')//
const ttsRecorder = new TtsRecorder();
export default {
  name: "ExplorationCenter",
  dicts: ['call_type'],
  data() {
    return {
      logo: logoImg,
      asks: asks,
      user: user,
      promptOptions: [],
      invocation: "model",
      hisList: [],
      promptId: "",
      menuRouting: "",
      dialogueList: [],
      dialogueNum: true,
      dialogueResult: {content: '正在回答，请稍等···'}, //

      loadSendBtn: false,
      content: "",
      id: "",
      translationFlag: false,

      playFlag: false,//自动播放
      isPlayAudio: false,//正在播放
      token: "", //使用ai的accessToken
      audioArr: [], //音频数组
      segments: [], //
      currentIndex: 0, //音频播放第几条
      per: "1",
      isPlayFinish: false,
      isStopFinsh: true,
      speed: 5, // 语速
      pitch: 5, // 语调
      volum: 5  // 音量
    };
  },
  created() {
    this.getPromptList();
    this.getDialogueList();
    //获取token

    this.getToken();
    this.getVoiceRole();
  },


  beforeDestroy() {
    //关闭页面前停止
    this.stopAudio();
  },


  computed: {
    computedAudioSrc() {
      return this.audioArr[this.currentIndex];
    },
  },


  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.isPlayAudio && this.playFlag) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play();
            this.isplaying = true;
          },
          {once: true}
        );
      }
    },
  },

  activated() {
  },


  methods: {

    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/test/recording/addLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const {value, done: isDone} = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            this.getDialogueList();
            if (this.playFlag) {
              this.playAudio(s);
            }
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s+=str;
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str;
            });

          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/test/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const {value, done: isDone} = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlag) {
              this.playAudio(s);
            }
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s+=str;
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str;
            });
          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    getVoiceRole(){
      getUserVoiceRole().then(res => {
        if(res.data != null){
          this.per = res.data.voiceRoleId;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
        }
      })
    },
    handlePlay() {
      if (this.playFlag) {
        this.playFlag = !this.playFlag
        this.pause()
      } else {
        this.playFlag = !this.playFlag
      }
    },

    play(content) {
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: content,
        // 角色
        //  voiceName: '',
        // 语速
        speed: 50,
        // 音量
        voice: 50,
      });
      ttsRecorder.start();
    },

    pause() {
      console.log("13")
      ttsRecorder.stop();
    },


    // ？？？？
    translation() {
      if (this.translationFlag) {
        iatRecorder.stop();
        this.translationFlag = false;
      } else {
        iatRecorder.start()
        this.translationFlag = true;
        iatRecorder.onTextChange = (text) => {
          let inputText = text;
          this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
          console.log(this.content);
        };
      }
    },


    getExplicitImplicit() {
      return this.$route.path !== '/explorationCenter7/explorationCenter01';
    },
    getModel() {
      return this.$route.path === '/explorationCenter7/explorationCenter01';
    },

    getDialogueList() {
      const param = {
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
        invocation: this.invocation,
      };
      getDialogueList(param).then((res) => {
        this.hisList = res.data;
      });
    },


    // 获取模板list
    getPromptList() {
      getPromptList().then((res) => {
        const promptOptions = [];
        if (res.data && res.data.length > 0) {
          res.data.forEach((item) => {
            promptOptions.push({
              label: item.templateName,
              value: item.id,
            });
          });
        }
        this.promptOptions = promptOptions;
      });
    },


    handleAdd() {
      this.dialogueNum = true;
      this.dialogueList = [];
      this.content = "";
      this.id = "";
    },
    async getId() {
      await getId()
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    //请求后端
    async ask() {
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }

      this.loadSendBtn = true;
      this.dialogueList.push({content: this.content, issue: "user"});
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      // this.dialogueList.push({content: '', issue: "assistant"});


      //首次发请求
      if (this.dialogueNum) {
        //后端ai询问
        await this.getId();
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          //menuRouting: this.getSecondSlash(this.routePath) + "/",
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          id: this.id,

        };

        //置空content
        this.content = ''
        //发送请求
        await this.addDialogue2(param)
          .catch(error => console.error('Error:', error));
      } else {
        //
        this.stopAudio()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          id: this.id,
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
        };

        this.content = ''
        //
        this.updateDialogue2(param)
          .catch(error => console.error('Error:', error));
      }

    },
    //获取token
    async getToken() {
      const res = await getBaiDuToken();
      this.token = res.token;
    },


    // 自动播放
    autoPlay() {
      //如果已是自动播放  关闭播放 改变状态
      if (this.playFlag) {
        this.playFlag = !this.playFlag;

        this.isPlayAudio = false
        this.$refs.audio.pause();
        //清除音频src []
        this.clearAudio();
        // this.isplaying = false
      } else {
        this.playFlag = !this.playFlag;
        this.isPlayAudio = false
      }
    },

    //停止播放
    stopAudio() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false;
        this.$refs.audio.pause();
        // this.isplaying = false;
        this.isPlayAudio = false;
        this.isPlayFinish = true;
        this.clearAudio();
      } else {
        setTimeout(() => {
          this.isStopFinsh = true;
        }, 500);
      }
    },


    // 封装异步  文本转音频   播放音频
    async playAudio(content) {
      if (this.isPlayAudio) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true

      //提前获取token 并赋值
      const res = await getBaiDuToken();
      this.token = res.token;

      this.isPlayFinish = false
      this.textToAudio2(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
      //this.isplaying = true;
    },


    // 每段音频结束后调用此函数播放下一段
    continuePlay() {
      this.currentIndex++;
      if (this.currentIndex < this.audioArr.length) {
        setTimeout(() => {
          this.$refs.audio.load();
          this.$refs.audio.play().catch((error) => {
          });
        }, 100);
      } else {
        this.isplaying = false;
      }
    },

    // 文本转语音  提供文本转语音
    async textToAudio2(text, token) {
      this.segments = this.splitTextByPunctuation(text, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          responseType: "blob",
        });
        if (this.isPlayFinish) {
          this.clearAudio()
          return;
        }
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    clearAudio() {
      this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.audioArr = []
    },


    // 拆分文本
    splitTextByPunctuation(text, maxLength) {

      const punctuation = /[。！？；]/;
      const secondaryPunctuation = /[ ， ]/;
      let result = []
      let currentSegment = ""
      while (text.length > 0) {
        // 正则表达式匹配字符
        let match = punctuation.exec(text);
        // 如果匹配到字符
        if (match) {
          let segment = text.slice(0, match.index + 1);
          if (segment.length <= maxLength) {
            text = text.slice(match.index + 1);
            result.push(segment.trim())
          } else {
            while (segment.length > maxLength) {
              let secondaryMatch = secondaryPunctuation.exec(segment);
              if (secondaryMatch && secondaryMatch.index < maxLength) {
                let subSegment = segment.slice(0, secondaryMatch.index + 1);
                if (subSegment.length <= maxLength) {
                  result.push(subSegment.trim());
                  segment = segment.slice(secondaryMatch.index + 1);
                } else {
                  result.push(segment.slice(0, maxLength).trim());
                  segment = segment.slice(maxLength);
                }
              } else {
                result.push(segment.slice(0, maxLength).trim());
                segment = segment.slice(maxLength);
              }
            }
            if (segment.length > 0) {
              result.push(segment.trim());
            }
            text = text.slice(match.index + 1);
          }
        } else {
          while (text.length > maxLength) {
            result.push(text.slice(0, maxLength).trim());
            text = text.slice(maxLength);
          }
          if (text.length > 0) {
            result.push(text.trim());
            text = "";
          }
        }
      }
      // 最后剩一个片段，将当前片段添加到结果数组中
      if (currentSegment.length > 0) {
        result.push(currentSegment.trim())
      }
      return result
    },


    //根据已有的文本转语音   dialogueResult
    async textToAudioBytxt(token) {

      this.segments = this.splitTextByPunctuation(this.dialogueResult.content, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("http://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          responseType: "blob",
        });
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },


    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },

    // 历史记录
    handleReview(row) {
      getDialogue(row.id).then((res) => {
        this.id = row.id;
        this.dialogueList = res.data.dialogueDetailsList;
        for (let i = 0; i < this.dialogueList.length; i++) {
          if (this.dialogueList[i].processing != null) {
            const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`;
            this.$set(this.dialogueList, i, {...this.dialogueList[i], imageUrl: imageData});
          }
        }
        this.dialogueNum = false;
      });
    },

    handleDel(row) {
      this.$modal
        .confirm("是否确认删除对话记录？")
        .then(function () {
          return delDialogue(row.id);
        })
        .then(() => {
          this.getDialogueList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
    },

    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    }
  },
};
</script>
<style lang="scss" scoped>
  .ck-container {
    margin: 0 auto;
    width: 100%;
    height: calc(100vh - 84px);
    background-size: 100% 100%;
    display: flex;
    .imgbox {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .img {
        height: 100%;
      }
    }
    .right-container {
      background: #dbe9f740;
      flex: 1;
      border-radius: 10px;
      margin-left: 20px;
      .answer-box {
        height: 72%;
        overflow-y: auto;
        border-bottom: 1px solid #e8e8e8;
        padding: 10px;
        .answer {
          width: 100%;
          height: 150px;
          overflow: auto;
        }
      }
      .ask-box {
        height: 28%;
        overflow-y: auto;
        padding: 8px;
        border-radius: 5px;
        .ask-button {
          position: absolute;
          bottom: 8px;
          right: 10px;
          margin: auto;
        }
        .talk-button {
          position: absolute;
          bottom: 8px;
          right: 60px;
          margin: auto;
        }
      }
    }
  }
</style>

