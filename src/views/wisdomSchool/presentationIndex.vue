<template>
  <div class="app-container">
    <el-form :model="queryParams2" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课件名称" prop="course">
        <el-input v-model="queryParams2.presentationName" placeholder="请输入课件名称" clearable style="width: 240px"
                  @keydown.enter.native.prevent="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-button style="position: absolute;top:20px;right:20px;" icon="el-icon-magic-stick" size="mini"
               @click="handleSelect">选择数字人形象
    </el-button>
    <el-table v-loading="loading" :data="courseList">
      <!--      <el-table-column label="课程名称" prop="course" min-width="180" />-->
      <el-table-column label="课件名称" prop="presentationName" min-width="180"/>
      <el-table-column label="专业名称" prop="majorName" min-width="120"/>
      <el-table-column label="课件教授人" prop="teacherName" min-width="120"/>
      <el-table-column label="学习进度" prop="progress" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.progress ? scope.row.progress + '%' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">学习</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">下载</el-button>
            <el-dropdown-menu slot="dropdown">
<!--              <el-dropdown-item command="download" icon="el-icon-download">演讲稿下载</el-dropdown-item>-->
              <el-dropdown-item command="downloadPPT" icon="el-icon-download">PPT下载</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button size="mini" type="text" v-if="scope.row.progress==100" icon="el-icon-bank-card"
                     @click="openQuestionModal(scope.row)">测评
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleDetails(scope.row)">知识点
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams2.pageNum" :limit.sync="queryParams2.pageSize"
                @pagination="getList2"/>

    <el-dialog title="选择数字人形象" width="500px" :visible.sync="dialogFormVisible">
      <el-form>
        <el-form-item label="">

          <el-radio-group v-model="region" class="radioImg">
            <el-radio v-for="(it,index) in iconList" :label="it.id" :key="index">
              <img :src="'/szr'+it.previewUrl" alt="" style="width: 60px;height: 60px">
            </el-radio>
          </el-radio-group>
          <!-- <el-select v-model="region" placeholder="请选择数字人形象">
            <el-option label="默认" value="shanghai"></el-option>
          </el-select> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePlat(region)">确 定</el-button>
      </div>
    </el-dialog>


    <el-dialog :visible.sync="dialogVisible" width="60%">
      <div v-for="(question, index) in questions" :key="index" class="question-block">
        <h3>{{ question.problem }}</h3>
        <el-radio-group v-model="testResultList[question.id]">
          <el-radio label="A">{{ question.optionA }}</el-radio>
          <el-radio label="B">{{ question.optionB }}</el-radio>
          <el-radio label="C">{{ question.optionC }}</el-radio>
          <el-radio label="D">{{ question.optionD }}</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAnswers">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCourseList,
  getCourseList3,
  getPlatlist,
  savePlat,
  evaluation,
  submit,
  checkID,
  queryImages
} from '@/api/explorationCenter/experience.js'

export default {
  name: 'WisdomSchool',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程表格数据
      courseList: [],
      isdownload: false, // 是否正在下载
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        course: undefined
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
        course: this.$route.query.course,
        major: this.$route.query.major,
        presentationName: undefined
      },
      dialogFormVisible: false,
      region: '',
      plat: {
        figureId: ''
      },
      dialogVisible: false,
      testResultList: {},
      iconList: [
        // { name: '默认', icon: require(`@/assets/images/figure_1.png`) },
        // { name: '刘老师', icon: require(`@/assets/images/figure_2.png`) },
      ],
      questions: []
    }
  },

  created() {
    this.queryParams2.course = this.$route.query.course
    this.queryParams2.major = this.$route.query.major
    const nowCourse = this.$route.query.course
    this.queryParams.major = this.$route.query.major
    localStorage.setItem('nowCourse', JSON.stringify(nowCourse))
    this.getList2()
    this.getPlatIdList()
    this.createDB()
  },
  beforeDestroy() {
    this.queryParams.course = ''
    this.queryParams.major = ''
    this.queryParams2.course = ''
    this.queryParams2.major = ''
  },
  activated() {
    this.queryParams.course = this.$route.query.course
    this.queryParams.major = this.$route.query.major
    const nowCourse = JSON.parse(localStorage.getItem('nowCourse'))
    this.queryParams2.course = this.$route.query.course
    this.queryParams2.major = this.$route.query.major
    this.getList2()
  },
  methods: {

    /** 查询数据集管理列表 */
    getList() {
      this.loading = true
      getCourseList(this.queryParams).then((response) => {
        this.courseList = response.rows
        this.total = response.total
        this.loading = false
        this.region = this.courseList[0].figureId
        // console.log(this.region)
      })
    },

    getList2() {
      this.loading = true
      getCourseList3(this.queryParams2).then((response) => {
        this.courseList = response.rows
        this.total = response.total
        this.loading = false
        this.region = this.courseList[0].figureId
        //console.log(this.region)
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams2.pageNum = 1
      this.getList2()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams2.presentationName = ''
      this.handleQuery()
    },

    /** 学习按钮操作 */
    async handleReview(row) {
      if (!row.figureId) {
        this.$message.warning('请先选择数字人形象')
        return
      }

      const id = Number(row.figureId)
      const res = await this.selectDigitalHumanUpdateStatus(id);
      const isupdate = await checkID(id);

      // 校验前端数据库是否有数据 不存在数据触发下载
      if(res == false){
        this.$message.warning('数字人正在更新，请稍后学习')
        this.changeDownloadDigitalHuman(isupdate.data.figureId);
        return;
      }


      if(isupdate.data.isequals == 0){
        this.$message.warning('数字人需要更新，请耐心等待')
        this.downloadDigitalHuman(isupdate.data.isequals, isupdate.data.figureId, id)
        this.isdownload = true; // 数字人需要下载
        this.getList2();
        return;
      }
      else if(res.data.updataStatus !== undefined && res.data.updataStatus == 1){
        // 更新中
        if(!this.isdownload){
          this.$message.warning('数字人正在更新，请稍后学习')
          // this.isdownload = true;
          const tableName = res.data.saveTable;
          const id = res.id
          this.insertEmptyTableData(tableName, id)
          return;
        }
        this.$message.warning('数字人正在更新，请稍后学习')
        return;
      }
      else {
        this.updateIndexDBDigitalHumanStatus(row.figureId, res.data.saveTable);
        this.$router.push({
          path: '/wisdomSchool/learn',
            query: {
              presentationId: row.presentationId,
              figureId: row.figureId,
              dialogueId: row.dialogueId
            }
        })
      }
    },

    // 更换浏览器更新数字人形象
    async changeDownloadDigitalHuman(id){
      const emptyTables = await this.getEmptyTable()
      let tableName = ''
      // 判断当前空表数量 》0 获取第一个作为存储数据 小于 0 方法获取当前数据表中 时间最早未使用的 数据表 名称  之后清空 当前表
      if (emptyTables.length > 0) {
        tableName = emptyTables[0]
      } else {
        const clearDate = await this.getTableName()
        // 清除表内数据
        this.clearTable(clearDate)
        tableName = clearDate.data.saveTable
      }
      const request = indexedDB.open('digitalHuman', 1)
      request.onsuccess = (event) => {
        const db = event.target.result
        const newDate = {
          id: id,
          data: {
            updateTime: new Date().toLocaleString(),
            saveTable: tableName,
            updataStatus: 1  // 更新中
          }
        }
        const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
        const store = transaction.objectStore('digitalHuman_table_relation')
        store.put(newDate)
      }
      this.insertEmptyTableData(tableName, id)
    },

    // 更新数字人形象
    async downloadDigitalHuman(isequals,figureId,oldId){
      // 数字人相同 不更新
      if(isequals == 1){
        return;
      }
      // 数字人不同 需要更新
      const res = await this.selectdigital(oldId);
      if(res == false){
        return;
      }
      const table = res.data.saveTable
      // 清空旧
      this.clearTable(res);
      const request = indexedDB.open("digitalHuman", 1);
        request.onsuccess = (event) => {
          const db = event.target.result;
          const newDate = {
            id: figureId,
            data : {
              updateTime: new Date().toLocaleString(),
              saveTable : table,
              updataStatus: 1  // 更新中
            }
          }
          const transaction = db.transaction("digitalHuman_table_relation", "readwrite");
          const store = transaction.objectStore("digitalHuman_table_relation");
          store.put(newDate);
        }
      // 插入新数据
      this.insertEmptyTableData(table,figureId)
    },
    handleDetails(row) {

      this.$router.push({
        path: '/wisdomSchool/reviewDataSet',
        query: {
          majorId: row.majorId,
          course: row.course,
          chapter: row.chapter,
          textBookId: row.textBookId
        }
      })
    },
    // 初始化可以选择的图像id
    getPlatIdList() {
      getPlatlist().then(res => {
        this.iconList = res.data
      })
    },
    // 创建数据库
    createDB() {
      // 判断用户 indexDB 中是否存在 digitalHuman 数据库
      const request = window.indexedDB.open('digitalHuman')
      // 数据库存在时触发
      request.onsuccess = (event) => {
        console.log('digitalHuman 数据库已存在')
        request.result.close() // 关闭数据库连接
      }
      // 数据库不存在时触发
      request.onupgradeneeded = (event) => {
        console.log('digitalHuman 数据库不存在，正在创建...')
        const db = event.target.result
        db.createObjectStore('digitalHuman_table_relation', { keyPath: 'id' }) // 创建对象存储
        db.createObjectStore('digitalHuman_1', { keyPath: 'id' })
        db.createObjectStore('digitalHuman_2', { keyPath: 'id' })
        db.createObjectStore('digitalHuman_3', { keyPath: 'id' })
        db.createObjectStore('digitalHuman_4', { keyPath: 'id' })
        db.createObjectStore('digitalHuman_5', { keyPath: 'id' })
        const transaction = event.target.transaction // Get the active transaction
        const tableRelationStore = transaction.objectStore('digitalHuman_table_relation')

        const initialData = { id: 0, name: 'Initial Entry', description: 'Sample data' }
        tableRelationStore.add(initialData) // Add
      }
    },
    handleSelect() {
      this.dialogFormVisible = true
      getPlatlist().then(res => {
        this.iconList = res.data
      })
    },
    async savePlat(id) {
      this.plat.figureId = id
      savePlat(this.plat).then(res => {
        if (res.code == 200) {
          this.dialogFormVisible = false
          this.getList2()
        }
      })
      // 在保存之前查询当前数字人形象是否保存
      const isExist = await this.selectdigital(id)
      console.log('数字人形象已经存在')
      if (isExist != false) {
        this.updateIndexDBDigitalHumanStatus(id, isExist.data.saveTable);
        return
      }

      const emptyTables = await this.getEmptyTable()
      let tableName = ''
      console.log('空表有：', emptyTables)
      // 判断当前空表数量 》0 获取第一个作为存储数据 小于 0 方法获取当前数据表中 时间最早未使用的 数据表 名称  之后清空 当前表
      if (emptyTables.length > 0) {
        tableName = emptyTables[0]
      } else {
        console.log('当前数据表内没有空表，开始获取时间最早的表名')
        const clearDate = await this.getTableName()
        // 清除表内数据
        console.log('清空表名：', clearDate)
        this.clearTable(clearDate)
        tableName = clearDate.data.saveTable
      }
      // const clearDate = await this.getTableName();
      console.log('清空表名：', 'clearDate')
      // tableName = emptyTables[0]
      const request = indexedDB.open('digitalHuman', 1)
      request.onsuccess = (event) => {
        const db = event.target.result
        const newDate = {
          id: id,
          data: {
            updateTime: new Date().toLocaleString(),
            saveTable: tableName,
            updataStatus: 1  // 更新中
          }
        }
        const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
        const store = transaction.objectStore('digitalHuman_table_relation')
        store.put(newDate)
      }
      this.insertEmptyTableData(tableName, id)
    },
    // 更新配置表中的数据
    // indexDB 插入数据
    async insertEmptyTableData(tableName, id) {
      this.isdownload = true;
      const digitalHumanData = await queryImages(id)
      const request = indexedDB.open('digitalHuman', 1)
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(tableName, 'readwrite')
        const store = transaction.objectStore(tableName)
        digitalHumanData.forEach(item => {
          const newDate = {
            id: item.id,
            base64String: item.base64String
          }
          store.put(newDate)
        })
        // 等待事务完成后更新状态
        transaction.oncomplete = () => {
          this.updateIndexDBDigitalHumanStatus(id, tableName);
          this.isdownload = false; // 不需要下载
        };
      }
    },
    // 更新数据关系表中的状态 下载完成后
    updateIndexDBDigitalHumanStatus(id, tableName){
      const request = indexedDB.open('digitalHuman', 1)
      request.onsuccess = (event) => {
        const db = event.target.result
        const newDate = {
          id: id,
          data: {
            updateTime: new Date().toLocaleString(),
            saveTable: tableName,
            updataStatus: 2  // 更新完成
          }
        }
        const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
        const store = transaction.objectStore('digitalHuman_table_relation')
        store.put(newDate)
        transaction.oncomplete = () => {
          console.log('数据更新成功');
        };

        transaction.onerror = (event) => {
          console.error('数据更新失败:', event.target.error);
        };
      }
    },

    // 遍历indexDB 获取空表返回
    getEmptyTable() {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open('digitalHuman')
        request.onsuccess = (event) => {
          const db = event.target.result
          const objectStoreNames = db.objectStoreNames
          const stores = Array.from(objectStoreNames)
          console.log('数据库中的数据表（对象存储）有：', stores)

          const emptyTables = []

          let completed = 0
          stores.forEach(storeName => {
            const transaction = db.transaction(storeName, 'readonly')
            const store = transaction.objectStore(storeName)
            const countRequest = store.count()

            countRequest.onsuccess = (event) => {
              const count = event.target.result
              if (count === 0) {
                emptyTables.push(storeName) // 将空表名称添加到数组中
              }
              // 检查是否已处理所有表
              completed++
              if (completed === stores.length) {
                db.close()
                resolve(emptyTables) // 返回所有空表
              }
            }
            countRequest.onerror = (error) => {
              db.close()
              reject(error)
            }
          })
        }

        request.onerror = (error) => {
          reject(error)
        }
      })
    },
    // 当不存在空表时 查询digitalHuman_table_relation 找到 最早数据的表
    getTableName() {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open('digitalHuman')
        request.onsuccess = (event) => {
          const db = event.target.result
          // 开启一个只读事务，从对象存储中读取数据
          const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
          const objectStore = transaction.objectStore('digitalHuman_table_relation')
          const getAllRequest = objectStore.getAll()
          let digitalHumanArr = []
          getAllRequest.onsuccess = () => {
            digitalHumanArr = getAllRequest.result
            console.log('所有数据：', getAllRequest.result)
            // 筛选出包含 `data.updateTime` 的记录，并转换为 Date 格式
            const updateTimes = digitalHumanArr
              .filter(item => item.data && item.data.updateTime) // 过滤出包含 updateTime 的数据
              .map(item => ({
                updateTime: new Date(item.data.updateTime), // 将 updateTime 转换为 Date 对象
                originalData: item                         // 保存原始数据
              }))

            // 找到最早的时间
            if (updateTimes.length > 0) {
              const earliestUpdate = updateTimes.reduce((earliest, current) => {
                return current.updateTime < earliest.updateTime ? current : earliest
              })
              resolve(earliestUpdate.originalData)
              // console.log("最早的记录：", earliestUpdate.originalData);
              // console.log("最早的 updateTime 值：", earliestUpdate.updateTime.toLocaleString());
            } else {
              // console.log("没有包含 updateTime 的数据。");
              reject('没有包含 updateTime 的数据。')
            }
          }

        }
      })
    },
    // 通过id查询是否存在数字人数据
    selectdigital(id) {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open('digitalHuman', 1)
        request.onsuccess = (event) => {
          const db = event.target.result
          const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
          const store = transaction.objectStore('digitalHuman_table_relation')
          const request = store.get(id)
          request.onsuccess = (event) => {
            const result = event.target.result
            if (result) {
              resolve(result)
            } else {
              resolve(false)
            }
          }
          request.onerror = (event) => {
            reject(new Error('查询数据时出错'))
            console.error('查询数据时出错:', event.target.error)
          }
        }
      })

    },
    // 清空表中数据
    clearTable(clearDate) {
      const request = indexedDB.open('digitalHuman')
      request.onsuccess = (event) => {
        const db = event.target.result
        // 开启一个写事务并清空对象存储
        const transaction1 = db.transaction(clearDate.data.saveTable, 'readwrite')
        const objectStore1 = transaction1.objectStore(clearDate.data.saveTable)
        objectStore1.clear()
        const transaction2 = db.transaction('digitalHuman_table_relation', 'readwrite')
        const objectStore2 = transaction2.objectStore('digitalHuman_table_relation')
        objectStore2.delete(clearDate.id)
        console.log('数据已清空' + clearDate.data.saveTable)
      }
    },
    // 查询数据表关系表中，更新状态
    selectDigitalHumanUpdateStatus(id){
      return new Promise((resolve, reject) => {
        const request = indexedDB.open('digitalHuman')
        request.onsuccess = (event) => {
          const db = event.target.result
          const transaction = db.transaction('digitalHuman_table_relation', 'readwrite')
          const store = transaction.objectStore('digitalHuman_table_relation')
          const request = store.get(id)
          request.onsuccess = (event) => {
            const result = event.target.result
            if (result) {
              resolve(result)
            } else {
              resolve(false)
            }
          }
          request.onerror = (event) => {
            reject(new Error('查询数据时出错'))
            console.error('查询数据时出错:', event.target.error)
          }
        }
      })
    },

    // 下载操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'download':
          this.handleDownload(row, 'download')
          break
        case 'downloadPPT':
          this.handleDownload(row, 'downloadPPT')
          break
        default:
          break
      }
    },
    handleDownload(row, type) {
      this.download(
        '/plat/studyrecord/fileDownload',
        {
          filePath: type == 'download' ? row.speechdraftPath : row.presentationPath
        },
        `${type == 'download' ? row.speechdraftOriginName : row.pptOriginName}`
      )
    },

    /**测评*/
    openQuestionModal(row) {
      const loadingToast = this.showLoadingToast()
      evaluation(row).then(res => {
        this.questions = res.data
        this.testResultList = {} // 初始化答案对象
        this.dialogVisible = true
        this.hideLoadingToast(loadingToast)
      })
        .catch(error => {
          console.error('获取题目失败:', error)
          this.hideLoadingToast(loadingToast)
        })

    },
    // 定义一个显示加载提示的方法
    showLoadingToast() {
      const toast = document.createElement('div')
      toast.style.position = 'fixed'
      toast.style.top = '50%'
      toast.style.left = '50%'
      toast.style.transform = 'translate(-50%, -50%)'
      toast.style.backgroundColor = '#fff'
      toast.style.padding = '10px 20px'
      toast.style.borderRadius = '5px'
      toast.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)'
      toast.style.zIndex = '9999'
      toast.innerText = '正在生成题目，请稍候...'

      document.body.appendChild(toast)

      return toast
    },
    hideLoadingToast(toast) {
      if (toast) {
        document.body.removeChild(toast)
      }
    },

    submitAnswers() {
      // 提交答案的逻辑
      console.log('提交的答案:', this.testResultList)
      console.log(this.questions)
      this.dialogVisible = false
      const answersToSubmit = this.questions.map(question => ({
        id: question.id, // 题目编号
        problem: question.problem, // 问题
        optionA: question.optionA, // 选项A
        optionB: question.optionB, // 选项B
        optionC: question.optionC, // 选项C
        optionD: question.optionD, // 选项D
        answer: question.answer, // 正确答案
        choice: this.testResultList[question.id] // 用户选择的答案
      }))
      console.log(answersToSubmit)
      const param = {
        testQuestionsList: answersToSubmit
      }
      submit(param).then(

      )
    }

  }
}

</script>
