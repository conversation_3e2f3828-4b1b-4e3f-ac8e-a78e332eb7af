<template>
  <div class="app-container ck-container">
    <el-button type="primary" id="pauseButton" class="pause-button" :title="pauseFlag ? '继续' : '暂停'" @click="pauseAudio"
               v-show="hasStarted" :disabled="dialogueFlag" :icon="pauseFlag ? 'el-icon-video-play' : 'el-icon-video-pause'" />
    <el-button type="primary" id="startButton" class="start-button" @click="start" :disabled="dialogueFlag">
      {{hasStarted ? '结束讲课' : '开始讲课'}}
    </el-button>
    <el-button type="success" id="dialogueButton" class="dialogue-button" @click="startDialogue"
    >
      {{dialogueFlag ? '结束问答' : '课堂问答'}}
    </el-button>
    <div class="pagination">
      <label>
        {{ '页码:'+this.pptIndex+' / '+this.presentationAllpage}}
      </label>
      <div>
        <el-input type="number" placeholder="请输入页码" v-model="goPages" style="width:200px;">
          <el-button slot="append" @click="goPage">跳转</el-button>
        </el-input>
      </div>
    </div>
    <div class="imgbox">
      <img class="img" :src="'/szr'+item" alt="" v-for="(item, index) in imglist" :key="index"
           v-show="v==index?true:false">
    </div>
    <div class="pptbox" id="pptbox">
      <iframe :src="src" ref="iframe" width="100%" height="100%" style="border:0px;"></iframe>
      <div class="subtitlesStyles">
        <div class="subtitle-content">
          {{ subtitles }}
        </div>
      </div>
    </div>

    <!--    查看详情-->
    <!-- <right-drawer class="details-drawer" :show-drawer.sync="show">
      <thesis-details v-show="detailsDrawer" ref="thesisDetailes" slot="drawer-content"/>
    </right-drawer> -->
    <!-- 课堂问答 -->
    <el-drawer style="font-size: 16px;" size="47%"
               :visible.sync="drawerRigth" direction="rtl" :before-close="handleCloseRightDrawer" :modal="false">
      <div class="app-container ck-container">

        <div class="right-container">
          <div class="answer-box" id="answer-box" ref="scrollContainer">
            <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
                    justify="center">
              <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
              <div v-if="item.issue == 'assistant'" style="width:100%;">
                <div
                  style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
                  {{item.content}}
                  <img v-if="item.imageUrl" :src="item.imageUrl" alt="Dialogue Image"
                       style="max-width: 100%; height: auto;">
                  <div style="width: 100%">

                    <!--                <el-button style="float: left;margin: 5px;" size="mini" round @click="updateDialoguelike(item.id,item.likeStomp)" v-html="item.likeStomp === 1 ?  iconLikeB : iconLikeA " >-->
                    <!--                  </el-button>-->
                    <!--                <el-button style="float: left;margin: 5px;" size="mini" round @click="updateDialogueStomp(item.id,item.likeStomp)" v-html="item.likeStomp === 2 ?  iconStompB : iconStompA ">-->
                    <!--                  </el-button>-->



                    <el-button style="float: right;margin: 5px;" size="mini" round @click="stopAudioRight">停止播放</el-button>
                    <el-button style="float: right;margin: 5px;" size="mini" round
                               @click="playAudioRigth(item.content)">开始播放</el-button>
                    <el-button style="float: right;margin: 5px;;padding-top:5px;padding-bottom: 5px;" size="mini" round
                               @click="likeOrStomp(item,index,2)">
                      <svg-icon :icon-class="item.likeStomp=='2'?'bad':'thumbs-down'" class-name="card-panel-icon" />
                    </el-button>
                    <el-button style="float: right;margin: 5px;padding-top:5px;padding-bottom: 5px;" size="mini" round
                               @click="likeOrStomp(item,index,1)">
                      <svg-icon :icon-class="item.likeStomp=='1'?'good':'thumbs-up'" class-name="card-panel-icon" />
                    </el-button>
                  </div>
                </div>

                <div style="position:absolute; bottom:-23px; left:5px;"> <!-- 调整bottom值以靠近内容框 -->
                  <!-- 重新生成按钮 -->
                  <el-button v-if="!loadSendBtn && index == dialogueList.length-1" size="mini" type="text"
                             class="copy-button" @click="handleRegen(index)"
                             style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 40px;">重新生成</el-button>
                </div>


              </div>
              <div v-if="item.issue == 'user'" style="width:100%;">
                <div
                  style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
                  {{item.content}}
                </div>
              </div>
              <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;" />
            </el-row>
          </div>

          <div class="ask-box">
            <div
              style="border:1px solid #a4a4a4;padding: 10px 10px;background-color: #fff;border-radius: 12px ;">
              <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'" placement="top">
                <el-button style="margin: 5px;" size="mini" round
                           :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'" @click="baiduASR">
                  {{translationFlag ? '停止' : '语音'}}
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="light" :content="playFlagRight ?  '点击停止播放': '点击播放'" placement="top">
                <el-button style="margin: 5px;" size="mini" round @click="autoPlayRight">
                  <svg-icon :icon-class="playFlagRight ?  'voice': 'voiceClose'" />
                  {{playFlagRight ?  ' 停播': ' 播放'}}
                </el-button>
              </el-tooltip>
              <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
                        show-word-limit @keydown.native="handleKeyCode($event)" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
            </div>
            <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"
                       @click="ask" :loading="loadSendBtn" />
          </div>
        </div>
        <audio :src="computedAudioSrcRigth" controls :playsinline="true" style="display: none;" ref="audioRigth" @ended="continuePlayRight"></audio>
      </div>
    </el-drawer>


    <el-drawer title="课堂小测试" style="font-size: 35px;" size="55%" :visible.sync="drawer" :close-on-press-escape="false"
               :show-close="false" :direction="this.direction" :wrapperClosable="false">
      <div class="demo-drawer__content">
        <el-form v-model="examination">
          <el-form-item>
            <span style="font-size: 24px; margin-left: 100px;" >题目 : {{ examination.question }}</span>
          </el-form-item>
          <el-form-item>
            <el-radio-group  v-show="this.radioShow" v-model="examination.checkList">
              <el-radio v-for="(item, index) in examination.options" :key="index" :label="item.id"
                        style="margin-left: 100px;height: 45px;overflow-y: auto;display: flex;flex-direction: column; ">
                {{ item.id }} {{ item.value }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-checkbox-group  v-show="this.checkShow"  v-model="examination.checkList">
              <el-checkbox  v-for="item in examination.options" :key="item.id" :label="item.id"
                            style="margin-left: 100px;height: 45px;overflow-y: auto;display: flex;flex-direction: column; ">
                {{ item.id }}  {{ item.value }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="getAnswerCorrectness()">确定</el-button>
        </div>
      </div>
    </el-drawer>


    <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio" @ended="continuePlay"></audio>
    <!-- 问答音频 -->
    <!-- <audio :src="computedAudioSrc2" controls :playsinline="true" style="display: none;" ref="audio2" @ended="continuePlay2"></audio> -->
  </div>

</template>

<script>
import "../../../public/ppt/css/pptxjs.css";
import "../../../public/ppt/css/nv.d3.min.css";
import thesisDetails from './thesisDetails.vue'
const ttsRecorder = new TtsRecorder();
const iatRecorder = new IatRecorder('en_us', 'mandarin', '5f27b6a9')//
import asks from "@/assets/logo/asks.png";
import user from "@/assets/logo/user.png";
import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
import IatRecorder from '@/assets/js/IatRecorder.js';
import { AudioRecorder } from "@/assets/js/BaiDuASR.js";
import {getToken} from "@/utils/auth";
import {
  getDialogueList,
  getDialogue,
  addDialogue,
  updateDialogue,
  updateDialoguelikeStomp,
  updateDiogloe,
  getpptInfo,
  getDigitalHumanSpeech,
  updateProgress,
  getppt,
  getRelativePath,
  getBaiDuToken,
  getInitialPlatById,
  getProblem,
  checkAnwer,
  getId,
} from "@/api/explorationCenter/experience.js";
import {
  splitTextByPunctuation
} from "@/assets/js/common.js";
import {
  getUserVoiceRole,
} from "@/api/system/voiceRole.js";
import axios from "axios";
import qs from "qs";
import Cookies from "js-cookie";

export default {
  name: "wisdomSchools",
  components: { thesisDetails },
  data() {
    return {
      v: 0,
      pptIndex: 2,
      currentMotionIndex: 0,
      curMotionMaxIndex: 0,
      imglist: [
      ],
      pptContent: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      src: '/ppt/pptx.html',
      iframeWin: {},
      pptPageDiv: "",
      //是否正在播放语音
      isplaying: false,
      currentPageContent: "",
      //是否开始播放ppt
      hasStarted: false,
      //ppt是否播放完成
      completeFlag: true,
      //当前ppt页是否正在播放
      curPageIsPlaying: false,
      intervalId_img: null,
      intervalId_ppt: null,
      intervalId_text: null,
      //动作播放速度
      interval_time: 0,
      //无动作说话循环次数
      nomotion_order: 0,
      //无动作说话最大循环次数
      nomotion_max_order: 6,
      //当前动作是否播放完成
      curMotionIsDone: false,
      asks: asks,
      user: user,
      invocation: "model",
      hisList: [],
      menuRouting: "",
      dialogueResult: { content: '正在回答，请稍等···' },
      dialogueNum: true,
      loadSendBtn: false,
      content: "",
      id: "",
      routePath: this.$route.path,
      playFlag: true,
      translationFlag: false,
      dialogueFlag: false,
      contentArray: [],
      isLastSegment: false,
      pauseFlag: false,
      curSegmentIndex: 0,
      pptInfo: {},
      ppts: '',
      goPages: '',
      pptPath: '',
      presentationAllpage: 0,//ppt总页数
      currentPage: 0,//ppt看过的最大页数
      getRelativePathParam:{
        figureId:this.$route.query.figureId,
        size:'1215 x 2160',
        beforeAnalysisContent:""
      },
      // 查询初始形象参数
      initialPlatParam:{
        figureId:this.$route.query.figureId
      },
      arr:[], // 接收数组数据
      pptContentAction:[], // ppt动作
      pathArr:[], // ppt动作路径
      interval: 155, // 图片切换的时间间隔（毫秒）
      token: '',
      per: "1",
      speed:5,
      pitch:5,
      volume:5,  // 音量
      pid: '',
      AsrintervalId: null, // 语音计时器
      audioArr: [],
      currentIndex: 0,
      isPlayAudio: false,
      audioArr2: [], // 问答音频数组
      currentvoiceIndex: 0, // 当前问答播放的音频索引
      contentArray2: [],  // 问答拆分文字数组
      initPath: '',
      show: false,
      detailsDrawer: false, // 提问
      // keywordInit: '',
      // keyword: '',
      drawer: false, // 底部抽屉
      drawerRigth: false,
      direction : 'btt',
      examination: {
        problemId: "",
        question: "",
        // 选项
        options: [

          // { id: "A", value: "这是一个正确答案" },
          // { id: "B", value: "这是一个非常只能够却" },
          // { id: "C", value: "这公司汉斯哦" },
          // { id: "D", value: "这是私事那句撒" },
        ],
        checkList: [], // 选中的选项
        answer:""
      },
      attemptCount: 0, // 错误次数计数器
      maxAttempts: 3, // 最大允许错误次数
      // 校验答案参数
      answerParam:{
        problemId: "",
        subAnwer: "",
      },
      // 获取题参数
      getProblemParam:{
        index:"",
        presentationId: "",
      },
      // 当前页是否有题
      hasProblem: false,
      radioShow: false,
      checkShow: false,
      // 答题是否完成
      answerIsDone: true,
      subtitles: "",// 字幕
      subtitlesArr: [], // 数组
      subtitlesIndex: 0,
      stopv : true, // 停止动作
      // 课堂问答需要使用到的相关参数============================
      dialogueList: [], // 对话列表
      playFlagRight: false, // 问答是否自动播放
      currentIndexRigth: 0, // 当前播放的索引
      audioArrRight: [], // 存放所有音频的数组
      isPlayAudioRigth: false,//正在播放
      iconLikeA: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18898 22.1733C4.08737 21.0047 5.00852 20 6.18146 20H10C11.1046 20 12 20.8954 12 22V41C12 42.1046 11.1046 43 10 43H7.83363C6.79622 43 5.93102 42.2068 5.84115 41.1733L4.18898 22.1733Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 21.3745C18 20.5388 18.5194 19.7908 19.2753 19.4345C20.9238 18.6574 23.7329 17.0938 25 14.9805C26.6331 12.2569 26.9411 7.33595 26.9912 6.20878C26.9982 6.05099 26.9937 5.89301 27.0154 5.73656C27.2861 3.78446 31.0543 6.06492 32.5 8.47612C33.2846 9.78471 33.3852 11.504 33.3027 12.8463C33.2144 14.2825 32.7933 15.6699 32.3802 17.0483L31.5 19.9845H42.3569C43.6832 19.9845 44.6421 21.2518 44.2816 22.5281L38.9113 41.5436C38.668 42.4051 37.8818 43 36.9866 43H20C18.8954 43 18 42.1046 18 41V21.3745Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconStompA: '<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18051 26.8339C4.08334 27.9999 5.00352 29 6.1736 29H10C11.1046 29 12 28.1046 12 27V7C12 5.89543 11.1046 5 10 5H7.84027C6.80009 5 5.93356 5.79733 5.84717 6.83391L4.18051 26.8339Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 26.6255C18 27.4612 18.5194 28.2092 19.2753 28.5655C20.9238 29.3426 23.7329 30.9062 25 33.0195C26.6331 35.7431 26.9411 40.664 26.9912 41.7912C26.9982 41.949 26.9937 42.107 27.0154 42.2634C27.2861 44.2155 31.0543 41.9351 32.5 39.5239C33.2846 38.2153 33.3852 36.496 33.3027 35.1537C33.2144 33.7175 32.7933 32.3301 32.3802 30.9517L31.5 28.0155H42.3569C43.6832 28.0155 44.6421 26.7482 44.2816 25.4719L38.9113 6.45642C38.668 5.5949 37.8818 5 36.9866 5H20C18.8954 5 18 5.89543 18 7V26.6255Z" fill="none" stroke="#616161" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconLikeB:'<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18898 22.1733C4.08737 21.0047 5.00852 20 6.18146 20H10C11.1046 20 12 20.8954 12 22V41C12 42.1046 11.1046 43 10 43H7.83363C6.79622 43 5.93102 42.2068 5.84115 41.1733L4.18898 22.1733Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 21.3745C18 20.5388 18.5194 19.7908 19.2753 19.4345C20.9238 18.6574 23.7329 17.0938 25 14.9805C26.6331 12.2569 26.9411 7.33595 26.9912 6.20878C26.9982 6.05099 26.9937 5.89301 27.0154 5.73656C27.2861 3.78446 31.0543 6.06492 32.5 8.47612C33.2846 9.78471 33.3852 11.504 33.3027 12.8463C33.2144 14.2825 32.7933 15.6699 32.3802 17.0483L31.5 19.9845H42.3569C43.6832 19.9845 44.6421 21.2518 44.2816 22.5281L38.9113 41.5436C38.668 42.4051 37.8818 43 36.9866 43H20C18.8954 43 18 42.1046 18 41V21.3745Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      iconStompB:'<svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.18051 26.8339C4.08334 27.9999 5.00352 29 6.1736 29H10C11.1046 29 12 28.1046 12 27V7C12 5.89543 11.1046 5 10 5H7.84027C6.80009 5 5.93356 5.79733 5.84717 6.83391L4.18051 26.8339Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 26.6255C18 27.4612 18.5194 28.2092 19.2753 28.5655C20.9238 29.3426 23.7329 30.9062 25 33.0195C26.6331 35.7431 26.9411 40.664 26.9912 41.7912C26.9982 41.949 26.9937 42.107 27.0154 42.2634C27.2861 44.2155 31.0543 41.9351 32.5 39.5239C33.2846 38.2153 33.3852 36.496 33.3027 35.1537C33.2144 33.7175 32.7933 32.3301 32.3802 30.9517L31.5 28.0155H42.3569C43.6832 28.0155 44.6421 26.7482 44.2816 25.4719L38.9113 6.45642C38.668 5.5949 37.8818 5 36.9866 5H20C18.8954 5 18 5.89543 18 7V26.6255Z" fill="#7b60eb" stroke="#7b60eb" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',
    };
  },

  created() {
    this.getDialogueId();
    this.getpptInfo();
    this.getDigitalHumanSpeech();
    this.getDialogueList();
    // this.getBaiDuToken();
    this.getVoiceRole();
    this.getToken();
    // 获取数字初始形象
    this.getInitialPlat();
    // 获取题目
    // this.getProblem()
    // ppt定时切换的定时器
    this.intervalId_ppt = setInterval(() => {
      //执行条件：已开始播放 且 当前没有播放内容 且 ppt未播放完成 题目回答完
      // console.log(this.hasStarted + "|" + !this.curPageIsPlaying + "|" + !this.completeFlag
      //   + "|" + !this.pauseFlag)
      if (this.hasStarted && !this.curPageIsPlaying && !this.completeFlag
        && !this.pauseFlag && this.answerIsDone
      ) {
        // 当前页有
        // console.log(this.hasProblem)
        if(this.hasProblem){
          this.answerIsDone = false
          this.drawer = true
          // 如果当前页面已达到最大错误次数，重新播放当前页
          if (this.attemptCount >= this.maxAttempts) {
            this.attemptCount = 0; // 重置错误次数
            this.drawer = false
            this.sendIframeWinpMessage(); // 重新播放当前页
          }
        }else{
          // 当前页没有题
          if(this.pptIndex < this.pptInfo.presentationAllpage){
            //console.log("pptIndex++")
            this.pptIndex++;
            // this.pptIndex++;
            this.sendIframeWinpMessage();
            // 获取题目
            this.getProblem()
          }

          const _this = this

          setTimeout(() => {
            if (_this.pptIndex >= _this.pptInfo.presentationAllpage) {
              _this.completeFlag = true;
              _this.hasStarted = false;
              _this.pptIndex = _this.pptInfo.presentationAllpage
              // _this.v = 0;
            }
          }, 5000)
        }
      }
    }, 2000);

    //  获取记录
    this.handleReviewById(this.id);
  },
  mounted() {


    this.iframeWin = this.$refs.iframe.contentWindow;
    //window.addEventListener("boforeunload", this.beforeunloadHandler());
    var _this = this
    setTimeout(function () {
      _this.iframeWin.cresssated(_this.ppts);
    }, 2000)

  },
  beforeDestroy() {

    this.updateDialogueId();
    //关闭页面前停止
    this.stop();
    this.clearAudio();
    // 清除定时器
    if (this.intervalId_img) {
      clearInterval(this.intervalId_img);
    }
    if (this.intervalId_ppt) {
      clearInterval(this.intervalId_ppt);
    }
    if (this.intervalId_text) {
      clearInterval(this.intervalId_text);
    }
  },
  computed :{
    computedAudioSrc() {
      return this.audioArr[this.currentIndex];
    },
    computedAudioSrcRigth(){
      return this.audioArrRight[this.currentIndexRigth];
    },
  },
  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.curPageIsPlaying) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play();
            // this.isplaying = true;
          },
          { once: true }
        );
      }
    },
    computedAudioSrcRigth(newSrc) {
      if (newSrc && this.isPlayAudioRigth && this.playFlagRight) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audioRigth.play();
            // this.isplaying = true;
          },
          { once: true }
        );
      }
    },
    // 监听show属性，控制抽屉的显示或隐藏
    show(newValue) {
      if(newValue){
        this.detailsDrawer = true
        this.dialogueFlag = true
      }else{
        this.detailsDrawer = false
        this.dialogueFlag = false
      }
    },
    dialogueList: {
      handler(newList) {
        this.scrollToBottom(); // 每当dialogueList更新时，滚动到底部
      },
      deep: true
    },
  },

  methods: {

    // 重新生成
    handleRegen(index) {
      this.stopAudioRight()
      this.loadSendBtn = true;
      let content=this.dialogueList[index - 1].content;
      this.dialogueList.push({ content: content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.getHeight()
      const param = {
        invocation: this.invocation,
        //promptId: this.promptId,
        content: this.dialogueList[index - 1].content,
        id: this.id,
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
      };

      this.content = ''
      //
      this.updateDialogue2(param)
        .catch(error => console.error('Error:', error));
    },


    // 点赞/点踩
    likeOrStomp(item, index, type) {
      const param = {
        id: item.id,
        likeStomp: type
      }
      updateDialoguelikeStomp(param).then(res => {
        if (res.code == 200) {
          this.dialogueList[index].likeStomp = type
        }
      })
    },

    updateDialoguelike(id,likeStomp){
      const param = {
        id:id,
        likeStomp: likeStomp == 1 ? 0 : 1
      };
      updateDialoguelikeStomp(param).then((res) => {
        if (res.code === 200) {
          this.handleReview(this.id)
        }
      });
    },

    updateDialogueStomp(id,likeStomp){
      const param = {
        id:id,
        likeStomp:likeStomp == 2 ? 0 : 2
      };
      updateDialoguelikeStomp(param).then((res) => {
        if (res.code === 200) {
          this.handleReview(this.id)
        }
      });
    },


    handleReview(id){
      getDialogue(id).then((res) => {
        this.dialogueList = res.data.dialogueDetailsList;
        for (let i = 0; i < this.dialogueList.length; i++) {
          if (this.dialogueList[i].processing != null) {
            const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`;
            this.$set(this.dialogueList, i, {...this.dialogueList[i], imageUrl: imageData});
          }
        }
        this.dialogueNum = false;
      });
    },


    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer;
        container.scrollTop = container.scrollHeight;
      });
    },

// 高度方法
    getHeight() {
      this.$nextTick(() => {
        var container = document.querySelector('.right-container');
        container.scrollTop = container.scrollHeight;
      })
    },



    getDialogueId(){

      let dialogueId=this.$route.query.dialogueId;
      if (dialogueId!=null&& dialogueId!=""){
        this.id=dialogueId;
        this.dialogueNum=false;
      }
    },


    handleReviewById(id) {

      if (id!=""){
        getDialogue(id).then((res) => {
          this.id = id;
          this.dialogueList = res.data.dialogueDetailsList;
          for (let i = 0; i < this.dialogueList.length; i++) {
            if (this.dialogueList[i].processing != null) {
              const imageData = `data:image/jpeg;base64,${this.dialogueList[i].processing}`;
              this.$set(this.dialogueList, i, {...this.dialogueList[i], imageUrl: imageData});
            }
          }
          this.dialogueNum = false;
        });
      }else {
        console.log("id==null")
      }
    },


    // 关闭左侧侧拉框
    handleCloseRightDrawer(){
      this.drawerRigth = !this.drawerRigth;
      this.dialogueFlag = !this.dialogueFlag;
      this.stopAudioRight();
    },
    // 获取题
    getProblem() {
      this.getProblemParam.index = this.pptIndex;
      this.getProblemParam.presentationId = this.$route.query.presentationId;
      getProblem(this.getProblemParam).then(res => {
        if (res.code == 200 && res.data != null) {
          this.hasProblem = true;
          this.examination.problemId = res.data.id;
          this.examination.question = res.data.problem;
          this.examination.options = res.data.optionsMap;
          const options = Object.keys(res.data.optionsMap).map(key => {
            return {
              id: key,
              value: res.data.optionsMap[key]
            };
          });
          this.examination.options = options;
          this.examination.answer = res.data.anwers;
          this.examination.checkList = [];
          if(res.data.remark = 1){
            this.radioShow = false;
            this.checkShow = true;
          }else{
            this.radioShow = true;
            this.checkShow = false;
          }
        }else{
          this.hasProblem = false;
        }
        // console.log(this.hasProblem)
      });
    },
    // 确认答案是否正确
    getAnswerCorrectness() {
      this.answerParam.problemId = this.examination.problemId;
      this.answerParam.subAnwer = this.examination.checkList.toString();
      checkAnwer(this.answerParam).then(anwer => {
        // console.log(anwer)
        this.checkAnswerAndProceed(anwer)
      });
    },
    // 播放ppt
    checkAnswerAndProceed(anwer) {
      // getAnswerCorrectness 是一个检查答案是否正确的方法，返回 true/false
      if (anwer) {
        this.pptIndex++; // 播放下一页
        this.attemptCount = 0; // 重置错误次数
        this.answerIsDone = true;
        // 获取题目
        this.getProblem()
        this.sendIframeWinpMessage(); // 发送播放下一页的消息
        this.drawer = false
      } else {
        this.attemptCount++; // 增加错误次数
        this.$message.error("答错了，请重新答")
        if (this.attemptCount >= this.maxAttempts) {
          // 如果错误次数达到三次，重新播放当前页
          this.attemptCount = 0;
          this.sendIframeWinpMessage(); // 重新播放当前页
          this.drawer = false
          this.answerIsDone = true;
          this.examination.checkList = [];
        }
      }

      // setTimeout(() => {
      //   if (this.pptIndex === this.pptInfo.presentationAllpage) {
      //     this.completeFlag = true;
      //     this.hasStarted = false;
      //   }
      // }, 2000);
    },
    // 获取初始数字人形象
    getInitialPlat(){
      getInitialPlatById(this.initialPlatParam).then(res => {
        this.initPath = res.msg
        this.imglist.push(this.initPath)
        // console.log(this.imglist)
      })
    },
    // 获取百度token
    getBaiDuToken(){
      this.token = getBaiDuToken().then(res => {
        this.token = res.token
      })
    },
    // 获取当前用户发言人id
    getVoiceRole(){
      getUserVoiceRole().then(res => {
        if(res.data != null){
          this.per = res.data.voiceRoleId;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
          this.playFlagRight = res.data.userPlayflag;
        }
      })

    },
    // 获取相对路径
    getRelativePath(content){
      this.getRelativePathParam.beforeAnalysisContent = content
      getRelativePath(this.getRelativePathParam).then((response) => {
        this.imglist = response.data;
        this.initMotion();
        // console.log(this.imglist);
      });
      // 此时应该已经去掉了 "szr/" 前缀
    },
    getpptInfo() {
      const params = {
        presentationId: this.$route.query && this.$route.query.presentationId
      }
      getpptInfo(params).then((response) => {
        this.pptInfo = response.data
        this.presentationAllpage = this.pptInfo.presentationAllpage
        this.currentPage = this.pptInfo.currentPage ? this.pptInfo.currentPage : 1
        this.pptIndex = this.pptInfo.currentPage ? this.pptInfo.currentPage : 1
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + ((this.currentPage && this.currentPage != this.presentationAllpage) ? this.currentPage : 1) + this.pptInfo.suffix
        this.getppt(this.pptPath)
      });
    },
    getppt(pptPath) {
      const paramss = {
        filePath: pptPath
      }
      getppt(paramss).then((response) => {
        this.ppts = response;
      });
    },
    // 获取分割后的两个数组
    getDigitalHumanSpeech() {
      const params = {
        presentationId: this.$route.query && this.$route.query.presentationId
      }
      getDigitalHumanSpeech(params).then((response) => {
        this.arr = response.data.speechdraftList
        this.arr.forEach(item => {
          this.pptContent.push(item.content)
          this.pptContentAction.push(item.beforeAnalysisContent)
        })
      });
    },
    dialogueButtonStatus() {
      // this.show = !this.show;
      // this.detailsDrawer = !this.detailsDrawer;
      // if (!this.hasStarted || this.pauseFlag) {
      //   return false;
      // } else {
      //   return true;
      // }
    },
    start() {
      const pptBox = document.querySelector(".pptbox");
      pptBox.style.display = "block";
      // console.log("start")
      if (!this.hasStarted) {
        // console.log("==========================")
        this.curPageIsPlaying = true;
        this.hasStarted = true;
        this.completeFlag = false;
        this.isLastSegment = false;
        this.stopv = true;
        this.sendIframeWinpMessage()
        // this.curSegmentIndex = 0;
        // const speakIndex = this.pptIndex - 1
        // this.currentPageContent = this.pptContent[speakIndex].content;
        // this.contentArray = splitTextByPunctuation(this.currentPageContent, 55);

        // // 清除定时器
        // if (this.intervalId_text) {
        //   clearInterval(this.intervalId_text);
        // }

        // this.intervalId_text = setInterval(() => {
        //   //执行条件：已开始播放 且 正在播放gppt 且 ppt未播放完成 且当前ppt页未播放到最后一句
        //   if (this.hasStarted && this.curPageIsPlaying
        //     && !this.completeFlag && !this.isLastSegment && !this.pauseFlag && !this.isplaying) {
        //     if (this.curSegmentIndex == this.contentArray.length - 1) {
        //       this.isLastSegment = true;
        //     }
        //     this.play(this.contentArray[this.curSegmentIndex]);
        //     this.curSegmentIndex++;
        //   }

        // }, 1500);

      } else {
        this.hasStarted = false;
        this.completeFlag = true;
        this.pauseFlag = false;
        this.stopv = false;
        // this.pptIndex = 1;
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex + this.pptInfo.suffix
        this.getppt(this.pptPath)
        const _this = this
        setTimeout(function () {
          _this.iframeWin.change(_this.ppts);
          _this.stop();
          // this.$refs.audio.load();
          _this.$refs.audio.pause();
          _this.clearAudio()
        }, 1000)
      }
    },
    sendIframeWinpMessage() {
      this.subtitlesIndex = 0;
      this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex + this.pptInfo.suffix
      this.getppt(this.pptPath)
      const _this = this
      setTimeout(function () {
        _this.iframeWin.change(_this.ppts);
      }, 1000)
      //切换ppt
      console.log(this.pptIndex, 'pptIndex')
      this.updateProgress(this.pptInfo, this.pptIndex)
      //当前动作播放状态
      this.curMotionIsDone = false;

      //语音播放ppt内容
      let page = this.pptContent[this.pptIndex - 1];
      if (page) {
        this.currentPageContent = this.pptContent[this.pptIndex - 1];
        this.contentArray = splitTextByPunctuation(this.currentPageContent, 55);
        this.subtitlesArr = this.contentArray
        this.subtitles = this.subtitlesArr[this.subtitlesIndex]
        this.getRelativePath(this.pptContentAction[this.pptIndex - 1])

        this.curPageIsPlaying = true;
        this.isLastSegment = false;
        this.curSegmentIndex = 0;

        // 清除定时器
        if (this.intervalId_text) {
          clearInterval(this.intervalId_text);
        }

        this.intervalId_text = setInterval(() => {
          // console.log(this.hasStarted + "|" +this.curPageIsPlaying
          // + "|" +!this.completeFlag + "|" +!this.isLastSegment + "|" +!this.pauseFlag + "|" +!this.isplaying)
          //执行条件：已开始播放 且 正在播放ppt 且 ppt未播放完成 且当前ppt页未播放到最后一句 且 不是暂停状态 且 没有正在播放的语音
          if (this.hasStarted && this.curPageIsPlaying
            && !this.completeFlag && !this.isLastSegment && !this.pauseFlag && !this.isplaying) {
            if (this.curSegmentIndex == this.contentArray.length - 1) {
              this.isLastSegment = true;
            }
            console.log(this.contentArray[this.curSegmentIndex]);
            // this.play(this.contentArray[this.curSegmentIndex]);
            this.playAudio(this.contentArray[this.curSegmentIndex]);
            this.changeMotion();
            this.curSegmentIndex++;
          }
        }, 500);
      }
    },
    // 百度文本转语音
    async playAudio(text){
      const option = {
        tex: text,
        tok: this.token,
        cuid: `${Math.floor(Math.random() * 1000000)}`,
        ctp: "1",
        lan: "zh",
        per: this.per,
        spd: this.speed,
        pit: this.pitch,
        vol: this.volume
      };

      const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        responseType: "blob",
      });
      // console.log(res.data)
      this.audioArr.push(URL.createObjectURL(res.data));
    },
    //每段音频结束后调用此函数播放下一段
    continuePlay() {
      this.currentIndex++;
      this.subtitlesIndex++;
      this.subtitles = this.subtitlesArr[this.subtitlesIndex]
      // console.log(this.subtitles)
      if (this.currentIndex < this.audioArr.length) {
        setTimeout(() => {
          this.$refs.audio.load();
          this.$refs.audio.play().catch((error) => {
            console.error('Failed to play:', error);
          });
        }, 100);
      } else {
        // this.isplaying = false;
        this.curPageIsPlaying = false;
      }
    },
    //初始化动作
    initMotion() {
      let max_index = this.imglist.length
      this.curMotionMaxIndex = max_index;
      this.v = 0;
    },
    //切换动作
    changeMotion() {
      // console.log("切换动作")
      //清除定时器
      if (this.intervalId_img) {
        clearInterval(this.intervalId_img);
      }
      //设置定时器
      this.intervalId_img = setInterval(() => {
        //如果语音正在播放
        if (!this.isplaying) {
          if (this.v >= this.curMotionMaxIndex-1) {

            this.v = this.curMotionMaxIndex-1
          } else {
            // this.v++
            if(!this.pauseFlag && this.stopv){
              this.v++;
              // console.log(this.v)
            }
            // console.log(this.imglist[this.v])
          }
        }
      }, this.interval);
    },
    clearAudio() {
      this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.currentvoiceIndex = 0
      this.audioArr = []
    },
    stop() {
      //this.isplaying = false;
      // ttsRecorder.mystop(this);
      // this.v = 0;
      // this.$refs.audio.pause();
      this.curMotionIsDone = true;
    },
    pauseAudio(){
      if(!this.pauseFlag){
        this.pauseFlag = !this.pauseFlag;
        this.$refs.audio.pause()
      }else {
        this.pauseFlag = !this.pauseFlag;
        this.$refs.audio.play()
      }
    },
    startDialogue() {
      this.drawerRigth = !this.drawerRigth
      // this.detailsDrawer = !this.detailsDrawer
      this.dialogueFlag = !this.dialogueFlag;
      this.scrollToBottom();
    },
    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    },


    baiduASR(){
      // 停止录音后结果返回
      clearInterval(this.AsrintervalId);
      this.AsrintervalId = setInterval(() => {
        if(this.translationFlag){
          AudioRecorder.monitorVolume().then(result => {
            // console.log(result);
            this.content += result.content;
            this.translationFlag = result.flag;
            clearInterval(this.AsrintervalId);
          })
        }
      }, 500);
      // 开始录音
      if(!this.translationFlag){
        this.translationFlag = true;
        AudioRecorder.startRecording().then(result => {
            this.content += result.content;
            this.translationFlag = result.flag;
          })
      }else{
        this.translationFlag = false;
        AudioRecorder.stopRecording().then(result => {
            this.content += result.content;  // 将识别结果赋值给 Vue 的 content
          })
      }
    },

    translation() {
      if (this.translationFlag) {
        iatRecorder.stop();
        this.translationFlag = false;
      } else {
        iatRecorder.start()
        this.translationFlag = true;
        iatRecorder.onTextChange = (text) => {
          let inputText = text;
          this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
          // console.log(this.content);
        };
      }
    },

    updateProgress(pptInfo, pptIndex) {
      const param = {
        currentPage: pptIndex,
        presentationAllpage: pptInfo.presentationAllpage,
        presentationId: pptInfo.presentationId,
        dialogueId: this.id
      };
      updateProgress(param).then((res) => {
        if (res.code === 200) {
          this.currentPage = this.currentPage > pptIndex ? this.currentPage : pptIndex;
        }
      })
    },

    updateDialogueId() {
      const param = {
        presentationId: this.pptInfo.presentationId,
        dialogueId: this.id
      };
      console.log(this.id)
      updateDiogloe(param).then((res) => {
        if (res.code === 200) {
          this.currentPage = this.currentPage > pptIndex ? this.currentPage : pptIndex;
        }
      })
    },
    // 课堂问答方法======================================
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/test/recording/addLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const {value, done: isDone} = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            this.getDialogueList();
            if (this.playFlagRight) {
              this.playAudioRigth(s);
            }

            this.handleReview(this.id)
            //获取高度
            this.getHeight();
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s+=str;
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str;
            });
            this.getHeight();
          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },

    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/test/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const {value, done: isDone} = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlagRight) {
              this.playAudioRigth(s);
            }

            this.handleReview(this.id)
            //获取高度
            this.getHeight();
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s+=str;
            // 更新助理回复项的内容
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content += str;
            });
            //获取高度
            this.getHeight();
          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },

    getDialogueList() {
      const param = {
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
        invocation: this.invocation,
      };
      getDialogueList(param).then((res) => {
        this.hisList = res.data;
        //获取高度
        this.getHeight();
      });
    },
    // 自动播放
    autoPlayRight() {
      //如果已是自动播放  关闭播放 改变状态
      if (this.playFlagRight) {
        this.playFlagRight = !this.playFlagRight;

        this.isPlayAudioRigth = false
        this.$refs.audioRigth.pause();
        //清除音频src []
        this.clearAudioRight();
        // this.isplaying = false
      } else {
        this.playFlagRight = !this.playFlagRight;
        this.isPlayAudioRigth = false
      }
    },
    // 封装异步  文本转音频   播放音频
    async playAudioRigth(content) {
      if (this.isPlayAudioRigth) {
        return;
      }
      this.clearAudioRight()
      this.isPlayAudioRigth = true

      //提前获取token 并赋值
      const res = await getBaiDuToken();
      this.token = res.token;

      this.isPlayFinish = false
      this.textToAudio2Right(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
      //this.isplaying = true;
    },
    // 每段音频结束后调用此函数播放下一段
    continuePlayRight() {
      this.currentIndexRigth++;
      if (this.currentIndexRigth < this.audioArrRight.length) {
        setTimeout(() => {
          this.$refs.audioRigth.load();
          this.$refs.audioRigth.play().catch((error) => {
          });
        }, 100);
      } else {
        // this.isplaying = false;
      }
    },
    async getId() {
      await getId()
        .then((res) => {
          // console.log(res);
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    // 文本转语音  提供文本转语音
    async textToAudio2Right(text, token) {
      this.segments = this.splitTextByPunctuation(text, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          responseType: "blob",
        });
        if (this.isPlayFinish) {
          this.clearAudioRight()
          return;
        }
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    async ask() {
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }

      this.loadSendBtn = true;
      this.dialogueList.push({content: this.content, issue: "user"});
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      // this.dialogueList.push({content: '', issue: "assistant"});
      //获取高度
      this.getHeight();
      //首次发请求
      if (this.dialogueNum) {
        //后端ai询问
        await this.getId();
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          //menuRouting: this.getSecondSlash(this.routePath) + "/",
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          id: this.id,
          language: Cookies.get("voiceType"),

        };

        //置空content
        this.content = ''
        //发送请求
        await this.addDialogue2(param)
          .catch(error => console.error('Error:', error));
      } else {
        //
        this.stopAudioRight()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          id: this.id,
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          language: Cookies.get("voiceType"),
        };

        this.content = ''
        //
        this.updateDialogue2(param)
          .catch(error => console.error('Error:', error));
      }

    },
    //获取token
    async getToken() {
      const res = await getBaiDuToken();
      this.token = res.token;
    },
    //停止播放
    stopAudioRight() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false;
        this.$refs.audio.pause();
        // this.isplaying = false;
        this.isPlayAudioRigth = false;
        this.isPlayFinish = true;
        this.clearAudioRight();
      } else {
        setTimeout(() => {
          this.isStopFinsh = true;
        }, 500);
      }
    },
    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },
    // 拆分文本
    splitTextByPunctuation(text, maxLength) {

      const punctuation = /[。！？；]/;
      const secondaryPunctuation = /[ ， ]/;
      let result = []
      let currentSegment = ""
      while (text.length > 0) {
        // 正则表达式匹配字符
        let match = punctuation.exec(text);
        // 如果匹配到字符
        if (match) {
          let segment = text.slice(0, match.index + 1);
          if (segment.length <= maxLength) {
            text = text.slice(match.index + 1);
            result.push(segment.trim())
          } else {
            while (segment.length > maxLength) {
              let secondaryMatch = secondaryPunctuation.exec(segment);
              if (secondaryMatch && secondaryMatch.index < maxLength) {
                let subSegment = segment.slice(0, secondaryMatch.index + 1);
                if (subSegment.length <= maxLength) {
                  result.push(subSegment.trim());
                  segment = segment.slice(secondaryMatch.index + 1);
                } else {
                  result.push(segment.slice(0, maxLength).trim());
                  segment = segment.slice(maxLength);
                }
              } else {
                result.push(segment.slice(0, maxLength).trim());
                segment = segment.slice(maxLength);
              }
            }
            if (segment.length > 0) {
              result.push(segment.trim());
            }
            text = text.slice(match.index + 1);
          }
        } else {
          while (text.length > maxLength) {
            result.push(text.slice(0, maxLength).trim());
            text = text.slice(maxLength);
          }
          if (text.length > 0) {
            result.push(text.trim());
            text = "";
          }
        }
      }
// 最后剩一个片段，将当前片段添加到结果数组中
      if (currentSegment.length > 0) {
        result.push(currentSegment.trim())
      }
      return result
    },
    clearAudioRight() {
      this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.audioArr = []
    },
    goPage() {
      if (this.goPages > this.currentPage) {
        this.$message.warning('请勿跳转至未播放的页面');
        return
      } else if (!this.hasStarted) {
        this.pptIndex = this.goPages;
        this.curPageIsPlaying = true;
        this.hasStarted = true;
        this.completeFlag = false;
        this.isLastSegment = false;
        this.goPages = ''
        this.sendIframeWinpMessage()
      } else {
        this.stop()
        this.clearAudio();
        this.pptIndex = this.goPages;
        this.sendIframeWinpMessage()
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  background-size: 100% 100%;
  display: flex;
  .pause-button {
    position: absolute;
    top: 600px;
    left: 170px;
    z-index: 10;
  }
  .start-button {
    position: absolute;
    top: 600px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .dialogue-button {
    position: absolute;
    top: 650px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .pagination {
    position: absolute;
    top: 700px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .imgbox {
    width: 17%;
    height: 60%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 130px;
    left: 20px;
    .img {
      height: 80%;
    }
  }
  .pptbox {
    position: absolute;
    height: 100%;
    width: 80%;
    top: 30px;
    right: 0px;
  }
  .right-container {
    background: #dbe9f740;
    flex: 1;
    border-radius: 10px;
    margin-left: 20px;
    .answer-box {
      height: 72%;
      overflow-y: auto;
      border-bottom: 1px solid #e8e8e8;
      padding: 10px;
      .answer {
        width: 100%;
        height: 150px;
        overflow: auto;
      }
    }
    .ask-box {
      height: 28%;
      overflow-y: auto;
      padding: 8px;
      border-radius: 5px;
      .ask-button {
        position: absolute;
        bottom: 8px;
        right: 10px;
        margin: auto;
      }
      .talk-button {
        position: absolute;
        bottom: 8px;
        right: 60px;
        margin: auto;
      }
    }
  }
}

.subtitlesStyles {
  position: fixed; /* 相对于 pptbox 定位 */
  bottom: 3vh; /* 距离底部一定距离 */
  width: 70%; /* 宽度为全屏 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  background-color: rgba(0, 0, 0, 0.5); /* 灰色透明背景 */
  padding: 25px 0; /* 上下内边距 */
  margin-left: 0%;
}
.svg-icon {
  width: 16px;
  height: 16px;
}
.subtitle-content {
  color: white; /* 字幕字体颜色为白色 */
  font-size: 26px; /* 字体大小 */
  text-align: center; /* 文字居中 */
  max-width: 90% /* 限制最大宽度 */
}
</style>

