<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程名称" prop="course">
        <el-input v-model="queryParams.course" placeholder="请输入课程名称" clearable style="width: 240px"
                  @keydown.enter.native.prevent="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>



    <el-table v-loading="loading" :data="courseList">
      <el-table-column label="课程名称" prop="course" min-width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="goStudy(scope.row)">{{ scope.row.course }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="专业名称" prop="majorName" min-width="120" />
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />



  </div>
</template>

<script>
import {
  getCourseList,
  getCourseList2,
  getPlatlist,
  savePlat,
  checkID,
  queryImages
} from "@/api/explorationCenter/experience.js";

export default {
  name: "WisdomSchool",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程表格数据
      courseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        course: undefined,
      },
      dialogFormVisible: false,
      region: '',
      isListLoaded: false, // 增加一个标识变量
      isequals: '',
      figureId: '',
      plat:{
        figureId: ""
      },
      iconList: [
        // { name: '默认', icon: require(`@/assets/images/figure_1.png`) },
        // { name: '刘老师', icon: require(`@/assets/images/figure_2.png`) },
      ]
    };
  },
  created() {
    this.getList();
    this.createDB();
  },
  activated() {
    this.getList(); // 只在未加载过时调用
  },
  methods: {
    /** 查询数据集管理列表 */
    async getList() {
      this.loading = true;
      const response = await getCourseList2(this.queryParams);
      this.courseList = response.rows;
      this.total = response.total;
      this.loading = false;
      this.region = this.courseList[0].figureId
      console.log(this.region)
      // await this.updateDigital();
      // this.isListLoaded = true; // 加载后设置为 true
    },

    // // 判断是否需要更新数字人
    // async updateDigital(){
    //   if(this.region!=''){
    //     let id = Number(this.region)
    //     checkID(id).then(res=>{
    //       this.isequals = res.data.isequals
    //       this.figureId = res.data.figureId
    //       this.downloadDigitalHuman(this.isequals,this.figureId)
    //     })
    //   }else{
    //     return;
    //   }
    // },
    // // 更新数字人形象
    // async downloadDigitalHuman(isequals,figureId){
    //   // 数字人相同 不更新
    //   if(isequals == 1){
    //     return;
    //   }
    //   // 数字人不同 需要更新
    //   const res = await this.selectdigital(this.region);
    //   if(res == false){
    //     return;
    //   }
    //   const table = res.data.saveTable
    //   // 清空旧
    //   this.clearTable(res);
    //   const request = indexedDB.open("digitalHuman", 1);
    //     request.onsuccess = (event) => {
    //       const db = event.target.result;
    //       const newDate = {
    //         id: figureId,
    //         data : {
    //           updateTime: new Date().toLocaleString(),
    //           saveTable : table
    //         }
    //       }
    //       const transaction = db.transaction("digitalHuman_table_relation", "readwrite");
    //       const store = transaction.objectStore("digitalHuman_table_relation");
    //       store.put(newDate);
    //     }
    //   // 插入新数据
    //   this.insertEmptyTableData(table,figureId)
    // },
    // 通过id查询是否存在数字人数据
  //   selectdigital(id){
  //     return new Promise((resolve, reject) => {
  //       const request = indexedDB.open("digitalHuman", 1);
  //       request.onsuccess = (event) => {
  //         const db = event.target.result;
  //         const transaction = db.transaction("digitalHuman_table_relation", "readwrite");
  //         const store = transaction.objectStore("digitalHuman_table_relation");
  //         const request = store.get(id);
  //         request.onsuccess = (event) => {
  //         const result = event.target.result;
  //         if (result) {
  //           resolve(result);
  //         } else {
  //           resolve(false);
  //         }
  //       };
  //       request.onerror = (event) => {
  //         reject(new Error("查询数据时出错"));
  //         console.error("查询数据时出错:", event.target.error);
  //       };
  //     }
  //   })
  // },
    // indexDB 插入数据
    // async insertEmptyTableData(tableName,id) {
    //     const digitalHumanData = await queryImages(id)
    //     const request = indexedDB.open("digitalHuman", 1);
    //     request.onsuccess = (event) => {
    //       const db = event.target.result;
    //       const transaction = db.transaction(tableName, "readwrite");
    //       const store = transaction.objectStore(tableName);
    //       digitalHumanData.forEach(item => {
    //         const newDate = {
    //         id: item.id,
    //         base64String: item.base64String,
    //       }
    //       store.put(newDate);
    //       })
    //     }
    //   },
      // 清空表中数据
      // clearTable(clearDate){
      //   const request = indexedDB.open("digitalHuman");
      //   request.onsuccess = (event) => {
      //     const db = event.target.result;
      //     // 开启一个写事务并清空对象存储
      //     const transaction1 = db.transaction(clearDate.data.saveTable, "readwrite");
      //     const objectStore1 = transaction1.objectStore(clearDate.data.saveTable);
      //     objectStore1.clear();
      //     const transaction2 = db.transaction("digitalHuman_table_relation", "readwrite");
      //     const objectStore2 = transaction2.objectStore("digitalHuman_table_relation");
      //     objectStore2.delete(clearDate.id)
      //     console.log("数据已清空"+clearDate.data.saveTable);
      //   };
      // },
    // 创建数据库
    createDB(){
        // 判断用户 indexDB 中是否存在 digitalHuman 数据库
        const request = window.indexedDB.open("digitalHuman");
        // 数据库存在时触发
        request.onsuccess = (event) => {
          console.log("digitalHuman 数据库已存在");
          request.result.close(); // 关闭数据库连接
        };
        // 数据库不存在时触发
        request.onupgradeneeded = (event) => {
          console.log("digitalHuman 数据库不存在，正在创建...");
          const db = event.target.result;
          db.createObjectStore("digitalHuman_table_relation", { keyPath: "id" }); // 创建对象存储
          db.createObjectStore("digitalHuman_1", { keyPath: "id" });
          db.createObjectStore("digitalHuman_2", { keyPath: "id" });
          db.createObjectStore("digitalHuman_3", { keyPath: "id" });
          db.createObjectStore("digitalHuman_4", { keyPath: "id" });
          db.createObjectStore("digitalHuman_5", { keyPath: "id" });
          const transaction = event.target.transaction; // Get the active transaction
          const tableRelationStore = transaction.objectStore("digitalHuman_table_relation");

          const initialData = { id: 0, name: "Initial Entry", description: "Sample data" };
          tableRelationStore.add(initialData); // Add
        };
      },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 学习按钮操作 */
    handleReview(row) {
      if (!row.figureId){
        this.$message.warning('请先选择数字人形象');
        return
      } else {
        this.$router.push({
          path: "/wisdomSchool/learn",
          query: {
            presentationId: row.presentationId,
            figureId: row.figureId,
            dialogueId: row.dialogueId,
          },
        });
      }
    },

    goStudy(row) {
      this.$router.push({
        path: "/wisdomSchool/presentation",
        query: {
          course: row.course,
          major: row.major,
        },
      });

    },
    goStudy2(row) {
      this.$router.push({
        name: 'Presentation',
        params: {
          course: row.course,
        },
      });
    },


    handleSelect() {
      this.dialogFormVisible = true
      getPlatlist().then(res => {
        this.iconList = res.data
      })
      console.log(this.iconList)
    },
    savePlat(id){
      this.plat.figureId = id;
      savePlat(this.plat).then(res=>{
        if(res.code==200){
          this.dialogFormVisible = false;
          this.getList()
        }
      });
    },
    // 下载操作触发
    handleCommand(command, row) {
      switch (command) {
        case "download":
          this.handleDownload(row, 'download');
          break;
        case "downloadPPT":
          this.handleDownload(row, 'downloadPPT');
          break;
        default:
          break;
      }
    },
    handleDownload(row, type) {
      this.download(
        "/file/file/fileDownloadByFileObjectName",
        {
          fileObjectName: type == 'download' ? row.speechdraftFileId : row.presentationFileId,
        },
        `${type == 'download' ? row.speechdraftOriginName : row.pptOriginName}`

      );
    }

  },
};
</script>
