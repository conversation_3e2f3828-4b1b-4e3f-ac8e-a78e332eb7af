<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="dataSetInfoList" border>
      <el-table-column type="index" />
      <el-table-column label="知识点" prop="keyword" :show-overflow-tooltip="true" min-width="500" />
      <el-table-column label="是否掌握" min-width="250">
        <template #default="scope">
          <span v-if="scope.row.isMaster === '1'">掌握</span>
          <span v-else>未掌握</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />
  </div>
</template>

<script>
import { getpoints } from "@/api/explorationCenter/experience.js";

export default {
  name: "ReviewDataSet",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据集详情表格数据
      dataSetInfoList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        majorId: '',
        course: '',
        chapter: '',
        textBookId: ''
      },
      open: false,
      title: '数据集详情',
      form: {}
    };
  },
  activated() {
    this.getList()
  },
  created(){
    this.getList()
  },
  methods: {
    getList() {
      console.log(this.$router)
      this.queryParams.majorId = this.$router.currentRoute.query && this.$router.currentRoute.query.majorId;
      this.queryParams.course = this.$router.currentRoute.query && this.$router.currentRoute.query.course;
      this.queryParams.chapter = this.$router.currentRoute.query && this.$router.currentRoute.query.chapter;
      this.queryParams.textBookId = this.$router.currentRoute.query && this.$router.currentRoute.query.textBookId;
      this.loading = true;
      console.log(this.queryParams)
      getpoints(this.queryParams).then(response => {
        this.dataSetInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },

  }
};
</script>
