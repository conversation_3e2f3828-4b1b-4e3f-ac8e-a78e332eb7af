<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="prompt模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入prompt模板名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="templateTypes">
        <el-select
          v-model="queryParams.templateTypes"
          multiple
          placeholder="请选择类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in filteredDictOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!--      <el-form-item label="创建时间">-->
      <!--        <el-date-picker-->
      <!--          v-model="dateRange"-->
      <!--          style="width: 240px"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          type="daterange"-->
      <!--          range-separator="-"-->
      <!--          start-placeholder="开始日期"-->
      <!--          end-placeholder="结束日期"-->
      <!--        ></el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置
        </el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="promptTemplateList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="模板名称" prop="templateName" width="200"/>
      <el-table-column label="模板内容" prop="templateContent" width="420"/>
      <el-table-column
        label="创建人"
        prop="createBy"
        :show-overflow-tooltip="true"
        width="250"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="prompt模板名称" prop="templateName">
          <el-input
            v-model="form.templateName"
            placeholder="请输入prompt模板名称"
          />
        </el-form-item>
        <!--        <el-form-item label="业务类型" prop="businessTypeId">-->
        <!--          <el-select v-model="form.businessTypeId" placeholder="请选择业务类型">-->
        <!--            <el-option-->
        <!--              style="width: 240px"-->
        <!--              v-for="item in businessTypeOptions"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value"-->
        <!--            >-->
        <!--            </el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="内容" prop="templateContent">
          <el-input
            type="textarea"
            v-model="form.templateContent"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="类型" prop="templateType" v-if="this.releaseStatus">
          <el-select
            v-model="form.templateType"
            placeholder="请选择类型"
          >
            <el-option
              v-for="dict in filteredDictOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <div style="text-align: center">
          <span style="color: red; font-size: 18px"
          >注: 涉及需要填充的信息可以用{}代替</span
          >
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getPromptTemplateList,
    getPromptTemplate,
    addPromptTemplate,
    updatePromptTemplate,
    delPromptTemplate,

  } from "@/api/promptEngineering/promptTemplate";

  export default {
    name: "promptTemplate",
    dicts: ['prompt_template_type'],
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        promptTemplateList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 日期范围
        dateRange: [],
        // 用户角色信息
        roles:this.$store.state.user.roles,

        // 数据范围选项
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          templateName: undefined,
          templateTypes:[],
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          templateName: [
            {required: true, message: "prompt模板名称不能为空", trigger: "blur"},
          ],
          // businessType: [
          //   { required: true, message: "业务类型不能为空", trigger: "blur" },
          // ],
          templateContent: [
            {required: true, message: "内容不能能为空", trigger: "blur"},
          ],
        },
        // businessTypeOptions: [],
      };
    },
    created() {
      this.getList();
      // getBusinessType().then((response) => {
      //   if (response.businessTypes && response.businessTypes.length != 0) {
      //     this.businessTypeOptions = response.businessTypes.map((item) => {
      //       return {
      //         value: item.businessTypeId,
      //         label: item.businessType,
      //         id: item.businessTypeId,
      //       };
      //     });
      //   }
      // });
    },
    computed:{
      filteredDictOptions() {
        // 如果roles中包含"administration"或"admin"，则显示所有选项；否则，只显示'公共'和'私有'
        const specialRoles = ['administration', 'admin'];
        if (this.roles.some(role => specialRoles.includes(role))) {
          return this.dict.type.prompt_template_type;
        } else {
          // 过滤出'公共'和'私有'的选项
          return this.dict.type.prompt_template_type.filter(dict => dict.label === '公共' || dict.label === '私有');
        }
      },

      releaseStatus() {
        const specialRoles = ['administration', 'admin','trainer'];
        if (this.roles.some(role => specialRoles.includes(role))) {
          return true;
        } else {
          return false;
        }
      },

    },
    methods: {


      /** 查询列表 */
      getList() {
        this.loading = true;
        getPromptTemplateList(this.queryParams).then(
          (response) => {
            this.promptTemplateList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.$refs["form"].clearValidate();
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {};
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        (this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          templateName: undefined,
        }),
          this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.id);
        this.single = selection.length != 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "新增";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        getPromptTemplate(row.id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = "修改";
        });
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.form.id != undefined) {
              updatePromptTemplate(this.form).then((response) => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addPromptTemplate(this.form).then((response) => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        const Ids = row.id || this.ids;
        this.$modal
          .confirm("是否确认删除？")
          .then(function () {
            return delPromptTemplate(Ids);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {
          });
      },
    },
  };
</script>
