<template>
  <div class="app-container ck-container">
    <div>
      <el-input v-model="content" type="textarea" :autosize="{ minRows: 6, maxRows: 6}" maxlength="1500"
        show-word-limit />
      <el-button type="text" icon="el-icon-set-up" @click="handleConfigure">优化参数配置<i
          class="el-icon-arrow-down" /></el-button>
      <el-button style="float:right;margin-top: 10px;" type="primary" size="mini" @click="handleOptimize"
        :loading="btnLoading">优化</el-button>
    </div>

    <div class="result-box">
      <div class="container-box left-box">
        <div class="title-box">原始</div>
        <div class="optimize-box">
          <div class="optimize-lable">Prompt：</div>
          <div class="optimize-text">{{ oldPrompt }}</div>
        </div>
        <!-- <div class="inference-box">
          <div class="inference-lable">推理结果：</div>
          <div class="inference-text">{{  }}</div>

        </div> -->
      </div>
      <div class="container-box">
        <div class="title-box">优化后</div>
        <div class="optimize-box">
          <div class="optimize-lable">Prompt：</div>
          <div class="optimize-text new-prompt">{{ newPrompt  }}</div>
        </div>
        <!-- <div class="inference-box">
          <div class="inference-lable">推理结果：</div>
          <div class="inference-text">{{  }}</div>
        </div> -->
      </div>
    </div>

    <el-dialog title="参数配置" width="600px" :visible.sync="dialogVisible">
      <el-form :model="form" label-width="100px">
        <el-form-item label="质量优化" prop="qualityOptFlag">
          <el-switch v-model="form.qualityOptFlag" />
          <div>启用此功能可能会为您提供更好质量的提示词，但也会花费更长的时间进行优化。</div>
        </el-form-item>
        <el-form-item label="缩短提示词" prop="shortPromptFlag">
          <el-switch v-model="form.shortPromptFlag" />
          <div>可以省去“的”、“吧”等含义不强的文本实体，精炼语料内容并降低推理成本</div>
        </el-form-item>
        <el-form-item label="迭代轮次" prop="iterationRound">
          <el-input-number v-model="form.iterationRound" :min="1" :max="2" :precision="0" controls-position="right" />
          <div>反复迭代Prompt优化，更多的迭代次数意味着优化效果越强，但也需要花费更长时间</div>
        </el-form-item>
        <el-form-item label="思维链条" prop="thoughtChainFlag">
          <el-switch v-model="form.thoughtChainFlag" />
          <div>开启后将指引模型拆解Prompt内容，逐步进行推理。建议仅在数学计算、逻辑推理等场景下开启使用</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { optimization, getOptimizationInfo } from '@/api/promptEngineering/promptOptimize.js'

export default {
  name: "PromptOptimize",
  data() {
    return {
      content: '',
      dialogVisible: false,
      form: {
        qualityOptFlag: false,
        shortPromptFlag: false,
        iterationRound: 1,
        thoughtChainFlag: false,
      },
      oldPrompt: '',
      newPrompt: '',
      id: '',
      btnLoading: false,
    };
  },
  created() {

  },
  activated() {

  },
  methods: {
    /**优化参数配置 */
    handleConfigure() {
      this.dialogVisible = true

    },
    /**优化 */
    handleOptimize() {
      const params = {
        content: this.content,
        ...this.form
      }
      this.oldPrompt = this.content
      this.newPrompt = ''
      this.btnLoading = true
      optimization(params).then(res => {
        if (res.code === 200) {
          this.id = res.data.id
          this.getOptimizationInfo()
        }
      }).catch(err => {
        this.btnLoading = false
      })
    },
    getOptimizationInfo() {
      getOptimizationInfo(this.id).then(res => {
        if (res.code === 200 && res.data.processStatus === 1) {
          this.getOptimizationInfo()
        } else if (res.code === 200 && res.data.processStatus !== 1) {
          this.newPrompt = res.data.optimizeContent
          this.btnLoading = false
        } else {
          this.btnLoading = false
        }
      }).catch(err => {
        this.btnLoading = false
      })
    },
    handleSubmit() {
      this.handleCancel()
    },
    handleCancel() {
      this.dialogVisible = false
    }

  }
};
</script>
<style lang="scss" scoped>
.ck-container {
  background-color: #ffffff;
  padding: 12px;
  .result-box {
    margin-top: 32px;
    border-radius: 6px;
    border: 1px solid #d4d6d9;
    overflow: hidden;
    height: 600px;
    display: flex;
    .container-box {
      width: 50%;
      height: 100%;
      .title-box {
        height: 60px;
        padding: 16px;
        font-size: 20px;
        color: #5c5f66;
        line-height: 28px;
        font-weight: 600;
      }
      .optimize-box {
        margin: 0 16px;
        padding: 16px 0;
        // border-bottom: 1px dashed #d4d6d9;
        height: 520px;
        .optimize-lable {
          height: 32px;
          line-height: 32px;
        }
        .optimize-text {
          height: 456px;
          overflow-y: auto;
        }
      }
      .inference-box {
        margin: 0 16px;
        padding: 16px 0;
        height: 260px;
        .inference-lable {
          height: 32px;
          line-height: 32px;
        }
        .inference-text {
          height: 196px;
          overflow-y: auto;
        }
      }
    }
    .left-box {
      border-right: 1px solid #d4d6d9;
    }
  }
}
</style>
