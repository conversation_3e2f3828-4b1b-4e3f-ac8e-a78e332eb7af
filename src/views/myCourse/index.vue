<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="courseList">>
      <el-table-column label="课程" prop="courseName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="学期" prop="term" :show-overflow-tooltip="true" min-width="180">
        <template slot-scope="scope">
          <span>{{ termChange(scope.row.term) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="必修/选修" prop="courseType" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.studentClass?scope.row.courseType=='0'?'必修':'选修' :'-'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.studentClass" size="mini" type="text" icon="el-icon-bank-card"
            @click="handleReview(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getCourseList
} from "@/api/myCourse/myCourse.js";
export default {
  name: "myCourse",
  dicts: ["semester"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程表格数据
      courseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询课程管理列表 */
    getList() {
      this.loading = true;
      getCourseList(this.queryParams).then((response) => {
        this.courseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/myCourse/reviewMyCourse",
        query: {

          courseType: row.courseType,
          term: row.term,
          studentClass: row.studentClass,
          studentId: row.studentId,
          courseName: row.courseName,
          courseClassId: row.id,
          teacherId: row.teacherId,
        },
      });
    },
    termChange(term) {
      return this.selectDictLabelByVal(
        this.dict.type.semester,
        term
      );
    },
  },
};
</script>
