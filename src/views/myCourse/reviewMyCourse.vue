<template>
  <div class="app-container tab-container">

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="通知" name="message">
        <message-list v-if="activeName=='message'" />
      </el-tab-pane>
      <el-tab-pane label="讨论" name="discuss">
        <discuss-list v-if="activeName=='discuss'" />
      </el-tab-pane>
      <el-tab-pane label="分析" name="analysis">
        <student-analysis v-if="activeName=='analysis'" />
      </el-tab-pane>
      <el-tab-pane label="习题" name="exercises">
        <exercises-list v-if="activeName=='exercises'" />
      </el-tab-pane>
      <el-tab-pane label="案例" name="case">
        <case-list v-if="activeName=='case'" />
      </el-tab-pane>
      <el-tab-pane label="视频" name="video">
        <video-list v-if="activeName=='video'" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import MessageList from './components/MessageList.vue'
import DiscussList from './components/DiscussList.vue'
import StudentAnalysis from './components/StudentAnalysis.vue'
import ExercisesList from './components/ExercisesList.vue'
import CaseList from './components/CaseList.vue'
import VideoList from './components/VideoList.vue'
export default {
  name: 'reviewMyCourse',
  components: { MessageList, DiscussList, StudentAnalysis,ExercisesList, CaseList, VideoList },
  data() {
    return {
      activeName: 'message',
    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === 'message') {
      } else if (tab.name === 'discuss') {
      } else if (tab.name === 'analysis') {
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.tab-container {
  padding: 0 10px;
}
</style>
