<!-- 讨论 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="discussList">
      <el-table-column label="标题" prop="title" min-width="180" :show-overflow-tooltip="true" />
      <el-table-column label="内容" prop="content" min-width="240" :show-overflow-tooltip="true" />
      <el-table-column label="创建人" prop="sendName" min-width="80" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getDiscussList,
} from "@/api/myCourse/myCourse.js";
export default {
  name: "DiscussList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      discussList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: this.$route.query && this.$route.query.courseName,
        courseType: this.$route.query && this.$route.query.courseType,
        term: this.$route.query && this.$route.query.term,
        studentClass: this.$route.query && this.$route.query.studentClass,
        studentCourseId: this.$route.query && this.$route.query.courseClassId,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getDiscussList(this.queryParams).then((response) => {
        this.discussList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/myCourse/discussion",
        query: { topicId: row.id },
      });
    },
  },
};
</script>
<style lang="scss">
.ck-input {
  width: 300px;
}
</style>



