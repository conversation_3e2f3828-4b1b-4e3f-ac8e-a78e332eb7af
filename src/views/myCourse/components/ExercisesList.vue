<!-- 资料 -->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="习题名称" prop="exercisesName">
        <el-input v-model="queryParams.exercisesName" placeholder="请输入习题名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="exercisesList">
      <el-table-column label="习题名称" prop="exercisesName" min-width="150" />
      <el-table-column label="习题简介" prop="exercisesSynopsis" min-width="150" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div class="button-group">
            <!-- 预览按钮 -->
            <div class="preview-button-container">
              <a v-if="isPdf(scope.row.filePath)" :href="getPreviewUrl(scope.row.filePath)" target="_blank" class="preview-button">
                <el-button size="mini" type="text" icon="el-icon-view">预览</el-button>
              </a>
              <span v-else-if="isDocx(scope.row.filePath)" class="preview-button">
            <el-button size="mini" type="text" icon="el-icon-view" @click="previewDocxInNewTab(scope.row)">预览</el-button>
          </span>
            </div>

            <!-- 下载和删除按钮 -->
            <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)">下载</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />
  </div>
</template>

<script>
  import {
    getStudentExercisesList,
  } from "@/api/courseManagement/courseManagement.js";
  import { getToken } from "@/utils/auth";
  import mammoth from 'mammoth';
  export default {
    name: "ExercisesList",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        exercisesList: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          courseName: this.$route.query && this.$route.query.courseName,
          teacherId: this.$route.query && this.$route.query.teacherId,
        },
        title: '添加习题',
        form: {},
        htmlContent: '', // 存储转换后的HTML内容
        rules: {
          exercisesName: [
            {required: true, message: '请输入习题名称', trigger: ['blur', 'change']},
          ],
          exercisesSynopsis: [],
        },
        dialogOpen: false,
        btnLoading: false,
        uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
        uploadData: {modeltype: 'courseExercises'},
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        fileList: [],
        disabledConfirmDownload: false,
      };
    },
    created() {
      this.getList();
      this.form.courseName = this.$router.currentRoute.query && this.$router.currentRoute.query.courseName

    },
    activated() {
      this.getList();
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true;
        getStudentExercisesList(this.queryParams).then((response) => {
          this.exercisesList = response.rows
          this.total = response.total;
          this.loading = false;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 判断是否为PDF文件
      isPdf(filePath) {
        return filePath.toLowerCase().endsWith('.pdf');
      },
      // 判断是否为DOCX文件
      isDocx(filePath) {
        return filePath.toLowerCase().endsWith('.docx');
      },
      // 获取预览链接
      getPreviewUrl(filePath) {
        // 返回文件的实际URL路径或API端点用于获取文件流
        return this.getImgUrl(filePath);
      },
      async previewDocxInNewTab(row) {
        try {
          const response = await fetch(this.getPreviewUrl(row.filePath));
          if (!response.ok) throw new Error('Network response was not ok');
          const arrayBuffer = await response.arrayBuffer();
          const result = await mammoth.convertToHtml({arrayBuffer});
          this.openHtmlInNewTab(result.value);
        } catch (error) {
          console.error('Error previewing DOCX:', error);
        }
      },
      // 在新标签页中打开HTML内容，并指定UTF-8字符集
      openHtmlInNewTab(htmlContent) {
        const htmlWithCharset = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <title>文档预览</title>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `;
        const blob = new Blob([htmlWithCharset], {type: 'text/html;charset=UTF-8'});
        const url = URL.createObjectURL(blob);
        window.open(url, '_blank');
        // 释放对象URL资源
        setTimeout(() => URL.revokeObjectURL(url), 30000); // 30秒后释放
      },

      async handleDownload(row) {
        if (!row.filePath) {
          this.$message.error('无法下载习题，请检查链接是否有效');
          return;
        }

        try {
          const response = await fetch(this.getImgUrl(row.filePath));
          if (!response.ok) throw new Error('网络响应不是OK');

          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = row.exercisesName || 'video.mp4'; // 设置下载后的文件名，默认为'video.mp4'
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url); // 清理临时URL
        } catch (error) {
          console.error('下载失败:', error);
          this.$message.error('下载失败，请稍后再试');
        }
      },

      getImgUrl(pptPath) {
        let baseUrl = window.location.origin
        var imgData;
        if (pptPath.includes('/ruoyi/')) {
          // 替换路径中的 ruoyi/ 之前的部分
          const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

          if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
            imgData = 'http://127.0.0.1:9215' + finalPath
            // console.log('Final baseUrl:', baseUrl)
          } else {
            imgData = baseUrl + finalPath
          }
        }
        return imgData;

      },
      handleClose() {
        this.$refs.form.clearValidate()
        this.resetForm("form");
        this.fileList = []
        this.dialogOpen = false
        this.getList()
      },
      validateForm() {
        let validate
        this.$refs.form.validate((valid) => {
          validate = valid
        })
        return validate
      },
      handleUploadSuccess(res, file,) {
        file.fileIds = res.data.id;
        this.fileList.push(res.data.id);
      },
      handleRemove(file, fileList) {
        const findex = this.fileList.map(f => f.indexOf(file.id));
        if (findex > -1) {
          this.fileList.splice(findex, 1);
        }
      },

    },
  };
</script>
<style scoped>
  .button-group {
    display: flex;
    align-items: center; /* 水平居中 */
    justify-content: center; /* 垂直居中 */
    height: 100%; /* 确保它占据整个单元格的高度 */
  }

  .preview-button-container, .download-button-container {
    width: 20%; /* 让容器占据父容器的全部宽度 */
    text-align: center; /* 确保按钮文本居中 */
  }

  .preview-button {
    color: #409EFF;
    text-decoration: none;
    display: inline-block;
    width: 60px; /* 设置固定的宽度 */
    text-align: center;
  }

  /* 确保 a 标签内的 el-button 和直接的 el-button 在视觉上一致 */
  .preview-button > .el-button,
  .download-button-container > .el-button {
    width: 100%;
  }

  /* 移除默认 margin 和 padding */
  .button-group > * {
    margin: 0;
    padding: 0;
  }

  /* 控制按钮之间的间距 */
  .button-group > * + * {
    margin-top: 2px; /* 调整这个值以控制按钮间的间距 */
  }

  /* 如果需要更紧密的排列，可以添加额外的样式 */
  .button-group .el-button {
    padding: 0 5px; /* 调整左右内边距 */
  }
</style>



