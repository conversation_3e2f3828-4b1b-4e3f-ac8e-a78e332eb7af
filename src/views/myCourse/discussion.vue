<template>
  <div class="app-container ck-container">
    <div class="ck-content" style="border-bottom: 1px solid #e8e8e8;">
      <el-row>
        <el-col :span="1">
          <div class="ck-content-title">标题：</div>
        </el-col>
        <el-col :span="23">
          <div class="ck-content-title">{{title}}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="1">
          <div class="ck-content-content">内容：</div>
        </el-col>
        <el-col :span="23">
          <div class="ck-content-content">{{content}}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="ck-message">
      <div class="ck-message-item" v-for="(item, index) in list" :key="index">
        <div class="ck-message-item-title">{{ item.nickName }}</div>
        <div class="ck-message-item-content">{{ item.message }}</div>
        <div style="display: flex;">
          <div class="ck-message-item-time"> {{ item.createTime }} </div>
          <div class="ck-message-item-btn">
            <el-button type="text" style="float: right;padding:0 5px;" size="mini"
              @click="likeOrStomp(item,index,1,'form')">
              <svg-icon :icon-class="item.kikeStep=='1'?'bestFull':'best'" class-name="card-panel-icon" />
            </el-button>
            <el-button type="text" style="float: right;padding: 0 5px;" size="mini"
              @click="likeOrStomp(item,index,2,'form')">
              <svg-icon :icon-class="item.kikeStep=='2'?'badlyFull':'badly'" class-name="card-panel-icon" />
            </el-button>
            <el-button type="text" size="mini" @click="handleResponse(item,'reply','form')">回复</el-button>
            <el-button type="text" size="mini" v-if="item.deleteFlag"
              @click="handleDel(item,'reply','form')">删除</el-button>
          </div>
        </div>

        <div style="margin-bottom: 10px;background: #f1f1f1; border-radius: 5px;">
          <div class="ck-response-item" v-for="(items, index) in item.topicsReplyList" :key="index">
            <span class="ck-response-item-title">{{ items.replyUserName }}</span>
            <span class="ck-response-item-content">回复</span>
            <span class="ck-response-item-title">{{ items.lastReplyUname }}</span>
            <span class="ck-response-item-content">{{ items.replyMsg }}</span>
            <span class="ck-response-item-time">{{ items.createTime }}</span>
            <el-button type="text" size="mini" @click="handleResponse(items,'replyreply','form')">回复</el-button>
            <el-button type="text" size="mini" v-if="items.deleteFlag"
              @click="handleDel(items,'replyreply','form')">删除</el-button>
          </div>
          <div class="ck-response-item-btn">
            <el-button type="text" size="mini" v-if="item.count>3" @click="handleMore(item)">更多回复</el-button>
          </div>
        </div>
      </div>
      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <div class="ck-content" style="border-top: 1px solid #e8e8e8;">
      <el-input v-model="message" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" maxlength="2000"
        placeholder="请输入讨论内容" show-word-limit />
      <el-button style="float: right" type="primary" size="mini" round icon="el-icon-s-promotion" @click="handleSend" />
    </div>
    <el-dialog title="回复" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="回复内容">
          <el-input v-model="form.replyMsg" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" maxlength="2000" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" class="dialogClass" :visible.sync="dialogResponseVisible">
      <div>
        <div v-for="(item, index) in infoList" :key="index">
          <div class="ck-message-item-title">{{ item.nickName }}</div>
          <div class="ck-message-item-content">{{ item.message }}</div>
          <div style="display: flex;">

            <div class="ck-message-item-time">{{ item.createTime }}</div>
            <div class="ck-message-item-btn">
              <el-button type="text" style="float: right;padding:0 5px;" size="mini"
                @click="likeOrStomp(item,index,1,'dialog')">
                <svg-icon :icon-class="item.kikeStep=='1'?'bestFull':'best'" class-name="card-panel-icon" />
              </el-button>
              <el-button type="text" style="float: right;padding: 0 5px;" size="mini"
                @click="likeOrStomp(item,index,2,'dialog')">
                <svg-icon :icon-class="item.kikeStep=='2'?'badlyFull':'badly'" class-name="card-panel-icon" />
              </el-button>
              <el-button type="text" size="mini" @click="handleResponse(item,'reply','dialog')">回复</el-button>
              <el-button type="text" size="mini" v-if="item.deleteFlag"
                @click="handleDel(item,'reply','dialog')">删除</el-button>
            </div>
          </div>
          <div class="ck-response-item" v-for="(items, index) in item.topicsReplyList" :key="index">
            <span class="ck-response-item-title">{{ items.replyUserName }}</span>
            <span class="ck-response-item-content">回复</span>
            <span class="ck-response-item-title">{{ items.lastReplyUname }}</span>
            <span class="ck-response-item-content">{{ items.replyMsg }}</span>
            <span class="ck-response-item-time">{{ items.createTime }}</span>
            <el-button type="text" size="mini" @click="handleResponse(items,'replyreply','dialog')">回复</el-button>
            <el-button type="text" size="mini" v-if="items.deleteFlag"
              @click="handleDel(items,'replyreply','dialog')">删除</el-button>

          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDiscuss, getDiscussLMessageList, addMessage, addReply, getMessage, delMessage, delReply, likeStomp, delLikeStep } from '@/api/myCourse/myCourse.js'
export default {
  name: "Discussion",
  data() {
    return {
      message: '',
      list: [],
      infoList: [],
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        topicId: this.$route.query && this.$route.query.topicId
      },
      dialogFormVisible: false,
      form: {
        replyMsg: ''
      },
      dialogResponseVisible: false,
      dialogFlag: false,
      title: '',
      content: '',
      topicId: this.$route.query && this.$route.query.topicId
    };
  },
  watch: {
    // 监视搜索词变化
    "$route.query.topicId": {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.topicId = newValue
          this.queryParams.topicId = newValue
          this.getList()
          this.getDiscuss()
        }
      },
    },
  },
  created() {
    this.getList()
    this.getDiscuss()
  },
  activated() { },
  methods: {
    getDiscuss() {
      getDiscuss(this.topicId).then(res => {
        this.title = res.data.title
        this.content = res.data.content
      })
    },
    getList() {
      getDiscussLMessageList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.list = res.rows
          this.total = res.total
        }
      })
    },
    handleSend() {
      addMessage({ topicId: this.topicId, message: this.message }).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '已发布您的讨论内容',
            type: 'success'
          });
          this.message = ''
          this.getList()
        }
      })

    },
    handleResponse(item, type, dialog) {
      this.dialogFormVisible = true
      this.dialogFlag = dialog == "dialog" ? true : false
      if (type === 'reply') {
        this.form.msgId = item.msgId
        this.form.lastReplyUid = item.msgUserId
        this.form.lastReplyUname = item.nickName
      } else {
        console.log(item);
        this.form.msgId = item.msgId
        this.form.lastReplyId = item.replyId
        this.form.lastReplyUid = item.replyUserid
        this.form.lastReplyUname = item.replyUserName
      }
    },
    handleClose() {
      this.dialogFormVisible = false
      this.form = {
        replyMsg: ''
      }
    },
    handleSubmit() {
      addReply(this.form).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '回复成功',
            type: 'success'
          });
          if (this.dialogFlag) {
            this.handleMore(this.form)
            this.handleClose()
            this.getList()
          } else {
            this.handleClose()
            this.getList()
          }
        }
      })
    },
    handleMore(item) {
      this.dialogResponseVisible = true
      getMessage(item.msgId).then(res => {
        if (res.code == 200) {
          this.infoList = res.data
        }
      })
    },
    handleDel(row, type, dialog) {
      this.dialogFlag = dialog == "dialog" ? true : false
      this.$modal
        .confirm("是否确认删除此讨论？")
        .then(() => {
          if (type === 'reply') {
            delMessage(row.msgId).then(() => {
              this.dialogResponseVisible = false
              this.getList()
              this.$modal.msgSuccess("删除成功");
            })
          } else {
            if (this.dialogFlag) {
              delReply(row.replyId).then(() => {
                this.handleMore(row)
                this.getList()
                this.$modal.msgSuccess("删除成功");
              })
            } else {
              delReply(row.replyId).then(() => {
                this.getList()
                this.$modal.msgSuccess("删除成功");
              })
            }
          }
        })
    },
    likeOrStomp(item, index, type, dialog) {
      const param = {
        msgId: item.msgId,
        kikeStep: type
      }
      if (dialog == 'form') {
        if (this.list[index].kikeStep && this.list[index].kikeStep == type) {
          delLikeStep(item.msgId).then(res => {
            if (res.code == 200) {
              this.list[index].kikeStep = null
            }
          })

        } else {
          likeStomp(param).then(res => {
            if (res.code == 200) {
              this.list[index].kikeStep = type
            }
          })
        }
      } else {
        if (this.infoList[index].kikeStep && this.infoList[index].kikeStep == type) {
          delLikeStep(item.msgId).then(res => {
            if (res.code == 200) {
              this.infoList[index].kikeStep = null
            }
          })

        } else {
          likeStomp(param).then(res => {
            if (res.code == 200) {
              this.infoList[index].kikeStep = type
              console.log(this.infoList)
            }
          })
        }

      }
    },

  }
};
</script>
<style lang="scss" scoped>
.ck-container {
  padding: 0;
  height: calc(100vh - 84px);
  background: #dbe9f740;
}
.ck-content {
  width: 100%;
  padding: 20px 10%;
  height: 160px;
  overflow: auto;
}
.ck-content-title {
  font-size: 16px;
  color: #666;
  line-height: 24px;
}
.ck-content-content {
  font-size: 14px;
  color: #666;
  line-height: 22px;
}
.ck-message {
  width: 100%;
  padding: 0 10% 10px 10%;
  height: calc(100vh - 410px);
  overflow-y: auto;
}
.ck-message-item {
  padding-top: 10px;
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
}
.ck-message-item-title {
  font-size: 16px;
  font-weight: bold;
}
.ck-message-item-content {
  font-size: 14px;
  color: #666;
  padding: 10px;
}
.ck-message-item-time {
  font-size: 12px;
  color: #666;
  padding: 0 10px;
  line-height: 28px;
}
.ck-message-item-btn {
  width: 50%;
  display: flex;
  justify-content: flex-start;
}
.ck-response-item {
  width: 100%;
  padding: 10px;
}
.ck-response-item-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
}
.ck-response-item-content {
  font-size: 12px;
  color: #666;
  padding: 10px;
}
.ck-response-item-time {
  font-size: 10px;
  color: #666;
  padding: 0 10px;
}
.ck-response-item-btn {
  padding: 0 10px;
  width: 100%;
}
.dialogClass .el-dialog__body {
  padding: 10px 20px;
}
.pagination-container {
  height: 50px;
}
</style>
