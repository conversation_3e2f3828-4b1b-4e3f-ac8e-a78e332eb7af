<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场景名称" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入场景名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item v-if="role.roleId === 102 || role.roleId === 1" label="专业名称" prop="lessonName">-->
<!--        <el-select class="ck-input" v-model="queryParams.major" filterable placeholder="请选择专业">-->
<!--                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">-->
<!--                    </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>

    </el-form>



<!--    <el-table v-loading="loading" :data="scenarioList" >-->
<!--      <el-table-column label="场景图" align="center" prop="imageUrl" >-->
<!--        <template slot-scope="scope" >-->
<!--          <div @click="goToAppScenaio(scope.row)">-->
<!--            <el-tooltip-->
<!--              class="box-item"-->
<!--              effect="light"-->
<!--              content="点击进入场景"-->
<!--              placement="right"-->
<!--            >-->
<!--              <img  :src="scope.row.imageUrl" alt="" style="max-width: 80px; height: 80px;" >-->
<!--            </el-tooltip>-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="场景名称" align="center" prop="applicationName" >-->
<!--        <template slot-scope="scope" >-->
<!--          <span @click="goToAppScenaio(scope.row)" class="blue-font-color">-->
<!--            {{ scope.row.applicationName }}-->
<!--          </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--          >设置背景图</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->

    <div class="box-list">
      <div class="box-item" v-for="item in scenarioList" :key="item.id">
        <el-card class="box-card">
          <div class="image-container" @click="goToAppScenaio(item)">
            <div style="padding: 5px 0 5px 90px;">
              <span @click="goToAppScenaio(item)" class="box-name">{{ item.applicationName }}</span>
            </div>
            <el-image
              :src="item.imageUrl"
              fit="cover"
              style="width: 100%; height: 200px; cursor: pointer;"
              lazy
            ></el-image>
          </div>
          <div style="padding: 10px 0px;" >
            <span  @click="goToAppScenaio(item)" class="blue-font-color">模拟训练</span>
            <span  @click="handleUpdate(item)" class="blue-font-color-right">场景设置</span>
          </div>
        </el-card>
      </div>
      <div v-if="scenarioList.length <= 0" class="no-data">
        暂无数据
      </div>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改应用场景对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body @close="deleteImgByPath()">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="场景名称" prop="applicationName">
          <el-input v-model="formData.applicationName" placeholder="请输入场景名称" />
        </el-form-item>
        <el-form-item label="描述" prop="applicationName">
          <el-input v-model="imageMake"
                    :rows="4"
                    type="textarea"
                    placeholder="请输入场景描述"
                    :maxlength="sizeMaxLength"
                    show-word-limit/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getImage">生成预览图</el-button>
        </el-form-item>

        <div v-if="makeImage && !haveImage">
          <el-skeleton style="width: 240px" animated>
            <template slot="template">
              <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
              <div style="padding: 14px;">
                <el-skeleton-item variant="p" style="width: 50%" />
                <div style="display: flex; align-items: center;">
                  <el-skeleton-item variant="text" style="margin-right: 16px;" />
                  <el-skeleton-item variant="text" style="width: 30%;" />
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
        <img v-if="haveImage" :src="imageBase64" alt=""
             style="max-width: 100%; height: auto;">


        <!--        <el-form-item label="场景背景图路径" prop="imagePath">-->
        <!--          <el-input v-model="formData.imagePath" placeholder="场景背景图路径"   />-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listScenario, getScenario, delScenario, addScenario, updateScenario ,
  getImage,getScenarioImage,getMajor,
  getScenarioS, addScenarioS, updateScenarioS,addOrUpdateScenarioS,deleteImgByPath
} from "@/api/applicationScenario/scenario.js";

import { getDicts } from "@/api/system/dict/data";
import {getAuthenticationInfo} from "@/api/system/authentication";
export default {
  name: "Scenario",
  dicts: ['size_max_length'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用场景表格数据
      scenarioList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationName: null,
        imagePath: null,
        major: null
      },
      options:[
        {  value: '城市管理学' },
        {  value: '1' },
        {  value: 'yingyu' }
      ],
      makeQuery: {
        make: '',
      },
      // 表单参数
      formData: {
        applicationName: '',
        imagePath: '',
      },
      // 表单校验
      rules: {
      },
      role: {

      },
      makeImage:false,
      haveImage:false,
      imageBase64:"",
      lookImages:false,
      imageUrl:'',
      imageMake:"",
      sizeMaxLength:200,//文本字数最大限制

    };
  },
  created() {
    this.getSizeMaxLength('size_max_length');
    this.getList();
    this.getMajor();
    this.getAuthenticationInfo()
  },
  watch: {
    open(newVal) {
      if (!newVal) {
        this.makeImage=false;
        this.haveImage=false;
      }
      this.formData.imagePath=null;
    },
  },
  methods: {
    getAuthenticationInfo(){
      getAuthenticationInfo().then(res=>{
        this.role = res.data;
        console.log("角色验证"+res)
        console.log(res.data)
        this.role.roleId = res.data.roleId;
        console.log("角色验证结果"+this.role.roleId)
      })
    },
    getMajor(){
      getMajor().then(res=>{
        console.log("开始获取专业")
        console.log(res)
        console.log(res.rows.majorName)
        const majorList = res.rows.map(item => ({
          label: item.majorName,
          value: item.id
        }));

        this.options = majorList;
        this.options = majorList;
      })
    },
    getSizeMaxLength(key){
      getDicts(key).then(res=>{
        console.log(res)
        res.data.forEach(item =>{
          if (item.dictLabel === 'scenarioSize') {
            this.sizeMaxLength=Number(item.dictValue)
            console.log(this.sizeMaxLength)
          }
        })
      })
    },

    getImage(){
      if (this.imageMake== null || this.imageMake=="") {
        this.$message.warning('请先输入描述')
        return
      }
      this.makeImage=true;
      this.haveImage=false;
      this.makeQuery.make=this.imageMake;
      getImage(this.makeQuery).then(response => {
        console.log(response)
        if (response.data.imagePath.includes("输入内容包含违禁词")) {
          this.$message.warning(response.data.imagePath)
          this.open=false;
          return
        }
        this.imageBase64=response.data.imageBase64;
        this.formData.imagePath=response.data.imagePath;
        console.log(response.data)
        this.haveImage=true;
      });
    },

    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScenario(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = "场景训练";
      });
    },
    submitForm() {
      this.$refs["formData"].validate(valid => {
        if (valid) {
          if (this.formData.imagePath == "" || this.formData.imagePath == null ) {
            this.$message.warning('请先制作背景图')
            return
          }
          if (this.formData.id != null) {
            addOrUpdateScenarioS(this.formData).then(response => {
              this.$modal.msgSuccess("设置成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    goToAppScenaio(row){
      console.log(row)
      this.$router.push({
        name: "ScenesEmulate", // 使用路由名称，而不是 path
        params: {
          imageUrl: row.imageUrl,
          title: row.applicationName,
          id: row.id,
        }
      });

    },


    getList() {
      this.loading = true;

      listScenario(this.queryParams)
        .then(response => {
          this.total = response.total;
          const promises = response.rows.map(record => {
            const id = record.id;
            console.log(id); // 输出 id

            // 使用 lookImage2 返回的 Promise 获取图像的 Base64
            return this.lookImage(id).then(base64data => {
              // 将 Base64 数据作为 imageUrl 添加到 record
              record.imageUrl = base64data;
              return record;
            });
          });

          // 使用 Promise.all 确保所有异步操作完成后再更新 scenarioList
          return Promise.all(promises);
        })
        .then(scenarioListWithImages => {
          this.scenarioList = scenarioListWithImages;
          this.loading = false;
        })
        .catch(error => {
          console.error("加载列表失败", error);
          this.loading = false;
        });
    },

// 示例的 lookImage2 方法，返回 Base64 格式的图像数据
    lookImage(id) {
      return getScenarioImage(id)
        .then(blob => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);

            reader.onloadend = () => {
              resolve(reader.result); // 返回 base64 字符串
            };

            reader.onerror = (error) => {
              console.error("图像加载失败", error);
              reject(error);
            };
          });
        });
    },


    /** 查询应用场景列表 */
    getList2() {
      this.loading = true;
      listScenario(this.queryParams).then(response => {
        this.scenarioList = response.rows;
        this.total = response.total;
        this.loading = false;

      });
    },

    deleteImgByPath(){
      if (this.formData.imagePath ==null || this.formData.imagePath ==""){
        return;
      }else {
        deleteImgByPath(this.formData).then(response => {
          console.log(response)
        });
      }
    },
    // 取消按钮
    cancel() {
      this.deleteImgByPath()
      this.open = false;
      this.makeImage=false;
      this.haveImage=false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.formData = {
        id: null,
        applicationName: null,
        imagePath: null,
        createBy: null,
        updateBy: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
      this.queryParams.major='';
    },




  }
};
</script>
<style scoped>
.blue-font-color {
  float: left;
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
.blue-font-color-right {
  float: right;
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}

.no-data {
  text-align: center;
  font-size: 18px;
  color: #999;
  padding: 20px;
}


.box-list {
  display: flex;
  flex-wrap: wrap;
  /* 移除 justify-content: space-between; */
  margin-left: -10px; /* 负间距用于抵消第一个和最后一个元素的间距 */
  margin-right: -10px; /* 负间距用于抵消第一个和最后一个元素的间距 */
}

.box-item {
  width: calc(20% - 20px); /* 调整宽度，减去两倍的间距 */
  margin-bottom: 10px;
  margin-left: 10px; /* 设置左右间距 */
  margin-right: 10px; /* 设置左右间距 */
}

.box-card {
  cursor: pointer;
}

.image-container {
  width: 100%;
  height: 160px;
  overflow: hidden;
  cursor: pointer;
}

.box-name {
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
