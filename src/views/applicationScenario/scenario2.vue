<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场景名称" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入场景名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <div class="box-list">
      <div class="box-item" v-for="item in scenarioList" :key="item.id">
        <el-card class="box-card">
          <div style="padding: 5px 0 5px 90px;">
            <span @click="goToAppScenaio(item)" class="box-name">{{ item.applicationName }}</span>
          </div>
          <div class="image-container" @click="goToAppScenaio(item)">
            <el-image
              :src="item.imageUrl"
              fit="cover"
              style="width: 100%; height: 200px; cursor: pointer;"
              lazy
            ></el-image>
          </div>

        </el-card>
      </div>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改应用场景对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="formData" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="场景名称" prop="applicationName">
          <el-input v-model="formData.applicationName" placeholder="请输入场景名称" />
        </el-form-item>
        <el-form-item label="描述" prop="applicationName">
          <el-input v-model="imageMake" placeholder="请输入场景名称" :maxlength="sizeMaxLength"
                    show-word-limit/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getImage">生成预览图</el-button>
        </el-form-item>

        <div v-if="makeImage && !haveImage">
          <el-skeleton style="width: 240px" animated>
            <template slot="template">
              <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
              <div style="padding: 14px;">
                <el-skeleton-item variant="p" style="width: 50%" />
                <div style="display: flex; align-items: center;">
                  <el-skeleton-item variant="text" style="margin-right: 16px;" />
                  <el-skeleton-item variant="text" style="width: 30%;" />
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
        <img v-if="haveImage" :src="imageBase64" alt=""
             style="max-width: 100%; height: auto;">


        <!--        <el-form-item label="场景背景图路径" prop="imagePath">-->
        <!--          <el-input v-model="formData.imagePath" placeholder="场景背景图路径"   />-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listScenario, getScenario, delScenario, addScenario, updateScenario ,
  getImage,getScenarioImage,
  getScenarioS, addScenarioS, updateScenarioS,addOrUpdateScenarioS
} from "@/api/applicationScenario/scenario.js";
import { getDicts } from "@/api/system/dict/data";
export default {
  name: "Scenario",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用场景表格数据
      scenarioList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationName: null,
        imagePath: null,
      },
      makeQuery: {
        make: '',
      },
      // 表单参数
      formData: {
        applicationName: '',
        imagePath: '',
      },
      // 表单校验
      rules: {
      },
      makeImage:false,
      haveImage:false,
      imageBase64:"",
      lookImages:false,
      imageUrl:'',
      imageMake:"",
      sizeMaxLength:500,//文本字数最大限制

    };
  },
  created() {
    this.getSizeMaxLength('size_max_length');
    this.getList();
  },
  watch: {
    open(newVal) {
      if (!newVal) {
        this.makeImage=false;
        this.haveImage=false;
      }
      this.formData.imagePath=null;
    },
  },
  methods: {
    getSizeMaxLength(key){
      getDicts(key).then(res=>{
        console.log(res)
        res.data.forEach(item =>{
          if (item.dictLabel === 'scenarioSize') {
            this.sizeMaxLength=Number(item.dictValue)
            console.log(this.sizeMaxLength)
          }
        })
      })
    },
    getImage(){
      if (this.imageMake== null || this.imageMake=="") {
        this.$message.warning('请先输入描述')
        return
      }
      this.makeImage=true;
      this.haveImage=false;
      this.makeQuery.make=this.imageMake;
      getImage(this.makeQuery).then(response => {
        console.log(response)
        this.imageBase64=response.data.imageBase64;
        this.formData.imagePath=response.data.imagePath;
        console.log(response.data)
        this.haveImage=true;
      });
    },

    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScenario(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = "修改应用场景";
      });
    },
    submitForm() {
      this.$refs["formData"].validate(valid => {
        if (valid) {
          if (this.formData.imagePath == "" || this.formData.imagePath == null ) {
            this.$message.warning('请先制作背景图')
            return
          }
          if (this.formData.id != null) {
            addOrUpdateScenarioS(this.formData).then(response => {
              this.$modal.msgSuccess("设置成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    goToAppScenaio(row){
      console.log(row)
      this.$router.push({
        name: "ScenesEmulate", // 使用路由名称，而不是 path
        params: {
          imageUrl: row.imageUrl,
          title: row.applicationName,
          id: row.id,
        }
      });

    },


    getList() {
      this.loading = true;

      listScenario(this.queryParams)
        .then(response => {
          this.total = response.total;
          const promises = response.rows.map(record => {
            const id = record.id;
            console.log(id); // 输出 id

            // 使用 lookImage2 返回的 Promise 获取图像的 Base64
            return this.lookImage(id).then(base64data => {
              // 将 Base64 数据作为 imageUrl 添加到 record
              record.imageUrl = base64data;
              return record;
            });
          });

          // 使用 Promise.all 确保所有异步操作完成后再更新 scenarioList
          return Promise.all(promises);
        })
        .then(scenarioListWithImages => {
          this.scenarioList = scenarioListWithImages;
          this.loading = false;
        })
        .catch(error => {
          console.error("加载列表失败", error);
          this.loading = false;
        });
    },

// 示例的 lookImage2 方法，返回 Base64 格式的图像数据
    lookImage(id) {
      return getScenarioImage(id)
        .then(blob => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);

            reader.onloadend = () => {
              resolve(reader.result); // 返回 base64 字符串
            };

            reader.onerror = (error) => {
              console.error("图像加载失败", error);
              reject(error);
            };
          });
        });
    },


    /** 查询应用场景列表 */
    getList2() {
      this.loading = true;
      listScenario(this.queryParams).then(response => {
        this.scenarioList = response.rows;
        this.total = response.total;
        this.loading = false;

      });
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.makeImage=false;
      this.haveImage=false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.formData = {
        id: null,
        applicationName: null,
        imagePath: null,
        createBy: null,
        updateBy: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },




  }
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}





.box-list {
  display: flex;
  flex-wrap: wrap;
  /* 移除 justify-content: space-between; */
  margin-left: -10px; /* 负间距用于抵消第一个和最后一个元素的间距 */
  margin-right: -10px; /* 负间距用于抵消第一个和最后一个元素的间距 */
}

.box-item {
  width: calc(20% - 20px); /* 调整宽度，减去两倍的间距 */
  margin-bottom: 10px;
  margin-left: 10px; /* 设置左右间距 */
  margin-right: 10px; /* 设置左右间距 */
}

.box-card {
  cursor: pointer;
}

.image-container {
  width: 100%;
  height: 160px;
  overflow: hidden;
  cursor: pointer;
}

.box-name {
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
/*.box-list {*/
/*  display: flex;*/
/*  flex-wrap: wrap;*/
/*  justify-content: space-between;*/
/*}*/

/*.box-item {*/
/*  width: calc(20% - 10px); !* 根据需要调整宽度 *!*/
/*  margin-bottom: 10px;*/
/*}*/

/*.box-card {*/
/*  cursor: pointer;*/
/*}*/

/*.image-container {*/
/*  width: 100%;*/
/*  height: 160px;*/
/*  overflow: hidden;*/
/*  cursor: pointer;*/
/*}*/

/*.box-name {*/
/*  font-size: 16px;*/
/*  color: #333;*/
/*  white-space: nowrap;*/
/*  overflow: hidden;*/
/*  text-overflow: ellipsis;*/
/*  cursor: pointer;*/
/*}*/
</style>
