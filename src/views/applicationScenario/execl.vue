<template>
  <div class="app-container">
    <div class="upload-container">
      <el-upload class="upload-demo ck-input" drag :action="uploadPptUrl"
                 :data="uploadPptData" :headers="headers" :on-success="handleUploadSuccess" :on-remove="handleRemovePpt"
                 :file-list="fileList" accept=".xls,.xlsx" :limit="1">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">支持Excel文件上传，仅能上传一个文件</div>
      </el-upload>
    </div>


<!--      <div class="selection">-->
<!--        <el-radio-group v-model="selectedRole" class="role-selection">-->
<!--          <el-radio :label="0">老师</el-radio>-->
<!--          <el-radio :label="1">学生</el-radio>-->
<!--          <el-radio :label="2">研究生</el-radio>-->
<!--        </el-radio-group>-->
<!--      </div>-->

      <div class="selection">
        <el-radio-group v-model="selectedRole" class="role-selection">
          <el-radio
            v-for="option in options"
            :key="option.value"
            :label="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </div>



    <div class="checkbox-group">
      <el-checkbox v-model="autoRegister">自动注册</el-checkbox>
      <el-checkbox v-model="autoAuth">自动认证</el-checkbox>
    </div>

    <div class="button-group">
      <el-button type="primary" class="submit-button" @click="submitData">提交</el-button>
<!--      <el-button type="danger" icon="el-icon-delete" @click="clearDeletedUsers">清理已删除用户</el-button>-->

      <el-button
        type="danger"
        icon="el-icon-delete"
        :loading="isLoading"
        :disabled="isLoading"
        @click="clearDeletedUsers2">
        {{ isLoading ? '清理中...' : '清理已删除用户' }}
      </el-button>

      <div class="progress-overlay" v-if="isLoading">
        <el-progress type="dashboard" :percentage="percentage2" :color="colors">
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage }}%</span>
            <span class="percentage-label">Progressing</span>
          </template>
        </el-progress>
      </div>
    </div>


    <el-progress
      :text-inside="true"
      v-if="uploading"
      :percentage="progressPercentage"
      striped
      striped-flow
      status="success"
      :stroke-width="40"
      :format="formatText"
      :color="colors"
    />


  </div>
</template>

<script>
import { saveExecl,delFalg } from "@/api/applicationScenario/scenario.js";
import { getToken } from "@/utils/auth";

export default {
  name: "Scenario",
  data() {
    return {
      uploadPptUrl: process.env.VUE_APP_BASE_API + "/file/file/upload",
      uploadPptData: { modeltype: 'execl' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      options: [
        { value: 0, label: '老师' },
        { value: 1, label: '学生' },
        { value: 2, label: '研究生' },
      ],
      colors : [
        { color: '#f56c6c', percentage: 10 },
        { color: '#f6965c', percentage: 20 },
        { color: '#f7c04b', percentage: 30 },
        { color: '#e8b43b', percentage: 40 },
        { color: '#5cb3ab', percentage: 50 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#42bdb2', percentage: 70 },
        { color: '#1989fa', percentage: 80 },
        { color: '#5778d9', percentage: 90 },
        { color: '#6f7ad3', percentage: 100 }
      ],
      fileId:'',
      fileList: [],
      filePath: '',  // 上传后的文件路径
      selectedRole: '',  // 学生或老师选择
      autoRegister: false,  // 自动注册选择
      autoAuth: false,  // 自动认证选择
      uploading: false,  // 是否正在上传
      progressPercentage: 0,  // 进度条百分比
      percentage2:0,
      isLoading: false,  // 添加加载状态
    };
  },
  watch:{
    autoAuth(newVal,oldVal){
      if (newVal){
        this.autoRegister=true;
      }
    }
  },
  methods: {

    clearDeletedUsers() {
      // 调用清理已删除用户的逻辑
      delFalg().then(res => {
        if (res.code === 200) {
          this.$message.success('已清理已删除用户');
        }
      })

      // 这里添加清理逻辑，比如调用 API
    },

    async clearDeletedUsers2() {
      this.isLoading = true;  // 开始加载

      this.percentage2 = 0;  // 重置百分比
      const interval = setInterval(() => {
        // 每次增加10，达到或超过100时停止
        this.percentage2 = Math.min(this.percentage2 + 10, 100);

        // 检查是否达到100
        if (this.percentage2 >= 100) {
          clearInterval(interval);  // 停止计时器
        }
      }, 200);


      // 模拟清理过程
      setTimeout(() => {
        // 清理逻辑...
        delFalg().then(res => {
          if (res.code === 200) {
            this.$message.success('已清理已删除用户');
          }
        })
        this.isLoading = false;  // 清理完成
      }, 2500); // 假设清理需要2秒
    },

    formatText(percentage) {
      return `进度:${percentage}%`;
    },

    handleRemovePpt(){

    },

    // 上传成功后的回调函数
    handleUploadSuccess(response, file) {
      //console.log(response)
      this.fileId=response.data.id
      this.filePath = response.data.preview;  // 假设上传成功返回路径
      console.log(this.filePath)
    },

    // 模拟数据提交进度
    async submitData() {
      if (!this.filePath || this.filePath == '') {
        this.$message.warning('请上传文件')
        return
      }

      if (this.selectedRole === '') {
        this.$message.warning('请选择学生或者老师')
        return
      }

      this.uploading = true;  // 开始上传，显示进度条
      this.progressPercentage = 0;

      // 模拟进度变化
      const interval = setInterval(() => {
        if (this.progressPercentage < 90) {
          this.progressPercentage += 10;
        }
      }, 500);

      try {



        const queryData = {
          fileId: this.fileId,
          filePath: this.filePath,
          isStu :this.selectedRole,
          isAutoRegister: this.autoRegister ? 0 : 1 ,
          isAutoAuth: this.autoAuth ? 0 : 1 ,
        };

       console.log(queryData)

        await saveExecl(queryData).then(res => {
          if (res.code === 200) {
            this.$message.success('导入成功')
          }
        })
        // 成功后将进度条设置为100%
        this.progressPercentage = 100;
        this.$message.success('提交成功');
      } catch (error) {
        this.$message.error('提交失败');
      } finally {
        // 清除定时器，延迟一段时间后隐藏进度条
        clearInterval(interval);
        setTimeout(() => {
          this.uploading = false;
        }, 500);
      }
    }
  }
};
</script>

<style scoped>
/*.button-group {*/
/*  display: flex;*/
/*  gap: 10px; !* 添加按钮间的间距 *!*/
/*}*/

/*.el-button[disabled] {*/
/*  background-color: #d3d3d3; !* 禁用状态下的背景色 *!*/
/*  color: #fff; !* 禁用状态下的文字颜色 *!*/
/*}*/

.button-group {
  position: relative; /* 使绝对定位的子元素相对于按钮组定位 */
}

.progress-overlay {
  position: absolute; /* 绝对定位 */
  top: 0; /* 距离顶部0 */
  left: 400px; /* 距离左侧0 */
  right: 0; /* 距离右侧0 */
  bottom: 0; /* 距离底部0 */
  display: flex; /* 使进度条居中 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  z-index: 10; /* 确保在按钮上方 */
  pointer-events: none; /* 不阻止按钮的点击事件 */
}

.submit-button {
  margin-bottom: 10px; /* 按钮间距 */
}





.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40px 20px 20px 20px;
}

.upload-container {
  width: 400px;
  margin-bottom: 30px;
}

.upload-demo {
  width: 100%;
  text-align: center;
  background-color: #fff;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;
  font-size: 18px;
}

.upload-demo:hover {
  border-color: #409eff;
}

.selection {
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
}

.checkbox-group {
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
}

.submit-button {
  width: 200px;
  font-size: 18px;
}
.el-progress {
  margin-top: 20px;
  width: 400px; /* 保持宽度大一些 */
}
</style>
