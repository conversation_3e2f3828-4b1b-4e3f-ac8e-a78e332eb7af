<!-- <template>
  <div class="app-container ck-container">
    <img :src="myPortraitImg" alt="myPortraitImg" style="width:50%;height:50%;display: block;margin: 0 auto;" />
    <div
      style="width:50%;height:50%;display: block;margin: 20px auto;line-height: 40px;font-size: 22px;text-align: left;">
      1、提供针对性教学建议和辅导资源；<br>2、提供互动性强、趣味性高的学习内容；<br>3、定制个性化学习计划</div>
  </div>
</template>

<script>
import myPortraitImg from "@/assets/images/wisdom.png";
export default {
  name: "PromotionSchool",
  data() {
    return {
      myPortraitImg: myPortraitImg,
    };
  },
};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
}
</style> -->

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程" prop="lessonName">
        <el-input v-model="queryParams.course" placeholder="请输入课程名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="数据库编号"  prop="id" v-if="false"></el-table-column>
      <el-table-column label="课程名称" prop="course" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="课件名称" prop="presentationName" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="课件id" prop="presentationId" v-if="false" />
      <el-table-column label="课程状态" prop="status" v-if="false"  />
      <el-table-column label="课程进度" prop="progress" :show-overflow-tooltip="true"  />
      <el-table-column label="创建人" prop="createUserName" :show-overflow-tooltip="true"  />
      <el-table-column label="创建时间" prop="createTime" :show-overflow-tooltip="true"  />
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button :disabled="scope.row.commitStatus == 1" size="mini" type="text" icon="el-icon-position"
                     @click="handleClass(scope.row)">去学习</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getPromotionClass" />

  </div>
</template>
<script>
import { getPromotionClass,addOrUpdateStudentStudyRecord,getPlatUserFigureByUserId } from "@/api/promotionSchool/promotionClass.js"

export default {
  name: "promotionSchool",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [{ }],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        course: "",
      },
      // 提交学习数据
      dataParam:{
        presentationId: "",
        digital_human_img:"",
        status:"",
        progress:"",
        createUser:"",
        fileLocalPath:"",
      },
      // 数字人id
      figureId: "",
    };
  },
  created() {
    this.getPromotionClass();
    this.getPlatUserFigureByUserId();
  },
  activated() {

  },
  methods: {
    getPlatUserFigureByUserId(){
      getPlatUserFigureByUserId().then((res) => {
        this.figureId = res.data.figureId
      })
    },
    getPromotionClass(){
      // console.log(this.queryParams)
      getPromotionClass(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // console.log(this.queryParams.lessonName)
      this.queryParams.pageNum = 1;
      this.getPromotionClass();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.course = "";
      this.handleQuery();
    },
    handleClass(row) {
      // this.dataParam.presentationId = row.presentationId
      // //this.dataParam.digital_human_img = row.digital_human_img
      // this.dataParam.status = row.status
      // this.dataParam.progress = row.progress
      // this.dataParam.createUser = row.createUser
      // //this.dataParam.fileLocalPath = row.fileLocalPath
      // addOrUpdateStudentStudyRecord(this.dataParam)
      const figureId = this.figureId
      this.$router.push({
        path: "/wisdomSchool/learn",
        query: {
          presentationId: row.presentationId,
          figureId: figureId,
        }
      });
    },
  },
};
</script>

