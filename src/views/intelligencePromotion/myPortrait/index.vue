<template>
  <div class="container">
    <div class="grid-container">
      <!-- 盒子1 -->
      <div class="grid-item personal-info">
        <el-alert
          title="个人信息"
          type="info"
          :closable="false"
          class="title"

        />
        <el-descriptions direction="vertical" border style=" color: black; height: 450px;">
          <el-descriptions-item :rowspan="1" :width="140" label="Photo" align="center">
            <el-image style="width: 50px; height: 50px" :src="userInfo.avatar" />
          </el-descriptions-item>
          <el-descriptions-item label="姓名">
            <h6 class="list">{{ studentInfo.student_name }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            <h6 class="list">{{ studentInfo.sex }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="学院">
            <h6 class="list">{{ colle_name }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="学制">
            <h6 class="list">{{ studentInfo.educational_system }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="学籍状态">
            <h6 class="list">{{ studentInfo.student_status }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="年级">
            <h6 class="list">{{ major_name }}（{{ studentInfo.current_grade }}）</h6>
          </el-descriptions-item>
          <el-descriptions-item label="工作电话">
            <h6 class="list">{{ userInfo.phonenumber }}</h6>
          </el-descriptions-item>
          <el-descriptions-item label="邮件地址">
            <h6 class="list">{{ userInfo.email }}</h6>
          </el-descriptions-item>
        </el-descriptions>
        <!-- 装饰效果 -->
        <div class="demo-progress" style="height: 100px;">
          <el-progress :percentage="progressValue1" :color="progressColor1" @progress-click="updateProgress(70)" />
          <el-progress :percentage="progressValue2" :color="progressColor2" @progress-click="updateProgress(100)" />
          <el-progress :percentage="progressValue3" :color="progressColor3" @progress-click="updateProgress(100)" />
          <el-progress :percentage="progressValue4" :color="progressColor4" @progress-click="updateProgress(100)" />
          <el-progress :percentage="progressValue5" :color="progressColor5" @progress-click="updateProgress(100)" />
          <h5 style="margin: 0; color: #909399">学分课程完成进度</h5>
        </div>
      </div>

      <!-- 盒子2 -->
      <div class="grid-item personal-evaluation">
        <el-alert title="个人综合评价" type="info" :closable="false" class="title" />
        <div class="radar-chart" ref="radarChart">
          <!-- 雷达图将在这里显示 -->
        </div>
      </div>

      <!-- 盒子3 -->
      <div class="grid-item core-competencies">
        <el-alert title="综合素质评估" type="info" :closable="false" class="title" />
        <div class="core-competencies-chart" ref="coreCompetenciesChart">
          <!-- 核心能力结构图将在这里显示 -->
        </div>
      </div>

      <!-- 网格4 -->
      <div class="grid-item credits-completion">
        <el-alert title="学分完成情况" type="info" :closable="false" class="title" />
        <div id="circle-chart" class="progress-chart" ref="circleChart">
          <!-- 圆形进度图将在这里显示 -->
        </div>
      </div>
    </div>
  </div>
</template>

  <script>
import * as echarts from "echarts";
import { getToken } from "@/utils/auth";
import { mapActions } from "vuex";
import { Row } from "element-ui";

export default {
  name: "StudentProfile",
  data() {
    return {
      statisticsTime: "",
      a: "",
      analysisData: "", //当前学期分析长字符串
      colle_name: "给排水与建筑环境学院", //学院id
      major_name: "给排水工程", //专业id
      yuce_fenxi: "", //预测分析文本
      userInfo: {
        userId: "",
        studentId: "",
        avatar:
          "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
        phonenumber: "",
        email: "",
      },
      studentInfo: {
        student_name: "",
        current_grade: "",
        sex: "",
        class_id: "",
        educational_system: "5", //学制
        student_status: "在校", //学籍状态
        major_id: "", //专业id
        colle_id: "", //学院i
        
      },
      averaryinfo: {
        xu_total_credits: "",
        xu_completed_credits: "",
      },
      chartDataFromBackend: [], // 初始化为数组
      radarData0FromBackend: [],
      radarData00FromBackend: [],
      radarData1FromBackend: [],
      radarData11FromBackend: [],
      progressValue1: 70, // 第一个进度条的百分比
      progressValue2: 100, // 第二个进度条的百分比
      progressValue3: 100, // 第三个进度条的百分比
      progressValue4: 100, // 第四个进度条的百分比
      progressValue5: 100, // 第五个进度条的百分比
      progressColor1: "#406EFF", // 第一个进度条的颜色
      progressColor2: "lightblue", // 第二个进度条的颜色
      progressColor3: "lightgreen", // 第三个进度条的颜色
      progressColor4: "orange", // 第四个进度条的颜色
      progressColor5: "red", // 第五个进度条的颜色
    };
  },
  mounted() {
    this.initialize();
    // this.initChart();
  },
  methods: {
    ...mapActions(["GetInfo"]),
    // 初始化方法，确保按顺序执行操作
    async initialize() {
      try {
        await this.fetchAndCommitUserInfo(); // 获取用户信息（studentId）
        if (this.userInfo.studentId) {
          await this.getStudentInfo(this.userInfo.studentId); // 获取学生信息
          await this.getcollege(this.studentInfo.colle_id); // 获取学院信息
          await this.getmajor(this.studentInfo.major_id); // 获取专业信息
          await this.getChartInfo(this.userInfo.studentId); // 获取图表数据
          // await this. generateAnalysis();
          console.log("所需数据存储情况：",localStorage.getItem("analysisData"));
          await this.getChartInfo1(this.studentInfo.class_id); // 获取图表数据
          this.updateProgressBars(); // 更新进度条
          this.initRadarChart0(); // 初始化雷达图0
          this.initCircleChart(); // 初始化圆形进度图
          this.initCoreCompetenciesChart(); // 初始化雷达图1
        } else {
          console.error("未能获取有效的 studentId");
        }
      } catch (error) {
        console.error("初始化过程中发生错误:", error);
      }
    },
    // 1.1 获取用户信息(userId)
    async fetchAndCommitUserInfo() {
      try {
        const result = await this.GetInfo();
        console.log("用户信息获取成功:", result);
        if (result && result.user) {
          // 检查 result 是否存在以及是否有 sysUser 属性
          const { user } = result;
          this.userInfo.userId = user.user_id || ""; // 提供默认值以防为空
          this.userInfo.studentId = user.studentId;
          console.log("这里是学生id:", this.userInfo.studentId);
          this.userInfo.phonenumber = user.phonenumber || "未知";
          this.userInfo.avatar = user.avatar || "/static/image/avatar.png"; // 默认头像
          this.userInfo.email = user.email || "未知";
        } else {
          console.error("返回结果中缺少 userInfo 或 user");
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
        throw error; // 抛出错误以便外部捕获
      }
    },
    // 1.2 个人信息进度条部分
    fetchData() {
      const data = this.chartDataFromBackend;
      this.progressValue1 = data[0].value;
      this.progressValue2 = data[1].value;
      this.progressValue3 = data[2].value;
      this.progressValue4 = data[3].value;
      this.progressValue5 = data[4].value;
    },
    // 2. 获取学生数据
    async getStudentInfo(stuid) {
      try {
        const url =
          process.env.VUE_APP_BASE_API + `/system/studentinfo/${stuid}`;

        console.log("这里是学生id型参:", stuid);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("向后端请求学生数据成功返回数据为：", res);

        if (res.code === 200 && res.data) {
          const student = res.data;
          this.studentInfo = {
            student_name: student.studentName, // 学生姓名
            current_grade: student.currentGrade, // 当前年级
            sex: student.sex === "0" ? "男" : "女", // 性别
            class_id: student.classId, // 班级ID
            educational_system: student.educationalSystem || "未知", // 教育制度
            student_status: student.studentStatus === "1" ? "在校" : "不在校", // 学籍状态
            colle_id: student.colleId, // 院系ID
            major_id: student.majorId, // 专业ID
          };
          // localStorage.setItem("studentInfo", JSON.stringify(this.studentInfo));

          // console.log("学生信息存储到临时存储:", localStorage.getItem("studentInfo"));
          console.log("班级id获取到:", student.colleId, student.majorId);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },
    // 2.1 获取学院信息
    async getcollege(id) {
      try {
        const url = process.env.VUE_APP_BASE_API + `/test/collegeInfo/${id}`;
        console.log("这里是学院id型参:", id);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("学院表", res);

        if (res.code === 200 && res.data) {
          const coll = res.data;
          this.colle_name = coll.colleName;
          console.log("班级id获取到:", this.colle_name);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },
    // 2.2 获取专业信息
    async getmajor(id) {
      try {
        const url = process.env.VUE_APP_BASE_API + `/test/majorInfo/${id}`;
        console.log("这里是学院id型参:", id);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("专业表", res);

        if (res.code === 200 && res.data) {
          const ma = res.data;
          this.major_name = ma.majorName;
          console.log("班级id获取到:", this.major_name);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },

    async getChartInfo(stuid) {
      try {
        console.log("getChartInfo");
        const url =
          process.env.VUE_APP_BASE_API + `/test/mySelfPortrait/${stuid}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log(data, "向后端请求图表个人评分数据成功");
        const transformedData = this.transformData(data);
        this.chartDataFromBackend = transformedData[0];
        this.radarData0FromBackend = transformedData[1];
        this.radarData1FromBackend = transformedData[2];

        const a = data.data;
        this.averaryinfo.xu_total_credits = a.xuTotalCredits || "";
        console.log("总分", this.averaryinfo.xu_total_credits);
        this.averaryinfo.xu_completed_credits = a.xuCompletedCredits || "0";
        console.log("完成分", this.averaryinfo.xu_completed_credits);
        this.yuce_fenxi=a.yuceFenxi, // 预测分析
        // console.log("预测分析文本", this.yuce_fenxi);
        this.statisticsTime= a.statisticsTime// 统计时间
      } catch (error) {
        console.error("获取图表数据失败:", error.message);
        console.error("错误堆栈信息:", error.stack);
        throw error;
      }
    },
    async getChartInfo1(classId) {
      try {
        console.log("getChartInfo1");
        const url = process.env.VUE_APP_BASE_API + `/test/Average/${classId}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log(data, "向后端请求图表平均分数据成功");
        const transformedData1 = this.transformData1(data);
        this.radarData00FromBackend = transformedData1[0];
        this.radarData11FromBackend = transformedData1[1];
      } catch (error) {
        console.error("获取图表数据失败:", error.message);
        console.error("错误堆栈信息:", error.stack);
        throw error;
      }
    },
    transformData(data) {
      if (!data || !data.data) {
        console.error("No data provided for transformation");
        return [[], [], []]; // 返回默认的空数组
      }

      const circleChart = [
        {
          value: data.data.yuGeneral || 0,
          name: "通识教育课程",
        },
        {
          value: data.data.yuFoundation || 0,
          name: "公共基础课程",
        },
        {
          value: data.data.yuCore || 0,
          name: "专业核心课程",
        },
        {
          value: data.data.yuElective || 0,
          name: "选修课程",
        },
        {
          value: data.data.yuExperiment || 0,
          name: "实践与实验课程",
        },
      ];

      const radarChart0 = [
        {
          name: "学业成绩",
          value: data.data.laScore || 0,
        },
        {
          name: "作业质量",
          value: data.data.laHomework || 0,
        },
        {
          name: "知识掌握",
          value: data.data.laMastery || 0,
        },
        {
          name: "课堂表现",
          value: data.data.laPerformance || 0,
        },
        {
          name: "他人评价",
          value: data.data.laEvaluation || 0,
        },
        {
          name: "个人特长",
          value: data.data.laTalent || 0,
        },
        {
          name: "专业兴趣",
          value: data.data.laInterest || 0,
        },
      ];

      const radarChart1 = [
        {
          name: "道德品质与公民素养",
          value: data.data.lbMorality || 0,
        },
        {
          name: "审美与表现能力",
          value: data.data.lbAesthetics || 0,
        },
        {
          name: "运动和健康",
          value: data.data.lbHealth || 0,
        },
        {
          name: "学习能力",
          value: data.data.lbLearning || 0,
        },
        {
          name: "交流合作与实践",
          value: data.data.lbCooperation || 0,
        },
      ];

      return [circleChart, radarChart0, radarChart1];
    },
    transformData1(data) {
      if (!data || !data.data) {
        console.error("No data provided for transformation1");
        return [[], []]; // 返回默认的空数组
      }

      const radarChart00 = [
        {
          name: "学业成绩",
          value: data.data.laScore || 0,
        },
        {
          name: "作业质量",
          value: data.data.laHomework || 0,
        },
        {
          name: "知识掌握",
          value: data.data.laMastery || 0,
        },
        {
          name: "课堂表现",
          value: data.data.laPerformance || 0,
        },
        {
          name: "他人评价",
          value: data.data.laEvaluation || 0,
        },
        {
          name: "个人特长",
          value: data.data.laTalent || 0,
        },
        {
          name: "专业兴趣",
          value: data.data.laInterest || 0,
        },
      ];

      const radarChart11 = [
        {
          name: "道德品质与公民素养",
          value: data.data.lbMorality || 0,
        },
        {
          name: "审美与\n表现能力",
          value: data.data.lbAesthetics || 0,
        },
        {
          name: "运动和健康",
          value: data.data.lbHealth || 0,
        },
        {
          name: "学习能力",
          value: data.data.lbLearning || 0,
        },
        {
          name: "交流合作\n与实践",
          value: data.data.lbCooperation || 0,
        },
      ];
      return [radarChart00, radarChart11];
    },
    // 进度条
    updateProgressBars() {
      const data = this.chartDataFromBackend;
      if (data.length >= 5) {
        this.progressValue1 = data[0].value;
        this.progressValue2 = data[1].value;
        this.progressValue3 = data[2].value;
        this.progressValue4 = data[3].value;
        this.progressValue5 = data[4].value;
      } else {
        console.error("chartDataFromBackend 数据不足");
      }
    },
    // 分析数据
    // // 构建预测页面所需的学生基本信息
    // async generateAnalysis() {
    //   const dataObj = {
    //     colle_name: this.colle_name, // 学院名称*
    //     major_name: this.major_name, // 专业名称*
    //     studentInfo: {
    //       studentId: this.userInfo.studentId, // 学号*
    //       student_name: this.studentInfo.student_name,
    //       current_grade: this.studentInfo.current_grade, // 当前年级
    //       sex: this.studentInfo.sex, // 性别*
    //       class_id: this.studentInfo.class_id, // 班级id*（2020税收学1班）
    //       educational_system: this.studentInfo.educational_system, // 学制
    //       student_status: this.studentInfo.student_status, // 学生状态*
    //       yuce_fenxi: this.yuce_fenxi, // 预测分析*
    //       statisticsTime: this.statisticsTime, // 统计时间*

    //     },
    //     averaryinfo: {
    //       xu_total_credits: this.averaryinfo.xu_total_credits,
    //       xu_completed_credits: this.averaryinfo.xu_completed_credits,
    //     },
    //   };
    //   // 输出日志
    //   console.log("所需数据存入本地存储:", dataObj);

    //   // 存储到 localStorage
    //   this.a = dataObj; // 这里应该是 dataObj，而不是 originalData
    //   localStorage.setItem("all", JSON.stringify(this.a)); 
    // },

    // 雷达表1
    initRadarChart0() {
      const radarChartDom = this.$refs.radarChart;
      if (!radarChartDom) {
        console.error("Radar chart container not found");
        return;
      }

      const radarChart = echarts.init(radarChartDom);
      const option = {
        title: {},
        tooltip: {},
        legend: {
          left: "right",
        },
        radar: {
          axisName: {
            formatter: "{value}",
            color: "#2A5CC0",
            fontSize: "20",
          },
          splitArea: {
            areaStyle: {
              color: ["white", "white", "white", "white"],
              shadowColor: "rgba(64, 150, 255, 0.1)",
              shadowBlur: 50,
            },
          },
          indicator: this.radarData0FromBackend.map((item) => ({
            name: item.name,
            max: 100,
          })),
        },
        series: [
          {
            //   name: "个人能力评估",
            type: "radar",
            data: [
              {
                value: this.radarData00FromBackend.map((item) => item.value),
                name: "班级平均分",
                areaStyle: {
                  color: "rgba(64, 150, 255, 0.8)",
                },
                lineStyle: {
                  color: "rgba(64, 150, 255, 0.8)",
                  width: 2,
                },
                label: {
                  show: false,
                  formatter: function (params) {
                    return params.value;
                  },
                  textStyle: {
                    color: "rgb(7, 36, 99)",
                    fontSize: 10,
                  },
                },
              },
              {
                value: this.radarData0FromBackend.map((item) => item.value),
                name: "我的分数",
                areaStyle: {
                  color: "lightgreen",
                },
                lineStyle: {
                  color: "lightgreen",
                  width: 2,
                },
                label: {
                  show: false,
                  formatter: function (params) {
                    return params.value;
                  },
                  textStyle: {
                    color: "black",
                    fontSize: 15,
                  },
                },
              },
            ],
          },
        ],
      };

      radarChart.setOption(option);
    },
    // 圆环表
    initCircleChart() {
      const chartDom = document.getElementById("circle-chart");
      if (!chartDom) {
        console.error("Circle chart container not found");
        return;
      }

      const myChart = echarts.init(chartDom);
      const a = this.averaryinfo.xu_completed_credits;
      const b = this.averaryinfo.xu_total_credits;
      myChart.setOption({
        title: {
          show: true,
          text: `CREDITS\n${a}/${b}`,
          textStyle: {
            fontSize: 30,
            fontWeight: "bold",
            color: "black",
          },
          left: "center",
          top: "center",
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "60%"],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 10,
              borderColor: "white",
              borderWidth: 6,
            },
            label: {
              fontSize: 20,
              color: "#2A5CC0",
              show: true,
            },
            labelLine: {
              show: true,
              length: 30,
              length2: 60,
            },
            data: this.chartDataFromBackend.map((item) => ({
              ...item,
            })),
          },
        ],
      });
    },
    // 雷达表2
    initCoreCompetenciesChart() {
      const chartDom = this.$refs.coreCompetenciesChart;
      if (!chartDom) return;

      const myChart = echarts.init(chartDom);

      const option = {
        color: ["#67F9D8", "#FFE434", "#56A3F1", "#FF917C"],
        title: {
          //   text: "个人能力评估",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "bottom",
        },
        radar: [
          {
            indicator: this.radarData1FromBackend.map((item) => ({
              name: item.name,
              max: 100,
            })),
            center: ["50%", "50%"],
            radius: 120,
            startAngle: 90,
            splitNumber: 4,
            shape: "circle",
            axisName: {
              formatter: "{value}",
              color: "#2A5CC0",
              fontSize: 20,
            },
            splitArea: {
              areaStyle: {
                color: ["#77EADF", "#26C3BE", "#64AFE9", "#428BD4"],
                shadowColor: "rgba(64, 150, 255, 0.9)",
                shadowBlur: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "rgba(211, 253, 250, 0.8)",
              },
            },
            axislabel: {},
            splitLine: {
              lineStyle: {
                color: "rgba(211, 253, 250, 0.8)",
              },
            },
          },
        ],
        series: [
          {
            type: "radar",
            emphasis: {
              lineStyle: {
                width: 4,
              },
            },
            data: [
              {
                value: this.radarData11FromBackend.map((item) => item.value),
                name: "班级平均分",
                areaStyle: {
                  //   color: "lightgray",
                },
                label: {
                  show: false,
                  formatter: function (params) {
                    return params.value;
                  },
                  textStyle: {
                    color: "black",
                    fontSize: 10,
                  },
                },
              },
              {
                value: this.radarData1FromBackend.map((item) => item.value),
                name: "我的分数",
                areaStyle: {
                  color: "lightgreen",
                },
                label: {
                  show: false,
                  formatter: function (params) {
                    return params.value;
                  },
                  textStyle: {
                    color: "black",
                    fontSize: 13,
                  },
                },
              },
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
  <style scoped>
  body {
  margin: 0;
  padding: 0;
width: 100%;
justify-content: center;
  align-items: center;
}


.container {
  width: 100%;
  height: 91vh;
  background-color: #f8f8f9;
  overflow: hidden;
  padding: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 修改网格容器样式，四个网格等分 */
.grid-container {
  /* margin-top: 20px; */
  width: 100%;
  height: 88vh;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr); /* 修改为1fr保证行高一致 */
  gap: 20px;
  background-color: #f8f8f9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  padding: 0;
  margin-top: 0;
}

/* grid-item 内部样式 */
.grid-item {
  background: white;
  border-radius: 5px;
  box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.3);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标题样式 */
.title {
  height: 38.5px;
  font-size: larger;
  font-weight: bold;
  background-color: #2a5cca;
  backdrop-filter: blur(1px);
  color: #e6ebf5;
  margin-bottom: 10px;
}

/* 让图表容器填满网格项剩余区域 */
.radar-chart,
.core-competencies-chart,
.progress-chart {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 列表文字样式 */
.list {
  margin: 1px;
  color: rgb(42, 92, 202);
  font-size: 15px;
}

</style>
  