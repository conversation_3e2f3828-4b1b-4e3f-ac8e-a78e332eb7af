<template>
  <div class="container">
    <div class="overlay">
      <div class="scrollable-content">
        <!-- 卡片内容 -->
        <div
          class="card"
          style="display: flex; flex-direction: row; align-items: center"
        >
          <!-- 图标 -->
          <div>

            <img :src="imgUrl"
             alt="分析图标"
              style="max-width: 250px; height: auto"
            />
          </div>
          <!-- 标题和描述 -->
          <div style="width: 65%">
            <h2
              class="text-2xl font-bold text-center"
              style="
                height: 30px;
                margin-top: 20px;
                font-size: 30px;
                color: rgb(42, 92, 202);
              "
            >
              <strong>学生综合分析预测</strong>
            </h2>
            <p
              class="text-gray-600 text-center"
              style="color: rgb(42, 92, 202); font-size: 18px"
            >
              ——基于个人信息的智能预测与规划——
            </p>
          </div>
          <div style="width: 400px; padding-left: 100px">
            <el-button
              type="danger"
              round
              :loading="loading"
              @click="getyucefenxi1"
            >
              开始预测
            </el-button>
          </div>
        </div>

        <!-- 学生基本信息 -->
        <div class="card">
          <el-alert
            title="个人基本信息"
            type="info"
            :closable="false"
            class="title"
            style="
              height: 45px;
              font-size: larger;
              font-weight: bold;
              background-color: #2a5cca;
              color: white;
            "
          />
          <div style="margin-top: 20px; padding: 30px; width: 100%">
            <span style="margin-right: 30px; font-size: 18px"
              ><strong
                >学生姓名：{{ studentInfo.student_name || "暂无数据" }}</strong
              ></span
            >
            <span style="margin-right: 30px; font-size: 18px"
              ><strong>性别：{{ studentInfo.sex || "暂无数据" }}</strong></span
            >
            <span style="margin-right: 30px; font-size: 18px"
              ><strong
                >年级：{{ major_name || "暂无数据" }}（{{
                  studentInfo.current_grade
                }}）</strong
              ></span
            >
            <span style="margin-right: 30px; font-size: 18px"
              ><strong>学院：{{ colle_name || "暂无数据" }}</strong></span
            >
            <span style="margin-right: 30px; font-size: 18px"
              ><strong
                >学制：{{
                  studentInfo.educational_system || "暂无数据"
                }}年</strong
              ></span
            >
            <span style="margin-right: 30px; font-size: 18px"
              ><strong
                >学籍状态：{{
                  studentInfo.student_status || "暂无数据"
                }}</strong
              ></span
            >
          </div>
        </div>

        <!-- 预测结果 -->
        <div
          v-if="analysisText"
          class="card scrollable-card"
          style="
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            margin-bottom: 20px;
          "
        >
          <div>
            <el-alert
              title="预测分析结果"
              type="info"
              :closable="false"
              class="title"
              style="
                width: 100%;
                height: 45px;
                font-size: larger;
                font-weight: bold;
                background-color: #2a5cca;
                color: white;
              "
            />
            <div
              style="
                width: 100%;
                padding-left: 50px;
                padding-right: 100px;
                height: 100px;
                margin-top: 10px;
              "
            >
              <h3
                class="text-xl font-semibold"
                style="float: left; margin-top: 30px"
              >
                <strong>分析结果：</strong>
              </h3>
              <h3 style="float: right; margin-top: 30px; color: red">
                分析创建时间：{{ studentInfo.statisticsTime || "暂无数据" }}
              </h3>
            </div>
            <div style="padding-left: 50px">
              <div
                class="markdown-content"
                v-html="markedContent(studentInfo.yuce_fenxi || '暂无数据')"
                style="
                  font-size: 20px;
                  font-family: 'Gill Sans', 'Gill Sans MT', Calibri,
                    'Trebuchet MS', sans-serif;
                "
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { mapActions } from "vuex"; //引入mapActions
import "quill/dist/quill.snow.css"; // 导入quill的样式
import marked from "marked"; // 引入marked库

export default {
  name: "AnalyticalPrediction", // 组件名称
  data() {
    return {
      imgUrl: require('@/assets/images/kkk.png'),
      loading: false,
      colle_name: "",
      major_name: "",
      userInfo: {
        userId: "",
        studentId: "",
      },
      studentInfo: {
        student_name: "",
        current_grade: "",
        sex: "",
        class_id: "",
        educational_system: "",
        student_status: "",
        colle_id: "",
        major_id: "",
        yuce_fenxi: "",
        statisticsTime: "",
      },
      analysisText: true, // ✅ 改为布尔值，控制预测区域是否显示
      averaryinfo: {
        xu_total_credits: "",
        xu_completed_credits: "",
      },
    };
  },
  mounted() {
    console.log("王怀煜: 无页面测试第3版 ");
    // 初始化时加载本地数据，并尝试请求预测结果
    this.initialize();
  },
  methods: {
    ...mapActions(["GetInfo"]),
    // 初始化方法，包含异步操作
    async initialize() {
      await this.fetchAndCommitUserInfo();
      await this.getStudentInfo(this.userInfo.studentId); // 获取学生信息
      this.$forceUpdate();
      await this.getcollege(this.studentInfo.colle_id); // 获取学院信息
      await this.getmajor(this.studentInfo.major_id); // 获取专业信息
      console.log("专业id", this.studentInfo.major_id);
      await this.getChartInfo1(this.userInfo.studentId);
      console.log(this.userInfo.studentId);
      // await this.getyucefenxi1();
    },
    // 1.1 获取用户信息(userId)
    async fetchAndCommitUserInfo() {
      try {
        const result = await this.GetInfo();
        console.log("用户信息获取成功:", result);
        if (result && result.user) {
          // 检查 result 是否存在以及是否有 sysUser 属性
          const { user } = result;
          this.userInfo.userId = user.user_id || ""; // 提供默认值以防为空
          this.userInfo.studentId = user.studentId;
          console.log("这里是学生id:", this.userInfo.studentId);
          this.userInfo.phonenumber = user.phonenumber || "未知";
          this.userInfo.avatar = user.avatar || "/static/image/avatar.png"; // 默认头像
          this.userInfo.email = user.email || "未知";
        } else {
          console.error("返回结果中缺少 userInfo 或 user");
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
        throw error; // 抛出错误以便外部捕获
      }
    },
    // 2. 获取学生数据
    async getStudentInfo(stuid) {
      try {
        const url =
          process.env.VUE_APP_BASE_API + `/system/studentinfo/${stuid}`;

        console.log("这里是学生id型参:", stuid);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("向后端请求学生数据成功返回数据为：", res);

        if (res.code === 200 && res.data) {
          const student = res.data;
          this.studentInfo = {
            student_name: student.studentName, // 学生姓名
            current_grade: student.currentGrade, // 当前年级
            sex: student.sex === "0" ? "男" : "女", // 性别
            class_id: student.classId, // 班级ID
            educational_system: student.educationalSystem || "未知", // 教育制度
            student_status: student.studentStatus === "1" ? "在校" : "不在校", // 学籍状态
            colle_id: student.colleId, // 院系ID
            major_id: student.majorId, // 专业ID
          };
          // localStorage.setItem("studentInfo", JSON.stringify(this.studentInfo));

          // console.log("学生信息存储到临时存储:", localStorage.getItem("studentInfo"));
          console.log("班级id获取到:", student.colleId, student.majorId);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },
    // 2.1 获取学院信息
    async getcollege(id) {
      try {
        const url = process.env.VUE_APP_BASE_API + `/test/collegeInfo/${id}`;
        console.log("这里是学院id型参:", id);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("学院表", res);

        if (res.code === 200 && res.data) {
          const coll = res.data;
          this.colle_name = coll.colleName;
          console.log("班级id获取到:", this.colle_name);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },
    // 2.2 获取专业信息
    async getmajor(id) {
      try {
        const url = process.env.VUE_APP_BASE_API + `/test/majorInfo/${id}`;
        console.log("这里是学院id型参:", id);
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("专业表", res);

        if (res.code === 200 && res.data) {
          const ma = res.data;
          this.major_name = ma.majorName;
          console.log("班级id获取到:", this.major_name);
        } else {
          console.error("返回结果中缺少 student 数据", res);
          throw new Error("返回结果中缺少 student 数据");
        }
      } catch (error) {
        console.error("没找到学生信息", error);
        throw error;
      }
    },
    // 获取预测文本数据
    async getChartInfo1(stuid) {
      try {
        console.log("getChartInfo:初始化预测结果与创建时间"); //非首次登录显示上次查询结果
        const url =
          process.env.VUE_APP_BASE_API + `/test/mySelfPortrait/${stuid}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("初始化预测结果与创建时间", data);
        const a = data.data;
        (this.studentInfo.yuce_fenxi = a.yuceFenxi), // 预测分析
          // console.log("预测分析文本", this.yuce_fenxi);
          (this.studentInfo.statisticsTime = a.statisticsTime); // 统计时间
        // 更新 singleData 内的预测信息
        if (a.yuceFenxi) {
          this.studentInfo.yuce_fenxi = a.yuceFenxi;
          console.log("初始化预测分析结果成功", a.yuceFenxi);
          // 显示预测分析页面
        } else {
          this.studentInfo.yuce_fenxi = "";
          console.log("预测分析结果初始化失败", a.yuceFenxi);
        }

        if (a.statisticsTime) {
          const dt = new Date(a.statisticsTime);
          // 获取本地时间字符串
          if (!isNaN(dt.getTime())) {
            this.studentInfo.statisticsTime = dt
              .toLocaleString()
              .replace("T", " ")
              .slice(0, 19);
            console.log("初始化创建时间成功", a.statisticsTime);
          } else {
            this.studentInfo.statisticsTime = "";
            console.log("初始化创建时间为空或异常", a.statisticsTime);
          }
        } else {
          this.studentInfo.statisticsTime = "";
        }

        if (a.xuTotalCredits % a.xuCompletedCredits) {
          this.averaryinfo.xu_total_credits = a.xuTotalCredits;
          this.averaryinfo.xu_completed_credits = a.xuCompletedCredits;
        } else {
          this.averaryinfo.xu_total_credits = "";
          this.averaryinfo.xu_completed_credits = "";
        }
        console.log("总分", this.averaryinfo.xu_total_credits);
        console.log("完成分", this.averaryinfo.xu_completed_credits);
        console.log("王怀煜: 无页面测试第3版 ");
      } catch (error) {
        console.error("获取初始化预测分析与创建时间失败:", error.message);
        throw error;
      }
    },
    // 按钮点击，触发预测接口（建议更新数据而不是刷新页面）
    async getyucefenxi1() {
      console.log("点击按钮新增请求开始执行");
      this.loading = true;
      try {
        const url =
          process.env.VUE_APP_BASE_API +
          `/test/mySelfPortrait/yucefenxi/${this.userInfo.studentId}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("向后端请求学生数据成功返回数据为:", res);
        this.loading = false;
        // 更新预测数据，不刷新整个页面
        await this.getChartInfo1(this.userInfo.studentId);
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.loading = false;
        throw error;
      }
    },

    // 文本处理：将 Markdown 转为 HTML
    markedContent(content) {
      if (content != null) {
        const htmlContent = marked.parse(content);
        const tempElement = document.createElement("div");
        tempElement.innerHTML = htmlContent;
        function removeEmptyTextNodes(node) {
          if (node.nodeType === Node.TEXT_NODE && !node.textContent.trim()) {
            node.parentNode.removeChild(node);
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            for (let i = node.childNodes.length - 1; i >= 0; i--) {
              removeEmptyTextNodes(node.childNodes[i]);
            }
          }
        }
        const blockElements = [
          "p",
          "ol",
          "ul",
          "li",
          "h1",
          "h2",
          "h3",
          "h4",
          "h5",
          "h6",
          "pre",
          "blockquote",
          "hr",
        ];
        blockElements.forEach((tagName) => {
          const elements = tempElement.querySelectorAll(tagName);
          elements.forEach((element) => {
            removeEmptyTextNodes(element);
          });
        });
        return tempElement.innerHTML;
      } else {
        return "暂无数据";
      }
    },
  },
};
</script>
<style scoped>
body {
  margin: 0;
  padding: 0;
  width: 100%;
}

.container {
  position: relative;
  width: 100%;
  height: 92vh;
  background-color: #f8f8f9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.overlay {
  position: absolute;
  border-radius: 10px;
  width: 100%;
  height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.scrollable-content {
  border-radius: 10px;
  height: 90vh;
  width: 100%;
  max-width: 99%;
  overflow-y: auto;
  height: 100%;
  pointer-events: auto;
  padding: 20px;
  padding-bottom: 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: #f8f8f9;
}

.card {
  background-color: #ffffff;
  backdrop-filter: blur(1px);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  z-index: 1;
  color: rgb(42, 92, 202);
  width: 98%;
  max-width: 100%;
  background-color: #ffffff;
  box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.3);
}

.scrollable-card {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
