<template>
    <div class="container">
      <div class="overlay">
        <div class="scrollable-content">
          <!-- 卡片内容 -->
          <div class="card" style="display: flex; flex-direction: row; align-items: center">
            <!-- 图标 -->
            <div>
              <img src="/logo/kkk.png" alt="分析图标" style="max-width: 250px; height: auto" />
            </div>
            <!-- 标题和描述 -->
            <div style="width: 65%">
              <h2 class="text-2xl font-bold text-center" style="height: 30px; margin-top: 20px; font-size: 30px; color: rgb(42, 92, 202)">
                <strong>学生综合分析预测</strong>
              </h2>
              <p class="text-gray-600 text-center" style="color: rgb(42, 92, 202); font-size: 18px">
                ——基于个人信息的智能预测与规划——
              </p>
            </div>
            <div style="width: 400px; padding-left: 100px">
              <el-button type="danger" round :loading="loading" @click="getyucefenxi">
                开始预测
              </el-button>
            </div>
          </div>
  
          <!-- 学生基本信息 -->
          <div
            v-if="singleData && singleData.studentInfo && singleData.studentInfo.student_name"
            class="card"
          >
            <div>
              <el-alert
                title="个人基本信息"
                type="info"
                :closable="false"
                class="title"
                style="height: 45px; font-size: larger; font-weight: bold; background-color: #2a5cca; color: white"
              />
            </div>
            <div style="margin-top: 20px; padding: 30px; width: 100%">
              <span style="margin-right: 30px; font-size: 18px">
                <strong>学生姓名：{{ singleData.studentInfo.student_name }}</strong>
              </span>
              <span style="margin-right: 30px; font-size: 18px">
                <strong>性别：{{ singleData.studentInfo.sex }}</strong>
              </span>
              <span style="margin-right: 30px; font-size: 18px">
                <strong>年级：{{ singleData.major_name }}（{{ singleData.studentInfo.current_grade }}）</strong>
              </span>
              <span style="margin-right: 30px; font-size: 18px">
                <strong>学院：{{ singleData.colle_name }}</strong>
              </span>
              <span style="margin-right: 30px; font-size: 18px">
                <strong>学制：{{ singleData.studentInfo.educational_system }}年</strong>
              </span>
              <span style="margin-right: 30px; font-size: 18px">
                <strong>学籍状态：{{ singleData.studentInfo.student_status }}</strong>
              </span>
            </div>
          </div>
  
          <!-- 预测结果 -->
          <div
            v-if="analysisText"
            class="card scrollable-card"
            style="display: flex; flex-direction: column; justify-content: space-between; height: 100%; margin-bottom: 20px"
          >
            <div>
              <!-- title -->
              <el-alert
                title="预测分析结果"
                type="info"
                :closable="false"
                class="title"
                style="width: 100%; height: 45px; font-size: larger; font-weight: bold; background-color: #2a5cca; color: white"
              />
              <!-- 创建时间 -->
              <div
                style="width: 100%; padding-left: 50px; padding-right: 100px; height: 100px; margin-top: 10px"
              >
                <h3 class="text-xl font-semibold" style="float: left; margin-top: 30px">
                  <strong>分析结果：</strong>
                </h3>
                <h3 style="float: right; margin-top: 30px; color: red">
                  分析创建时间：{{ singleData.studentInfo.statisticsTime }}
                </h3>
              </div>
              <!-- 结果内容 -->
              <div style="padding-left: 50px">
                <div
                  class="markdown-content"
                  v-html="markedContent(singleData.studentInfo.yuce_fenxi)"
                  style="font-size: 20px; font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script>
  import * as echarts from "echarts";
  import { getToken } from "@/utils/auth";
  import { mapActions } from "vuex";
  import "quill/dist/quill.snow.css";
  import marked from 'marked';
  
  export default {
    data() {
      return {
        loading: false,
      // 使用 singleData 统一管理所有数据
      singleData: {
        studentInfo: {
          studentId: "",
          student_name: "",
          current_grade: "",
          sex: "",
          class_id: "",
          educational_system: "",
          student_status: "",
          major_id: "",
          colle_id: "",
          yuce_fenxi: "",
          statisticsTime: "" // 统一用 camelCase
        },
        colle_name: "",
        major_name: "",
      },
      analysisText: "首次登录,需等待一分钟..."
    };
    },
    mounted() {
      // 使用 async/await 来处理异步操作
      this.initialize();
    },
  
    methods: {
      ...mapActions(["GetInfo"]),
      // 初始化方法，包含所有的异步操作
      async initialize() {
      await this.fetchLocalData();
      console.log("本地缓存中的数据：", this.singleData);
      // 检查是否有 studentId，再发请求
      if(this.singleData.studentInfo.studentId) {
        await this.getChartInfo(this.singleData.studentInfo.studentId);
      }
    },
    async fetchLocalData() {
      try {
        const storedData = localStorage.getItem("all");
        if (storedData) {
          // 假设存储的数据格式与 singleData 格式一致
          this.singleData = JSON.parse(storedData);
        } else {
          console.warn("No analysis data found in localStorage");
        }
      } catch (error) {
        console.error("Failed to parse analysisData from localStorage:", error);
      }
    },
    async getChartInfo(stuid) {
      try {
        console.log("getChartInfo查询更新开始执行");
        const url = process.env.VUE_APP_BASE_API + `/test/mySelfPortrait/${stuid}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("index.vue数据库查询预测文本刷新成功", data);
        const a = data.data;
        // 更新 singleData 内的数据
        this.singleData.studentInfo.yuce_fenxi = a.yuceFenxi;
        if (a.statisticsTime) {
          const dt = new Date(a.statisticsTime);
          if (!isNaN(dt.getTime())) {
            this.singleData.studentInfo.statisticsTime = dt.toISOString().replace('T', ' ').slice(0, 19);
          } else {
            this.singleData.studentInfo.statisticsTime = '';
          }
        } else {
          this.singleData.studentInfo.statisticsTime = '';
        }
      } catch (error) {
        console.error("获取刷新数据失败:", error.message);
        throw error;
      }
    },
    // 文本处理方法保持不变
    markedContent(content) {
      if (content != null) {
        const htmlContent = marked.parse(content);
        const tempElement = document.createElement('div');
        tempElement.innerHTML = htmlContent;
        function removeEmptyTextNodes(node) {
          if (node.nodeType === Node.TEXT_NODE && !node.textContent.trim()) {
            node.parentNode.removeChild(node);
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            for (let i = node.childNodes.length - 1; i >= 0; i--) {
              removeEmptyTextNodes(node.childNodes[i]);
            }
          }
        }
        const blockElements = ['p', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'blockquote', 'hr'];
        blockElements.forEach(tagName => {
          const elements = tempElement.querySelectorAll(tagName);
          elements.forEach(element => {
            removeEmptyTextNodes(element);
          });
        });
        return tempElement.innerHTML;
      } else {
        return "";
      }
    },
    async getyucefenxi() {
      console.log("getyucefenxi按钮新增请求开始执行");
      this.loading = true;
      try {
        const url = process.env.VUE_APP_BASE_API + `/test/mySelfPortrait/yucefenxi/${this.singleData.studentInfo.studentId}`;
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const res = await response.json();
        console.log("向后端请求学生数据成功返回数据为: ", res);
        this.loading = false;
        // 建议只更新数据而不是刷新整个页面
        await this.getChartInfo(this.singleData.studentInfo.studentId);
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.loading = false;
        throw error;
      }
    }
  }
  }
  </script>
  
  <style>
  body {
    margin: 0;
    padding: 0;
  width: 100%;
  justify-content: center;
    align-items: center;
  }
  
  .container {
    /* position: relative; */
    width: 100%;
    height: 92vh;
    /* margin-right: 50px; */
    background-color: #f8f8f9;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    /* 防止整个容器滚动 */
  }
  
  .overlay {
    position: absolute;
    border-radius: 10px;
    width: 100%;
    height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    /* box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.2); */
    /* 确保背景不会干扰点击 */
  }
  
  .scrollable-content {
    /* box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.5); */
    border-radius: 10px;
    /* margin-top: 20px; */
    height: 90vh;
    width: 100%;
    /* 根据需要调整宽度 */
    max-width: 99%;
    /* 最大宽度限制 */
    overflow-y: auto;
    /* 允许垂直滚动 */
    height: 100%;
    /* 使内容高度占满整个可用空间 */
    pointer-events: auto;
    /* 确保卡片可以点击 */
    /* margin-top: 20px; */
    padding: 20px;
    /* 内边距 */
    padding-bottom: 0px;
    box-sizing: border-box;
    /* 包括内边距和边框 */
    display: flex;
    flex-direction: column;
    align-items: center;
  
    width: 100%;
    /* 确保卡片宽度为父容器的100% */
    background-color: #f8f8f9;
  }
  
  .card {
    background-color: rgb(255, 255, 255, 1);
    backdrop-filter: blur(1px);
    padding: 20px;
    border-radius: 10px;
    /* box-shadow: 0 4px 15px rgb(255, 255, 255); */
    margin-bottom: 20px;
    z-index: 1;
    /* 确保卡片在其上方 */
    color: rgb(42, 92, 202);
    /* margin-bottom: 50px; */
    width: 98%;
    /* 确保卡片宽度为父容器的100% */
    max-width: 100%;
    /* 最大宽度限制 */
    /* margin-right: 50px; */
    background-color: #ffffff;
    box-shadow: 5px 5px 10px 2px rgba(0, 0, 0, 0.3);
  }
  
  /* 第三个卡片的高度自由，内容超出时可滚动 */
  .scrollable-card {
    max-height: calc(100vh - 200px);
    /* 减去其他卡片和间距的高度 */
    overflow-y: auto;
    /* 允许垂直滚动 */
    white-space: pre-wrap;
    /* 保留空白符并允许换行 */
    word-wrap: break-word;
    /* 长单词或URL自动换行 */
  }
  </style>