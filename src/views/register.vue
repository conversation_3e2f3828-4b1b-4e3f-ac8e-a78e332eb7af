<template>
  <div :class="prodFLag === true ? 'register' : 'register-test'">
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-forms"
      style="position: relative;">
      <!--      <h3 class="title">注册</h3>-->
      <h3 class="title">
        <img :src="prodFLag === true ? url1: url2"
          style="position: absolute;top: -1px;left: -1px;width:502px;height: auto;object-fit: cover;z-index: 1;border-radius:7px 7px 0px 0px;">

        <img :src="prodFLag === true ? url3: url4"
          style="position: relative; z-index: 2; width: 250px; height: 100px; margin-top: 88px;">
      </h3>
      <el-form-item prop="username" style="margin-left: 55px;">
        <el-input v-model="registerForm.username" type="text" auto-complete="off" placeholder="用户名"
          class="input-with-select">
          <svg-icon slot="prepend" icon-class="user" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="password" style="margin-left: 55px;">
        <el-input v-model="registerForm.password" type="password" auto-complete="off" placeholder="密码"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="password" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword" style="margin-left: 55px;">
        <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="off" placeholder="确认密码"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="password" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled" style="margin-left: 55px;">
        <el-input v-model="registerForm.code" auto-complete="off" placeholder="验证码" style="width: 43%"
          @keyup.enter.native="handleRegister" class="input-with-select">
          <svg-icon slot="prepend" icon-class="validCode" class="el-input__icon input-icon" style="background-color: #FFFFFF;
                      color: #6dade2;
                      width: 28px;
                      height: 32px;
                      border-radius: 50%;
                      overflow: hidden;
                      display: flex;
                      justify-content: center;
                      align-items: center; " />
        </el-input>
        <div class="register-code">
          <img :src="codeUrl" @click="getCode" class="register-code-img" />
        </div>
      </el-form-item>
      <el-form-item prop="haveRead" style="margin:0px 0px 25px 55px;">
        <el-checkbox-group v-model="registerForm.haveRead">
          <el-checkbox label="haveRead">已阅读并同意
            <a href="/policy/privacy-policy.html" target="_blank" rel="noopener noreferrer">隐私政策</a> 和 <a
              href="/policy/user-agreement.html" target="_blank" rel="noopener noreferrer">用户协议</a></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item style="width: 100%;align-items: center;">
        <el-button :loading="loading" size="medium" type="primary"
          style="width: 30%;margin-bottom: 10px;margin-left: 155px;border-radius: 20px;background: #5a9dd8;"
          @click.native.prevent="handleRegister">
          <span v-if="!loading">注 册</span>
          <span v-else>注 册 中...</span>
        </el-button>
        <div style="text-align: right; margin-left: auto;">
          <router-link class="link-type" :to="'/login'">使用已有账户登录</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank"><span>{{ icpNum }}</span></a><br />
      <span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;客户电话: 0531-88809809</span><br />
      <span>&emsp;&emsp;&emsp;&emsp;176-1006-0813</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg, register } from "@/api/login";

export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      codeUrl: "",
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        code: "",
        uuid: "",
        icpNum: "鲁ICP备2022016453号",
        prodFLag: false,
        haveRead: []
      },
      registerRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的用户名" },
          // {
          //   min: 11,
          //   max: 11,
          //   message: "手机号长度必须为11位",
          //   trigger: "blur",
          // },
          // {
          //   pattern: /^1[3-9]\d{9}$/, // 正则表达式，匹配中国手机号格式
          //   message: "请输入正确的手机号格式",
          //   trigger: "blur",
          // },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
          {
            min: 8, // 修改最小长度为8
            max: 12,
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, // 添加正则表达式确保包含大小写字母和数字
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
        haveRead: [{ required: true, trigger: ["blur", "change"], message: "请勾选'已阅读并同意隐私政策和大模型服务协议'" }],
      },
      loading: false,
      captchaEnabled: true,
      url1: require('@/assets/icons/loing.png'),
      url2: require('@/assets/icons/loing-test.jpg'),
      url3: require('@/assets/icons/aicaidmx3.png'),
      url4: require('@/assets/icons/jy-test.png'),
    };
  },
  created() {
    this.getCode();
    this.prodFLag = window.location.hostname === 'www.papaicai.com' ? false : true
    this.icpNum = window.location.hostname === 'www.papaicai.com' ? '鲁ICP备2022016453号-2' :
      window.location.hostname === 'www.aicaipap.com' ? '鲁ICP备2022016453号-3' :
        window.location.hostname === 'aicai.sdufe.edu.cn' ? '鲁ICP备05001933号-2' : ''
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.registerForm.uuid = res.uuid;
        }
      });
    },
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          register(this.registerForm)
            .then((res) => {
              const username = this.registerForm.username;
              this.$alert(
                "<font color='red'>恭喜你，您的账号 " +
                username +
                " 注册成功！</font>",
                "系统提示",
                {
                  dangerouslyUseHTMLString: true,
                  type: "success",
                }
              )
                .then(() => {
                  this.$router.push("/login");
                })
                .catch(() => { });
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.input-with-select .el-input-group__prepend {
  border-radius: 20px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background: #6dade2;
  width: 34px;
}

.input-with-select .el-input__inner {
  border-radius: 20px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background: #5a9dd8;
}
.register {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login(1).jpg");
  background-size: cover;
}
.register-test {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-test.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.register-forms {
  border-radius: 6px;
  background: #ffffff;
  width: 500px;
  padding: 25px 25px 5px 25px;
  margin-right: 100px;
  .el-input {
    height: 38px;
    width: 350px;
    // margin-left: 55px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.register-code-img {
  height: 38px;
}
.el-login-footer {
  height: 80px;
  line-height: 20px;
  position: fixed;
  bottom: 0;
  /*width: 100%;*/
  right: 50%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
