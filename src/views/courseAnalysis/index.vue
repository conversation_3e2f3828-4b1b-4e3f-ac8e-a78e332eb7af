<template>
  <div class="app-container">
    <student-analysis v-if="!gradeFlag" ref="StudentAnalysis" />
    <grade-analysis v-if="gradeFlag" ref="GradeAnalysis" />
  </div>
</template>

<script>
import StudentAnalysis from './components/StudentAnalysis.vue'
import GradeAnalysis from './components/GradeAnalysis.vue';
export default {
  name: "CourseAnalysis",
  components: { StudentAnalysis, GradeAnalysis },
  data() {
    return {
      gradeFlag: false,
    };
  },
  watch: {
    "$route.query.gradeFlag": {
      immediate: true,
      handler(value) {
        this.assign(value);
      },
    },
  },
  created() {
  },
  activated() {
  },
  methods: {
    assign(value) {
      console.log("gradeFlag (before conversion):", value);
      if (value != undefined) {
        this.gradeFlag = value === "true" || value === true; // 转换为布尔类型
        console.log("gradeFlag (after conversion):", this.gradeFlag);
      }
    }

  },
};
</script>
