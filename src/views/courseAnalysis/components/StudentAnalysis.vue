<template>
  <div class="app-container bg-container">

    <h1 style="font-weight:bold;margin:15px;text-align: center"> 学生课程分析</h1>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
                     content-class-name="descriptions-content" title="一、学生基本信息">
      <el-descriptions-item label="姓名">{{ form.studentName }}</el-descriptions-item>
      <el-descriptions-item label="课程">{{ form.courseName }}</el-descriptions-item>
      <el-descriptions-item label="报告日期">{{ form.date }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
                     content-class-name="descriptions-content" title="二、学习进度分析">
      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"
                            label="课程进度概述" :span="3"></el-descriptions-item>
      <el-descriptions-item label="总章数">{{ form.totalChapters }}</el-descriptions-item>
      <el-descriptions-item label="已完成章节">{{ form.completedChapter }}</el-descriptions-item>
      <el-descriptions-item label="当前已完成章节进度">{{ form.chapterCompletionProgress }}</el-descriptions-item>
      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"
                            label="学习速度评估" :span="3"></el-descriptions-item>
      <el-descriptions-item label="平均完成进度">{{ form.averageCompletionProgress }}</el-descriptions-item>
      <el-descriptions-item label="与班级平均进度比较">{{ form.compareWithClassProgress }}</el-descriptions-item>
      <el-descriptions-item label-class-name="hide-label" content-class-name="hide-content"></el-descriptions-item>
      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"
                            label="提问频次评估" :span="3"></el-descriptions-item>
      <el-descriptions-item label="智能问答总次数">{{ form.numberOfIntelligentQuestions }}</el-descriptions-item>
      <el-descriptions-item label=" 平均每周问答次数">{{ form.NumberOfWeekQuestions }}</el-descriptions-item>
      <el-descriptions-item label="与班级平均问答次数比较">{{ form.comparedWithClassQuestions }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
                     content-class-name="descriptions-content" title="三、课后作业完成度分析">
      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"
                            label="作业提交情况" :span="3"></el-descriptions-item>
      <el-descriptions-item label="总作业次数">{{ form.numberOfAssignments }}</el-descriptions-item>
      <el-descriptions-item label="已提交次数">{{ form.NumberOfCommits }}</el-descriptions-item>
      <el-descriptions-item label=" 提交比例">{{ form.commitRatio }}</el-descriptions-item>
    </el-descriptions>

    <!--    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"-->
    <!--                     content-class-name="descriptions-content" title="四、课后作业正确率分析">-->
    <!--      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"-->
    <!--                            label="整体正确率" :span="3"></el-descriptions-item>-->
    <!--      <el-descriptions-item label="总题目数">{{ form.numberOofQuestions }}</el-descriptions-item>-->
    <!--      <el-descriptions-item label="正确题目数">{{ form.correctNumberOfProblems }}</el-descriptions-item>-->
    <!--      <el-descriptions-item label="正确率">{{ form.accuracy }}</el-descriptions-item>-->
    <!--      <el-descriptions-item label-class-name="descriptions-label s-label" content-class-name="hide-content"-->
    <!--                            label="知识点掌握情况" :span="3">{{ form.masteryOfKnowledgePoints }}-->
    <!--      </el-descriptions-item>-->
    <!--      <el-descriptions-item label-class-name="hide-label" content-class-name="descriptions-content s-content"-->
    <!--                            label="知识点掌握情况" :span="3">{{ form.masteryOfKnowledgePoints }}-->
    <!--      </el-descriptions-item>-->
    <!--    </el-descriptions>-->

    <el-descriptions :column="1" class="analysis-descriptions s-analysis-descriptions"
                     label-class-name="descriptions-label s-label" content-class-name="descriptions-content s-content"
                     title="四、综合评估与建议" direction="vertical">
      <el-descriptions-item label="学习进度">{{ form.learningProgress }}</el-descriptions-item>
      <el-descriptions-item label="提问频次">{{ form.frequencyOfQuestions }}</el-descriptions-item>
      <!--      <el-descriptions-item label="作业正确率">{{ form.jobAccuracyRate }}</el-descriptions-item>-->
      <el-descriptions-item label="学习策略">{{ form.learningStrategy }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {getStudentCourseStatistics} from "@/api/courseManagement/courseManagement";

export default {
  name: 'StudentAnalysis',
  data() {
    return {
      // form: {
      //   studentName: this.$route.query && this.$route.query.studentName,
      //   courseName: this.$route.query && this.$route.query.courseName,
      //   date: new Date().toLocaleDateString(),
      //   totalChapters: '20',
      //   completedChapter: '16',
      //   chapterCompletionProgress: '80%',
      //   completionIsHigherThan90: '80%',
      //   averageCompletionProgress: '80%',
      //   compareWithClassProgress: '落后',
      //   numberOfIntelligentQuestions: '64',
      //   NumberOfWeekQuestions: '4',
      //   comparedWithClassQuestions: '落后',
      //   numberOfAssignments: '32',
      //   NumberOfCommits: '30',
      //   commitRatio: '93.75%',
      //   numberOofQuestions: '400',
      //   correctNumberOfProblems: '360',
      //   accuracy: '90%',
      //   masteryOfKnowledgePoints: '易错点分布：城市管理的概念和内涵特征，城市管理系统的左右和框架。',
      //   learningProgress: '学生进度落后，建议调整学习计划，增加学习时间，必要时寻求教师或同学帮助。',
      //   frequencyOfQuestions: '学生提问次数低于班级平均值，建议调整学习计划，提高提问频率，及时提问。',
      //   jobAccuracyRate: '作业正确率较低，针对错误率较高的知识点，建议学生复习相关教材，观看教学视频，或进行针对性练习。',
      //   learningStrategy: '建议学生采用主动学习策略，如小组讨论、思维导图等，以提高学习效率和理解深度。通过小组讨论，学生可以分享彼此的观点，互相启发，从而获得更全面的知识。'
      // },
      form: {
        studentName: this.$route.query && this.$route.query.studentName,
        courseName: this.$route.query && this.$route.query.courseName,
        date: '',
        totalChapters: '',
        completedChapter: '',
        chapterCompletionProgress: '',
        completionIsHigherThan90: '',
        averageCompletionProgress: '',
        compareWithClassProgress: '',
        numberOfIntelligentQuestions: '',
        NumberOfWeekQuestions: '',
        comparedWithClassQuestions: '',
        numberOfAssignments: '',
        NumberOfCommits: '',
        commitRatio: '',
        numberOofQuestions: '',
        correctNumberOfProblems: '',
        accuracy: '',
        masteryOfKnowledgePoints: '',
        learningProgress: '',
        frequencyOfQuestions: '',
        jobAccuracyRate: '',
        learningStrategy: ''
      },
      queryParams: {
        studentId: '',
        courseId: '',
        courseClassId: '',
        courseName: ''
      },
    }
  },
  computed: {
    queryParams_new() {
      return {
        studentName: this.$route.query.studentName,
        courseName: this.$route.query.courseName
      }
    }
  },
  watch: {
    queryParams_new: {
      handler(newVal) {
        // 同时更新两个参数到本地变量
        this.$route.query.studentName = newVal.studentName;
        this.$route.query.courseName = newVal.courseName;
        this.getRouteData();
      },
      immediate: true,
      deep: true // 若参数是对象需开启深度监听
    }
  },
  created() {
    this.getRouteData()
  },
  activated() {
    this.getRouteData();  // 在这里重新请求数据
  },
  methods: {
    getRouteData() {
      const {gradeFlag, courseName, studentId, id} = this.$route.query;
      console.log("接收到的参数：", {
        gradeFlag,
        courseName,
        studentId,
        id,
      });
      this.queryParams.studentId = studentId
      this.queryParams.courseName = courseName
      this.queryParams.courseClassId = id
      this.handleStudentCourseStatistics()
    },
    // 获取数据
    handleStudentCourseStatistics() {
      getStudentCourseStatistics(this.queryParams).then(res => {
        console.log("返回的数据：", res);
        // this.form.studentName = res.data.nickName
        // this.form.courseName = res.data.courseName
        // this.form.date = res.data.reportDate;
        // this.form.totalChapters = res.data.totalChapter;
        // this.form.completedChapter = res.data.finishedChapter;
        // this.form.chapterCompletionProgress = res.data.completionProgress;
        // this.form.averageCompletionProgress = res.data.averageCompletionProgress;
        // this.form.compareWithClassProgress = res.data.comparison
        // this.form.numberOfIntelligentQuestions = res.data.intelligentQuestionAnsweringCount
        // this.form.NumberOfWeekQuestions = res.data.averageWeeklyQuestionAnsweringCount
        // this.form.comparedWithClassQuestions = res.data.comparisonWithClassAverageQuestionAnsweringCount;
        // this.form.numberOfAssignments = res.data.totalHomework
        // this.form.NumberOfCommits = res.data.submittedTimes
        // this.form.commitRatio = res.data.submissionRatio
        // this.form.learningProgress = res.data.learningProgressAdvice
        // this.form.frequencyOfQuestions = res.data.frequencyOfQuestionsAdvice
        // this.form.learningStrategy = res.data.learningStrategyAdvice
        // 使用 this.$set 确保更新时数据是响应式的
        if (res.data) {
          if (res.data.nickName != null && res.data.nickName != undefined && res.data.nickName != '') {
            this.$set(this.form, 'studentName', res.data.nickName);
          }
          if (res.data.courseName != null && res.data.courseName != undefined && res.data.courseName != '') {
            this.$set(this.form, 'courseName', res.data.courseName);
          }
          this.$set(this.form, 'date', res.data.reportDate);
          this.$set(this.form, 'totalChapters', res.data.totalChapter);
          this.$set(this.form, 'completedChapter', res.data.finishedChapter);
          this.$set(this.form, 'chapterCompletionProgress', res.data.completionProgress);
          this.$set(this.form, 'averageCompletionProgress', res.data.averageCompletionProgress);
          this.$set(this.form, 'compareWithClassProgress', res.data.comparison);
          this.$set(this.form, 'numberOfIntelligentQuestions', res.data.intelligentQuestionAnsweringCount);
          this.$set(this.form, 'NumberOfWeekQuestions', res.data.averageWeeklyQuestionAnsweringCount);
          this.$set(this.form, 'comparedWithClassQuestions', res.data.comparisonWithClassAverageQuestionAnsweringCount);
          this.$set(this.form, 'numberOfAssignments', res.data.totalHomework);
          this.$set(this.form, 'NumberOfCommits', res.data.submittedTimes);
          this.$set(this.form, 'commitRatio', res.data.submissionRatio);
          this.$set(this.form, 'learningProgress', res.data.learningProgressAdvice);
          this.$set(this.form, 'frequencyOfQuestions', res.data.frequencyOfQuestionsAdvice);
          this.$set(this.form, 'learningStrategy', res.data.learningStrategyAdvice);
        } else {
          const date = new Date();
          // 格式化为 年-月-日
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，因此需要 +1
          const day = String(date.getDate()).padStart(2, '0');
          this.$set(this.form, 'reportDate', `${year}-${month}-${day}`);
        }
      })
    },
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.bg-container {
  width: 95%;
  margin: 0 auto;
  height: auto;
  background: url("../../../views/studentCourseAnalysis/1.png") no-repeat fixed center;
  background-size: cover;
}
</style>
<style>
.analysis-descriptions .el-descriptions__body {
  background-color: transparent !important;
}

.analysis-descriptions .descriptions-label {
  background: #f0f0f0;
  font-size: 16px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  padding: 5px 10px;
  border-radius: 5px 0 0 5px;
  margin: 0;
}

.analysis-descriptions .descriptions-content {
  background: #f0f0f0;
  font-size: 16px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  padding: 5px 10px;
  border-radius: 0 5px 5px 0;
  margin-right: 20px;
}

.analysis-descriptions .s-label {
  background: none;
}

.s-analysis-descriptions .el-descriptions__body {
  width: calc(100% - 20px) !important;
}

.analysis-descriptions .s-content {
  border-radius: 5px;
}

.analysis-descriptions .hide-content {
  display: none;
}

.analysis-descriptions .hide-label {
  display: none;
}
</style>
