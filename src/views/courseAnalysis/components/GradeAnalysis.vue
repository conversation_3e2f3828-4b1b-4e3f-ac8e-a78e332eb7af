<template>
  <div class="app-container bg-container">
    <h1 style="font-weight:bold;margin:15px;text-align: center"> 班级课程分析</h1>
    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
      content-class-name="descriptions-content" title="一、班级基本信息">
      <el-descriptions-item label="班级">{{ form.studentClass }}</el-descriptions-item>
      <el-descriptions-item label="课程">{{ form.courseName }}</el-descriptions-item>
      <el-descriptions-item label="报告日期">{{ form.reportDate }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
      content-class-name="descriptions-content" title="二、学习进度统计">
      <el-descriptions-item label="完成进度领先的学生比例">{{ form.countLeadingRatio }}</el-descriptions-item>
      <el-descriptions-item label="完成进度持平的学生比例">{{ form.countFlatRatio }}</el-descriptions-item>
      <el-descriptions-item label="完成进度落后的学生比例">{{ form.countLagRatio }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
      content-class-name="descriptions-content" title="三、课后作业情况">
      <el-descriptions-item label="完成作业的学生比例">{{ form.countP100 }}</el-descriptions-item>
      <el-descriptions-item label="作业完成百分之90比例">{{ form.countP9 }}</el-descriptions-item>
      <el-descriptions-item label="作业完成百分之60比例">{{ form.countP6 }}</el-descriptions-item>
      <el-descriptions-item label-class-name="hide-label" content-class-name="hide-content"></el-descriptions-item>

    </el-descriptions>
    <el-descriptions :column="1" class="analysis-descriptions s-analysis-descriptions"
      label-class-name="descriptions-label s-label" content-class-name="descriptions-content s-content"
      direction="vertical">
      <el-descriptions-item label="作业提交率低于百分之60的学生">{{ form.lessThanP6NameStr }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="3" class="analysis-descriptions" label-class-name="descriptions-label"
      content-class-name="descriptions-content" title="四、问答频率与质量">
      <el-descriptions-item label="问答次数高于90%的比例">{{ form.quoteAnswerCountP9 }}</el-descriptions-item>
      <el-descriptions-item label="问答次数高于60%的比例">{{ form.quoteAnswerCountP6 }}</el-descriptions-item>
      <el-descriptions-item label-class-name="hide-label" content-class-name="hide-content"></el-descriptions-item>

    </el-descriptions>
    <el-descriptions :column="1" class="analysis-descriptions s-analysis-descriptions"
      label-class-name="descriptions-label s-label" content-class-name="descriptions-content s-content"
      direction="vertical">
      <el-descriptions-item label="问答次数低于 60% 学生姓名">{{ form.quoteAnswerLessThantStr }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="1" class="analysis-descriptions s-analysis-descriptions"
      label-class-name="descriptions-label s-label" content-class-name="descriptions-content s-content" title="五、总结与建议"
      direction="vertical">
      <el-descriptions-item label="学习进度">{{ form.suggestionsForClassProgress }}</el-descriptions-item>
      <el-descriptions-item label="课后作业">{{ form.suggestionsForClassHomework }}</el-descriptions-item>
      <el-descriptions-item label="问答互动">{{ form.suggestionsForClassInteractions }}</el-descriptions-item>
    </el-descriptions>

  </div>
</template>

<script>
import {getClassCourseAnalysis,} from "@/api/courseManagement/courseManagement";

export default {
  name: 'GradeAnalysis',
  data() {
    return {
      // form: {
      //   studentClass: this.$route.query && this.$route.query.studentClass,
      //   courseName: this.$route.query && this.$route.query.courseName,
      //   date: new Date().toLocaleDateString(),
      //   finishAheadOfSchedule: '60%',
      //   flatCompletionSchedule: '10%',
      //   behindSchedule: '30%',
      //   completionIsHigherThan90: '80%',
      //   completionIsHigherThan80: '90%',
      //   specialAttentionToStudents: '陌小杰',
      //   activelyParticipateInQA: '100%',
      //   higherResponseQuality: '80%',
      //   needToImproveQuality: '陌小杰',
      //   learningProgress: '班级整体学习进度较为均衡，但仍有部分学生需要加快进度。建议教师关注落后学生的情况，提供必要的帮助和指导。',
      //   homework: '大部分学生的作业完成度和正确率较高，但仍有少数学生需要提高。建议教师针对这些学生进行个别辅导，帮助他们提高作业质量。',
      //   interactiveQuestionAndAnswer: '班级整体问答氛围较好，但仍有部分学生需要积极参与。建议教师鼓励学生多提问和回答问题，促进班级内部的交流和互动。同时，针对回答质量较低的学生进行个别指导，提高他们的问答能力。'
      // },
      form: {
        studentClass: this.$route.query && this.$route.query.studentClass,
        courseName: this.$route.query && this.$route.query.courseName,
        reportDate: '',
        countLeadingRatio: '',
        countFlatRatio: '',
        countLagRatio: '',
        countP100: '',
        countP9: '',
        countP6: '',
        lessThanP6NameStr: '',
        quoteAnswerCountP9: '',
        quoteAnswerCountP6: '',
        quoteAnswerLessThantStr: '',
        suggestionsForClassProgress: '',
        suggestionsForClassHomework: '',
        suggestionsForClassInteractions: '',
      },
      queryParams: {
        studentId: '',
        courseId: '',
        courseClassId: '',
        courseName:''
      },
    }
  },
  watch: {
    '$route': {
      handler: 'getRouteData',
      immediate: true // 立即执行一次 handler，确保首次加载时获取数据
    }
  },
  created() {
    // this.getRouteData() // 移除这里的调用，由 watch 的 immediate: true 负责首次加载
  },

  methods: {
    getRouteData() {
      const {gradeFlag, courseName, courseClassId} = this.$route.query;
      console.log("接收到的参数：", {
        gradeFlag,
        courseName,
        courseClassId,
      });
      this.queryParams.courseName = courseName
      this.queryParams.courseClassId = courseClassId
      this.handlegetClassCourseAnalysis()
    },
    // 获取数据
    handlegetClassCourseAnalysis() {
      getClassCourseAnalysis(this.queryParams).then(res => {
        console.log("返回的数据：", res);

        // this.form.reportDate = res.data.reportDate;
        // this.form.countLeadingRatio = res.data.countLeadingRatio;
        // this.form.countFlatRatio = res.data.countFlatRatio;
        // this.form.countLagRatio = res.data.countLagRatio;
        // this.form.countP100 = res.data.countP100;
        // this.form.countP9 = res.data.countP9;
        // this.form.countP6 = res.data.countP6;
        // this.form.lessThanP6NameStr = res.data.lessThanP6NameStr;
        // this.form.quoteAnswerCountP9 = res.data.quoteAnswerCountP9;
        // this.form.quoteAnswerCountP6 = res.data.quoteAnswerCountP6;
        // this.form.quoteAnswerLessThantStr = res.data.quoteAnswerLessThantStr;
        // this.form.suggestionsForClassProgress = res.data.suggestionsForClassProgress;
        // this.form.suggestionsForClassHomework = res.data.suggestionsForClassHomework;
        // this.form.suggestionsForClassInteractions = res.data.suggestionsForClassInteractions;
        if (res.data) {
          this.$set(this.form, 'reportDate', res.data.reportDate);
          this.$set(this.form, 'countLeadingRatio', res.data.countLeadingRatio);
          this.$set(this.form, 'countFlatRatio', res.data.countFlatRatio);
          this.$set(this.form, 'countLagRatio', res.data.countLagRatio);
          this.$set(this.form, 'countP100', res.data.countP100);
          this.$set(this.form, 'countP9', res.data.countP9);
          this.$set(this.form, 'countP6', res.data.countP6);
          this.$set(this.form, 'lessThanP6NameStr', res.data.lessThanP6NameStr);
          this.$set(this.form, 'quoteAnswerCountP9', res.data.quoteAnswerCountP9);
          this.$set(this.form, 'quoteAnswerCountP6', res.data.quoteAnswerCountP6);
          this.$set(this.form, 'quoteAnswerLessThantStr', res.data.quoteAnswerLessThantStr);
          this.$set(this.form, 'suggestionsForClassProgress', res.data.suggestionsForClassProgress);
          this.$set(this.form, 'suggestionsForClassHomework', res.data.suggestionsForClassHomework);
          this.$set(this.form, 'suggestionsForClassInteractions', res.data.suggestionsForClassInteractions);
        }else{
          const date = new Date();
          // 格式化为 年-月-日
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，因此需要 +1
          const day = String(date.getDate()).padStart(2, '0');
          this.$set(this.form, 'reportDate', `${year}-${month}-${day}`);
        }
      })
    },
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.bg-container {
  width: 95%;
  margin: 0 auto;
  height: auto;
  background: url("../../../views/studentCourseAnalysis/1.png") no-repeat fixed
    center;
  background-size: cover;
}
</style>
<style>
.analysis-descriptions .el-descriptions__body {
  background-color: transparent !important;
}

.analysis-descriptions .descriptions-label {
  background: #f0f0f0;
  font-size: 16px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  padding: 5px 10px;
  border-radius: 5px 0 0 5px;
  margin: 0;
}

.analysis-descriptions .descriptions-content {
  background: #f0f0f0;
  font-size: 16px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  padding: 5px 10px;
  border-radius: 0 5px 5px 0;
  margin-right: 20px;
}

.analysis-descriptions .s-label {
  background: none;
}

.s-analysis-descriptions .el-descriptions__body {
  width: calc(100% - 20px) !important;
}

.analysis-descriptions .s-content {
  border-radius: 5px;
}

.analysis-descriptions .hide-content {
  display: none;
}
.analysis-descriptions .hide-label {
  display: none;
}
</style>
