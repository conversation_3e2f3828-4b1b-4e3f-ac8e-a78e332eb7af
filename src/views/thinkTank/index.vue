<template>
  <div class="app-container ck-container">
    <img :src="thinkTankImg" alt="thinkTankImg" style="width:100%;height: 100%;" />
  </div>
</template>

<script>
import thinkTankImg from "@/assets/images/thinkTank.png";
export default {
  name: "ThinkTankImg",
  data() {
    return {
      thinkTankImg: thinkTankImg,
    };
  },
};
</script>
<style lang="scss" scoped>
.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  overflow: auto;
}
</style>
