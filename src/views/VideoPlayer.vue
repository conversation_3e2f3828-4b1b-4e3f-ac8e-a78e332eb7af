<!--<template>-->
<!--  <div class="video-player">-->
<!--    <div v-for="(video, index) in videos" :key="index" class="video-container">-->
<!--      <button @click="showVideo(index)">-->
<!--        {{ video.title }}-->
<!--        <hr /> &lt;!&ndash; 这里是横线 &ndash;&gt;-->
<!--      </button>-->
<!--      <video-->
<!--        v-show="activeVideo === index"-->
<!--        style="height: 100%; width: 100%"-->
<!--        ref="videoPlayer"-->
<!--        :src="require(`@/assets/video/${video.src}.mp4`)"-->
<!--        controls-->
<!--        @loadedmetadata="onLoadedMetadata"-->
<!--      ></video>-->
<!--    </div>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--  export default {-->
<!--    data() {-->
<!--      return {-->
<!--        activeVideo: null, // 用于跟踪当前激活的视频索引-->
<!--        videos: [-->
<!--          { title: '总介绍', src: 'zongjieshao' },-->
<!--          { title: '数据集生成', src: 'shujushengc' },-->
<!--          { title: '知识建立', src: 'zhishihua' },-->
<!--          { title: '知识库', src: 'zhishiku' },-->
<!--          { title: 'prompt优化', src: 'promptyh' },-->
<!--          { title: '生成讲义', src: 'jiaoxue' },-->
<!--          { title: 'ppt生成', src: 'pptsc' },-->
<!--          { title: '作业生成', src: 'zuoye' },-->
<!--          { title: '课后作业', src: 'kehouzuoye' },-->
<!--          { title: '效果监测反馈', src: 'xiaoguojianc' },-->
<!--          { title: '论文研读', src: 'lunwenyandu' },-->
<!--          { title: '体验中心', src: 'tiyanzhongxin' },-->
<!--          { title: '学术写作', src: 'xueshuxiezuo' },-->
<!--          { title: '知识点问答', src: 'zhishidianwenda' },-->
<!--          { title: '智慧学堂', src: 'zhihuiketang' },-->
<!--          { title: '场景模拟', src: 'changjingmoni' },-->
<!--        ],-->
<!--      };-->
<!--    },-->
<!--    methods: {-->
<!--      showVideo(index) {-->
<!--        this.activeVideo = this.activeVideo === index ? null : index;-->
<!--        // 可以在此处添加逻辑，比如暂停其他视频-->
<!--      },-->
<!--      onLoadedMetadata(event) {-->
<!--        // 视频元数据加载完成的处理逻辑-->
<!--      },-->
<!--    },-->
<!--  };-->
<!--</script>-->

<!--<style scoped>-->
<!--  .video-container {-->
<!--    /* 添加样式以调整布局，如需要 */-->
<!--  }-->

<!--  button {-->
<!--    background: transparent; /* 设置按钮背景为透明 */-->
<!--    border: none; /* 移除边框 */-->
<!--    color: blue; /* 文字颜色设为蓝色 */-->
<!--    font-style: italic; /* 文字倾斜 */-->
<!--    position: relative; /* 为下划线定位做准备 */-->
<!--    cursor: pointer; /* 更改鼠标悬停时的指针形状 */-->
<!--  }-->

<!--  /* 添加下划线样式 */-->
<!--  button::after {-->
<!--    content: ''; /* 伪元素内容为空 */-->
<!--    position: absolute; /* 绝对定位 */-->
<!--    bottom: 0; /* 下划线位于底部 */-->
<!--    left: 0; /* 从左边开始 */-->
<!--    width: 100%; /* 宽度与按钮相同 */-->
<!--    height: 1px; /* 下划线高度 */-->
<!--    background-color: currentColor; /* 颜色与文字颜色相同 */-->
<!--    transform-origin: left center; /* 转换原点在左侧中心 */-->
<!--    transform: scaleX(0); /* 初始宽度为0，实现动画效果 */-->
<!--    transition: transform 0.3s ease; /* 添加过渡效果 */-->
<!--  }-->

<!--  /* 当鼠标悬停时，改变下划线的宽度 */-->
<!--  button:hover::after {-->
<!--    transform: scaleX(1);-->
<!--  }-->
<!--</style>-->
<template>
  <video
    style="height: 100%; width: 100%"
    ref="videoPlayer"
    :src="require('@/assets/video/xuanchuan.mp4')"
    controls
  ></video>
</template>
