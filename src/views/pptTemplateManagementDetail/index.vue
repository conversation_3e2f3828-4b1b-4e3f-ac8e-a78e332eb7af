<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名" prop="fileName">
        <el-input
            v-model="queryParams.fileName"
            placeholder="请输入文件名"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="主题标志" prop="themeLabel">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.themeLabel"-->
      <!--          placeholder="请输入主题标志"-->
      <!--          clearable-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="内容类型" prop="contentType">
        <el-input
            v-model="queryParams.contentType"
            placeholder="请输入内容类型"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结构编号" prop="versionStructure">
        <el-input
            v-model="queryParams.versionStructure"
            placeholder="请输入结构编号"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            type="primary"-->
<!--            plain-->
<!--            icon="el-icon-plus"-->
<!--            size="mini"-->
<!--            @click="handleAdd"-->
<!--        >新增-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            type="success"-->
<!--            plain-->
<!--            icon="el-icon-edit"-->
<!--            size="mini"-->
<!--            :disabled="single"-->
<!--            @click="handleUpdate"-->
<!--        >修改-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            type="danger"-->
<!--            plain-->
<!--            icon="el-icon-delete"-->
<!--            size="mini"-->
<!--            :disabled="multiple"-->
<!--            @click="handleDelete"-->
<!--        >删除-->
<!--        </el-button>-->
<!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--        >导出-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="el-icon-back"
            size="mini"
            @click="handleGoBack"
        >返回
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table highlight-selection-row v-loading="loading" :data="pptTemplateThemeDetailList"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
<!--      <el-table-column fixed="left" label="预览" align="center" prop="previewList" min-width="250"-->
<!--                       show-overflow-tooltip>-->
<!--        <template v-slot="scope">-->
<!--          <div v-if="scope.row.contentType === 'bgc_background'-->
<!--            || scope.row.contentType === 'bgc_begin' || scope.row.contentType === 'bgc_bigTitle'-->
<!--            || scope.row.contentType === 'bgc_content' || scope.row.contentType === 'bgc_page'-->
<!--            || scope.row.contentType === 'bgc_end'-->
<!--">-->
<!--            <el-carousel height="200px" :indicator-position="scope.row.previewList.length > 9 ? 'none' : ''">-->
<!--              <el-carousel-item v-for="(item,index) in [scope.row.absolutePathUrl]" :key="index">-->
<!--                <el-image :src="item+'?t='+ new Date().getTime()" fit="contain"-->
<!--                          :preview-src-list="[scope.row.absolutePathUrl]"></el-image>-->
<!--              </el-carousel-item>-->
<!--            </el-carousel>-->
<!--          </div>-->
<!--          <div v-else-if="scope.row.previewList && scope.row.previewList.length > 0">-->
<!--            &lt;!&ndash; 如果是其他类型，遍历 previewList 展示图片 &ndash;&gt;-->
<!--            <el-carousel height="200px" :indicator-position="scope.row.previewList.length > 9 ? 'none' : ''">-->
<!--              <el-carousel-item v-for="(item,index) in scope.row.previewList" :key="index">-->
<!--                <el-image :src="item" fit="contain" :preview-src-list="scope.row.previewList"></el-image>-->
<!--              </el-carousel-item>-->
<!--            </el-carousel>-->
<!--          </div>-->
<!--          <div v-else style="height: 70px; display: flex; justify-content: center; align-items: center;">-->
<!--            &lt;!&ndash; 如果没有图片数据，显示加载中，保持高度一致 &ndash;&gt;-->
<!--            <i class="el-icon-loading" style="font-size: 24px;"></i>-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="文件名" align="center" prop="fileName" min-width="150" show-overflow-tooltip>
        <template v-slot="scope">
          <el-popover trigger="hover" placement="left">
            <div>
              <p>{{ scope.row.fileName }}</p>

            </div>
            <div slot="reference" class="no-wrapper">
              <el-link type="primary" @click="handleClickScopeRowFileName(scope.row, scope.$index)">
                <span class="ellipsis">{{ scope.row.fileName }}</span>
              </el-link>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="主题标志" align="center" prop="themeLabel" show-overflow-tooltip/>
      <el-table-column label="内容类型" align="center" prop="contentType" show-overflow-tooltip>
        <template v-slot="scope">
          <el-tag
              v-if="scope.row.contentType === 'bgc_background'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'bgc_begin'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'bgc_bigTitle'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'bgc_content'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'bgc_page'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'bgc_end'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-if="scope.row.contentType === 'begin'"
              type="success" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-else-if="scope.row.contentType === 'bigTitle'"
              type="info" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-else-if="scope.row.contentType === 'content'"
              type="warning" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-else-if="scope.row.contentType === 'end'"
              type="danger" effect="plain">{{ scope.row.contentType }}
          </el-tag>
          <el-tag
              v-else-if="scope.row.contentType === 'page'"
              type="primary" effect="plain">{{ scope.row.contentType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="结构编号" align="center" prop="versionStructure" sortable/>
      <el-table-column label="序号" align="center" prop="sort" min-width="60" sortable>
      <template v-slot="scope">
        <el-link type="info">{{ scope.row.sort }}</el-link>
      </template>
    </el-table-column>
      <el-table-column label="文件后缀" align="center" prop="suffix"/>
      <el-table-column label="基址" align="center" prop="unzipFolder">
        <template v-slot="scope">
          <el-popover trigger="hover" placement="left">
            <p>{{ scope.row.unzipFolder }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag class="clickable-tag"
                      ref="clickableTag"
                      effect="plain"
                      type="info"
              >{{ scope.row.unzipFolder }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--              size="mini"-->
<!--              type="text"-->
<!--              icon="el-icon-edit"-->
<!--              @click="handleUpdate(scope.row)"-->
<!--          >修改-->
<!--          </el-button>-->
<!--          <el-button-->
<!--              size="mini"-->
<!--              type="text"-->
<!--              icon="el-icon-delete"-->
<!--              @click="handleDelete(scope.row)"-->
<!--          >删除-->
<!--          </el-button>-->
<!--        </template>-->
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50,100]"
        @pagination="getList"
    />

    <!-- 添加或修改PPT模板（解压后的模板管理）详细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件名" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名" :disabled="true"/>
        </el-form-item>
        <el-form-item label="主题标志" prop="themeLabel">
          <el-input v-model="form.themeLabel" placeholder="请输入主题标志" :disabled="true"/>
        </el-form-item>
        <el-form-item label="内容类型" prop="contentType">
          <el-select v-model="form.contentType" placeholder="请选择内容类型" style="width: 100%;"
                     @change="handleChangeContentTypeOption" :disabled="isDisabled">
            <el-option
                v-for="item in contentTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结构编号" prop="versionStructure">
          <el-input v-model="form.versionStructure"
                    placeholder="请输入结构编号"
                    @input="handleInputVersionStructure"
                    :disabled="isDisabled"/>
        </el-form-item>
        <el-form-item label="文件后缀" prop="suffix">
          <!--          <el-input v-model="form.suffix" placeholder="请输入文件后缀" :disabled="isDisabled"/>-->
          <el-select v-model="form.suffix" placeholder="请选择文件后缀"
                     style="width: 100%;" @change="handleChangesuffixOption" :disabled="isDisabled">
            <el-option
                v-for="item in suffixOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="基础目录" prop="unzipFolder">
          <el-input type="textarea" v-model="form.unzipFolder" placeholder="文件基础目录" :disabled="true"/>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :precision="2" :step="1.0" :min="1" :max="9999"
                           style="width: 100%;"></el-input-number>
        </el-form-item>
        <el-form-item label="文件上传" prop="absolutePath" v-show="uploadType==='.pptx'">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" multiple
                     :data="uploadDataLabel" :headers="headers" :on-success="handleUploadSuccess"
                     :on-remove="handleRemoveData"

                     :file-list="fileList" accept=".pptx" :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">仅支持pptx上传,仅能上传一个文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="文件上传" prop="absolutePath" v-show="uploadType==='.png'">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" multiple
                     :data="uploadDataLabel" :headers="headers" :on-success="handleUploadSuccess"
                     :on-remove="handleRemoveData"
                     :file-list="fileList" accept=".png" :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">仅支持png上传,仅能上传一个文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPptTemplateThemeDetail,
  getPptTemplateThemeDetail,
  delPptTemplateThemeDetail,
  addPptTemplateThemeDetail,
  updatePptTemplateThemeDetail
} from '@/api/pptTemplateManagementDetail/pptTemplateManagementDetail'
import {downLoad} from '@/api/intellectSmartPpt/intellectSmartPpt'
import {getToken} from '@/utils/auth'
import {getPptTemplateManagement} from '@/api/pptTemplateManagement/pptTemplateManagement'
import log from '@/views/monitor/job/log.vue'

export default {
  name: 'PptTemplateManagementDetail',
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/file/uploadTarget',
      uploadDataLabel: {},
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // PPT模板（解压后的模板管理）详细表格数据
      pptTemplateThemeDetailList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateManagementId: null,
        fileName: null,
        themeLabel: null,
        contentType: null,
        versionStructure: null,
        suffix: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        fileName: [
          {required: true, message: '文件名不能为空', trigger: ['blur', 'change', 'input']}
        ],
        themeLabel: [
          {required: true, message: '主题标志不能为空', trigger: ['blur', 'change', 'input']}
        ],
        contentType: [{required: true, message: '主题标志不能为空', trigger: ['blur', 'change', 'input']}
        ],
        versionStructure: [
          {
            required: true,
            message: '结构编号不能为空',
            trigger: ['blur', 'change', 'input']
          },
          {
            pattern: /^\d+(_\d+){0,4}$/,
            message: '请输入正确的格式：最多5个数字块，用下划线分开，示例：0_0_0_0_0',
            trigger: ['blur', 'change', 'input']
          }
        ],
        suffix: [{required: true, message: '文件后缀不能为空', trigger: ['blur', 'change', 'input']}
        ],
        unzipFolder: [{required: true, message: '基础目录不能为空', trigger: ['blur', 'change', 'input']}
        ]
      },
      routeParams: {
        templateManagementId: null // 路由传递的参数
      },
      lastClickTime: 0, // 记录上次点击时间
      isDisabled: false,//  是否继续修改或者新增的表单组件
      suffixOptions: [ // 文件后缀选择
        {
          value: 'pptx',
          label: 'pptx'
        },
        {
          value: 'png',
          label: 'png'
        }
      ],
      contentTypeOptions: [ // 内容类型选择
        {
          value: 'begin',
          label: 'begin'
        },
        {
          value: 'bigTitle',
          label: 'bigTitle'
        },
        {
          value: 'content',
          label: 'content'
        },
        {
          value: 'page',
          label: 'page'
        },
        {
          value: 'end',
          label: 'end'
        },
      ],
      uploadType: '.pptx',
    }
  },
  mounted() {
    // 获取查询参数
    this.updateRouteParams()
  }
  ,
  watch: {
    '$route'(to, from) {
      // 每当路由变化时，更新 templateManagementId
      this.updateRouteParams()
    }
  }
  ,
  created() {
  }
  ,
  methods: {
    /** 更新从路由中获取的参数信息*/
    updateRouteParams() {
      // 更新参数
      if (this.$route.query.templateManagementId) {
        this.routeParams.templateManagementId = this.$route.query.templateManagementId
        this.queryParams.templateManagementId = this.routeParams.templateManagementId
        this.getList()
      }
    }
    ,
    /** 文件上传后回调*/
    handleUploadSuccess(res, file) {
      console.log('upload')
      this.form.absolutePath = res.data.path
    }
    ,
    /** 文件上传前回调 */
    handleBeforeUpload(file) {
      console.log('before upload')

      // 返回一个 Promise
      return new Promise((resolve, reject) => {
        // 触发验证，检查整个表单数据
        this.$refs.form.validate((valid) => {
          if (valid) {
            console.log('表单验证成功')
            // 填充文件上传的位置
            this.uploadDataLabel = {path: this.form.unzipFolder + this.form.fileName}
            resolve(true) // 验证成功，允许上传
          } else {
            this.$notify({
              title: '警告',
              message: '请确保在上传文件之前填写完整的表单字段',
              type: 'warning',
              position: 'top-left'

            })
            reject(new Error('表单验证失败')) // 验证失败，拒绝上传
          }
        })
      })
    }
    ,
    /** 文件移除*/
    handleRemoveData(file, fileList) {
    }
    ,
    /** 查询PPT模板（解压后的模板管理）详细列表 */
    getList() {
      this.loading = true
      listPptTemplateThemeDetail(this.queryParams).then(response => {
        this.pptTemplateThemeDetailList = response.rows
        this.total = response.total
        this.loading = false
      })
    }
    ,
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    }
    ,
    // 表单重置
    reset() {
      this.form = {
        id: null,
        templateManagementId: null,
        fileName: null,
        themeLabel: null,
        contentType: null,
        versionStructure: null,
        suffix: null,
        absolutePath: null,
        unzipFolder: null,
        sort: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm('form')
    }
    ,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    }
    ,
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
    ,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    }
    ,
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isDisabled = false
      this.open = true
      this.title = '添加PPT模板（解压后的模板管理）详细'

      // 获取默认数据
      this.getDefaultAddData()

    }
    ,
    /** 新增模板详细，默认的数据内容 */
    getDefaultAddData() {
      if (this.routeParams.templateManagementId) {
        getPptTemplateManagement(this.routeParams.templateManagementId).then(response => {
          this.form.themeLabel = response.data.tValue
          this.form.unzipFolder = response.data.unzipFolder
          this.form.suffix = 'pptx'
          this.form.contentType = 'page'
          this.form.fileName = response.data.tValue + '_' + this.form.contentType + '_' + this.form.versionStructure + '.' + this.form.suffix
          this.form.templateManagementId = response.data.id
          console.log(response)
        })
      }
    }
    ,
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.uploadType = '.' + row.suffix
      this.isDisabled = true
      // 填充文件上传的位置
      this.uploadDataLabel = {path: row.absolutePath}
      const id = row.id || this.ids
      getPptTemplateThemeDetail(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改PPT模板（解压后的模板管理）详细'
      })
    }
    ,
    /** 提交按钮 */
    submitForm() {
      if (this.form.absolutePath == null || this.form.absolutePath == '') {
        this.$notify({
          title: '警告',
          message: '请确保文件完成上传',
          type: 'warning',
          position: 'top-left'

        })
        return
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePptTemplateThemeDetail(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addPptTemplateThemeDetail(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
          this.fileList = []
        }
      })
    }
    ,
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除PPT模板（解压后的模板管理）详细编号为"' + ids + '"的数据项？').then(function () {
        return delPptTemplateThemeDetail(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('ppt/pptTemplateManagementDetail/export', {
        ...this.queryParams
      }, `pptTemplateThemeDetail_${new Date().getTime()}.xlsx`)
    }
    ,

    /** 返回 */
    handleGoBack() {
      this.$router.push({
        path: '/system/pptTemplateManagement'
      })
    },


    /**
     * 事件触发防抖 判断点击是否在指定时间内
     * @param limit 限制时间（毫秒），默认为 1500 毫秒（1.5 秒）
     * @returns {boolean} true 可以继续执行，false 不能继续执行
     */
    isClickWithinLimit(limit = 1500) {
      const currentTime = new Date().getTime()
      const timeDiff = currentTime - this.lastClickTime

      // 如果点击时间差小于指定的限制时间（默认1秒）
      if (timeDiff < limit) {
        this.$notify({
          title: '提示',
          message: '点击频繁，请稍等！',
          type: 'warning',
          duration: 1500
        })
        return false // 在时间限制内，禁止继续执行
      } else {
        // 更新上次点击的时间戳
        this.lastClickTime = currentTime // 这里在用户成功点击后更新
        return true // 超过时间限制，可以继续执行
      }
    }
    ,
    // 点击文件名标签
    handleClickScopeRowFileName(row, index) {
      if (!this.isClickWithinLimit()) {
        console.log('触发防抖')
        return // 如果不能继续执行，则返回
      }
      this.$set(row, 'isDisabled', true) // 禁用标签
      this.$notify({
        title: '开始下载',
        message: '正在下载中',
        type: 'success',
        duration: 0
      })

      // 请求参数
      const data = {'filePath': row.absolutePath}

      // 调用方法下载
      downLoad(data).then(res => {
        if (res) {
          const link = document.createElement('a')
          link.download = row.fileName + '.' + row.suffix
          link.style.display = 'none'
          const blob = new Blob([res])
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          setTimeout(() => {
            link.click()
            document.body.removeChild(link)
            this.$notify.closeAll()
            this.$set(row, 'isDisabled', false) // 恢复标签
          }, 100)

        }
      }).catch(error => {
        this.$notify({
          title: '下载失败',
          message: '文件下载时出现错误，请稍后再试。',
          type: 'error',
          duration: 3000
        })
        setTimeout(() => {
          this.$notify.closeAll()
          this.$set(row, 'isDisabled', false) // 恢复标签
        }, 3000)
      }).finally(
      )
    }
    ,
    /** 处理输入结构编号时触发*/
    handleInputVersionStructure() {
      let input = this.form.versionStructure

      input += '_' // 自动添加下划线

      // 拆分输入内容为下划线分隔的块
      const parts = input.split('_')

      // 只允许输入最多5个数值块
      if (parts.length >= 4) {
        input = parts.slice(0, 5).join('_')
      }
      // 更新值
      this.form.versionStructure = input

      // 更新文件名
      this.form.fileName = `${this.form.themeLabel}_${this.form.contentType}_${this.form.versionStructure}.${this.form.suffix}`
    }
    ,
    /** 处理选择内容类型时触发*/
    handleChangeContentTypeOption(data) {
      // 更新值
      this.form.fileName = this.form.themeLabel + '_' + this.form.contentType + '_' + this.form.versionStructure + '.' + this.form.suffix
    }
    ,
    /** 处理选择文件后缀时触发*/
    handleChangesuffixOption(data) {
      if (data === 'png') {
        this.contentTypeOptions = [ // 内容类型选择
          {
            value: 'bgc_background',
            label: 'bgc_background'
          },
          {
            value: 'bgc_begin',
            label: 'bgc_begin'
          },
          {
            value: 'bgc_bigTitle',
            label: 'bgc_bigTitle'
          },
          {
            value: 'bgc_content',
            label: 'bgc_content'
          },
          {
            value: 'bgc_page',
            label: 'bgc_page'
          },
          {
            value: 'bgc_end',
            label: 'bgc_end'
          },
        ]
        this.suffixOptions= [ // 文件后缀选择
          {
            value: 'png',
            label: 'png'
          },
          {
            value: 'pptx',
            label: 'pptx'
          }
        ]
      }
      if (data === 'pptx') {
        this.contentTypeOptions = [ // 内容类型选择
          {
            value: 'begin',
            label: 'begin'
          },
          {
            value: 'bigTitle',
            label: 'bigTitle'
          },
          {
            value: 'content',
            label: 'content'
          },
          {
            value: 'page',
            label: 'page'
          },
          {
            value: 'end',
            label: 'end'
          },
        ]
      }
      // 更新值
      this.uploadType = '.' + data
      this.form.fileName = this.form.themeLabel + '_' + this.form.contentType + '_' + this.form.versionStructure + '.' + this.form.suffix
    },
  }
}
</script>

<style scoped>
.clickable-tag {
  cursor: pointer; /* 鼠标悬浮时显示小手 */
}

.no-wrapper {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
