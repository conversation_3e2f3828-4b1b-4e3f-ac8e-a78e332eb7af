<template>
  <div class="app-container background-container">

    <div class="box1">
      <div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bigTitle">
              单门课程分析统计
            </div>
          </el-col>
        </el-row>
      </div>

      <div>
        <!-- 一级标题样式 -->
        <div class="title1">一、学生基本信息</div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              学生姓名：陌小杰
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              课程名称: 城市管理学
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              报告日期: 2024/10/15
            </div>
          </el-col>
        </el-row>
      </div>


      <div>
        <!-- 一级标题样式 -->
        <div class="title1">二、学习进度分析</div>

        <div class="litTitle">课程进度概述</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              总章数：20
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              已完成章节：16
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              当前已完成章节进度：80%
            </div>
          </el-col>
        </el-row>

        <div class="litTitle">学习速度评估</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              平均完成进度：80%
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              与班级平均进度比较：落后
            </div>
          </el-col>
        </el-row>

        <div class="litTitle">提问频次评估</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              智能问答总次数：64
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              平均每周问答次数：4
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              与班级平均问答次数比较：落后
            </div>
          </el-col>
        </el-row>
      </div>


      <div>
        <div class="title1">三、课后作业完成度分析</div>

        <div class="litTitle">作业提交情况</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              总作业次数：32
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              已提交次数：30
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              提交比例：93.75%
            </div>
          </el-col>
        </el-row>
      </div>

      <div>
        <div class="title1">四、课后作业正确率分析</div>

        <div class="litTitle">整体正确率</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              总题目数：400
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              正确题目数：360
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              正确率：90%
            </div>
          </el-col>
        </el-row>

        <div class="litTitle">知识点掌握情况</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              易错点分布：城市管理的概念和内涵特征，城市管理系统的左右和框架。
            </div>
          </el-col>
        </el-row>
      </div>


      <div>
        <div class="title1">五、综合评估与建议</div>
        <div class="litTitle">学习进度</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              学生进度落后，建议调整学习计划，增加学习时间，必要时寻求教师或同学帮助。
            </div>
          </el-col>
        </el-row>
        <div class="litTitle">提问频次</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              学生提问次数低于班级平均值，建议调整学习计划，提高提问频率，及时提问。
            </div>
          </el-col>
        </el-row>


        <div class="litTitle">作业正确率</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              作业正确率较低，针对错误率较高的知识点，建议学生复习相关教材，观看教学视频，或进行针对性练习。
            </div>
          </el-col>
        </el-row>
        <div class="litTitle">学习策略</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              建议学生采用主动学习策略，如小组讨论、思维导图等，以提高学习效率和理解深度。通过小组讨论，学生可以分享彼此的观点，互相启发，从而获得更全面的知识。
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'studentCourseAnalysis',
  data() {
    return {
      showPdfView: false,
      pdfSrc: '',
      loading: false,
      fullscreenLoading: false, // 全局加载状态
      lable: ''
    }
  },
  created() {
  },
  methods: {
    handleShowEmbedPdf() {
      this.openFullScreen1()
      this.pdfSrc = 'https://www.papaicai.com/home/<USER>/view/StudentSingleCourseAnalysisandStatistics.pdf'
      this.showPdfView = true
      setTimeout(() => {
        this.closeFullScreen() // 关闭加载
      }, 800)
    },
    handleShowEmbedPdfAll() {
      this.openFullScreen1()
      this.pdfSrc = 'https://www.papaicai.com/home/<USER>/view/StudentAllLearningCourses.pdf'
      this.showPdfView = true

      setTimeout(() => {
        this.closeFullScreen() // 关闭加载
      }, 800)

    },
    handleShowEmbedPdfClass() {
      this.openFullScreen1()
      this.pdfSrc = 'https://www.papaicai.com/home/<USER>/view/ClassCourseStatistics.pdf'
      this.showPdfView = true

      setTimeout(() => {
        this.closeFullScreen() // 关闭加载
      }, 800)

    },
    openFullScreen1() {
      this.fullscreenLoading = true // 开始加载
    },
    closeFullScreen() {
      this.fullscreenLoading = false // 关闭加载
    },
    closeShowEmbedPdf() {
      this.fullscreenLoading = false // 关闭加载
      this.showPdfView = false // 关闭显示pdf

    }
  }
}
</script>

<style scoped>

.app-container {
  padding: 20px;
}

.background-container {
  /* 设置盒子的大小 */
  width: 100%;
  height: auto; /* 让盒子高度为整个视窗高度 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  margin-top: 10px;
}


.bg-gray {
  background-color: #f0f0f0; /* 浅灰色背景 */
}


.grid-content {
  border-radius: 4px;
  min-height: 40px;
  padding: 10px;
  box-sizing: border-box;
  max-width: 100%; /* 最大宽度为100% */
  overflow-wrap: break-word;
  word-break: break-word; /* 强制长单词换行 */
  margin-top: 5px;
  overflow: hidden; /* 防止溢出 */
}


.bigTitle {
  /* 字体居中 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 设置盒子的高度为一行 */
  height: 30px; /* 可以根据需要调整高度 */

  /* 设置字体大小 */
  font-size: 24px;

  /* 可选的样式 */
  font-weight: bold; /* 加粗 */
}

.title1 {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  font-style: italic; /* 设置字体为斜体 */
}

.litTitle {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #3e3f41;
}

.box-center {
  /* 使用 flex 布局 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 确保盒子有一定的高度 */
  height: 20px; /* 根据需要调整 */

  /* 可选的样式 */
  font-size: 16px;
}


.box-left {
  display: flex;
  flex-direction: row;
  justify-content: left;
  width: 100%; /* 确保盒子宽度充满容器 */
  align-items: flex-start; /* 垂直顶部对齐 */
  font-size: 16px;
  overflow-wrap: break-word; /* 确保文本换行 */
  word-break: break-all; /* 强制长单词换行 */
}

.box1 {
  width: 90%;
  height: auto;

  /* 背景图片 */
  background-image: url('../../../views/studentCourseAnalysis/1.png'); /* 替换为实际图片路径 */

  /* 设置背景图片铺满整个盒子 */
  background-size: cover;

  /* 图片居中 */
  background-position: center;

  /* 不重复图片 */
  background-repeat: no-repeat;

  /* 确保背景图片不会跟随页面滚动 */
  background-attachment: fixed;
}

.textDataStyle {
  font-size: 16px;
  color: #666666; /* 设置字体颜色为更灰色 */
  line-height: 1.5; /* 设置行高，增加行间距 */
}
</style>
