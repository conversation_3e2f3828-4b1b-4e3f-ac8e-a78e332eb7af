<template>
  <div class="app-container background-container">

    <div class="box1">
      <div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bigTitle">
              班级课程分析统计
            </div>
          </el-col>
        </el-row>
      </div>

      <div>
        <!-- 一级标题样式 -->
        <div class="title1">一、班级基本信息</div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              班级名称：管理学1班
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              报告日期: 2024/10/15
            </div>
          </el-col>
        </el-row>
      </div>


      <div>
        <!-- 一级标题样式 -->
        <div class="title1">二、学习进度统计</div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              完成进度领先的学生比例：60%
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              完成进度持平的学生比例：10%
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              完成进度落后的学生比例：20%
            </div>
          </el-col>
        </el-row>
      </div>

      <div>
        <div class="title1">三、课后作业情况</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              完成度高于90%的学生比例：80%
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-gray box-center textDataStyle">
              正确率高于80%的学生比例：90%
            </div>
          </el-col>
        </el-row>
        <div class="litTitle">需要特别关注的学生（作业完成度或正确率较低)</div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              陌小杰
            </div>
          </el-col>
        </el-row>

      </div>


      <div>
        <div class="title1">四、问答频率与质量</div>
        <el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="grid-content bg-gray box-center textDataStyle">
                积极参与问答的学生比例：100%
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content bg-gray box-center textDataStyle">
                回答质量较高的学生比例：80%
              </div>
            </el-col>
          </el-row>
        </el-row>
        <div class="litTitle">需要提高问答质量的学生（提问较少或回答不准确）</div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              陌小杰
            </div>
          </el-col>
        </el-row>
      </div>
      <div>
        <div class="title1">五、总结与建议</div>
        <div class="litTitle">学习进度</div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              班级整体学习进度较为均衡，但仍有部分学生需要加快进度。建议教师关注落后学生的情况，提供必要的帮助和指导。
            </div>
          </el-col>
        </el-row>

        <div class="litTitle">课后作业</div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              大部分学生的作业完成度和正确率较高，但仍有少数学生需要提高。建议教师针对这些学生进行个别辅导，帮助他们提高作业质量。
            </div>
          </el-col>
        </el-row>

        <div class="litTitle">问答互动</div>
        <el-row>
          <el-col :span="24">
            <div class="grid-content bg-gray box-left textDataStyle">
              班级整体问答氛围较好，但仍有部分学生需要积极参与。建议教师鼓励学生多提问和回答问题，促进班级内部的交流和互动。
              同时，针对回答质量较低的学生进行个别指导，提高他们的问答能力。
            </div>
          </el-col>
        </el-row>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'classCourseAnalysis',
  data() {
    return {
      showPdfView: false,
      pdfSrc: '',
      loading: false,
      fullscreenLoading: false, // 全局加载状态
      lable: ''
    }
  },
  created() {
  },
  methods: {
    openFullScreen1() {
      this.fullscreenLoading = true // 开始加载
    },
    closeFullScreen() {
      this.fullscreenLoading = false // 关闭加载
    },
    closeShowEmbedPdf() {
      this.fullscreenLoading = false // 关闭加载
      this.showPdfView = false // 关闭显示pdf

    }
  }
}
</script>

<style scoped>

.app-container {
  padding: 20px;

}

.background-container {
  /* 设置盒子的大小 */
  width: 100%;
  height: auto; /* 让盒子高度为整个视窗高度 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  margin-top: 10px;
}


.bg-gray {
  background-color: #f0f0f0; /* 浅灰色背景 */
}



.grid-content {
  border-radius: 4px;
  min-height: 40px; /* 最小高度，确保内容不会太少 */
  padding: 10px; /* 内边距，保持内容距离盒子边框 */
  box-sizing: border-box; /* 确保padding包含在总宽度内 */
  max-width: 100%; /* 最大宽度为100% */
  overflow-wrap: break-word; /* 确保长单词换行 */
  display: block; /* 确保内容可以正常显示 */
  margin-top: 5px;
}



.bigTitle {
  /* 字体居中 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 设置盒子的高度为一行 */
  height: 30px; /* 可以根据需要调整高度 */

  /* 设置字体大小 */
  font-size: 24px;

  /* 可选的样式 */
  font-weight: bold; /* 加粗 */
}

.title1 {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  font-style: italic; /* 设置字体为斜体 */
}

.litTitle {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #3e3f41;
}

.box-center {
  /* 使用 flex 布局 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 确保盒子有一定的高度 */
  height: 20px; /* 根据需要调整 */

  /* 可选的样式 */
  font-size: 16px;
}


.box-left {
  display: flex; /* 使内容在垂直方向排列 */
  flex-direction: row; /* 左到右排列 */
  justify-content: left; /* 开始对齐 */
  width: 100%; /* 确保盒子宽度充满容器 */
  align-items: center; /* 垂直居中 */
  font-size: 16px;
}

.box1 {
  width: 90%;
  height: auto;

  /* 背景图片 */
  background-image: url('../../../views/studentCourseAnalysis/1.png'); /* 替换为实际图片路径 */

  /* 设置背景图片铺满整个盒子 */
  background-size: cover;

  /* 图片居中 */
  background-position: center;

  /* 不重复图片 */
  background-repeat: no-repeat;

  /* 确保背景图片不会跟随页面滚动 */
  background-attachment: fixed;
}

.textDataStyle {
  font-size: 16px;
  color: #666666; /* 设置字体颜色为更灰色 */
  line-height: 1.5; /* 设置行高，增加行间距 */
}
</style>
