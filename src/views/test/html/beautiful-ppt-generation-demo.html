<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美化PPT生成页面演示 - AI PPT 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--theme-gradient, linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%));
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            padding: 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .step-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .step-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .step-header h2 {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .step-description {
            font-size: 1.1rem;
            color: #606266;
        }

        .generation-container {
            max-width: 800px;
            margin: 0 auto 30px;
        }

        /* 美化的空状态样式 */
        .beautiful-empty-state {
            position: relative;
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border-radius: 24px;
            padding: 40px 30px;
            text-align: center;
            border: 1px solid rgba(64, 158, 255, 0.1);
            box-shadow: 0 8px 32px rgba(64, 158, 255, 0.08);
            overflow: hidden;
            height: auto;
            max-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 浮动装饰元素 */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
            animation: float 6s ease-in-out infinite;
        }

        .floating-element.element-1 {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element.element-2 {
            width: 60px;
            height: 60px;
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }

        .floating-element.element-3 {
            width: 40px;
            height: 40px;
            bottom: 30%;
            left: 20%;
            animation-delay: 2s;
        }

        .floating-element.element-4 {
            width: 70px;
            height: 70px;
            bottom: 15%;
            right: 10%;
            animation-delay: 3s;
        }

        .floating-element.element-5 {
            width: 50px;
            height: 50px;
            top: 50%;
            left: 5%;
            animation-delay: 4s;
        }

        /* 主要内容 */
        .main-content {
            position: relative;
            z-index: 2;
            max-width: 500px;
            margin: 0 auto;
        }

        /* 动画图标容器 */
        .animated-icon-container {
            position: relative;
            display: inline-block;
            margin-bottom: 30px;
        }

        .icon-background {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        .animated-icon {
            position: relative;
            font-size: 80px;
            color: #409EFF;
            z-index: 3;
            animation: bounce 2s ease-in-out infinite;
        }

        .icon-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 140px;
            height: 140px;
            border: 2px solid rgba(64, 158, 255, 0.3);
            border-radius: 50%;
            animation: ripple 3s ease-out infinite;
        }

        /* 内容文字 */
        .content-text {
            margin-bottom: 40px;
        }

        .main-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .description {
            font-size: 16px;
            color: #606266;
            line-height: 1.6;
            margin: 0;
        }

        /* 特性网格 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            border: 1px solid rgba(64, 158, 255, 0.1);
            transition: all 0.3s ease;
            animation: fadeInUp 0.6s ease-out;
        }

        .feature-item:nth-child(1) { animation-delay: 0.1s; }
        .feature-item:nth-child(2) { animation-delay: 0.2s; }
        .feature-item:nth-child(3) { animation-delay: 0.3s; }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
            background: rgba(255, 255, 255, 0.95);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
        }

        .feature-icon i {
            font-size: 24px;
            color: white;
        }

        .feature-item span {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
        }

        /* 操作提示 */
        .action-hint {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 16px;
            border: 1px solid rgba(64, 158, 255, 0.1);
        }

        .hint-icon {
            animation: bounce 1.5s ease-in-out infinite;
        }

        .hint-icon i {
            font-size: 20px;
            color: #409EFF;
        }

        .action-hint p {
            font-size: 16px;
            color: #409EFF;
            margin: 0;
            font-weight: 500;
        }

        /* 底部按钮 */
        .demo-button {
            display: block;
            margin: 30px auto 0;
            padding: 15px 40px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
        }

        /* 动画定义 */
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.7;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.9;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes ripple {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .beautiful-empty-state {
                padding: 40px 20px;
                border-radius: 16px;
                min-height: 300px;
            }

            .animated-icon {
                font-size: 60px;
            }

            .main-title {
                font-size: 24px;
            }

            .description {
                font-size: 14px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .action-hint p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>🎨 美化PPT生成页面</h1>
            <p>全新设计的PPT生成等待界面，包含丰富的动画效果</p>
        </div>

        <div class="step-container">
            <div class="step-header">
                <h2>生成您的PPT</h2>
                <p class="step-description">点击"生成PPT"按钮开始创建</p>
            </div>

            <div class="generation-container">
                <div class="beautiful-empty-state">
                    <!-- 动画背景装饰 -->
                    <div class="floating-elements">
                        <div class="floating-element element-1"></div>
                        <div class="floating-element element-2"></div>
                        <div class="floating-element element-3"></div>
                        <div class="floating-element element-4"></div>
                        <div class="floating-element element-5"></div>
                    </div>

                    <!-- 主要内容区域 -->
                    <div class="main-content">
                        <!-- 动画图标 -->
                        <div class="animated-icon-container">
                            <div class="icon-background"></div>
                            <i class="animated-icon">📄</i>
                            <div class="icon-pulse"></div>
                        </div>

                        <!-- 标题和描述 -->
                        <div class="content-text">
                            <h3 class="main-title">准备生成您的专属PPT</h3>
                            <p class="description">AI将根据您的大纲内容，智能生成精美的演示文稿</p>
                        </div>

                        <!-- 特性展示 -->
                        <div class="features-grid">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i>✨</i>
                                </div>
                                <span>AI智能生成</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i>🎨</i>
                                </div>
                                <span>精美模板</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i>⚡</i>
                                </div>
                                <span>快速生成</span>
                            </div>
                        </div>

                        <!-- 提示文字 -->
                        <div class="action-hint">
                            <div class="hint-icon">
                                <i>👇</i>
                            </div>
                            <p>点击下方"生成PPT"按钮开始创建</p>
                        </div>
                    </div>
                </div>
            </div>

            <button class="demo-button" onclick="showMessage()">
                🚀 生成PPT
            </button>
        </div>
    </div>

    <script>
        function showMessage() {
            alert('🎉 PPT生成功能演示！\n\n在实际应用中，这里会开始PPT生成流程。');
        }
    </script>
</body>
</html>
