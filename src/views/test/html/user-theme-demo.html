<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户头像主题演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--theme-gradient, linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%));
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            transition: background 0.6s ease-in-out;
            z-index: -1;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .user-settings-area {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 15px;
            pointer-events: none;
        }

        .user-settings-area > * {
            pointer-events: auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            background: transparent;
            padding: 6px 12px 6px 6px;
            border-radius: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            transform: translateY(-1px);
        }

        .user-info:hover .user-avatar {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }

        .user-info:hover .user-name {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--theme-gradient, linear-gradient(135deg, #409EFF, #67C23A));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.6s ease-in-out;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .content p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .theme-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .theme-btn {
            padding: 12px 24px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .theme-btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: white;
        }

        .demo-info {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            padding: 15px 25px;
            border-radius: 20px;
            color: white;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-info h3 {
            margin-bottom: 5px;
            font-size: 16px;
        }

        .demo-info p {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="background-overlay"></div>
    
    <div class="user-settings-area">
        <div class="user-info">
            <div class="user-avatar">👤</div>
            <span class="user-name">admin</span>
        </div>
    </div>

    <div class="content">
        <h1>🎨 用户头像主题演示</h1>
        <p>观察右上角用户头像如何跟随主题变化</p>
        
        <div class="theme-buttons">
            <button class="theme-btn active" onclick="switchTheme('default')">星河幻境</button>
            <button class="theme-btn" onclick="switchTheme('ocean')">深海蓝调</button>
            <button class="theme-btn" onclick="switchTheme('sunset')">晚霞余晖</button>
            <button class="theme-btn" onclick="switchTheme('forest')">翡翠森林</button>
            <button class="theme-btn" onclick="switchTheme('aurora')">极光之夜</button>
        </div>
    </div>

    <div class="demo-info">
        <h3 id="currentThemeName">星河幻境</h3>
        <p>用户头像背景会实时跟随主题渐变色变化</p>
    </div>

    <script>
        const themes = {
            default: {
                name: '星河幻境',
                gradient: 'linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)'
            },
            ocean: {
                name: '深海蓝调',
                gradient: 'linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(25, 118, 210, 0.75) 15%, rgba(33, 150, 243, 0.70) 30%, rgba(3, 169, 244, 0.65) 45%, rgba(0, 188, 212, 0.60) 60%, rgba(0, 150, 136, 0.70) 75%, rgba(76, 175, 80, 0.65) 90%, rgba(139, 195, 74, 0.75) 100%)'
            },
            sunset: {
                name: '晚霞余晖',
                gradient: 'linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 152, 0, 0.70) 15%, rgba(255, 193, 7, 0.60) 30%, rgba(255, 235, 59, 0.55) 45%, rgba(255, 111, 97, 0.65) 60%, rgba(240, 98, 146, 0.70) 75%, rgba(186, 104, 200, 0.65) 90%, rgba(149, 117, 205, 0.70) 100%)'
            },
            forest: {
                name: '翡翠森林',
                gradient: 'linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(46, 125, 50, 0.75) 15%, rgba(67, 160, 71, 0.65) 30%, rgba(102, 187, 106, 0.60) 45%, rgba(129, 199, 132, 0.65) 60%, rgba(165, 214, 167, 0.55) 75%, rgba(200, 230, 201, 0.50) 90%, rgba(232, 245, 233, 0.45) 100%)'
            },
            aurora: {
                name: '极光之夜',
                gradient: 'linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(106, 27, 154, 0.70) 15%, rgba(142, 36, 170, 0.65) 30%, rgba(171, 71, 188, 0.60) 45%, rgba(186, 104, 200, 0.65) 60%, rgba(206, 147, 216, 0.55) 75%, rgba(225, 190, 231, 0.50) 90%, rgba(243, 229, 245, 0.45) 100%)'
            }
        };

        function switchTheme(themeId) {
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新CSS变量和主题名称
            const theme = themes[themeId];
            document.documentElement.style.setProperty('--theme-gradient', theme.gradient);
            document.getElementById('currentThemeName').textContent = theme.name;
            
            console.log(`已切换到主题: ${theme.name}`);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始主题
            document.documentElement.style.setProperty('--theme-gradient', themes.default.gradient);
        });
    </script>
</body>
</html>
