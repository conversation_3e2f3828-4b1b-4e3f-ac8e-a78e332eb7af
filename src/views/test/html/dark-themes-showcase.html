<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色主题展示 - AI PPT 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .themes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .theme-card {
            background: #2a2a2a;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 1px solid #333;
        }

        .theme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
        }

        .theme-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .theme-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .theme-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .theme-content h3 {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .theme-content p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .theme-info {
            padding: 20px;
            background: #2a2a2a;
        }

        .theme-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        .theme-description {
            color: #ccc;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .theme-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .theme-tag {
            background: #444;
            color: #ccc;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #555;
        }

        .current-demo {
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: #2a2a2a;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }

        .current-demo h2 {
            color: white;
            margin-bottom: 20px;
        }

        .demo-area {
            height: 300px;
            border-radius: 12px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .demo-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            transition: background 0.6s ease-in-out;
        }

        .user-preview {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 12px 6px 6px;
            border-radius: 25px;
            backdrop-filter: blur(15px);
        }

        .user-preview-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--theme-gradient, linear-gradient(135deg, #409EFF, #67C23A));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.6s ease-in-out;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-preview-name {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
            letter-spacing: 0.5px;
        }

        .demo-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .demo-content h3 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .demo-content p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .current-theme-name {
            font-size: 1.5rem;
            color: #409EFF;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .theme-category {
            margin-bottom: 40px;
        }

        .category-title {
            font-size: 1.8rem;
            color: white;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .dark-badge {
            display: inline-block;
            background: linear-gradient(135deg, #333, #555);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
            border: 1px solid #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌙 深色主题展示</h1>
        
        <div class="theme-category">
            <h2 class="category-title">深色系主题 <span class="dark-badge">DARK THEMES</span></h2>
            
            <div class="themes-grid">
                <!-- 暗夜森林 -->
                <div class="theme-card" onclick="selectTheme('darkforest')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(10, 40, 10, 0.80) 15%, rgba(20, 60, 20, 0.75) 30%, rgba(15, 80, 15, 0.70) 45%, rgba(25, 100, 25, 0.65) 60%, rgba(35, 120, 35, 0.60) 75%, rgba(45, 140, 45, 0.55) 90%, rgba(55, 160, 55, 0.50) 100%)"></div>
                        <div class="theme-content">
                            <h3>🌲 暗夜森林</h3>
                            <p>深邃神秘的森林夜色</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">暗夜森林</div>
                        <div class="theme-description">深绿色系渐变，营造神秘森林的夜晚氛围，适合自然和环保主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">神秘</span>
                            <span class="theme-tag">自然</span>
                        </div>
                    </div>
                </div>

                <!-- 深海深渊 -->
                <div class="theme-card" onclick="selectTheme('deepocean')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(0, 10, 30, 0.90) 0%, rgba(5, 20, 50, 0.85) 15%, rgba(10, 30, 70, 0.80) 30%, rgba(15, 40, 90, 0.75) 45%, rgba(20, 50, 110, 0.70) 60%, rgba(25, 60, 130, 0.65) 75%, rgba(30, 70, 150, 0.60) 90%, rgba(35, 80, 170, 0.55) 100%)"></div>
                        <div class="theme-content">
                            <h3>🌊 深海深渊</h3>
                            <p>深邃无底的海洋深处</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">深海深渊</div>
                        <div class="theme-description">深蓝色系渐变，模拟深海的神秘与深邃，适合科技和探索主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">科技</span>
                            <span class="theme-tag">探索</span>
                        </div>
                    </div>
                </div>

                <!-- 暗影紫魅 -->
                <div class="theme-card" onclick="selectTheme('shadowpurple')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(20, 0, 40, 0.90) 0%, rgba(40, 10, 60, 0.85) 15%, rgba(60, 20, 80, 0.80) 30%, rgba(80, 30, 100, 0.75) 45%, rgba(100, 40, 120, 0.70) 60%, rgba(120, 50, 140, 0.65) 75%, rgba(140, 60, 160, 0.60) 90%, rgba(160, 70, 180, 0.55) 100%)"></div>
                        <div class="theme-content">
                            <h3>🔮 暗影紫魅</h3>
                            <p>神秘魅惑的紫色暗影</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">暗影紫魅</div>
                        <div class="theme-description">深紫色系渐变，充满神秘魅力，适合艺术和创意主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">魅惑</span>
                            <span class="theme-tag">艺术</span>
                        </div>
                    </div>
                </div>

                <!-- 碳黑科技 -->
                <div class="theme-card" onclick="selectTheme('carbonblack')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(20, 20, 25, 0.90) 15%, rgba(30, 30, 40, 0.85) 30%, rgba(40, 40, 55, 0.80) 45%, rgba(50, 50, 70, 0.75) 60%, rgba(60, 60, 85, 0.70) 75%, rgba(70, 70, 100, 0.65) 90%, rgba(80, 80, 115, 0.60) 100%)"></div>
                        <div class="theme-content">
                            <h3>⚫ 碳黑科技</h3>
                            <p>极简现代的科技感</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">碳黑科技</div>
                        <div class="theme-description">深灰黑色系渐变，极简现代，适合科技和商务主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">科技</span>
                            <span class="theme-tag">现代</span>
                        </div>
                    </div>
                </div>

                <!-- 血月之夜 -->
                <div class="theme-card" onclick="selectTheme('bloodmoon')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(40, 0, 0, 0.90) 0%, rgba(60, 10, 10, 0.85) 15%, rgba(80, 20, 20, 0.80) 30%, rgba(100, 30, 30, 0.75) 45%, rgba(120, 40, 40, 0.70) 60%, rgba(140, 50, 50, 0.65) 75%, rgba(160, 60, 60, 0.60) 90%, rgba(180, 70, 70, 0.55) 100%)"></div>
                        <div class="theme-content">
                            <h3>🌙 血月之夜</h3>
                            <p>神秘诡异的血色月夜</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">血月之夜</div>
                        <div class="theme-description">深红色系渐变，营造神秘诡异的氛围，适合悬疑和戏剧主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">神秘</span>
                            <span class="theme-tag">戏剧</span>
                        </div>
                    </div>
                </div>

                <!-- 神秘夜幕 -->
                <div class="theme-card" onclick="selectTheme('mysticnight')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(15, 15, 35, 0.90) 0%, rgba(25, 25, 55, 0.85) 15%, rgba(35, 35, 75, 0.80) 30%, rgba(45, 45, 95, 0.75) 45%, rgba(55, 55, 115, 0.70) 60%, rgba(65, 65, 135, 0.65) 75%, rgba(75, 75, 155, 0.60) 90%, rgba(85, 85, 175, 0.55) 100%)"></div>
                        <div class="theme-content">
                            <h3>🌌 神秘夜幕</h3>
                            <p>深邃神秘的夜空幕布</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">神秘夜幕</div>
                        <div class="theme-description">深蓝紫色系渐变，营造神秘夜空的氛围，适合哲学和思考主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">哲学</span>
                            <span class="theme-tag">思考</span>
                        </div>
                    </div>
                </div>

                <!-- 翡翠暗影 -->
                <div class="theme-card" onclick="selectTheme('emeralddark')">
                    <div class="theme-preview">
                        <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(0, 30, 20, 0.90) 0%, rgba(10, 50, 35, 0.85) 15%, rgba(20, 70, 50, 0.80) 30%, rgba(30, 90, 65, 0.75) 45%, rgba(40, 110, 80, 0.70) 60%, rgba(50, 130, 95, 0.65) 75%, rgba(60, 150, 110, 0.60) 90%, rgba(70, 170, 125, 0.55) 100%)"></div>
                        <div class="theme-content">
                            <h3>💎 翡翠暗影</h3>
                            <p>深邃优雅的翡翠光影</p>
                        </div>
                    </div>
                    <div class="theme-info">
                        <div class="theme-name">翡翠暗影</div>
                        <div class="theme-description">深绿色系渐变，优雅而神秘，适合奢华和品质主题</div>
                        <div class="theme-tags">
                            <span class="theme-tag">深色</span>
                            <span class="theme-tag">奢华</span>
                            <span class="theme-tag">品质</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="current-demo">
            <h2>🎯 深色主题预览</h2>
            <div class="current-theme-name" id="currentThemeName">暗夜森林</div>
            <div class="demo-area">
                <div id="demoGradient" class="demo-gradient" style="background: linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(10, 40, 10, 0.80) 15%, rgba(20, 60, 20, 0.75) 30%, rgba(15, 80, 15, 0.70) 45%, rgba(25, 100, 25, 0.65) 60%, rgba(35, 120, 35, 0.60) 75%, rgba(45, 140, 45, 0.55) 90%, rgba(55, 160, 55, 0.50) 100%)"></div>
                <div class="user-preview">
                    <div class="user-preview-avatar">👤</div>
                    <span class="user-preview-name">admin</span>
                </div>
                <div class="demo-content">
                    <h3>AI PPT 生成器</h3>
                    <p>深色主题 - 专业而神秘</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themes = {
            darkforest: {
                name: '暗夜森林',
                gradient: 'linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(10, 40, 10, 0.80) 15%, rgba(20, 60, 20, 0.75) 30%, rgba(15, 80, 15, 0.70) 45%, rgba(25, 100, 25, 0.65) 60%, rgba(35, 120, 35, 0.60) 75%, rgba(45, 140, 45, 0.55) 90%, rgba(55, 160, 55, 0.50) 100%)'
            },
            deepocean: {
                name: '深海深渊',
                gradient: 'linear-gradient(135deg, rgba(0, 10, 30, 0.90) 0%, rgba(5, 20, 50, 0.85) 15%, rgba(10, 30, 70, 0.80) 30%, rgba(15, 40, 90, 0.75) 45%, rgba(20, 50, 110, 0.70) 60%, rgba(25, 60, 130, 0.65) 75%, rgba(30, 70, 150, 0.60) 90%, rgba(35, 80, 170, 0.55) 100%)'
            },
            shadowpurple: {
                name: '暗影紫魅',
                gradient: 'linear-gradient(135deg, rgba(20, 0, 40, 0.90) 0%, rgba(40, 10, 60, 0.85) 15%, rgba(60, 20, 80, 0.80) 30%, rgba(80, 30, 100, 0.75) 45%, rgba(100, 40, 120, 0.70) 60%, rgba(120, 50, 140, 0.65) 75%, rgba(140, 60, 160, 0.60) 90%, rgba(160, 70, 180, 0.55) 100%)'
            },
            carbonblack: {
                name: '碳黑科技',
                gradient: 'linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(20, 20, 25, 0.90) 15%, rgba(30, 30, 40, 0.85) 30%, rgba(40, 40, 55, 0.80) 45%, rgba(50, 50, 70, 0.75) 60%, rgba(60, 60, 85, 0.70) 75%, rgba(70, 70, 100, 0.65) 90%, rgba(80, 80, 115, 0.60) 100%)'
            },
            bloodmoon: {
                name: '血月之夜',
                gradient: 'linear-gradient(135deg, rgba(40, 0, 0, 0.90) 0%, rgba(60, 10, 10, 0.85) 15%, rgba(80, 20, 20, 0.80) 30%, rgba(100, 30, 30, 0.75) 45%, rgba(120, 40, 40, 0.70) 60%, rgba(140, 50, 50, 0.65) 75%, rgba(160, 60, 60, 0.60) 90%, rgba(180, 70, 70, 0.55) 100%)'
            },
            mysticnight: {
                name: '神秘夜幕',
                gradient: 'linear-gradient(135deg, rgba(15, 15, 35, 0.90) 0%, rgba(25, 25, 55, 0.85) 15%, rgba(35, 35, 75, 0.80) 30%, rgba(45, 45, 95, 0.75) 45%, rgba(55, 55, 115, 0.70) 60%, rgba(65, 65, 135, 0.65) 75%, rgba(75, 75, 155, 0.60) 90%, rgba(85, 85, 175, 0.55) 100%)'
            },
            emeralddark: {
                name: '翡翠暗影',
                gradient: 'linear-gradient(135deg, rgba(0, 30, 20, 0.90) 0%, rgba(10, 50, 35, 0.85) 15%, rgba(20, 70, 50, 0.80) 30%, rgba(30, 90, 65, 0.75) 45%, rgba(40, 110, 80, 0.70) 60%, rgba(50, 130, 95, 0.65) 75%, rgba(60, 150, 110, 0.60) 90%, rgba(70, 170, 125, 0.55) 100%)'
            }
        };

        function selectTheme(themeId) {
            const theme = themes[themeId];
            if (theme) {
                // 更新CSS变量和主题名称
                document.documentElement.style.setProperty('--theme-gradient', theme.gradient);
                document.getElementById('demoGradient').style.background = theme.gradient;
                document.getElementById('currentThemeName').textContent = theme.name;
                
                console.log(`已切换到深色主题: ${theme.name}`);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.documentElement.style.setProperty('--theme-gradient', themes.darkforest.gradient);
        });
    </script>
</body>
</html>
