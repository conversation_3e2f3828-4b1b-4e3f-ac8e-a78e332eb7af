<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高度修复对比 - PPT生成页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            padding: 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .section-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.5rem;
            color: #333;
        }

        .section-title.problem {
            color: #e74c3c;
        }

        .section-title.fixed {
            color: #27ae60;
        }

        .mock-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .mock-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .mock-header h2 {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .mock-header p {
            color: #606266;
        }

        /* 修复前的样式 - 高度过高 */
        .before-fix {
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border-radius: 24px;
            padding: 60px 40px;
            text-align: center;
            border: 1px solid rgba(64, 158, 255, 0.1);
            box-shadow: 0 8px 32px rgba(64, 158, 255, 0.08);
            min-height: 400px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }

        /* 修复后的样式 - 高度适中 */
        .after-fix {
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border-radius: 24px;
            padding: 40px 30px;
            text-align: center;
            border: 1px solid rgba(64, 158, 255, 0.1);
            box-shadow: 0 8px 32px rgba(64, 158, 255, 0.08);
            height: auto;
            max-height: 60vh;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }

        .mock-icon {
            font-size: 60px;
            color: #409EFF;
            margin-bottom: 20px;
            animation: bounce 2s ease-in-out infinite;
        }

        .mock-icon.small {
            font-size: 50px;
            margin-bottom: 15px;
        }

        .mock-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mock-title.small {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .mock-description {
            font-size: 14px;
            color: #606266;
            margin-bottom: 20px;
        }

        .mock-features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .mock-features.compact {
            gap: 10px;
            margin-bottom: 15px;
        }

        .mock-feature {
            padding: 15px 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(64, 158, 255, 0.1);
            font-size: 12px;
            color: #2c3e50;
        }

        .mock-feature.compact {
            padding: 10px 8px;
            font-size: 11px;
        }

        .mock-hint {
            padding: 15px;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(64, 158, 255, 0.1);
            color: #409EFF;
            font-size: 14px;
        }

        .mock-hint.compact {
            padding: 12px;
            font-size: 13px;
        }

        .mock-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mock-button {
            padding: 10px 20px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mock-button.prev {
            background: #f5f5f5;
            color: #666;
        }

        .mock-button.next {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
        }

        .mock-button:hover {
            transform: translateY(-2px);
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-indicator.problem {
            background: #fee;
            color: #e74c3c;
        }

        .status-indicator.fixed {
            background: #efe;
            color: #27ae60;
        }

        .overflow-warning {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .improvement-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .improvement-list h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .improvement-list ul {
            list-style: none;
            padding: 0;
        }

        .improvement-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            color: #555;
        }

        .improvement-list li:last-child {
            border-bottom: none;
        }

        .improvement-list li::before {
            content: "✅ ";
            color: #27ae60;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-section {
                height: 70vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>🔧 PPT生成页面高度修复对比</h1>
            <p>解决动画盒子过高导致按钮被挤出视图的问题</p>
        </div>

        <div class="comparison-grid">
            <!-- 修复前 -->
            <div class="demo-section">
                <div class="status-indicator problem">❌ 问题版本</div>
                <h2 class="section-title problem">修复前 - 高度过高</h2>
                
                <div class="mock-content">
                    <div class="mock-header">
                        <h2>生成您的PPT</h2>
                        <p>点击"生成PPT"按钮开始创建</p>
                    </div>

                    <div class="before-fix">
                        <div class="mock-icon">📄</div>
                        <div class="mock-title">准备生成您的专属PPT</div>
                        <div class="mock-description">AI将根据您的大纲内容，智能生成精美的演示文稿</div>
                        
                        <div class="mock-features">
                            <div class="mock-feature">✨ AI智能生成</div>
                            <div class="mock-feature">🎨 精美模板</div>
                            <div class="mock-feature">⚡ 快速生成</div>
                        </div>
                        
                        <div class="mock-hint">👇 点击下方"生成PPT"按钮开始创建</div>
                    </div>
                </div>
                
                <div class="overflow-warning">按钮被挤出视图外！</div>
            </div>

            <!-- 修复后 -->
            <div class="demo-section">
                <div class="status-indicator fixed">✅ 修复版本</div>
                <h2 class="section-title fixed">修复后 - 高度适中</h2>
                
                <div class="mock-content">
                    <div class="mock-header">
                        <h2>生成您的PPT</h2>
                        <p>点击"生成PPT"按钮开始创建</p>
                    </div>

                    <div class="after-fix">
                        <div class="mock-icon small">📄</div>
                        <div class="mock-title small">准备生成您的专属PPT</div>
                        <div class="mock-description">AI将根据您的大纲内容，智能生成精美的演示文稿</div>
                        
                        <div class="mock-features compact">
                            <div class="mock-feature compact">✨ AI智能生成</div>
                            <div class="mock-feature compact">🎨 精美模板</div>
                            <div class="mock-feature compact">⚡ 快速生成</div>
                        </div>
                        
                        <div class="mock-hint compact">👇 点击下方"生成PPT"按钮开始创建</div>
                    </div>

                    <div class="mock-buttons">
                        <button class="mock-button prev">← 上一步</button>
                        <button class="mock-button next">生成PPT 🚀</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="improvement-list">
            <h3>🎯 修复改进点</h3>
            <ul>
                <li>将容器最大高度限制为60vh，确保不超出视口</li>
                <li>减少内边距：从60px 40px调整为40px 30px</li>
                <li>缩小图标尺寸：从80px调整为60px</li>
                <li>压缩文字间距：标题和描述的margin减少</li>
                <li>优化特性卡片：减少padding和gap间距</li>
                <li>调整提示区域：减少padding和字体大小</li>
                <li>保持所有动画效果和视觉美观度</li>
                <li>确保底部按钮始终在视图内可见</li>
            </ul>
        </div>
    </div>
</body>
</html>
