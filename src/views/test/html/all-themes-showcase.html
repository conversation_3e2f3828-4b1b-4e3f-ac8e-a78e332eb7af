<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>10个主题完整展示 - AI PPT 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
            font-size: 2.5rem;
        }

        .themes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .theme-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .theme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .theme-preview {
            height: 180px;
            position: relative;
            overflow: hidden;
        }

        .theme-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .theme-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .theme-content h3 {
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .theme-content p {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .theme-info {
            padding: 16px;
        }

        .theme-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .theme-description {
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
            font-size: 0.9rem;
        }

        .theme-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .theme-tag {
            background: #f0f0f0;
            color: #666;
            padding: 3px 10px;
            border-radius: 10px;
            font-size: 0.75rem;
        }

        .current-demo {
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .current-demo h2 {
            color: #333;
            margin-bottom: 20px;
        }

        .demo-area {
            height: 300px;
            border-radius: 12px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .demo-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            transition: background 0.6s ease-in-out;
        }

        .user-preview {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 12px 6px 6px;
            border-radius: 25px;
            backdrop-filter: blur(15px);
        }

        .user-preview-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--theme-gradient, linear-gradient(135deg, #409EFF, #67C23A));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.6s ease-in-out;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-preview-name {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .demo-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .demo-content h3 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .demo-content p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .current-theme-name {
            font-size: 1.5rem;
            color: #409EFF;
            margin-bottom: 10px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI PPT 主题完整展示</h1>

        <div class="themes-grid">
            <!-- 星河幻境 -->
            <div class="theme-card" onclick="selectTheme('default')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)"></div>
                    <div class="theme-content">
                        <h3>✨ 星河幻境</h3>
                        <p>梦幻多彩的宇宙之旅</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">星河幻境</div>
                    <div class="theme-description">融合了宇宙星河的神秘色彩，从青蓝到紫红的渐变营造出梦幻的视觉体验</div>
                    <div class="theme-tags">
                        <span class="theme-tag">创意</span>
                        <span class="theme-tag">艺术</span>
                        <span class="theme-tag">梦幻</span>
                    </div>
                </div>
            </div>

            <!-- 深海蓝调 -->
            <div class="theme-card" onclick="selectTheme('ocean')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(25, 118, 210, 0.75) 15%, rgba(33, 150, 243, 0.70) 30%, rgba(3, 169, 244, 0.65) 45%, rgba(0, 188, 212, 0.60) 60%, rgba(0, 150, 136, 0.70) 75%, rgba(76, 175, 80, 0.65) 90%, rgba(139, 195, 74, 0.75) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌊 深海蓝调</h3>
                        <p>宁静深邃的海洋世界</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">深海蓝调</div>
                    <div class="theme-description">从深海蓝到翠绿的自然过渡，营造出宁静而专业的氛围</div>
                    <div class="theme-tags">
                        <span class="theme-tag">商务</span>
                        <span class="theme-tag">科技</span>
                        <span class="theme-tag">专业</span>
                    </div>
                </div>
            </div>

            <!-- 晚霞余晖 -->
            <div class="theme-card" onclick="selectTheme('sunset')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 152, 0, 0.70) 15%, rgba(255, 193, 7, 0.60) 30%, rgba(255, 235, 59, 0.55) 45%, rgba(255, 111, 97, 0.65) 60%, rgba(240, 98, 146, 0.70) 75%, rgba(186, 104, 200, 0.65) 90%, rgba(149, 117, 205, 0.70) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌅 晚霞余晖</h3>
                        <p>温暖浪漫的黄昏时光</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">晚霞余晖</div>
                    <div class="theme-description">温暖的橙黄色调配以浪漫的粉紫色彩，如晚霞般绚烂</div>
                    <div class="theme-tags">
                        <span class="theme-tag">温暖</span>
                        <span class="theme-tag">浪漫</span>
                        <span class="theme-tag">生活</span>
                    </div>
                </div>
            </div>

            <!-- 翡翠森林 -->
            <div class="theme-card" onclick="selectTheme('forest')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(46, 125, 50, 0.75) 15%, rgba(67, 160, 71, 0.65) 30%, rgba(102, 187, 106, 0.60) 45%, rgba(129, 199, 132, 0.65) 60%, rgba(165, 214, 167, 0.55) 75%, rgba(200, 230, 201, 0.50) 90%, rgba(232, 245, 233, 0.45) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌿 翡翠森林</h3>
                        <p>清新自然的绿色生机</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">翡翠森林</div>
                    <div class="theme-description">从深绿到浅绿的自然渐变，象征生机与成长</div>
                    <div class="theme-tags">
                        <span class="theme-tag">自然</span>
                        <span class="theme-tag">环保</span>
                        <span class="theme-tag">健康</span>
                    </div>
                </div>
            </div>

            <!-- 极光之夜 -->
            <div class="theme-card" onclick="selectTheme('aurora')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(106, 27, 154, 0.70) 15%, rgba(142, 36, 170, 0.65) 30%, rgba(171, 71, 188, 0.60) 45%, rgba(186, 104, 200, 0.65) 60%, rgba(206, 147, 216, 0.55) 75%, rgba(225, 190, 231, 0.50) 90%, rgba(243, 229, 245, 0.45) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌌 极光之夜</h3>
                        <p>神秘优雅的紫色极光</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">极光之夜</div>
                    <div class="theme-description">深紫到浅紫的优雅渐变，如极光般神秘美丽</div>
                    <div class="theme-tags">
                        <span class="theme-tag">优雅</span>
                        <span class="theme-tag">神秘</span>
                        <span class="theme-tag">高端</span>
                    </div>
                </div>
            </div>

            <!-- 樱花飞舞 -->
            <div class="theme-card" onclick="selectTheme('sakura')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(255, 182, 193, 0.65) 0%, rgba(255, 192, 203, 0.70) 15%, rgba(255, 160, 180, 0.60) 30%, rgba(255, 105, 135, 0.65) 45%, rgba(255, 20, 147, 0.55) 60%, rgba(219, 112, 147, 0.60) 75%, rgba(199, 21, 133, 0.65) 90%, rgba(139, 69, 19, 0.45) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌸 樱花飞舞</h3>
                        <p>浪漫粉色的春日樱花</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">樱花飞舞</div>
                    <div class="theme-description">粉色系渐变，如春日樱花般浪漫温柔</div>
                    <div class="theme-tags">
                        <span class="theme-tag">浪漫</span>
                        <span class="theme-tag">温柔</span>
                        <span class="theme-tag">春日</span>
                    </div>
                </div>
            </div>

            <!-- 黄金时代 -->
            <div class="theme-card" onclick="selectTheme('golden')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.70) 0%, rgba(255, 193, 7, 0.75) 15%, rgba(255, 152, 0, 0.65) 30%, rgba(255, 111, 0, 0.60) 45%, rgba(205, 133, 63, 0.65) 60%, rgba(184, 134, 11, 0.70) 75%, rgba(139, 69, 19, 0.60) 90%, rgba(101, 67, 33, 0.55) 100%)"></div>
                    <div class="theme-content">
                        <h3>✨ 黄金时代</h3>
                        <p>奢华金色的辉煌年代</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">黄金时代</div>
                    <div class="theme-description">金色系渐变，象征财富与成功的辉煌</div>
                    <div class="theme-tags">
                        <span class="theme-tag">奢华</span>
                        <span class="theme-tag">成功</span>
                        <span class="theme-tag">财富</span>
                    </div>
                </div>
            </div>

            <!-- 冰雪奇缘 -->
            <div class="theme-card" onclick="selectTheme('ice')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(240, 248, 255, 0.60) 0%, rgba(176, 224, 230, 0.65) 15%, rgba(135, 206, 235, 0.70) 30%, rgba(70, 130, 180, 0.65) 45%, rgba(100, 149, 237, 0.60) 60%, rgba(123, 104, 238, 0.65) 75%, rgba(147, 112, 219, 0.55) 90%, rgba(186, 85, 211, 0.50) 100%)"></div>
                    <div class="theme-content">
                        <h3>❄️ 冰雪奇缘</h3>
                        <p>纯净清冷的冰雪世界</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">冰雪奇缘</div>
                    <div class="theme-description">冰蓝色系渐变，营造纯净清冷的氛围</div>
                    <div class="theme-tags">
                        <span class="theme-tag">纯净</span>
                        <span class="theme-tag">清冷</span>
                        <span class="theme-tag">科技</span>
                    </div>
                </div>
            </div>

            <!-- 火山熔岩 -->
            <div class="theme-card" onclick="selectTheme('volcano')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(139, 0, 0, 0.75) 0%, rgba(178, 34, 34, 0.70) 15%, rgba(220, 20, 60, 0.65) 30%, rgba(255, 69, 0, 0.70) 45%, rgba(255, 140, 0, 0.60) 60%, rgba(255, 165, 0, 0.55) 75%, rgba(255, 215, 0, 0.50) 90%, rgba(255, 255, 224, 0.45) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌋 火山熔岩</h3>
                        <p>炽热激情的火焰力量</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">火山熔岩</div>
                    <div class="theme-description">红橙色系渐变，展现炽热的激情与力量</div>
                    <div class="theme-tags">
                        <span class="theme-tag">激情</span>
                        <span class="theme-tag">力量</span>
                        <span class="theme-tag">热烈</span>
                    </div>
                </div>
            </div>

            <!-- 午夜星空 -->
            <div class="theme-card" onclick="selectTheme('midnight')">
                <div class="theme-preview">
                    <div class="theme-overlay" style="background: linear-gradient(135deg, rgba(25, 25, 112, 0.80) 0%, rgba(72, 61, 139, 0.75) 15%, rgba(106, 90, 205, 0.70) 30%, rgba(123, 104, 238, 0.65) 45%, rgba(147, 112, 219, 0.60) 60%, rgba(138, 43, 226, 0.65) 75%, rgba(75, 0, 130, 0.70) 90%, rgba(25, 25, 112, 0.75) 100%)"></div>
                    <div class="theme-content">
                        <h3>🌙 午夜星空</h3>
                        <p>深邃神秘的夜空星辰</p>
                    </div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">午夜星空</div>
                    <div class="theme-description">深蓝紫色系渐变，如午夜星空般深邃神秘</div>
                    <div class="theme-tags">
                        <span class="theme-tag">深邃</span>
                        <span class="theme-tag">神秘</span>
                        <span class="theme-tag">宁静</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="current-demo">
            <h2>🎯 当前主题预览</h2>
            <div class="current-theme-name" id="currentThemeName">星河幻境</div>
            <div class="demo-area">
                <div id="demoGradient" class="demo-gradient" style="background: linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)"></div>
                <div class="user-preview">
                    <div class="user-preview-avatar">👤</div>
                    <span class="user-preview-name">admin</span>
                </div>
                <div class="demo-content">
                    <h3>AI PPT 生成器</h3>
                    <p>点击上方主题卡片体验不同效果</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themes = {
            default: {
                name: '星河幻境',
                gradient: 'linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)'
            },
            ocean: {
                name: '深海蓝调',
                gradient: 'linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(25, 118, 210, 0.75) 15%, rgba(33, 150, 243, 0.70) 30%, rgba(3, 169, 244, 0.65) 45%, rgba(0, 188, 212, 0.60) 60%, rgba(0, 150, 136, 0.70) 75%, rgba(76, 175, 80, 0.65) 90%, rgba(139, 195, 74, 0.75) 100%)'
            },
            sunset: {
                name: '晚霞余晖',
                gradient: 'linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 152, 0, 0.70) 15%, rgba(255, 193, 7, 0.60) 30%, rgba(255, 235, 59, 0.55) 45%, rgba(255, 111, 97, 0.65) 60%, rgba(240, 98, 146, 0.70) 75%, rgba(186, 104, 200, 0.65) 90%, rgba(149, 117, 205, 0.70) 100%)'
            },
            forest: {
                name: '翡翠森林',
                gradient: 'linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(46, 125, 50, 0.75) 15%, rgba(67, 160, 71, 0.65) 30%, rgba(102, 187, 106, 0.60) 45%, rgba(129, 199, 132, 0.65) 60%, rgba(165, 214, 167, 0.55) 75%, rgba(200, 230, 201, 0.50) 90%, rgba(232, 245, 233, 0.45) 100%)'
            },
            aurora: {
                name: '极光之夜',
                gradient: 'linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(106, 27, 154, 0.70) 15%, rgba(142, 36, 170, 0.65) 30%, rgba(171, 71, 188, 0.60) 45%, rgba(186, 104, 200, 0.65) 60%, rgba(206, 147, 216, 0.55) 75%, rgba(225, 190, 231, 0.50) 90%, rgba(243, 229, 245, 0.45) 100%)'
            },
            sakura: {
                name: '樱花飞舞',
                gradient: 'linear-gradient(135deg, rgba(255, 182, 193, 0.65) 0%, rgba(255, 192, 203, 0.70) 15%, rgba(255, 160, 180, 0.60) 30%, rgba(255, 105, 135, 0.65) 45%, rgba(255, 20, 147, 0.55) 60%, rgba(219, 112, 147, 0.60) 75%, rgba(199, 21, 133, 0.65) 90%, rgba(139, 69, 19, 0.45) 100%)'
            },
            golden: {
                name: '黄金时代',
                gradient: 'linear-gradient(135deg, rgba(255, 215, 0, 0.70) 0%, rgba(255, 193, 7, 0.75) 15%, rgba(255, 152, 0, 0.65) 30%, rgba(255, 111, 0, 0.60) 45%, rgba(205, 133, 63, 0.65) 60%, rgba(184, 134, 11, 0.70) 75%, rgba(139, 69, 19, 0.60) 90%, rgba(101, 67, 33, 0.55) 100%)'
            },
            ice: {
                name: '冰雪奇缘',
                gradient: 'linear-gradient(135deg, rgba(240, 248, 255, 0.60) 0%, rgba(176, 224, 230, 0.65) 15%, rgba(135, 206, 235, 0.70) 30%, rgba(70, 130, 180, 0.65) 45%, rgba(100, 149, 237, 0.60) 60%, rgba(123, 104, 238, 0.65) 75%, rgba(147, 112, 219, 0.55) 90%, rgba(186, 85, 211, 0.50) 100%)'
            },
            volcano: {
                name: '火山熔岩',
                gradient: 'linear-gradient(135deg, rgba(139, 0, 0, 0.75) 0%, rgba(178, 34, 34, 0.70) 15%, rgba(220, 20, 60, 0.65) 30%, rgba(255, 69, 0, 0.70) 45%, rgba(255, 140, 0, 0.60) 60%, rgba(255, 165, 0, 0.55) 75%, rgba(255, 215, 0, 0.50) 90%, rgba(255, 255, 224, 0.45) 100%)'
            },
            midnight: {
                name: '午夜星空',
                gradient: 'linear-gradient(135deg, rgba(25, 25, 112, 0.80) 0%, rgba(72, 61, 139, 0.75) 15%, rgba(106, 90, 205, 0.70) 30%, rgba(123, 104, 238, 0.65) 45%, rgba(147, 112, 219, 0.60) 60%, rgba(138, 43, 226, 0.65) 75%, rgba(75, 0, 130, 0.70) 90%, rgba(25, 25, 112, 0.75) 100%)'
            }
        };

        function selectTheme(themeId) {
            const theme = themes[themeId];
            if (theme) {
                // 更新CSS变量和主题名称
                document.documentElement.style.setProperty('--theme-gradient', theme.gradient);
                document.getElementById('demoGradient').style.background = theme.gradient;
                document.getElementById('currentThemeName').textContent = theme.name;

                console.log(`已切换到主题: ${theme.name}`);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.documentElement.style.setProperty('--theme-gradient', themes.default.gradient);
        });
    </script>
</body>
</html>
