<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话框区域演示 - AI PPT 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--theme-gradient, linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%));
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            padding: 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .section-title {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 对话框容器样式 */
        .chat-container {
            margin-top: 20px;
            padding: 25px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;
        }

        .modern-chat-container {
            margin-bottom: 32px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8e8e8;
            padding: 20px;
        }

        .chat-header {
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .chat-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .chat-title i {
            font-size: 18px;
            color: #409EFF;
        }

        .chat-messages {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px 15px;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.03);
            border-radius: 3px;
        }

        .message-item {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            max-width: 85%;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 12px;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #f0f0f0;
            position: relative;
            transition: transform 0.2s ease;
        }

        .message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
        }

        .message-item.message-user {
            align-self: flex-end;
            margin-left: auto;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
        }

        .message-item.message-assistant {
            align-self: flex-start;
            margin-right: auto;
        }

        .message-bubble {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message-user .message-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .message-assistant .message-icon {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
        }

        .message-content {
            flex: 1;
            line-height: 1.6;
        }

        .modern-message-item {
            display: flex;
            margin-bottom: 24px;
            align-items: flex-start;
        }

        .modern-message-item.message-user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            margin: 0 12px;
        }

        .avatar-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #409EFF, #67C23A);
        }

        .modern-message-item .message-bubble {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 12px 16px;
            max-width: 70%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .modern-message-item.message-user .message-bubble {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
        }

        .user-message {
            font-weight: 500;
        }

        .assistant-message {
            color: #333;
        }

        .message-edit-button {
            position: absolute;
            bottom: -8px;
            right: -8px;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #409EFF;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
            transition: all 0.2s ease;
        }

        .message-edit-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
        }

        .demo-controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-button {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .demo-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .demo-button.active {
            background: #409EFF;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 对话框区域演示</h1>

        <div class="demo-controls">
            <button class="demo-button active" onclick="showDemo('topic')">一句话生成</button>
            <button class="demo-button" onclick="showDemo('text')">文本输入</button>
            <button class="demo-button" onclick="showDemo('file')">文件上传</button>
        </div>

        <!-- 一句话生成对话框 -->
        <div id="topicDemo" class="demo-section">
            <div class="modern-chat-container">
                <div class="chat-header">
                    <div class="chat-title">
                        <i class="el-icon-magic-stick">✨</i>
                        <span>一句话生成对话记录</span>
                    </div>
                </div>
                <div class="chat-messages">
                    <div class="modern-message-item message-user">
                        <div class="message-avatar">
                            <div class="avatar-icon">👤</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <span class="user-message">请帮我生成一个关于人工智能发展趋势的PPT</span>
                            </div>
                        </div>
                    </div>
                    <div class="modern-message-item message-assistant">
                        <div class="message-avatar">
                            <div class="avatar-icon">🤖</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <div class="assistant-message">
                                    <h3>人工智能发展趋势 PPT 大纲</h3>
                                    <p><strong>第一章：人工智能概述</strong></p>
                                    <ul>
                                        <li>AI的定义与核心概念</li>
                                        <li>发展历程回顾</li>
                                        <li>当前技术水平</li>
                                    </ul>
                                    <p><strong>第二章：技术发展趋势</strong></p>
                                    <ul>
                                        <li>机器学习与深度学习</li>
                                        <li>自然语言处理突破</li>
                                        <li>计算机视觉进展</li>
                                    </ul>
                                </div>
                            </div>
                            <button class="message-edit-button" title="编辑此回复">✏️</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文本输入对话框 -->
        <div id="textDemo" class="demo-section" style="display: none;">
            <div class="modern-chat-container">
                <div class="chat-header">
                    <div class="chat-title">
                        <i class="el-icon-edit-outline">📝</i>
                        <span>文本输入生成对话记录</span>
                    </div>
                </div>
                <div class="chat-messages">
                    <div class="modern-message-item message-user">
                        <div class="message-avatar">
                            <div class="avatar-icon">👤</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <span class="user-message">我想基于以下内容生成PPT：数字化转型是企业在数字时代的必然选择。它不仅仅是技术的升级，更是商业模式、组织架构、企业文化的全面变革...</span>
                            </div>
                        </div>
                    </div>
                    <div class="modern-message-item message-assistant">
                        <div class="message-avatar">
                            <div class="avatar-icon">🤖</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <div class="assistant-message">
                                    <h3>数字化转型 PPT 大纲</h3>
                                    <p><strong>第一章：数字化转型概述</strong></p>
                                    <ul>
                                        <li>什么是数字化转型</li>
                                        <li>转型的必要性</li>
                                        <li>转型的核心要素</li>
                                    </ul>
                                    <p><strong>第二章：转型策略与实施</strong></p>
                                    <ul>
                                        <li>技术架构升级</li>
                                        <li>业务流程重塑</li>
                                        <li>组织文化变革</li>
                                    </ul>
                                </div>
                            </div>
                            <button class="message-edit-button" title="编辑此回复">✏️</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件上传对话框 -->
        <div id="fileDemo" class="demo-section" style="display: none;">
            <div class="modern-chat-container">
                <div class="chat-header">
                    <div class="chat-title">
                        <i class="el-icon-upload2">📁</i>
                        <span>文件上传生成对话记录</span>
                    </div>
                </div>
                <div class="chat-messages">
                    <div class="modern-message-item message-user">
                        <div class="message-avatar">
                            <div class="avatar-icon">📁</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <span class="user-message">文件 '市场分析报告.docx' 上传成功，开始生成大纲...</span>
                            </div>
                        </div>
                    </div>
                    <div class="modern-message-item message-assistant">
                        <div class="message-avatar">
                            <div class="avatar-icon">🤖</div>
                        </div>
                        <div class="message-bubble">
                            <div class="message-content">
                                <div class="assistant-message">
                                    <h3>市场分析报告 PPT 大纲</h3>
                                    <p><strong>第一章：市场概况</strong></p>
                                    <ul>
                                        <li>市场规模与增长</li>
                                        <li>主要参与者分析</li>
                                        <li>市场趋势洞察</li>
                                    </ul>
                                    <p><strong>第二章：竞争分析</strong></p>
                                    <ul>
                                        <li>竞争格局分析</li>
                                        <li>SWOT分析</li>
                                        <li>机会与挑战</li>
                                    </ul>
                                </div>
                            </div>
                            <button class="message-edit-button" title="编辑此回复">✏️</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDemo(type) {
            // 隐藏所有演示区域
            document.getElementById('topicDemo').style.display = 'none';
            document.getElementById('textDemo').style.display = 'none';
            document.getElementById('fileDemo').style.display = 'none';

            // 显示选中的演示区域
            document.getElementById(type + 'Demo').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('.demo-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
