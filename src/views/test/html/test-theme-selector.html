<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题选择器测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
        }

        .gradient-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--theme-gradient, linear-gradient(135deg,
                rgba(26, 206, 233, 0.60) 0%,
                rgba(64, 160, 222, 0.70) 10%,
                rgba(100, 100, 216, 0.70) 20%,
                rgba(123, 35, 211, 0.70) 30%,
                rgba(150, 20, 205, 0.60) 40%,
                rgba(176, 10, 199, 0.50) 50%,
                rgba(168, 22, 130, 0.60) 60%,
                rgba(160, 33, 52, 0.40) 70%,
                rgba(80, 90, 190, 0.60) 85%,
                rgba(5, 135, 250, 0.80) 100%
            ));
            transition: background 0.6s ease-in-out;
            z-index: -1;
        }

        .user-settings-area {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 15px;
            pointer-events: none;
        }

        .user-settings-area > * {
            pointer-events: auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            background: transparent;
            padding: 6px 12px 6px 6px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            transform: translateY(-1px);
        }

        .user-info:hover .user-avatar {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }

        .user-info:hover .user-name {
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--theme-gradient, linear-gradient(135deg, #409EFF, #67C23A));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.6s ease-in-out;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .settings-container {
            position: relative;
        }

        .settings-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: transparent;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
            color: #666;
        }

        .settings-button:hover {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transform: scale(1.05);
            color: #409EFF;
        }

        .settings-button.active {
            background: rgba(64, 158, 255, 0.2);
            backdrop-filter: blur(10px);
            color: #409EFF;
        }

        .theme-selector-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            min-width: 200px;
            animation: themeDropdownFadeIn 0.3s ease;
        }

        @keyframes themeDropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .theme-selector-header {
            padding: 12px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .theme-options {
            padding: 8px 0;
            max-height: 250px;
            overflow-y: auto;
        }

        .theme-options::-webkit-scrollbar {
            width: 6px;
        }

        .theme-options::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .theme-options::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .theme-options::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .theme-option {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .theme-option:hover {
            background: #f8f9fa;
        }

        .theme-option.active {
            background: #e6f7ff;
        }

        .theme-option.active .theme-name {
            color: #409EFF;
            font-weight: 500;
        }

        .theme-preview {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            margin-right: 12px;
            border: 2px solid #eee;
            transition: all 0.2s ease;
        }

        .theme-name {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .theme-check {
            color: #409EFF;
            font-size: 16px;
        }

        .content {
            padding: 100px 50px;
            text-align: center;
            color: white;
        }

        .content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content p {
            font-size: 1.2rem;
            opacity: 0.9;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="gradient-overlay"></div>

    <div class="user-settings-area">
        <div class="user-info">
            <div class="user-avatar">👤</div>
            <span class="user-name">admin</span>
        </div>
        <div class="settings-container">
            <button class="settings-button" onclick="toggleThemeSelector()">
                ⚙️
            </button>

            <div id="themeSelector" class="theme-selector-dropdown" style="display: none;">
                <div class="theme-selector-header">
                    <span>选择主题</span>
                </div>
                <div class="theme-options">
                    <div class="theme-option active" onclick="selectTheme('default')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(100, 100, 216, 0.70) 25%, rgba(123, 35, 211, 0.70) 50%, rgba(5, 135, 250, 0.80) 100%)"></div>
                        <span class="theme-name">星河幻境</span>
                        <span class="theme-check">✓</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('ocean')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(33, 150, 243, 0.70) 25%, rgba(0, 188, 212, 0.60) 50%, rgba(139, 195, 74, 0.75) 100%)"></div>
                        <span class="theme-name">深海蓝调</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('sunset')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 193, 7, 0.60) 25%, rgba(240, 98, 146, 0.70) 50%, rgba(149, 117, 205, 0.70) 100%)"></div>
                        <span class="theme-name">晚霞余晖</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('forest')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(67, 160, 71, 0.65) 25%, rgba(129, 199, 132, 0.65) 50%, rgba(232, 245, 233, 0.45) 100%)"></div>
                        <span class="theme-name">翡翠森林</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('aurora')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(142, 36, 170, 0.65) 25%, rgba(186, 104, 200, 0.65) 50%, rgba(243, 229, 245, 0.45) 100%)"></div>
                        <span class="theme-name">极光之夜</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('sakura')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(255, 182, 193, 0.65) 0%, rgba(255, 160, 180, 0.60) 25%, rgba(255, 20, 147, 0.55) 50%, rgba(199, 21, 133, 0.65) 100%)"></div>
                        <span class="theme-name">樱花飞舞</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('golden')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.70) 0%, rgba(255, 152, 0, 0.65) 25%, rgba(205, 133, 63, 0.65) 50%, rgba(101, 67, 33, 0.55) 100%)"></div>
                        <span class="theme-name">黄金时代</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('ice')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(240, 248, 255, 0.60) 0%, rgba(135, 206, 235, 0.70) 25%, rgba(123, 104, 238, 0.65) 50%, rgba(186, 85, 211, 0.50) 100%)"></div>
                        <span class="theme-name">冰雪奇缘</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('volcano')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(139, 0, 0, 0.75) 0%, rgba(220, 20, 60, 0.65) 25%, rgba(255, 140, 0, 0.60) 50%, rgba(255, 255, 224, 0.45) 100%)"></div>
                        <span class="theme-name">火山熔岩</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('midnight')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(25, 25, 112, 0.80) 0%, rgba(106, 90, 205, 0.70) 25%, rgba(138, 43, 226, 0.65) 50%, rgba(25, 25, 112, 0.75) 100%)"></div>
                        <span class="theme-name">午夜星空</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('darkforest')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(20, 60, 20, 0.75) 25%, rgba(25, 100, 25, 0.65) 50%, rgba(55, 160, 55, 0.50) 100%)"></div>
                        <span class="theme-name">暗夜森林</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('deepocean')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(0, 10, 30, 0.90) 0%, rgba(10, 30, 70, 0.80) 25%, rgba(20, 50, 110, 0.70) 50%, rgba(35, 80, 170, 0.55) 100%)"></div>
                        <span class="theme-name">深海深渊</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('shadowpurple')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(20, 0, 40, 0.90) 0%, rgba(60, 20, 80, 0.80) 25%, rgba(100, 40, 120, 0.70) 50%, rgba(160, 70, 180, 0.55) 100%)"></div>
                        <span class="theme-name">暗影紫魅</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('carbonblack')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(30, 30, 40, 0.85) 25%, rgba(50, 50, 70, 0.75) 50%, rgba(80, 80, 115, 0.60) 100%)"></div>
                        <span class="theme-name">碳黑科技</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('bloodmoon')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(40, 0, 0, 0.90) 0%, rgba(80, 20, 20, 0.80) 25%, rgba(120, 40, 40, 0.70) 50%, rgba(180, 70, 70, 0.55) 100%)"></div>
                        <span class="theme-name">血月之夜</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('mysticnight')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(15, 15, 35, 0.90) 0%, rgba(35, 35, 75, 0.80) 25%, rgba(55, 55, 115, 0.70) 50%, rgba(85, 85, 175, 0.55) 100%)"></div>
                        <span class="theme-name">神秘夜幕</span>
                    </div>
                    <div class="theme-option" onclick="selectTheme('emeralddark')">
                        <div class="theme-preview" style="background: linear-gradient(135deg, rgba(0, 30, 20, 0.90) 0%, rgba(20, 70, 50, 0.80) 25%, rgba(40, 110, 80, 0.70) 50%, rgba(70, 170, 125, 0.55) 100%)"></div>
                        <span class="theme-name">翡翠暗影</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <h1>AI PPT 生成器</h1>
        <p>主题选择器测试页面</p>
    </div>

    <script>
        const themes = {
            default: 'linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)',
            ocean: 'linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(25, 118, 210, 0.75) 15%, rgba(33, 150, 243, 0.70) 30%, rgba(3, 169, 244, 0.65) 45%, rgba(0, 188, 212, 0.60) 60%, rgba(0, 150, 136, 0.70) 75%, rgba(76, 175, 80, 0.65) 90%, rgba(139, 195, 74, 0.75) 100%)',
            sunset: 'linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 152, 0, 0.70) 15%, rgba(255, 193, 7, 0.60) 30%, rgba(255, 235, 59, 0.55) 45%, rgba(255, 111, 97, 0.65) 60%, rgba(240, 98, 146, 0.70) 75%, rgba(186, 104, 200, 0.65) 90%, rgba(149, 117, 205, 0.70) 100%)',
            forest: 'linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(46, 125, 50, 0.75) 15%, rgba(67, 160, 71, 0.65) 30%, rgba(102, 187, 106, 0.60) 45%, rgba(129, 199, 132, 0.65) 60%, rgba(165, 214, 167, 0.55) 75%, rgba(200, 230, 201, 0.50) 90%, rgba(232, 245, 233, 0.45) 100%)',
            aurora: 'linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(106, 27, 154, 0.70) 15%, rgba(142, 36, 170, 0.65) 30%, rgba(171, 71, 188, 0.60) 45%, rgba(186, 104, 200, 0.65) 60%, rgba(206, 147, 216, 0.55) 75%, rgba(225, 190, 231, 0.50) 90%, rgba(243, 229, 245, 0.45) 100%)',
            sakura: 'linear-gradient(135deg, rgba(255, 182, 193, 0.65) 0%, rgba(255, 192, 203, 0.70) 15%, rgba(255, 160, 180, 0.60) 30%, rgba(255, 105, 135, 0.65) 45%, rgba(255, 20, 147, 0.55) 60%, rgba(219, 112, 147, 0.60) 75%, rgba(199, 21, 133, 0.65) 90%, rgba(139, 69, 19, 0.45) 100%)',
            golden: 'linear-gradient(135deg, rgba(255, 215, 0, 0.70) 0%, rgba(255, 193, 7, 0.75) 15%, rgba(255, 152, 0, 0.65) 30%, rgba(255, 111, 0, 0.60) 45%, rgba(205, 133, 63, 0.65) 60%, rgba(184, 134, 11, 0.70) 75%, rgba(139, 69, 19, 0.60) 90%, rgba(101, 67, 33, 0.55) 100%)',
            ice: 'linear-gradient(135deg, rgba(240, 248, 255, 0.60) 0%, rgba(176, 224, 230, 0.65) 15%, rgba(135, 206, 235, 0.70) 30%, rgba(70, 130, 180, 0.65) 45%, rgba(100, 149, 237, 0.60) 60%, rgba(123, 104, 238, 0.65) 75%, rgba(147, 112, 219, 0.55) 90%, rgba(186, 85, 211, 0.50) 100%)',
            volcano: 'linear-gradient(135deg, rgba(139, 0, 0, 0.75) 0%, rgba(178, 34, 34, 0.70) 15%, rgba(220, 20, 60, 0.65) 30%, rgba(255, 69, 0, 0.70) 45%, rgba(255, 140, 0, 0.60) 60%, rgba(255, 165, 0, 0.55) 75%, rgba(255, 215, 0, 0.50) 90%, rgba(255, 255, 224, 0.45) 100%)',
            midnight: 'linear-gradient(135deg, rgba(25, 25, 112, 0.80) 0%, rgba(72, 61, 139, 0.75) 15%, rgba(106, 90, 205, 0.70) 30%, rgba(123, 104, 238, 0.65) 45%, rgba(147, 112, 219, 0.60) 60%, rgba(138, 43, 226, 0.65) 75%, rgba(75, 0, 130, 0.70) 90%, rgba(25, 25, 112, 0.75) 100%)',
            darkforest: 'linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(10, 40, 10, 0.80) 15%, rgba(20, 60, 20, 0.75) 30%, rgba(15, 80, 15, 0.70) 45%, rgba(25, 100, 25, 0.65) 60%, rgba(35, 120, 35, 0.60) 75%, rgba(45, 140, 45, 0.55) 90%, rgba(55, 160, 55, 0.50) 100%)',
            deepocean: 'linear-gradient(135deg, rgba(0, 10, 30, 0.90) 0%, rgba(5, 20, 50, 0.85) 15%, rgba(10, 30, 70, 0.80) 30%, rgba(15, 40, 90, 0.75) 45%, rgba(20, 50, 110, 0.70) 60%, rgba(25, 60, 130, 0.65) 75%, rgba(30, 70, 150, 0.60) 90%, rgba(35, 80, 170, 0.55) 100%)',
            shadowpurple: 'linear-gradient(135deg, rgba(20, 0, 40, 0.90) 0%, rgba(40, 10, 60, 0.85) 15%, rgba(60, 20, 80, 0.80) 30%, rgba(80, 30, 100, 0.75) 45%, rgba(100, 40, 120, 0.70) 60%, rgba(120, 50, 140, 0.65) 75%, rgba(140, 60, 160, 0.60) 90%, rgba(160, 70, 180, 0.55) 100%)',
            carbonblack: 'linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(20, 20, 25, 0.90) 15%, rgba(30, 30, 40, 0.85) 30%, rgba(40, 40, 55, 0.80) 45%, rgba(50, 50, 70, 0.75) 60%, rgba(60, 60, 85, 0.70) 75%, rgba(70, 70, 100, 0.65) 90%, rgba(80, 80, 115, 0.60) 100%)',
            bloodmoon: 'linear-gradient(135deg, rgba(40, 0, 0, 0.90) 0%, rgba(60, 10, 10, 0.85) 15%, rgba(80, 20, 20, 0.80) 30%, rgba(100, 30, 30, 0.75) 45%, rgba(120, 40, 40, 0.70) 60%, rgba(140, 50, 50, 0.65) 75%, rgba(160, 60, 60, 0.60) 90%, rgba(180, 70, 70, 0.55) 100%)',
            mysticnight: 'linear-gradient(135deg, rgba(15, 15, 35, 0.90) 0%, rgba(25, 25, 55, 0.85) 15%, rgba(35, 35, 75, 0.80) 30%, rgba(45, 45, 95, 0.75) 45%, rgba(55, 55, 115, 0.70) 60%, rgba(65, 65, 135, 0.65) 75%, rgba(75, 75, 155, 0.60) 90%, rgba(85, 85, 175, 0.55) 100%)',
            emeralddark: 'linear-gradient(135deg, rgba(0, 30, 20, 0.90) 0%, rgba(10, 50, 35, 0.85) 15%, rgba(20, 70, 50, 0.80) 30%, rgba(30, 90, 65, 0.75) 45%, rgba(40, 110, 80, 0.70) 60%, rgba(50, 130, 95, 0.65) 75%, rgba(60, 150, 110, 0.60) 90%, rgba(70, 170, 125, 0.55) 100%)'
        };

        let currentTheme = 'default';
        let showThemeSelector = false;

        function toggleThemeSelector() {
            showThemeSelector = !showThemeSelector;
            const selector = document.getElementById('themeSelector');
            const button = document.querySelector('.settings-button');

            if (showThemeSelector) {
                selector.style.display = 'block';
                button.classList.add('active');
            } else {
                selector.style.display = 'none';
                button.classList.remove('active');
            }
        }

        function selectTheme(themeId) {
            currentTheme = themeId;

            // 更新CSS变量
            document.documentElement.style.setProperty('--theme-gradient', themes[themeId]);

            // 更新选中状态
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
                const checkIcon = option.querySelector('.theme-check');
                if (checkIcon) checkIcon.style.display = 'none';
            });

            const selectedOption = event.target.closest('.theme-option');
            selectedOption.classList.add('active');
            const selectedCheck = selectedOption.querySelector('.theme-check');
            if (selectedCheck) selectedCheck.style.display = 'inline';

            // 关闭选择器
            toggleThemeSelector();

            // 显示主题切换提示
            console.log(`已切换到主题: ${themeId}`);
        }

        // 点击外部关闭选择器
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.settings-container')) {
                if (showThemeSelector) {
                    toggleThemeSelector();
                }
            }
        });
    </script>
</body>
</html>
