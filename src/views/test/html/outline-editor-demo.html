<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大纲编辑器缩进线对比 - AI PPT 生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
            font-size: 2.5rem;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #409EFF;
        }

        .outline-tree {
            background: transparent;
        }

        .tree-node {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(64, 158, 255, 0.05);
            border: 1px solid rgba(64, 158, 255, 0.1);
            position: relative;
        }

        .tree-node.level-1 {
            background: rgba(64, 158, 255, 0.08);
            border-color: rgba(64, 158, 255, 0.15);
            font-weight: 600;
            font-size: 16px;
        }

        .tree-node.level-2 {
            margin-left: 20px;
            background: rgba(64, 158, 255, 0.05);
            border-color: rgba(64, 158, 255, 0.1);
            font-weight: 500;
            font-size: 14px;
        }

        .tree-node.level-3 {
            margin-left: 40px;
            background: rgba(23, 162, 184, 0.05);
            border-color: rgba(23, 162, 184, 0.1);
            font-size: 13px;
        }

        .tree-node.level-4 {
            margin-left: 60px;
            background: rgba(206, 212, 218, 0.1);
            border-color: rgba(206, 212, 218, 0.2);
            font-size: 12px;
            font-style: italic;
            color: #6c757d;
        }

        /* 带缩进线的样式 */
        .with-indent-lines .tree-node.level-2,
        .with-indent-lines .tree-node.level-3,
        .with-indent-lines .tree-node.level-4 {
            position: relative;
        }

        .with-indent-lines .tree-node.level-2::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(64, 158, 255, 0.3);
        }

        .with-indent-lines .tree-node.level-3::before {
            content: '';
            position: absolute;
            left: -40px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(64, 158, 255, 0.3);
        }

        .with-indent-lines .tree-node.level-3::after {
            content: '';
            position: absolute;
            left: -20px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(64, 158, 255, 0.2);
        }

        .with-indent-lines .tree-node.level-4::before {
            content: '';
            position: absolute;
            left: -60px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(64, 158, 255, 0.3);
        }

        .with-indent-lines .tree-node.level-4::after {
            content: '';
            position: absolute;
            left: -40px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(64, 158, 255, 0.2);
        }

        .node-prefix {
            color: #409EFF;
            font-weight: 600;
            margin-right: 8px;
        }

        .node-content {
            color: #333;
        }

        .expand-icon {
            color: #409EFF;
            margin-right: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .highlight-box {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight-box h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        .highlight-box p {
            color: #856404;
            font-size: 14px;
        }

        .benefits-list {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .benefits-list h3 {
            color: #155724;
            margin-bottom: 15px;
        }

        .benefits-list ul {
            color: #155724;
            padding-left: 20px;
        }

        .benefits-list li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 大纲编辑器缩进线对比</h1>
        
        <div class="comparison-container">
            <!-- 带缩进线的版本 -->
            <div class="demo-section with-indent-lines">
                <h2 class="section-title">❌ 带缩进线（修改前）</h2>
                <div class="outline-tree">
                    <div class="tree-node level-1">
                        <span class="expand-icon expanded">▶</span>
                        <span class="node-prefix">1</span>
                        <span class="node-content">引言：未来城市发展</span>
                    </div>
                    <div class="tree-node level-2">
                        <span class="expand-icon expanded">▶</span>
                        <span class="node-prefix">1.1</span>
                        <span class="node-content">未来城市的概念</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.1.1</span>
                        <span class="node-content">未来城市定义</span>
                    </div>
                    <div class="tree-node level-4">
                        <span class="node-content">未来城市是指通过先进技术...</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.1.2</span>
                        <span class="node-content">未来城市特征</span>
                    </div>
                    <div class="tree-node level-4">
                        <span class="node-content">随着全球城市化进程...</span>
                    </div>
                    <div class="tree-node level-2">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.2</span>
                        <span class="node-content">未来城市的发展趋势</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.2.1</span>
                        <span class="node-content">城市化进程</span>
                    </div>
                </div>
            </div>

            <!-- 无缩进线的版本 -->
            <div class="demo-section">
                <h2 class="section-title">✅ 无缩进线（修改后）</h2>
                <div class="outline-tree">
                    <div class="tree-node level-1">
                        <span class="expand-icon expanded">▶</span>
                        <span class="node-prefix">1</span>
                        <span class="node-content">引言：未来城市发展</span>
                    </div>
                    <div class="tree-node level-2">
                        <span class="expand-icon expanded">▶</span>
                        <span class="node-prefix">1.1</span>
                        <span class="node-content">未来城市的概念</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.1.1</span>
                        <span class="node-content">未来城市定义</span>
                    </div>
                    <div class="tree-node level-4">
                        <span class="node-content">未来城市是指通过先进技术...</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.1.2</span>
                        <span class="node-content">未来城市特征</span>
                    </div>
                    <div class="tree-node level-4">
                        <span class="node-content">随着全球城市化进程...</span>
                    </div>
                    <div class="tree-node level-2">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.2</span>
                        <span class="node-content">未来城市的发展趋势</span>
                    </div>
                    <div class="tree-node level-3">
                        <span class="expand-icon">▶</span>
                        <span class="node-prefix">1.2.1</span>
                        <span class="node-content">城市化进程</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 修改说明</h3>
            <p>已成功去除大纲编辑器中节点前的缩进参考线，界面更加简洁清爽</p>
        </div>

        <div class="benefits-list">
            <h3>✨ 去除缩进线的优势</h3>
            <ul>
                <li><strong>视觉更简洁</strong>：减少了视觉干扰，让用户更专注于内容本身</li>
                <li><strong>界面更现代</strong>：符合现代扁平化设计趋势，看起来更清爽</li>
                <li><strong>层级依然清晰</strong>：通过缩进距离和卡片样式仍能清楚区分层级关系</li>
                <li><strong>减少视觉噪音</strong>：去除不必要的线条，让界面更加干净整洁</li>
                <li><strong>提升用户体验</strong>：简化的界面让用户操作更加舒适自然</li>
            </ul>
        </div>
    </div>

    <script>
        // 简单的展开/折叠交互演示
        document.querySelectorAll('.expand-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                this.classList.toggle('expanded');
            });
        });
    </script>
</body>
</html>
