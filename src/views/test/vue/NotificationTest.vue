<template>
  <div class="notification-test-container">
    <div class="test-header">
      <h1>通知工具测试页面</h1>
      <p>测试扩展后的4个角位置通知功能</p>
    </div>

    <div class="test-sections">
      <!-- 基础4角通知测试 -->
      <div class="test-section">
        <h2>基础4角位置通知</h2>
        <div class="button-group">
          <el-button type="success" @click="showTopLeft">左上角通知</el-button>
          <el-button type="primary" @click="showTopRight">右上角通知</el-button>
          <el-button type="warning" @click="showBottomLeft">左下角通知</el-button>
          <el-button type="danger" @click="showBottomRight">右下角通知</el-button>
        </div>
      </div>

      <!-- 不同类型通知测试 -->
      <div class="test-section">
        <h2>不同类型通知测试</h2>
        <div class="button-group">
          <el-button type="success" @click="showSuccess">成功通知</el-button>
          <el-button type="danger" @click="showError">错误通知</el-button>
          <el-button type="warning" @click="showWarning">警告通知</el-button>
          <el-button type="info" @click="showInfo">信息通知</el-button>
        </div>
      </div>

      <!-- 特殊功能测试 -->
      <div class="test-section">
        <h2>特殊功能测试</h2>
        <div class="button-group">
          <el-button @click="showAllCorners">显示所有角通知</el-button>
          <el-button @click="showPersistent">持久通知</el-button>
          <el-button @click="showProgress">进度通知</el-button>
          <el-button @click="clearAllNotifications">清除所有通知</el-button>
        </div>
      </div>

      <!-- 自定义位置测试 -->
      <div class="test-section">
        <h2>自定义位置测试</h2>
        <div class="button-group">
          <el-button @click="showCenterTop">顶部居中</el-button>
          <el-button @click="showCenterBottom">底部居中</el-button>
          <el-button @click="showCustomPosition">完全自定义位置</el-button>
        </div>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <h2>使用说明</h2>
      <div class="code-examples">
        <h3>基础用法：</h3>
        <pre><code>// 左上角通知
import { createNotificationTopLeft } from '@/utils/common';
createNotificationTopLeft('消息内容', 'success', 3000);

// 右上角通知
import { createNotificationTopRight } from '@/utils/common';
createNotificationTopRight('消息内容', 'warning', 3000);

// 左下角通知
import { createNotificationBottomLeft } from '@/utils/common';
createNotificationBottomLeft('消息内容', 'info', 3000);

// 右下角通知
import { createNotificationBottomRight } from '@/utils/common';
createNotificationBottomRight('消息内容', 'error', 3000);</code></pre>

        <h3>高级用法：</h3>
        <pre><code>// 带自定义选项
createNotificationTopRight('消息', 'success', 5000, {
  zIndex: 10000,
  containerId: 'custom-container'
});

// 不自动关闭的通知
const notification = createNotificationBottomRight('持久通知', 'warning', 0);
// 手动关闭
notification.close();</code></pre>
      </div>
    </div>
  </div>
</template>

<script>
import {
  createNotification,
  createNotificationTopLeft,
  createNotificationTopRight,
  createNotificationBottomLeft,
  createNotificationBottomRight
} from '@/utils/common';

export default {
  name: 'NotificationTest',
  data() {
    return {
      progressNotification: null,
      progressValue: 0
    };
  },
  methods: {
    // 基础4角通知
    showTopLeft() {
      createNotificationTopLeft('这是左上角通知消息', 'success', 3000);
    },
    
    showTopRight() {
      createNotificationTopRight('这是右上角通知消息', 'info', 3000);
    },
    
    showBottomLeft() {
      createNotificationBottomLeft('这是左下角通知消息', 'warning', 3000);
    },
    
    showBottomRight() {
      createNotificationBottomRight('这是右下角通知消息', 'error', 3000);
    },

    // 不同类型通知
    showSuccess() {
      createNotificationTopRight('操作成功！', 'success', 3000);
    },
    
    showError() {
      createNotificationTopLeft('发生错误！', 'error', 5000);
    },
    
    showWarning() {
      createNotificationBottomRight('警告信息！', 'warning', 4000);
    },
    
    showInfo() {
      createNotificationBottomLeft('提示信息！', 'info', 3000);
    },

    // 特殊功能
    showAllCorners() {
      setTimeout(() => createNotificationTopLeft('左上角', 'success', 3000), 0);
      setTimeout(() => createNotificationTopRight('右上角', 'info', 3000), 300);
      setTimeout(() => createNotificationBottomRight('右下角', 'warning', 3000), 600);
      setTimeout(() => createNotificationBottomLeft('左下角', 'error', 3000), 900);
    },
    
    showPersistent() {
      createNotificationBottomRight(
        '这是一个持久通知，不会自动关闭，请手动点击关闭按钮',
        'warning',
        0
      );
    },
    
    showProgress() {
      this.progressNotification = createNotificationTopRight(
        '正在处理中... 0%',
        'info',
        0
      );
      
      this.progressValue = 0;
      const interval = setInterval(() => {
        this.progressValue += 10;
        if (this.progressNotification && this.progressNotification.element) {
          const textElement = this.progressNotification.element.querySelector('span:nth-child(2)');
          if (textElement) {
            textElement.textContent = `正在处理中... ${this.progressValue}%`;
          }
        }
        
        if (this.progressValue >= 100) {
          clearInterval(interval);
          if (this.progressNotification) {
            this.progressNotification.close();
            this.progressNotification = null;
          }
          createNotificationTopRight('处理完成！', 'success', 3000);
        }
      }, 500);
    },
    
    clearAllNotifications() {
      // 清除所有通知容器
      const containerIds = [
        'notification-top-left-container',
        'notification-top-right-container',
        'notification-bottom-left-container',
        'notification-bottom-right-container'
      ];
      
      containerIds.forEach(id => {
        const container = document.getElementById(id);
        if (container) {
          const closeButtons = container.querySelectorAll('span[onclick]');
          closeButtons.forEach(button => {
            if (button.textContent === '×') {
              button.click();
            }
          });
        }
      });
    },

    // 自定义位置
    showCenterTop() {
      createNotification(
        '顶部居中通知',
        'info',
        3000,
        {
          position: { top: '20px', left: '50%', transform: 'translateX(-50%)' },
          containerId: 'center-top-container'
        }
      );
    },
    
    showCenterBottom() {
      createNotification(
        '底部居中通知',
        'success',
        3000,
        {
          position: { bottom: '20px', left: '50%', transform: 'translateX(-50%)' },
          containerId: 'center-bottom-container'
        }
      );
    },
    
    showCustomPosition() {
      createNotification(
        '完全自定义位置通知',
        'warning',
        3000,
        {
          position: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' },
          containerId: 'center-center-container'
        }
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.notification-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #606266;
    font-size: 16px;
  }
}

.test-sections {
  display: grid;
  gap: 30px;
  margin-bottom: 40px;
}

.test-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  h2 {
    color: #303133;
    margin-bottom: 15px;
    font-size: 18px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  
  .el-button {
    margin: 0;
  }
}

.usage-guide {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  
  h2 {
    color: #303133;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #606266;
    margin: 15px 0 10px 0;
    font-size: 16px;
  }
}

.code-examples {
  pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.5;
    
    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
