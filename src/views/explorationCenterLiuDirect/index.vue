<template>
  <div class="app-container ck-container">

    <div v-if="hisVisible" class="his-overlay" @click.self="closeHis">
      <div class="his-content" @click.stop>
        <el-table ref="multipleTable" :data="hisList" height="830" style="width: 100%" :show-header="false"
          @row-click="handleReview">
          <el-table-column label="内容" prop="content" :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" width="50">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-delete" @click.native.stop="handleDel(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
        <el-button class="close-button" circle icon="el-icon-close" @click="closeHis"></el-button>
      </div>
    </div>

    <div class="sidebar-menu">
      <!-- 这里可以放置你的侧边栏内容，比如链接、按钮等 -->
      <el-button class="creat-btn" type="text" @click="handleAdd"><i style="font-size: 35px;font-weight: 600;padding: 0"
          class="el-icon-plus"></i></el-button>
      <el-button class="creat-btn" type="text" @click="handleReviewHis"><i
          style="font-size: 35px;font-weight: 600;padding: 0" class="el-icon-tickets"></i></el-button>
    </div>

    <div class="right-container">

      <div class="right-container-box" v-if="!dialogueList||dialogueList.length==0">
        <img :src="explorationlogo" style="width: 400px;height: 200px" />

        <div style="width: 100%;height: 100%">
          <el-input style=" border: none;" v-model="content" type="textarea" :rows="6" maxlength="2000" show-word-limit
            @keydown.native="handleKeyCode($event)" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
          <el-button style="position: relative;bottom:30px;right: -750px;background-color: cornflowerblue;"
            type="primary" size="mini" round icon="el-icon-s-promotion" @click="ask" :loading="loadSendBtn" />
        </div>

      </div>

      <div class="right-container-top" v-if="dialogueList&&dialogueList.length>0">
        <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
          justify="center">
          <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
          <div v-if="item.issue == 'assistant'" style="width:100%;">
            <div
              style="float:left;background-color: aliceblue;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">

              <div v-if="!item.content.startsWith('data:image/png;base64,')">
                <!--                {{item.content}}-->
                <div class="markdown-content" v-html="markedContent(item.content)"></div>
                <div v-show="item.imageUrls">
                  <img v-for="(imageUrl, index) in item.imageUrls" :key="index" :src="imageUrl" alt="Dialogue Image"
                    style="max-width: 100%; height: auto; margin-top: 5px;">
                </div>
                <div style="width: 100%">
                  <!--                  <el-button style="margin: 5px;" size="mini" round @click="stopAudio">停止播放</el-button>-->
                  <!--                  <el-button style="margin: 5px;" size="mini" round @click="playAudio(item.content)">开始播放-->
                  <!--                  </el-button>-->
                  <el-button
                    style="border: none;margin: 5px;padding-top:5px;padding-bottom: 5px;background-color: unset;"
                    size="mini" round @click="likeOrStomp(item,index,1)">
                    <svg-icon :icon-class="item.likeStomp=='1'?'good':'thumbs-up'" class-name="card-panel-icon" />
                  </el-button>
                  <el-button
                    style="border: none;margin: 5px;padding-top:5px;padding-bottom: 5px;background-color: unset;"
                    size="mini" round @click="likeOrStomp(item,index,2)">
                    <svg-icon :icon-class="item.likeStomp=='2'?'bad':'thumbs-down'" class-name="card-panel-icon" />
                  </el-button>

                  <el-button v-if="!loadSendBtn && index == dialogueList.length-1" round icon="el-icon-refresh"
                    style="background-color: unset;border: none;float: left;margin: 5px;" size="mini"
                    @click="handleRegen(index)">
                    重新生成
                  </el-button>
                </div>
                <div style="position:absolute; bottom:-23px; left:5px;"> <!-- 调整bottom值以靠近内容框 -->
                  <!-- 重新生成按钮 -->
                  <!--                  <el-button v-if="loadSendBtn && index == dialogueList.length-1" size="mini" type="text"-->
                  <!--                    class="copy-button" @click="handleStopGeneration()"-->
                  <!--                    style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 40px;">停止生成</el-button>-->
                </div>
              </div>

              <div v-if="item.content.startsWith('data:image/png;base64,') || txtToImage ">
                <div v-if="!haveImage&&index === dialogueList.length - 1">
                  <el-skeleton style="width: 240px">
                    <template slot="template">
                      <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
                      <div style="padding: 14px;">
                        <el-skeleton-item variant="p" style="width: 50%" />
                        <div style="display: flex; align-items: center;">
                          <el-skeleton-item variant="text" style="margin-right: 16px;" />
                          <el-skeleton-item variant="text" style="width: 30%;" />
                        </div>
                      </div>
                    </template>
                  </el-skeleton>
                </div>
                <!--  index === dialogueList.length - 1   -->
                <img v-if="haveImage || index != dialogueList.length - 1" :src="item.content " alt=""
                  style="max-width: 100%; height: auto;">
              </div>

            </div>
          </div>
          <div v-if="item.issue == 'user'" style="width:100%;">
            <div
              style="float:right;background:#97a8be;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
              <br>
              <img v-if="item.imageUrl" :src="item.imageUrl" alt="Dialogue Image"
                style="max-width: 100%; height: auto;">
            </div>
          </div>
          <!-- <el-input v-model=" item.content" type="textarea" autosize readonly /> -->
          <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;" />
        </el-row>

        <!-- 相关问题展示 -->
        <div
          v-if="relatedIssuesList && relatedIssuesList.length && dialogueList[dialogueList.length - 1].issue === 'assistant'"
          style="margin-top: 30px;">
          <span style="padding-left:40px;color:#7d83c5;font-size: 14px;line-height: 26px;"> 你可以继续问我： </span>
          <ul>
            <li v-for="(issue, index) in relatedIssuesList" :key="index" style="list-style-type: none; cursor: pointer;"
              @click="askNewQuestion(issue,dialogueList.length-1)">
              <span class="issue-text">{{issue}}</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="right-container-bottom" v-if="dialogueList&&dialogueList.length>0">
        <div
          style="border:1px solid #efefff;margin-top: 10px;padding: 10px 10px 2px 10px;background-color: #fff;border-radius: 12px ;">
          <div style="display: flex; align-items: center;">
            <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'" placement="top">
              <el-button style="margin: 5px;" size="mini" round
                :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'"
                @click="voiceASRType === 1 ? baiduASR() : translation()">
                {{translationFlag ? '停止' : '语音'}}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="light" :content="playFlag ?  '点击停止播放': '点击播放'" placement="top">
              <el-button style="margin: 5px;border: none;" size="mini" round @click="autoPlay">
                <svg-icon :icon-class="playFlag ?  'voice': 'voiceClose'" />
                {{playFlag ? ' ': ' '}}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="light" :content="txtToImage?'点击切换体验中心':'点击切换文生图'" placement="top">
              <el-button style="font-size: 22px;margin: 5px;border: none;" size="mini" round
                :disabled="shouldDisableUpload" :icon="txtToImage?'el-icon-magic-stick':'el-icon-chat-line-square'"
                @click="changeTxtToImage">
                {{txtToImage ? '' :  ''}}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="light" content="最大5MB" placement="top-start">
              <el-upload class="upload-btn-bottom-left" :action="uploadUrl" :data="uploadData" :headers="headers"
                :limit="1" :file-size-limit="5 * 1024 * 1024" :on-success="handleUploadSuccess" accept=".png, .jpg, "
                :before-upload="beforeUpload" :file-list="fileList" :on-remove="RemoveFile" :disabled="txtToImage"
                :on-progress="handleProgress">
                <el-button slot="trigger" size="mini" round icon="el-icon-upload"
                  style="font-size: 22px;border: none;"></el-button>
              </el-upload>
            </el-tooltip>
          </div>

          <el-input v-model="content" type="textarea" :rows="4" maxlength="2000" show-word-limit
            @keydown.native="handleKeyCode($event)" @blur="saveEditedContent" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
          <el-button style="position: relative;bottom:30px; float: right;background-color: cornflowerblue;"
            type="primary" size="mini" round icon="el-icon-position" @click="ask" :loading="loadSendBtn" />
          <!--          <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"-->
          <!--                     @click="ask" :loading="loadSendBtn" />-->
        </div>

      </div>
      <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio"
        @ended="continuePlay">
        您的浏览器不支持音频播放。
      </audio>

    </div>
  </div>
</template>

<script>
import logoImg from "@/assets/logo/logo1.png";
import asks from "@/assets/logo/asks1.png";
import user from "@/assets/logo/user1.png";
import explorationlogo from "@/assets/images/explorationlogo.png";
import IatRecorder from '@/assets/js/IatRecorder.js'
import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
import { AudioRecorder } from "@/assets/js/BaiDuASR.js";
import axios from "axios";
import qs from "qs";
import { getToken } from "@/utils/auth";
import Cookies from "js-cookie";
import marked from 'marked';
import speechRecognitionService from '@/utils/speechRecognitionService';
import { eventBus } from '@/utils/eventBus';
import {
  getDialogueList,
  getPromptList,
  addDialogue,
  updateDialogue,
  delDialogue,
  getDialogue,
  getBaiDuToken,
  getId,
  likeOrStomp,
  stopGeneration,
  relatedIssues
} from "@/api/explorationCenterDirect/experience.js";
import {
  getUserVoiceRole,
} from "@/api/system/voiceRole.js";

const iatRecorder = new IatRecorder('zh_cn', 'mandarin')////
const ttsRecorder = new TtsRecorder();
export default {
  name: "ExplorationCenter",
  dicts: ['call_type','system_websocket_param','user_voiceasr_choose','user_voice_choose'],
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
      uploadData: { modeltype: 'img' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },

      progress: {
        visible: false, // 控制进度条是否显示
        percentage: 0, // 进度百分比
        status: 'success' // 进度条状态，如 'success'、'exception' 等，根据需要设定
      },
      logo: logoImg,
      asks: asks,
      user: user,
      explorationlogo: explorationlogo,
      promptOptions: [],
      invocation: "model",
      hisList: [],
      relatedIssuesList: [],
      promptId: "",
      menuRouting: "",
      dialogueList: [],
      dialogueNum: true,
      dialogueResult: { content: '正在回答，请稍等···' }, //

      loadSendBtn: false,
      content: "",
      id: "",
      fileList: [], // 用于存储待上传的文件列表
      translationFlag: false,

      playFlag: false,//自动播放
      isPlayAudio: false,//正在播放
      token: "", //使用ai的accessToken
      audioArr: [], //音频数组
      segments: [], //
      currentIndex: 0, //音频播放第几条
      per: "1",
      isPlayFinish: false,
      isStopFinsh: true,
      speed: 5, // 语速
      pitch: 5, // 语调
      volum: 5,  // 音量
      pid: '',
      AsrintervalId: null,
      txtToImage: false,
      haveImage: false,
      hisVisible: false,
      baiduASRParam:{
        appId: '',
        appKey: '',
        dev_pid: '',
        name: this.$route.path
      },
      contenMark:0,
      finalText: "", // 累加的最终结果
      timer: null, // 定时器
      voiceASRType: 1, // 识别服务商
      voiceType: 1, // 语音合成服务商
    };
  },
  created() {
    this.getPromptList();
    this.getDialogueList();
    //获取token

    this.getToken();
    this.getVoiceRole();
    this.initRecorder();
  },


  beforeDestroy() {
    //关闭页面前停止
    this.stopAudio();
  },


  computed: {

    shouldDisableUpload() { // 计算属性，判断是否应该禁用上传按钮
      return this.fileList.length > 0; // 如果 fileList 不为空，即有文件，则禁用上传按钮
    },

    computedAudioSrc() {
      return this.audioArr[this.currentIndex];
    },
  },


  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.isPlayAudio && this.playFlag && !this.txtToImage) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play();
            this.isplaying = true;
          },
          { once: true }
        );
      }
    },
  },

  activated() {
  },


  mounted(){
    setTimeout(() => {
      this.getVoiceASRChoose()
    }, 1500);
  },
  methods: {
    // 获取语音识别调用
    getVoiceASRChoose() {
      // 通用函数：根据字典值设置类型
      const setTypeFromDict = (dict, key) => {
        dict.forEach(item => {
          if (item.label === "baidu") {
            this[key] = 1;
          } else if (item.label === "xfSpark") {
            this[key] = 0;
          }
        });
      };
      // 设置 voiceASRType
      setTypeFromDict(this.dict.type.user_voiceasr_choose, "voiceASRType");
      // 设置 voiceType
      setTypeFromDict(this.dict.type.user_voice_choose, "voiceType");
    },
    markedContent(content) {
      const htmlContent = marked.parse(content);
      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div');
      tempElement.innerHTML = htmlContent;
      // 递归函数来移除空白文本节点
      function removeEmptyTextNodes(node) {
        if (node.nodeType === Node.TEXT_NODE && !node.textContent.trim()) {
          node.parentNode.removeChild(node);
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          for (let i = node.childNodes.length - 1; i >= 0; i--) {
            removeEmptyTextNodes(node.childNodes[i]);
          }
        }
      }
      // 遍历所有块级元素并移除空白文本节点
      const blockElements = ['p', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'blockquote', 'hr'];
      blockElements.forEach(tagName => {
        const elements = tempElement.querySelectorAll(tagName);
        elements.forEach(element => {
          removeEmptyTextNodes(element);
        });
      });
      // 返回处理后的HTML内容
      return tempElement.innerHTML;
    },
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/addLiu_Direct', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            this.getDialogueList();
            if (this.playFlag && !this.txtToImage) {
              this.playAudio(s);
            }
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight()
          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    getImgUrl(pptPath) {
      let baseUrl = window.location.origin
      var imgData;
      if (pptPath.includes('/ruoyi/')) {
        // 替换路径中的 ruoyi/ 之前的部分
        const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

        if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
          imgData = 'http://127.0.0.1:9215' + finalPath
          // console.log('Final baseUrl:', baseUrl)
        } else {
          imgData = baseUrl + finalPath
        }
      }
      return imgData;

    },
    //相关问题
    askNewQuestion(issue, index) {
      this.stopAudio()
      this.loadSendBtn = true;
      this.dialogueList.push({ content: issue, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      this.getHeight()
      const param = {
        invocation: this.invocation,
        //promptId: this.promptId,
        content: issue,
        id: this.id,
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
        language: Cookies.get("voiceType"),
        txtToImage: this.txtToImage ? 1 : 0,
        imagePath: this.fileList.length > 0 ? this.fileList[0] : null,
      };

      this.content = '';
      this.relatedIssuesList = [];
      //
      this.updateDialogue2(param).then(() => {
        // 确保对话列表中至少有两个元素
        if (this.dialogueList.length >= 2) {
          const quae = {
            content: this.dialogueList[this.dialogueList.length - 2].content,
          };

          // 调用 relatedIssues 函数并传递 quae 对象
          // relatedIssues(quae).then((res) => {
          //   if (res.code === 200) {
          //     this.relatedIssuesList = res.data;
          //   }
          // });
        }
      })
        .catch(error => console.error('Error:', error));
    },
    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlag && !this.txtToImage) {
              this.playAudio(s);
            }
            this.handleReview({ id: this.id })
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight()
          }
        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    // 获取发言人
    getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
        })
      },
    handlePlay() {
      if (this.playFlag) {
        this.playFlag = !this.playFlag
        this.pause()
      } else {
        this.playFlag = !this.playFlag
      }
    },

    play(content) {
      let resText = this.markdownToPlainText(content)
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: resText,
        // 角色
        voiceName: this.per,
        // 语速
        speed: this.speed,
        // 音量
        voice: this.volume,
        // 音高
        pitch: this.pitch,
      });
      ttsRecorder.start();
    },

    pause() {
      ttsRecorder.stop();
    },


    initRecorder(){
      speechRecognitionService.initRecorder((blob, encTime) => {
        if (speechRecognitionService.websocket && speechRecognitionService.isRecording) {
          speechRecognitionService.websocket.send(blob);
        }
      });
    },
    baiduASROptions(){
      const type = Cookies.get("voiceType")
      if(type === 'CN'){
        this.baiduASRParam.dev_pid = 1537
      }else if(type === 'EN'){
        this.baiduASRParam.dev_pid = 1737
      }else{
        this.baiduASRParam.dev_pid = 1537
      }
      this.dict.type.system_websocket_param.forEach(item => {
        console.log(item)
        if(item.label === 'appId'){
          this.baiduASRParam.appId = Number(item.value)
        }
        if(item.label === 'appKey'){
          this.baiduASRParam.appKey = item.value
        }
      })
    },
    closeASR(){
      this.translationFlag = false;
      this.finalText = "";
      if(this.voiceASRType === 1){
        speechRecognitionService.closeWebsocket();
        eventBus.$off(this.baiduASRParam.name);
        if (this.timer) {
          clearTimeout(this.timer); // 清除定时器
          this.timer = null;
        }
        return;
      }
      iatRecorder.stop();
    },
    baiduASR(){
      this.baiduASROptions();
      if(this.translationFlag){
        this.closeASR();
      }else{
        this.translationFlag = true;
        speechRecognitionService.startSpeechRecognition(error => {
        if (error) {
          this.$message.error('麦克风未打开！');
          switch (error.message || error.name) {
            case 'PERMISSION_DENIED':
            case 'PermissionDeniedError':
              console.info('用户拒绝提供信息。');
              break;
            case 'NOT_SUPPORTED_ERROR':
            case 'NotSupportedError':
              console.info('浏览器不支持硬件设备。');
              break;
            case 'MANDATORY_UNSATISFIED_ERROR':
            case 'MandatoryUnsatisfiedError':
              console.info('无法发现指定的硬件设备。');
              break;
            default:
              console.info('无法打开麦克风。异常信息:' + (error.code || error.name));
              break;
          }
        }
      }, this.baiduASRParam);
      eventBus.$on(this.baiduASRParam.name, (result) => {
        console.log("识别结果:", result);

        if (result.type === "MID_TEXT"){
          this.content = this.finalText + result.text;
        }
        if (result.type === "FIN_TEXT"){
          console.log("最终结果:", this.finalText);
           // 累加最终结果
          this.finalText += result.text;
          this.content = this.finalText;
          console.log("最终结果:", this.content);
        }
      });
      // 设置 60 秒后自动关闭录音
      this.timer = setTimeout(() => {
        speechRecognitionService.closeWebsocket(); // 调用关闭方法
        eventBus.$off(this.baiduASRParam.name) // 清除监听器
        this.translationFlag = false; // 更新标识
        this.finalText = "";
        this.$message.info('录音已自动停止。');
      }, 60000);
      }
    },

    saveEditedContent() {
      // 用户编辑完成后同步最终内容
      this.finalText = this.content;
      console.log("保存用户编辑后的结果:", this.finalText);
    },


    // ？？？？
    translation() {
      if (this.translationFlag) {
        iatRecorder.stop();
        this.translationFlag = false;
      } else {
        let language = ""
        const type = Cookies.get("voiceType")
        if(type === 'CN'){
          language = "zh_cn"
        }else if(type === 'EN'){
          language = "en_us"
        }else{
          language = "zh_cn"
        }
        iatRecorder.setParams({
          language: language
        })
        iatRecorder.start()
        this.translationFlag = true;
        iatRecorder.onTextChange = (text) => {
          let inputText = text;
          this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
        };
      }
    },

    // 上传
    handleUploadSuccess(res, file,) {
      file.id = res.data.id;
      this.fileList.push(res.data.preview);
      this.progress.visible = false;
      // console.log(this.fileList);
    },
    RemoveFile() {
      this.fileList = [];
    },
    beforeUpload(file) {
      // console.log(file);
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.showAlert = true; // 显示警告提示
        this.$message.error('文件大小超过5MB，无法上传！');
        return false; // 阻止文件上传
      }
      return isLt5M;
    },
    handleProgress(event, file, fileList) {
      // 更新进度信息
      this.progress.visible = true;
      this.progress.percentage = parseInt(event.percent);
      // 可以根据需要设置进度条状态，例如上传失败时设置为 'exception'
    },

    getExplicitImplicit() {
      return this.$route.path !== '/explorationCenter7/explorationCenter01';
    },
    getModel() {
      return this.$route.path === '/explorationCenter7/explorationCenter01';
    },

    getDialogueList() {
      const param = {
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
        invocation: this.invocation,
      };
      getDialogueList(param).then((res) => {
        this.hisList = res.data;
      });
    },


    getPromptList() {
      getPromptList().then((res) => {
        const promptOptions = [];
        if (res.data && res.data.length > 0) {
          res.data.forEach((item) => {
            promptOptions.push({
              label: item.templateName,
              value: item.id,
            });
          });
        }
        this.promptOptions = promptOptions;
      });
    },


    handleAdd() {
      this.dialogueNum = true;
      this.dialogueList = [];
      this.fileList = [];
      this.content = "";
      this.id = "";
      this.relatedIssuesList = [];
    },
    async getId() {
      await getId()
        .then((res) => {
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    //请求后端
    async ask() {
      if(this.translationFlag){
        this.closeASR();
      } // 关闭语音输入
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }

      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      // this.dialogueList.push({content: '', issue: "assistant"});
      this.getHeight()
      //首次发请求
      if (this.dialogueNum) {
        //后端ai询问
        await this.getId();
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          //menuRouting: this.getSecondSlash(this.routePath) + "/",
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          id: this.id,
          language: Cookies.get("voiceType"),
          imagePath: this.fileList.length > 0 ? this.fileList[0] : null,
          txtToImage: this.txtToImage ? 1 : 0,
        };
        console.log(param);
        //置空content
        this.content = ''
        this.relatedIssuesList = [];
        //发送请求
        await this.addDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };

            // 调用 relatedIssues 函数并传递 quae 对象
            // relatedIssues(quae).then((res) => {
            //   if (res.code === 200) {
            //     this.relatedIssuesList = res.data;
            //   }
            // });
          }
        }).catch(error => {
          console.error('添加对话时出错:', error);
        });
        this.haveImage = true
        this.handleReview({ id: this.id })
      } else {
        //
        this.stopAudio()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          id: this.id,
          menuRouting: this.getSecondSlash(this.$route.path) + "/",
          language: Cookies.get("voiceType"),
          txtToImage: this.txtToImage ? 1 : 0,
          imagePath: this.fileList.length > 0 ? this.fileList[0] : null,
        };

        this.content = ''
        this.relatedIssuesList = [];
        //
        this.updateDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };

            // 调用 relatedIssues 函数并传递 quae 对象
            // relatedIssues(quae).then((res) => {
            //   if (res.code === 200) {
            //     this.relatedIssuesList = res.data;
            //   }
            // });
          }
        })
          .catch(error => console.error('Error:', error));
      }

    },
    //获取token
    async getToken() {
      const res = await getBaiDuToken();
      this.token = res.token;
    },


    // 自动播放
    autoPlay() {
      //如果已是自动播放  关闭播放 改变状态
      if (this.playFlag) {
        this.playFlag = !this.playFlag;

        this.isPlayAudio = false
        this.$refs.audio.pause();
        //清除音频src []
        this.clearAudio();
        // this.isplaying = false
      } else {
        this.playFlag = !this.playFlag;
        this.isPlayAudio = false
      }
    },

    //停止播放
    stopAudio() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false;
        this.$refs.audio.pause();
        // this.isplaying = false;
        this.isPlayAudio = false;
        this.isPlayFinish = true;
        this.clearAudio();
      } else {
        setTimeout(() => {
          this.isStopFinsh = true;
        }, 500);
      }
    },


    // 封装异步  文本转音频   播放音频
    async playAudio(content) {
      if (this.isPlayAudio && !this.txtToImage) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true

      //提前获取token 并赋值
      const res = await getBaiDuToken();
      this.token = res.token;

      this.isPlayFinish = false
      this.textToAudio2(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
      //this.isplaying = true;
    },



    // 每段音频结束后调用此函数播放下一段
    continuePlay() {
      this.currentIndex++;
      if (this.currentIndex < this.audioArr.length) {
        setTimeout(() => {
          this.$refs.audio.load();
          this.$refs.audio.play().catch((error) => {
          });
        }, 100);
      } else {
        this.isplaying = false;
        this.isPlayAudio = false;
      }
    },

    // 文本转语音  提供文本转语音
    async textToAudio2(text, token) {
      const tex = this.markdownToPlainText(text);
      this.segments = this.splitTextByPunctuation(tex, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          responseType: "blob",
        });
        if (this.isPlayFinish) {
          this.clearAudio()
          return;
        }
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    clearAudio() {
      this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.audioArr = []
    },

    markdownToPlainText(markdown) {
    if (!markdown) return '';

    // 移除 Markdown 标题
    markdown = markdown.replace(/^#+\s(.+)/gm, '$1');

    // 移除 Markdown 图片和链接
    markdown = markdown.replace(/!\[.*?\]\(.*?\)/g, '');
    markdown = markdown.replace(/\[.*?\]\(.*?\)/g, '');

    // 移除 Markdown 粗体和斜体
    markdown = markdown.replace(/\*\*(.*?)\*\*/g, '$1'); // 粗体
    markdown = markdown.replace(/\*(.*?)\*/g, '$1');     // 斜体
    markdown = markdown.replace(/__(.*?)__/g, '$1');     // 粗体
    markdown = markdown.replace(/_(.*?)_/g, '$1');       // 斜体

    // 移除 Markdown 代码块和行内代码
    markdown = markdown.replace(/```[\s\S]*?```/g, '');
    markdown = markdown.replace(/`(.*?)`/g, '$1');

    // 移除 Markdown 分割线
    markdown = markdown.replace(/-{3,}/g, '');

    // 移除 Markdown 列表
    markdown = markdown.replace(/^\s*[-*+]\s+/gm, '');
    markdown = markdown.replace(/^\d+\.\s+/gm, '');

    // 移除 Markdown 引用
    markdown = markdown.replace(/^>\s+/gm, '');

    // 移除 Markdown 表格
    markdown = markdown.replace(/\|.*?\|/g, '');
    markdown = markdown.replace(/-\|/g, '');

    // 移除多余的换行和空格
    markdown = markdown.replace(/\n{2,}/g, '\n');
    markdown = markdown.trim();

    return markdown;
},

    // 拆分文本
    splitTextByPunctuation(text, maxLength) {

      const punctuation = /[。！？；]/;
      const secondaryPunctuation = /[ ， ]/;
      let result = []
      let currentSegment = ""
      while (text.length > 0) {
        // 正则表达式匹配字符
        let match = punctuation.exec(text);
        // 如果匹配到字符
        if (match) {
          let segment = text.slice(0, match.index + 1);
          if (segment.length <= maxLength) {
            text = text.slice(match.index + 1);
            result.push(segment.trim())
          } else {
            while (segment.length > maxLength) {
              let secondaryMatch = secondaryPunctuation.exec(segment);
              if (secondaryMatch && secondaryMatch.index < maxLength) {
                let subSegment = segment.slice(0, secondaryMatch.index + 1);
                if (subSegment.length <= maxLength) {
                  result.push(subSegment.trim());
                  segment = segment.slice(secondaryMatch.index + 1);
                } else {
                  result.push(segment.slice(0, maxLength).trim());
                  segment = segment.slice(maxLength);
                }
              } else {
                result.push(segment.slice(0, maxLength).trim());
                segment = segment.slice(maxLength);
              }
            }
            if (segment.length > 0) {
              result.push(segment.trim());
            }
            text = text.slice(match.index + 1);
          }
        } else {
          while (text.length > maxLength) {
            result.push(text.slice(0, maxLength).trim());
            text = text.slice(maxLength);
          }
          if (text.length > 0) {
            result.push(text.trim());
            text = "";
          }
        }
      }
      // 最后剩一个片段，将当前片段添加到结果数组中
      if (currentSegment.length > 0) {
        result.push(currentSegment.trim())
      }
      return result
    },


    //根据已有的文本转语音   dialogueResult
    async textToAudioBytxt(token) {

      this.segments = this.splitTextByPunctuation(this.dialogueResult.content, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("http://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          responseType: "blob",
        });
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },


    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },


    handleReview(row) {
      this.hisVisible = false
      this.relatedIssuesList = [];
      getDialogue(row.id).then((res) => {
        this.id = row.id;
        this.dialogueList = res.data.dialogueDetailsList;
        for (let i = 0; i < this.dialogueList.length; i++) {
          if (this.dialogueList[i].issue === 'user') {
            if (this.dialogueList[i].processing != null) {
              const imageData = `${this.dialogueList[i].processing}`;
              this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrl: imageData });
            }
          } else {
            let imageUrls = [];
            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            let content = this.dialogueList[i].content;

            // 解析图片路径
            while ((match = imageRegex.exec(content)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = content.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');

            this.$nextTick(() => {
              this.$set(this.dialogueList, i, { ...this.dialogueList[i], content: cleanedContent });
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrls });
                console.log("imageUrls set:", this.dialogueList[i].imageUrls);
              }
            });
          }
        }
        this.dialogueNum = false;
        this.getHeight()
        this.haveImage = true
      });
    },

    handleDel(row) {
      this.$modal
        .confirm("是否确认删除对话记录？")
        .then(function () {
          return delDialogue(row.id);
        })
        .then(() => {
          this.getDialogueList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
    },

    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    },
    // 重新生成
    handleRegen(index) {
      this.stopAudio()
      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.dialogueList[index - 1].content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      this.getHeight()
      const param = {
        invocation: this.invocation,
        //promptId: this.promptId,
        content: this.dialogueList[index - 1].content,
        id: this.id,
        menuRouting: this.getSecondSlash(this.$route.path) + "/",
        language: Cookies.get("voiceType"),
        txtToImage: this.txtToImage ? 1 : 0,
        imagePath: this.fileList.length > 0 ? this.fileList[0] : null,
      };

      this.content = ''
      this.relatedIssuesList = [];
      //
      this.updateDialogue2(param).then(() => {
        // 确保对话列表中至少有两个元素
        if (this.dialogueList.length >= 2) {
          const quae = {
            content: this.dialogueList[this.dialogueList.length - 2].content,
          };

          // // 调用 relatedIssues 函数并传递 quae 对象
          // relatedIssues(quae).then((res) => {
          //   if (res.code === 200) {
          //     this.relatedIssuesList = res.data;
          //   }
          // });
        }
      })
        .catch(error => console.error('Error:', error));
    },
    // 点赞/点踩
    likeOrStomp(item, index, type) {
      const param = {
        id: item.id,
        likeStomp: type
      }
      likeOrStomp(param).then(res => {
        if (res.code == 200) {
          this.dialogueList[index].likeStomp = type
        }
      })
    },
    getHeight() {
      this.$nextTick(() => {
        var container = document.querySelector('.right-container-top');
        container.scrollTop = container.scrollHeight;
      })
    },
    handleStopGeneration() {
      stopGeneration(this.id).then(res => {

      })
    },
    changeTxtToImage() {
      this.txtToImage = !this.txtToImage
    },
    handleReviewHis() {
      this.hisVisible = !this.hisVisible
    },
    closeHis() {
      this.hisVisible = false
    }
  },
};
</script>
<style lang="scss" scoped>
.el-input__count {
  right: 52px;
}
.ck-container {
  padding: 0;
  display: flex;
  height: 100vh;

  .sidebar-menu {
    position: fixed;
    top: 35%;
    left: 20px;
    height: 110px;
    width: 50px; /* 你可以根据需要调整宽度 */
    background-color: #ffffff;
    color: white;
    border-radius: 10px;
    padding: 10px 5px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
    z-index: 9999;
    .creat-btn {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      margin-bottom: 5px;
    }
  }

  .right-container {
    background: #dbe9f740;
    flex: 1;

    .right-container-top {
      height: 72%;
      overflow-y: auto;
      border-bottom: 1px solid #e8e8e8;
      padding: 0 20%;
    }

    .right-container-bottom {
      height: 23%;
      overflow-y: auto;
      // margin-top: 20px;
      padding: 0 300px;
    }
  }
}

.upload-btn-bottom-left {
  float: right;
  width: calc(100% - 500px);
  margin: 5px;
}

.svg-icon {
  width: 16px;
  height: 16px;
}

.issue-text {
  display: inline-block;
  padding: 5px 10px;
  border: 1px solid #ebebeb;
  background-color: #fff;
  color: #7d83c5;
  border-radius: 8px;
  transition: border-color 0.3s ease;
  margin-bottom: 5px;
}

.issue-text:hover {
  border-color: blue;
}
.right-container-box {
  width: 800px;
  height: 80%;
  margin: 100px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.his-overlay {
  position: fixed;
  top: 30px;
  left: 80px;
  right: 0;
  bottom: 0;
  width: 500px;
  z-index: 1;
}

.his-content {
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  position: relative;
  z-index: 1; /* 确保内容在 overlay 之上 */
  border: #ccc 1px solid;
}

.close-button {
  position: absolute;
  top: 5px;
  right: 5px;
}
</style>
<style lang="scss">
.creat-btn .el-button--text {
  padding: 0 !important;
}
.el-textarea .el-input__count {
  color: #909399;
  background: #ffffff;
  position: absolute;
  font-size: 12px;
  bottom: 5px;
  right: 52px;
}
/* 重置所有元素的默认外边距和内边距 */
.markdown-content  * {
  margin: 0;
  padding: 0;
  box-sizing: border-box; /* 确保盒模型计算包括边框和内边距 */
}

/* 为整个Markdown内容容器设置基本样式，并添加宽度限制 */
.markdown-content {
  font-size: 16px; /* 根据需要调整字体大小 */
  line-height: 1.5; /* 根据需要调整行高 */
  max-width: 800px; /* 设置最大宽度，可以根据实际情况调整 */
  margin: 0 auto; /* 自动水平居中 */
  padding: 1em; /* 为内容四周增加一些空间 */
  word-wrap: break-word; /* 强制文本在容器内换行 */
  overflow-wrap: break-word; /* 确保长单词或URL能够换行 */
  white-space: pre-wrap; /* 保留空白符序列，但允许自动换行 */
}

/* 对于长链接或其他可能溢出的内容，进一步确保它们不会超出容器 */
.markdown-content a,
.markdown-content code {
  word-break: break-word; /* 允许任意字符间断开以适应容器 */
  white-space: pre-wrap; /* 保留空白符序列，但允许自动换行 */
}

/* 针对代码块进行特殊处理，确保它们不会超出容器 */
.markdown-content pre {
  overflow-x: auto; /* 如果内容太宽，则启用水平滚动条 */
  white-space: pre; /* 保留空白符和换行符 */
  padding: 1em; /* 为代码块增加一些内边距 */
  background-color: #f8f8f8; /* 可选：为代码块添加背景色 */
  border: 1px solid #ddd; /* 可选：为代码块添加边框 */
}

/* 针对代码块内的代码进行特殊处理 */
.markdown-content pre code {
  white-space: pre; /* 保留空白符和换行符 */
}

/* 为段落设置适当的间距 */
.markdown-content p {
  margin: 0.1em 0.1em 0;
}

/* 为列表项设置适当的间距 */
.markdown-content li {
  margin: 0.25em 0; /* 根据需要调整 */
  padding-left: 1em; /* 如果需要缩进 */
}

/* 为有序列表和无序列表设置适当的间距 */
.markdown-content ol, .markdown-content ul {
  margin: 0.5em 0; /* 根据需要调整 */
  padding-left: 1em; /* 如果需要缩进 */
}

/* 为标题设置适当的间距 */
.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
  margin: 1em 0 0.5em; /* 根据需要调整 */
}

/* 为块级元素（如代码块）设置适当的间距 */
.markdown-content pre, .markdown-content blockquote {
  margin: 1em 0; /* 根据需要调整 */
  padding: 0.5em; /* 根据需要调整 */
}

/* 为水平线设置适当的间距 */
.markdown-content hr {
  margin: 1em 0; /* 根据需要调整 */
}

/* 确保段落和列表之间没有额外的间距 */
.markdown-content p + ol, .markdown-content p + ul, .markdown-content ol + p, .markdown-content ul + p {
  margin-top: 0.5em; /* 根据需要调整 */
}

/* 确保段落和标题之间没有额外的间距 */
.markdown-content p + h1, .markdown-content p + h2, .markdown-content p + h3,
.markdown-content p + h4, .markdown-content p + h5, .markdown-content p + h6,
.markdown-content h1 + p, .markdown-content h2 + p, .markdown-content h3 + p,
.markdown-content h4 + p, .markdown-content h5 + p, .markdown-content h6 + p {
  margin-top: 0.5em; /* 根据需要调整 */
}

/* 去除段落间的多余空行 */
.markdown-content > * + * {
  margin-top: 0.5em; /* 根据需要调整 */
}
</style>
