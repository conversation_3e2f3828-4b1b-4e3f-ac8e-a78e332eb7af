<template>
  <!-- 主容器 -->
  <div class="dataDaPing">

    <!-- 边框 -->
    <dv-border-box-11 title="AI才监控中心" class="biankuang">
      <!-- 装饰栏 -->
      <div class="daohanglan" style="
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        ">
        <!-- 装饰 -->
        <div style="
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
          ">
          <dv-decoration-3 style="width: 300px; height: 40px" />
          <dv-decoration-8 style="width: 300px; height: 40px" />
        </div>
        <div style="display: flex; flex-direction: row; justify-content: flex-end">
          <dv-decoration-8 :reverse="true" style="width: 300px; height: 40px; justify-content: flex-end" />
          <dv-decoration-3 style="width: 300px; height: 40px; justify-content: flex-end" />
        </div>
      </div>
      <!-- 按钮 -->
      <div style="
          width: 100%;
          height: auto;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-evenly;
        ">
        <dv-decoration-8 :reverse="true" style="
            width: 300px;
            height: 50px;
            transform: rotate(180deg);
            float: left;
          " />

        <dv-decoration-2 style="width:480px;height:5px;transform: rotate(180deg);" />
        <dv-decoration-5 style="width: 300px; height: 40px" />
        <dv-decoration-2 style="width:480px;height:5px;" />

        <dv-decoration-8 style="
            width: 300px;
            height: 50px;
            transform: rotate(180deg);
            float: right;
          " />
      </div>
      <!-- 大盒子 -->
      <div class="bigdiv">
        <!-- 第一行 -->
        <div class="hang1">
          <!-- 三个桶图 -->
          <div class="hang11" style="position: relative;">

            <!--智慧学堂学习情况统计 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 200px; height: 100px;position: absolute;top: -10px;left: 14px;
                      display: flex;  align-items: center; ">
              <dv-border-box-7 style="width: 200px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  智慧学堂学习情况统计</p>
              </dv-border-box-7>
            </div>
            <!----------------------------------------------------------------------------------------------------------------- -->
            <!--1.学习时长 -->
            <dv-border-box-8 style="
                margin-top: 15px;
                width: 85%;
                height: 120px;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between; 
              ">
              <!--1.电池组件 -->
              <div style="  display: flex;
                          flex-direction: row;
                          align-items: center;
                          justify-content: center;height: 230px; ">

                <div
                  style="width: 30%; height: 200px; transform: scale(0.45); transform-origin: top left; margin-top:45px px;margin-left: 40px;">
                  <dv-percent-pond :config="dianchi"
                    style="width:200px;height:100px;margin-left: -60PX;margin-top: 60PX;transform: scale(1);" />
                </div>

                <!-- 1.进度值 -->
                <div style="width: 60%; height: 40px; display: flex;
                          flex-direction: column;
                          align-items: center;
                          justify-content: center;
                          border: 'rgba(255, 255, 255, 0.0)' 1px solid;
                          margin-bottom: 100px;">
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;margin-right: 20px;">
                    <div>
                      <p>总学习时长:</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;margin-top: 10px;">
                    <div>
                      <p style="font-size:30px ;margin-right: 20px;">{{this.wisdomStatistics.totalDuration}}</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                </div>
              </div>
            </dv-border-box-8>
            <!-- 2.智能问答 -->
            <dv-border-box-8 style="
                margin-top: 15px;
                width: 85%;
                height: 120px;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between; 
              ">
              <!-- 2.桶图 -->
              <div style="  display: flex;
                          flex-direction: row;
                          align-items: center;
                          justify-content: center;height: 230px; ">

                <div
                  style="width: 30%; height: 200px; transform: scale(0.45); transform-origin: top left; margin-bottom:25px px;margin-left: 10px;">
                  <dv-water-level-pond :config="tong1" style="width: 150px; height: 200px;" />
                </div>

                <!-- 2.问答次数 -->
                <div style="width: 60%; height: 40px; display: flex;
                          flex-direction: column;
                          align-items: center;
                          justify-content: center;
                          border: 'rgba(255, 255, 255, 0.0)' 1px solid;
                          margin-bottom: 120px;">
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;">
                    <div>
                      <p>智能问答次数:</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;margin-top: 10px;">
                    <div>
                      <p style="font-size:30px ;">{{this.wisdomStatistics.intelligentCount}}</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                </div>
              </div>
            </dv-border-box-8>
            <!-- 3.学习次数 -->
            <dv-border-box-8 style="
                margin-top: 15px;
                width: 85%;
                height: 120px;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between; 
              ">
              <!-- 3.桶图 -->
              <div style="  display: flex;
                          flex-direction: row;
                          align-items: center;
                          justify-content: center;height: 230px; ">

                <div
                  style="width: 30%; height: 200px; transform: scale(0.45); transform-origin: top left; margin-bottom:25px px;margin-left: 10px;">
                  <dv-water-level-pond :config="tong2" style="width: 150px; height: 200px;" />
                </div>
                <!-- 3.学习次数值 -->
                <div style="width: 60%; height: 40px; display: flex;
                          flex-direction: column;
                          align-items: center;
                          justify-content: center;
                          border: 'rgba(255, 255, 255, 0.0)' 1px solid;
                          margin-bottom: 120px;">
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;">
                    <div>
                      <p>学习次数:</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;margin-top: 10px;">
                    <div>
                      <p style="font-size:30px ;">{{this.wisdomStatistics.studyCount}}</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                </div>
              </div>
            </dv-border-box-8>
            <!-- 4.课件数量 -->
            <dv-border-box-8 style="
                margin-top: 15px;
                width: 85%;
                height: 120px;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between; 
              ">
              <!-- 4.桶图 -->
              <div style="  display: flex;
                          flex-direction: row;
                          align-items: center;
                          justify-content: center;height: 230px; ">
                <div
                  style="width: 30%; height: 200px; transform: scale(0.45); transform-origin: top left; margin-bottom:25px px;margin-left: 10px;">
                  <dv-water-level-pond :config="tong" style="width: 150px; height: 200px;" />
                </div>
                <!-- 4.课件数量值 -->
                <div style="width: 60%; height: 40px; display: flex;
                          flex-direction: column;
                          align-items: center;
                          justify-content: center;
                          border: 'rgba(255, 255, 255, 0.0)' 1px solid;
                          margin-bottom: 120px;">
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;">
                    <div>
                      <p>课件数量:</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                  <div
                    style="width: 100%; height:50%;display: flex; flex-direction: column; align-items: center; justify-content: center;  color: #3ce1c5;margin-top: 10px;">
                    <div>
                      <p style="font-size:30px ;">{{this.wisdomStatistics.coursewareCount}}</p>
                    </div>
                    <!-- <div> <dv-digital-flop :config="student1" style="width:100px;height:50px;" /></div> -->
                  </div>
                </div>
              </div>
            </dv-border-box-8>
          </div>
          <!-- 仪表盘 -->
          <div class="hang12" style="position: relative;">
            <!--实时在线用户数量 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: -14px;left: -5px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 150px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  
                    font-family: DIN Alternate Microsoft YaHei Helvetica Neue Helvetica Arial sans-serif;
                    margin-left: 30px;margin-top: 5px;
                  ">
                  平台实时数据</p>
              </dv-border-box-7>
            </div>
            <!--仪表盘 ------------------------------------------------------------------------------------------ -->
            <dv-border-box-8 style="
                              width: 100%;
                              height: 100%;
                              background: linear-gradient(
                                to right,
                                rgba(3, 20, 90, 1),
                                rgba(127, 170, 255, 0.2)
                              );
                              display: flex;
                              flex-direction: column;
                              align-items: center;
                              justify-content: space-between;
                            ">
              <!-- 指标模块 -->
              <div style="width: 100%; height: 100%;">
                <div ref="yibiaopan" style="width: 100%; height: 66%; position: relative; margin-top: 20px;">
                  <!-- 仪表盘图表区域 -->
                </div>

                <div style="width: 95%; height: 34%; position: relative; top: -37px;left: 10px; color: #67e0e3;">
                  <div style="
                      display: grid;
                      grid-template-columns: repeat(4, 1fr);
                      grid-template-rows: repeat(2, 1fr);
                      gap: 10px;
                      width: 100%;
                      height: 100%;
                    ">


                    <!-- 每项模块 -->
                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <!-- <el-icon style="font-size: 26px; color: #3ce1c5;" name="data-line" /> -->
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="user" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">教师数量
                        </p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.teacherCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <!-- <el-icon style="font-size: 26px; color: #3ce1c5;" name="warning" /> -->
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="user" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">学生数量</p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.studentCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <!-- <el-icon style="font-size: 26px; color: #3ce1c5;" name="check" /> -->
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="data-line" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">课程数量
                        </p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.courseCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="mobile" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">教材数量</p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.textbookCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="data-analysis" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">知识点数量</p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.knowledgePointCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="search" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">问答次数</p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.qaCount}}</p>
                    </div>

                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="edit" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">论文数量</p>
                       
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.thesisCount}}</p>
                    </div>

                    <!-- 替换 heart 图标 -->
                    <div
                      style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 14px;">
                      <div style="display: flex; align-items: center; justify-content: center;">
                        <el-icon style="font-size: 26px; color: #3ce1c5;" name="monitor" />
                        <p style="color: #67e0e3; margin: 0 0 5px 8px; text-align: center; white-space: nowrap;">实训场景
                        </p>
                      </div>
                      <p style="color: #67e0e3; text-align: center; font-size: 16px;">{{this.platformReal.implementedScenariosCount}}</p>
                    </div>
                  </div>
                </div>


              </div>

            </dv-border-box-8>

          </div>
          <!-- 组合图表 -->
          <div class="hang13" style="position: relative;">
          <!-- 翻牌 -->
          <div
              style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
              <!-- 上半部分 -->
              <div style="
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-template-rows: repeat(2, 1fr);
              gap: 10px;
              width: 550px;
              height: 130px;
            ">
                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">语音使用次数<br>{{this.platformReal.voiceUsageCount}}</p>
                </dv-border-box-7>

                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">PPT生成数量<br>{{this.platformReal.pptGeneratedCount}}</p>
                </dv-border-box-7>

                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">定时任务执行次数<br>{{this.platformReal.taskCount}}</p>
                </dv-border-box-7>

                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">用户在线率<br>{{(this.platformReal.userOnlineRate)}}%</p>
                </dv-border-box-7>

                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">数据集生成次数<br>{{this.platformReal.datasetGenerationCount}}</p>
                </dv-border-box-7>

                <dv-border-box-7
                  style="display: flex; align-items: center; justify-content: center; font-size: 15px; color: #fff;">
                  <p style="color: #3ce1c5; text-align: center;">数据集数量<br>{{this.platformReal.datasetCount}}</p>
                </dv-border-box-7>
              </div>

              <!-- 下半部分 -->
              <div style="width: 99%; height: 70%;  margin-top: 20px;display: flex; flex-direction: row;
                 align-items: center; justify-content: center;">

                <div
                  style="width: 57%; height: 94%; display: flex; align-items: center; justify-content: right;position: relative;">
                  <dv-border-box-7 style="width: 90%; height: 100%;">
                    <dv-scroll-board :config="huadongliebiao" style="width:100%;height:85%; font-size: small;" />

                    <!--标题1 ------------------------------------------------------------------------------------------ -->
                    <div style="position: relative; width: 500px; height: 100px;position: absolute;top: 287px;left: -7px;
                          display: flex;  align-items: center; ">

                      <dv-border-box-7 style="width: 200px;height: 30px;">
                        <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                          专业知识点热度排行</p>
                      </dv-border-box-7>
                    </div>
                    <!--标题1 ------------------------------------------------------------------------------------------ -->
                  </dv-border-box-7>


                </div>
                <div
                  style="width: 48%; height: 100%; display: flex; align-items: center;flex-direction: column; justify-content: left;">
                  <div
                    style="width: 100%;height: 40%;display: flex; flex-direction: row; align-items: center; justify-content: space-around;margin-top: 10px;">
                    <dv-active-ring-chart v-if="chartVisible" :config="yuanhuan1" style="width:400px;height:400px" />
                    <dv-active-ring-chart v-if="chartVisible" :config="yuanhuan2" style="width:400px;height:400px" />
                  </div>
                  <div style="width: 100%;height: 100%;margin-left: 25px;">
                    <div ref="chart" style="width: 100%; height:100%;margin-bottom: 20px;"></div>
                  </div>
                </div>

              </div>

          </div>
          </div>

          <!-- 柱状图/进度图-->
          <div class="hang14" style="position: relative;">
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: -25px;left: 5px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 180px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  实践实训场景数据</p>
              </dv-border-box-7>
            </div>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <!-- 柱状图 -->
            <dv-border-box-7 style="
                width: 95%;
                height: 50%;
                /* margin-top: 5px; */
                background: linear-gradient(
                  to right,s
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
              ">
              <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: space-evenly">
                <!-- 柱状图纵向 -->
                <div style="width: 100%;top: 102px; right: -168px;  position: absolute; height: 100%; display: flex; flex-direction: row; align-items: center; justify-content: center; line-height: 1;">
                    <!-- <span style="font-size: 11px;">实</span>
                    <span style="font-size: 11px;">训</span> -->
                    <span style="font-size: 11px;">（/人</span>
                    <span style="font-size: 11px;">次）</span>

                </div>
                  <dv-conical-column-chart  v-if="chartVisible" :config="config1" style="width: 430px; height: 220px" />
              </div>


            </dv-border-box-7>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: 255px;left: 5px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 150px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  知识库课程数量</p>
              </dv-border-box-7>
            </div>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <!-- 进度图 -->
            <dv-border-box-7 style="
                margin-top: 20px;
                width: 95%;
                height: 50%;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
              ">
              <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: space-evenly">
                <!-- <el-progress :percentage="50" :stroke-width="15" striped /> -->
                <dv-capsule-chart v-if="chartVisible" :config="config" style="width: 300px; height: 200px" />
              </div>

            </dv-border-box-7>
          </div>
        </div>
        <!-- 第二行 -->
        <div class="hang2">
          <!-- 排序图 -->
          <div class="hang21" style="position: relative;">
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: -14px;left: 12px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 180px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  学生综合活跃度排行</p>
              </dv-border-box-7>
            </div>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <dv-border-box-7 style="
                width: 95%;
                height: 95%;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
              ">
              <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: space-evenly">
                <dv-scroll-ranking-board v-if="chartVisible" :config="paiming" style="width: 550px; height: 350px" />
              </div>

            </dv-border-box-7>
          </div>
          <!-- 玫瑰图 -->
          <div class="hang22" style="position: relative;">
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: -6px;left: 12px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 220px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  学生专业课程知识点掌握数量</p>
              </dv-border-box-7>
            </div>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <dv-border-box-7 style=" width:90%;
                height: 90%;
                ">

              <!-- 雷达图 -->
              <div ref="pieChart" style="width: 380px; height: 400px;margin-top: 10px;"></div>

            </dv-border-box-7>
          </div>
          <!-- 折线图 -->
          <div class="hang23" style="position: relative;">
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <div style="position: relative; width: 500px; height: 100px;position: absolute;top: -10px;left: 16px;
                display: flex;  align-items: center; ">

              <dv-border-box-7 style="width: 180px;height: 30px;">
                <p style="font-size: 14px; font-weight: 80px;  margin-left: 30px;margin-top: 5px;">
                  学生作业周完成情况</p>
              </dv-border-box-7>
            </div>
            <!--标题1 ------------------------------------------------------------------------------------------ -->
            <dv-border-box-5 style="
                width: 95%;
                height: 95%;
                background: linear-gradient(
                  to right,
                  rgba(3, 20, 90, 1),
                  rgba(127, 170, 255, 0.2)
                );
              ">
              <!-- 折线图容器 -->
              <div id="lineChart"
                style="margin-top: 30px; margin-left: 20px;   width: 90%; height: 80%; display: flex; align-items: center; justify-content: space-evenly">
              </div>
            </dv-border-box-5>
          </div>
        </div>
      </div>
    </dv-border-box-11>

  </div>
</template>

<script>
import * as echarts from "echarts";
import { getToken } from "@/utils/auth";
import { mapActions } from "vuex";
export default {
  name: "dataDaPing",
  data() {
    return {
      chartVisible: true, // 控制柱状图是否显示0
      wisdomStatistics: {}, // 智慧学堂统计（单条最新）1
      platformReal: {}, // 平台实时数据（单条最新）0
      knowledgeRankingList: [], // 专业知识点热度排行（多条，按rank升序）1
      dailyTrendList: [], // 每日趋势（多条，周一到周日）1
      practicalTrainingList: [], // 实践实训（多条，不同场景）1
      courseKnowledgeList: [], // 知识库课程数量（多条，不同学科）1
      studentRankingList: [], //学生活跃度排行（多条，按rank升序）1
      studentAcquiredList: [], // 学生专业课程知识点掌握数量（多条）1
      weeklyCompletionList: [], //学生作业周完成情况（多条，周一到周日）1
        // 学习进度电池
      dianchi: {
        value: 3200,
        borderWidth: 10,
        borderRadius: 10,
        borderGap: 5,
        textColor: 'black',
        formatter: '{value} h',
      },
      //智能问答次数
      tong1: {
        data: [60],
        formatter: '{value}%',
        shape: 'round',
      },
      // 学习次数
      tong2: {
        data: [82],
        formatter: '{value}%',
        shape: 'roundRect',
        colors: ["#00adff"]
      },
       // 课件数量
      tong: {
        data: [25],
        formatter: '{value}%',
        colors: [
          "#ffdb5c"  // 亮橙黄 - 点缀色
        ]
      },
      // 第一个圆环
      yuanhuan1: {
        data: [
          {
            name: "AI服务调用成功率",
            value: 100
          }
        ],
        lineWidth: 10,
        showText: true, // ✅ 一定要启用文字展示
        digitalFlopStyle: {
          fontSize: 12,   // ✅ 控制中间文字大小（旧版是 size，推荐写 fontSize）

        },
        color: ['#ffdb5c']
      },
      // 第二个圆环
      yuanhuan2: {
        data: [
          {
            name: "教学资源使用率",
            value: 20
          }
        ],
        lineWidth: 10,
        showText: true, // ✅ 一定要启用文字展示
        digitalFlopStyle: {
          fontSize: 12,   // ✅ 控制中间文字大小（旧版是 size，推荐写 fontSize）
          color: '#fff'
        },
        color: ['#ffdb5c']
      },
      // 专业热度滑动列表
      huadongliebiao: {
        header: ['专业', '课程', '数量'],
        data: [
          ['计算机科学与技术', '数据结构与算法', '120'],
          ['软件工程', '软件开发方法论', '95'],
          ['人工智能', '机器学习', '150'],
          ['物理学', '量子力学', '110'],
          ['电子信息工程', '数字电路', '130'],
          ['机械工程', '力学基础', '100'],
          ['数学与应用数学', '数学建模', '80'],
          ['化学工程', '有机化学', '75'],
          ['生物技术', '生物化学', '90'],
          ['环境科学', '环境污染控制', '85'],
          ['经济学', '宏观经济学', '65'],
          ['管理学', '企业管理', '95'],
          ['心理学', '认知心理学', '120'],
          ['法学', '刑法学', '110'],
          ['历史学', '世界历史', '105'],
        ],
        headerBGC: 'rgba(255,255,255,0.2)',  // 表头背景色：透明白
        oddRowBGC: 'rgba(255,255,255,0.2)',  // 奇数行背景色：更浅透明白
        evenRowBGC: '#03145a',               // 偶数行背景色：透明蓝
        // index: true,                          // 显示索引
        columnWidth: [120, 120, 80],         // 可选：列宽
        align: ['center', 'center', 'center'], // 可选：对齐方式
        tableName: '专业知识点热度排行表',  // 新增：表名
        fontSize: '14px'  // 新增：字体大小设置
      },
       // 柱状图
       config1: {
        data: [
          {
            name: "社保大厅",
            value: 867,
          },
          {
            name: "城市摊贩",
            value: 523,
          },
          {
            name: "城市管理",
            value: 980,
          },
          {
            name: "国际法院",
            value: 750,
          },
          {
            name: "主题餐厅",
            value: 660,
          },
          
        ],
        colors: ["#e062ae", "#fb7293", "#e690d1", "#32c5e9", "#96bfff"],
        unit: "单位",
        showValue: true
      },
      // 进度图
      config: {
          data: [
            { value: 55, name: "哲学" },
            { value: 120, name: "经济学" },
            { value: 71, name: "法学" },
            { value: 66, name: "文学" },
            { value: 80, name: "管理学" },
            { value: 35, name: "工学" },
            { value: 15, name: "理学" },
            { value: 15, name: "农学" },
          ],
      },
     
// 第二行排行榜
      paiming: {
        data: [
        { name: "祝德成（计算机学院·软件工程·2021级1班）", value: 55 },
        { name: "王怀煜（经济管理学院·金融学·2021级2班）", value: 120 },
        { name: "齐城（外国语学院·英语·2021级3班）", value: 78 },
        { name: "党安东（信息工程学院·通信工程·2021级1班）", value: 66 },
        { name: "刘超群（机电学院·机械设计制造·2021级2班）", value: 80 },
        { name: "姜登宇（教育科学学院·心理学·2021级3班）", value: 45 },
        { name: "张浩（艺术学院·视觉传达设计·2021级1班）", value: 29 },
        { name: "张晗（计算机学院·人工智能·2021级2班）", value: 98 },
        { name: "王敬瑶（法学院·法学·2021级3班）", value: 102 },
        { name: "宋佳（医学院·临床医学·2021级1班）", value: 88 }
      ]
      }
    };
  },
  components: {},
  mounted() {
    this.initialize();// 初始化       
  },
  methods: {
    // 顺序异步方法
    async initialize() {
      try {
        await this.fetchData(); // 1.获取后端数据
        await this.initGaugeChart(); //仪表盘
        await this.initChart()  // 小折线图
        await this.initPieChart(); // 玫瑰图
        await this.drawChart() // 大折线图
      } catch (error) {
        console.error("初始化过程中发生错误:", error);
      }
    },
    // 向后端获取数据
    async fetchData() {
      try {
        const url = process.env.VUE_APP_BASE_API + "/test/dataDaping/getDataDapingVo";
        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: "Bearer " + getToken(),
            "Content-Type": "application/json",
          },
        });
        const res = await response.json();
        console.log("获取大屏解析到的数据：", res);
          // 1.1智慧学堂学习情况
            this.wisdomStatistics = res.wisdomStatistics;
            const max = 100000; // 最大值设为10万
            this.dianchi = { ...this.dianchi,value:  this.wisdomStatistics.totalDuration};// 4个桶图
            this.tong1 = {...this.tong1,data: [Math.round((this.wisdomStatistics.intelligentCount / max) * 100)]};
            this.tong2 = { ...this.tong2,data: [Math.round((this.wisdomStatistics.studyCount / max) * 100)]};
            this.tong = {...this.tong,data: [Math.round((this.wisdomStatistics.coursewareCount / max) * 100)]};
          //  1.2平台实时数据（单条最新）
            this.platformReal = res.platformReal;
            this.yuanhuan1 = {...this.yuanhuan1,data: [{name: 'AI服务调用成功率',value:(this.platformReal.aiRate) }]};// 2个圆环
            this.yuanhuan2 = {...this.yuanhuan2,data: [{name: '教学资源使用率',value:(this.platformReal.teachingRate)}]};
            this.chartVisible = false;this.$nextTick(() => {this.chartVisible = true;}); // 重新渲染图表
          //  1.3专业知识点热度排行（多条，按counts升序）
            this.knowledgeRankingList = res.knowledgeRankingList; //10个指标模块
            this.huadongliebiao = {...this.huadongliebiao,data: this.knowledgeRankingList.map(item => [item.major, item.course,  item.count])};
          //  1.3专业知识点热度排行（多条，按counts升序）
            this.dailyTrendList = res.dailyTrendList;//小折线图
          //  1.4智慧实训情况
            this.practicalTrainingList = res.practicalTrainingList;
            this.config1.data = this.practicalTrainingList.map(item => ({ name: item.scenarioName, value: item.trainingParticipantsCount }));
            this.chartVisible = false;this.$nextTick(() => {this.chartVisible = true;}); // 重新渲染图表
          //  1.4知识库课程数量
            this.courseKnowledgeList = res.courseKnowledgeList;
            this.config.data = this.courseKnowledgeList.map(item => ({ name: item.subject, value: item.count }));
            this.chartVisible = false;this.$nextTick(() => {this.chartVisible = true;}); // 重新渲染图表
            //2.1学生活跃度排行（多条，按counts升序）
            this.studentRankingList = res.studentRankingList;
            this.paiming= { ...this.paiming,data: this.studentRankingList.map(item => ({ name:`${item.studentName}（${item.institute}-${item.major}-${item.studentClass}）`,value: -Number(item.rank)}))};
            this.chartVisible = false;this.$nextTick(() => {this.chartVisible = true;}); // 重新渲染图表
            //2.2学生掌握知识点数量（多条，按counts升序）
            this.studentAcquiredList = res.studentAcquiredList;

            //2.3 学生每周完成情况（多条，按counts升序）
            this.weeklyCompletionList = res.weeklyCompletionList;

            console.log('AI成功率：', this.wisdomStatistics.aiRate);//这里是没找到
            console.log('教学资源使用率：', this.wisdomStatistics.teachingRate);//这里是没找到
      } catch (error) {
        console.error("没有返回数据", error);
        throw error;
      }
    },
    // 小折线图
    async initChart() {
      this.chart = echarts.init(this.$refs.chart)

      const option = {
        title: {
          text: '每日签到趋势',
          left: 'center',
          top: '10px',
          textStyle: {
            fontSize: 18,
            fontWeight: 'thin',
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function (value) {
              return value >= 1000 ? (value / 1000) + 'k' : value;
            }
          }
        }
        ,
        series: [
          {
            data: this.dailyTrendList.map(item => item.checkInCount),
            type: 'line',
            smooth: true,
            show: true,
            label: {
          show: true,           // ✅ 启用标签
          position: 'top',      // ✅ 显示在折线点上方
          color: '#3BA272',        // ✅ 文字颜色
          fontSize: 10          // ✅ 字号
        },
        itemStyle: {
          // color: '#3BA272'      // 可选：设置折线颜色
        }
          }
        ]
      }

      this.chart.setOption(option)
    },
    // 玫瑰图
    async initPieChart() {
      // 获取 DOM 容器
      const chartDom = this.$refs.pieChart;
      if (!chartDom) {
        console.error("Failed to find the element for rendering the rose chart.");
        return;
      }

      const myChart = echarts.init(chartDom);

      // 配置项
      const option = {
        title: {
          text: '学生知识点掌握数量', // 表名
          left: 'center',
          bottom: 30,
          textStyle: {
            color: '#fff',
            fontSize: 18,
            fontWeight: 'thin',
          }
        },
        legend: {
          show: false,
          textStyle: {
            color: '#7ce7fd'
          }
        },
        toolbox: {
          show: true
        },
        series: [
  {
    name: '知识点掌握数量',
    type: 'pie',
    roseType: 'radius',
    radius: [20, 120],
    center: ['50%', '50%'],
    label: { show: true, color: '#fff' },
    labelLine: { show: true },
    itemStyle: {
      color: (params) => {
        const colors = [
          '#f44336', '#ff9800', '#ffeb3b', '#8bc34a', '#00bcd4',
          '#2196f3', '#3f51b5', '#9c27b0', '#e91e63', '#795548'
        ];
        const to = colors[params.dataIndex % colors.length];
        const from = '#ccf2ff';
        return new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0, color: from },
          { offset: 1, color: to }
        ]);
      },
      borderWidth: 5,
      borderColor: '#03145a',
      borderRadius: 5,
      shadowBlur: 5,
      shadowColor: 'rgba(0, 0, 0, 0.2)'
    },
    data: this.studentAcquiredList.map(item => ({
      name: item.subject,
      value: item.count
    }))
  }
]

      };

      myChart.setOption(option);
    } ,
    // 仪表盘
    async initGaugeChart() {
      // 获取 DOM 容器
      const chartDom = this.$refs.yibiaopan;
      const tatal= this.platformReal.studentCount+this.platformReal.teacherCount
      if (!chartDom) {
        console.error("Failed to find the element for rendering the gauge chart.");
        return;
      }
      const myChart = echarts.init(chartDom);

      // 配置项，数值写死
      const option = {
        backgroundColor: 'rgba(0, 0, 0, 0)', // 背景透明
        tooltip: {
          formatter: "{a} <br/>{b} : {c}%",
        },
        series: [
          {
            name: "Humidity",
            type: "gauge",
            min: 0,
            max: 100000, // 自定义取值范围
            splitNumber: 10, // 分成几个刻度段
            radius: '90%',   // 仪表盘大小

            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, '#67e0e3'],
                  [0.7, '#37a2da'],
                  [1, '#fd666d']
                ]
              }
            },
            axisTick: {
              length: 8,
              lineStyle: {
                color: 'auto'
              }
            },
            splitLine: {
              length: 15,
              lineStyle: {
                color: '#fff',
                width: 2
              }
            },
            axisLabel: {
              color: '#fff',
              fontSize: 12
            },
            pointer: {
              width: 5,
              itemStyle: {
                color: '#fff'
              }
            },
            title: {
              offsetCenter: [0, '-30%'],
              color: '#3ce1c5',
              fontSize: 14
            },
            detail: {
              valueAnimation: true,
              formatter: '{value}',
              fontSize: 20,
              color: '#fff'
            },
            data: [
              {
                value: tatal, // 固定值，可改为动态
                name: "当前在线用户量",
               
              }
            ]
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表
      myChart.setOption(option);
    },
    // 大折线图
    async drawChart() {
      const myChart = echarts.init(document.getElementById('lineChart'))

      const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

      const homeworkData = {
        '按时完成作业数量': [],
        '迟交但完成作业数量': [],
        '未完成作业数量': []
      };

      const courseMaterialData = {
        '发布作业数量': [],
        '发布课件量': []
      };

      // 按一周顺序整理数据
      weekDays.forEach(day => {
        const dayObj = this.weeklyCompletionList.find(item => item.weekDay === day) || {};

        homeworkData['按时完成作业数量'].push(dayObj.homeworkCount || 0);
        homeworkData['迟交但完成作业数量'].push(dayObj.lateHomeworkCount || 0);
        homeworkData['未完成作业数量'].push(dayObj.incompleteHomeworkCount || 0);

        courseMaterialData['发布作业数量'].push(dayObj.putHomework || 0);
        courseMaterialData['发布课件量'].push(dayObj.putPpt || 0);
      });
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['完成作业数量', '发布数量']
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']  // 一周的日期
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
  {
    name: '按时完成作业',
    type: 'line',
    stack: '完成情况',
    label: { show: true, position: 'top' },
    areaStyle: {},
    emphasis: { focus: 'series' },
    data: homeworkData['按时完成作业数量']
  },
  {
    name: '迟交但完成',
    type: 'line',
    stack: '完成情况',
    label: { show: true, position: 'top' },
    areaStyle: {},
    emphasis: { focus: 'series' },
    data: homeworkData['迟交但完成作业数量']
  },
  {
    name: '未完成作业',
    type: 'line',
    stack: '完成情况',
    label: { show: true, position: 'top' },
    areaStyle: {},
    emphasis: { focus: 'series' },
    data: homeworkData['未完成作业数量']
  },
  {
    name: '发布作业数量',
    type: 'bar',
    stack: '发布数量',
    label: { show: true, position: 'insideTop' },
    data: courseMaterialData['发布作业数量'],
    itemStyle: { color: '#ff9800' }
  },
  {
    name: '发布课件量',
    type: 'bar',
    stack: '发布数量',
    label: { show: true, position: 'insideTop' },
    data: courseMaterialData['发布课件量'],
    itemStyle: { color: '#8bc34a' }
  }
]

      };

      myChart.setOption(option);
    }

  }
};
</script>

<style scoped>
html,
body {
  background-color: #03145a;
}

.dataDaPing {
  width: 100vw;
  height: 100vh;
  background-color: #03145a;
  color: aliceblue;
  /* background-image: url('/src/assets/images/VCG211329268380.jpg'); */
  background-size: cover;
  /* 保持比例铺满容器 */
  background-position: center;
  /* 居中显示 */
  background-repeat: no-repeat;
  /* 不重复 */
}

.biankuang {
  width: 97vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 避免滚动条 */
}

.bigdiv {
  width: 100%;
  height: 90%;

  /* border: red 1px solid; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.hang1 {
  width: 95%;
  height: 55%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.hang2 {
  width: 95%;
  height: 45%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.hang11 {
  width: 16%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.hang12 {
  width: 28%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

 

}

.hang13 {
  width: 34%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.hang14 {
  width: 22%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.hang21 {
  width: 37%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.hang22 {
  width: 25%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.hang23 {
  width: 37%;
  height: 100%;
  /* border: red 1px solid; */
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>