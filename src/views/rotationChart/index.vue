<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="轮播图名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入轮播图名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="rotationChartList">
      <el-table-column label="学校" prop="school" min-width="100" />
      <el-table-column label="轮播图ID" prop="id" min-width="100" />
      <el-table-column label="轮播图名称" prop="title" min-width="100" />
      <el-table-column label="轮播图描述" prop="description" min-width="100" />
      <el-table-column label="轮播图" prop="presentationFileList" min-width="100">
        <template slot-scope="scope">
          <el-upload list-type="picture-card" :action="uploadUrl" :headers="headers"
            :file-list="scope.row.presentationFileList" disabled class='disabledImg'
            :on-preview="handlePictureCardPreview">
            <i class="el-icon-plus"></i>
          </el-upload>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="dialogOpen" width="1000px" append-to-body :before-close="handleClose">
      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-position="left" label-width="100px">
        <el-form-item label="学校" prop="school">
          <el-input v-model="form.school" disabled />
        </el-form-item>
        <el-form-item label="轮播图名称" prop="title">
          <el-input v-model="form.title" type="textarea" autosize placeholder="请输入轮播图名称" maxlength="100"
            show-word-limit />
        </el-form-item>
        <el-form-item label="轮播图描述" prop="description">
          <el-input v-model="form.description" type="textarea" autosize placeholder="请输入轮播图描述" maxlength="255"
            show-word-limit />
        </el-form-item>
        <el-form-item label="轮播图" prop="keyword">
          <el-upload list-type="picture-card" :action="uploadUrl" :headers="headers" :file-list="fileList"
            :on-success="handleUploadSuccess" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
            :limit="1" :class="{ 'disabledImg': fileList.length > 0 ? true : false }">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import {
  getRotationChartList,
  addRotationChart,
  updateRotationChart,
  delRotationChart,
  getSchoolName
} from "@/api/rotationChart/rotationChart.js";
import { getToken } from "@/utils/auth";
export default {
  name: "RotationChart",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      rotationChartList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      title: '创建轮播图',
      form: {},
      rules: {
        title: [
          { required: true, message: '请输入轮播图名称', trigger: ['blur', 'change'] },
        ],
        description: [
          { required: true, message: '请输入轮播图描述', trigger: ['blur', 'change'] },
        ],
      },
      dialogOpen: false,
      btnLoading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/test/circularbannerconfig/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      dialogVisible: false,
      dialogImageUrl: '',
      school: ''
    };
  },
  created() {
    this.getList();
    this.getSchoolName()
  },
  activated() {
    this.getList();
    this.getSchoolName()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getRotationChartList(this.queryParams).then((response) => {
        this.rotationChartList = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    getSchoolName() {
      getSchoolName().then(res => {
        this.school = res.msg
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.form.school = this.school
      this.dialogOpen = true;
      this.title = '创建轮播图';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = { ...row }
      this.fileList = row.presentationFileList
      this.dialogOpen = true;
      this.title = '修改轮播图';
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delRotationChart(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    handleSubmit() {
      this.btnLoading = true
      if (this.validateForm()) {
        if (this.form.fileId && this.form.fileId != '') {
          if (this.form.id) {
            updateRotationChart(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.handleClose()
                this.btnLoading = false
              } else {
                this.btnLoading = false
              }
            }).catch(() => {
              this.btnLoading = false
            })
          } else {
            addRotationChart(this.form).then(res => {
              if (res.code === 200) {
                this.$message.success('创建成功')
                this.handleClose()
                this.btnLoading = false
              } else {
                this.btnLoading = false
              }
            }).catch(() => {
              this.btnLoading = false
            })
          }
        } else {
          this.$message.warning('请上传轮播图图片')
          this.btnLoading = false
        }
      } else {
        this.btnLoading = false
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      this.dialogOpen = false
      this.getList()
      this.resetForm("form");
      this.fileList = []
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleUploadSuccess(res, file) {
      this.form.fileId = res.fileId
      this.form.fileName = res.fileName
      this.fileList.push(file)
    },
    handleRemove(file, fileList) {
      this.form.fileId = ''
      this.form.fileName = ''
      this.fileList = []
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    }
  },
};
</script>
<style lang="scss">
.disabledImg {
  .el-upload--picture-card {
    display: none;
  }
}
</style>



