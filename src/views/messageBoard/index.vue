<template>
  <div class="app-container ck-container">
    <div class="ck-content">
      <el-input v-model="message" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" maxlength="2000"
        placeholder="请输入留言" show-word-limit />
      <el-button style="float: right" type="primary" size="mini" round icon="el-icon-s-promotion" @click="handleSend" />
    </div>
    <div class="ck-message">
      <div class="ck-message-item" v-for="(item, index) in list" :key="index">
        <div class="ck-message-item-title">{{ item.nickName }}</div>
        <div class="ck-message-item-content">{{ item.message }}</div>
        <div class="ck-message-item-time">{{ item.createTime }}</div>
        <div class="ck-message-item-btn">
          <el-button type="text" size="mini" @click="handleResponse(item,'reply','form')">回复</el-button>
          <el-button type="text" size="mini" v-if="item.deleteFlag"
            @click="handleDel(item,'reply','form')">删除</el-button>
        </div>
        <div class="ck-response-item" v-for="(items, index) in item.sMsgReplyList" :key="index">
          <span class="ck-response-item-title">{{ items.replyUserName }}</span>
          <span class="ck-response-item-content">回复</span>
          <span class="ck-response-item-title">{{ items.lastReplyUname }}</span>
          <span class="ck-response-item-content">{{ items.replyMsg }}</span>
          <span class="ck-response-item-time">{{ items.createTime }}</span>
          <el-button type="text" size="mini" @click="handleResponse(items,'replyreply','form')">回复</el-button>
          <el-button type="text" size="mini" v-if="items.deleteFlag"
            @click="handleDel(items,'replyreply','form')">删除</el-button>
        </div>
        <div class="ck-response-item-btn">
          <el-button type="text" size="mini" @click="handleMore(item)">更多</el-button>
        </div>
      </div>
      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <el-dialog title="回复" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="回复内容">
          <el-input v-model="form.replyMsg" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" maxlength="2000" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" class="dialogClass" :visible.sync="dialogResponseVisible">
      <div>
        <div v-for="(item, index) in infoList" :key="index">
          <div class="ck-message-item-title">{{ item.nickName }}</div>
          <div class="ck-message-item-content">{{ item.message }}</div>
          <div class="ck-message-item-time">{{ item.createTime }}</div>
          <div class="ck-message-item-btn">
            <el-button type="text" size="mini" @click="handleResponse(item,'reply','dialog')">回复</el-button>
            <el-button type="text" size="mini" v-if="item.deleteFlag"
              @click="handleDel(item,'reply','dialog')">删除</el-button>
          </div>
          <div class="ck-response-item" v-for="(items, index) in item.sMsgReplyList" :key="index">
            <span class="ck-response-item-title">{{ items.replyUserName }}</span>
            <span class="ck-response-item-content">回复</span>
            <span class="ck-response-item-title">{{ items.lastReplyUname }}</span>
            <span class="ck-response-item-content">{{ items.replyMsg }}</span>
            <span class="ck-response-item-time">{{ items.createTime }}</span>
            <el-button type="text" size="mini" @click="handleResponse(items,'replyreply','dialog')">回复</el-button>
            <el-button type="text" size="mini" v-if="items.deleteFlag"
              @click="handleDel(items,'replyreply','dialog')">删除</el-button>

          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getMessageList, addMessage, addReply, getMessage, delMessage, delReply } from '@/api/messageBoard/messageBoard.js'
export default {
  name: "MessageBoard",
  data() {
    return {
      message: '',
      list: [],
      infoList: [],
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dialogFormVisible: false,
      form: {
        replyMsg: ''
      },
      dialogResponseVisible: false,
      dialogFlag: false
    };
  },
  created() {
    this.getList()
  },
  activated() { },
  methods: {
    getList() {
      getMessageList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.list = res.rows
          this.total = res.total
        }
      })
    },
    handleSend() {
      addMessage({ message: this.message }).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '留言成功',
            type: 'success'
          });
          this.message = ''
          this.getList()
        }
      })

    },
    handleResponse(item, type, dialog) {
      this.dialogFormVisible = true
      this.dialogFlag = dialog == "dialog" ? true : false
      if (type === 'reply') {
        this.form.msgId = item.msgId
        this.form.lastReplyUid = item.msgUserId
        this.form.lastReplyUname = item.nickName
      } else {
        this.form.msgId = item.msgId
        this.form.lastReplyId = item.replyId
        this.form.lastReplyUid = item.replyUserid
        this.form.lastReplyUname = item.replyUserName
      }
    },
    handleClose() {
      this.dialogFormVisible = false
      this.form = {
        replyMsg: ''
      }
    },
    handleSubmit() {
      addReply(this.form).then(res => {
        if (res.code == 200) {
          this.$message({
            message: '回复成功',
            type: 'success'
          });
          if (this.dialogFlag) {
            this.handleMore(this.form)
            this.handleClose()
            this.getList()
          } else {
            this.handleClose()
            this.getList()
          }
        }
      })
    },
    handleMore(item) {
      this.dialogResponseVisible = true
      getMessage(item.msgId).then(res => {
        if (res.code == 200) {
          this.infoList = res.data
        }
      })
    },
    handleDel(row, type, dialog) {
      this.dialogFlag = dialog == "dialog" ? true : false
      this.$modal
        .confirm("是否确认删除此留言？")
        .then(() => {
          if (type === 'reply') {
            delMessage(row.msgId).then(() => {
              this.dialogResponseVisible = false
              this.getList()
              this.$modal.msgSuccess("删除成功");
            })
          } else {
            if (this.dialogFlag) {
              delReply(row.replyId).then(() => {
                this.handleMore(row)
                this.getList()
                this.$modal.msgSuccess("删除成功");
              })
            } else {
              delReply(row.replyId).then(() => {
                this.getList()
                this.$modal.msgSuccess("删除成功");
              })
            }
          }
        })
    }
  },
};
</script>
<style lang="scss" scoped>
.ck-container {
  padding: 0;
  height: calc(100vh - 84px);
  background: #dbe9f740;
}
.ck-content {
  width: 100%;
  padding: 20px 10%;
  height: 160px;
  border-bottom: 1px solid #e8e8e8;
}
.ck-message {
  width: 100%;
  padding: 20px 10%;
  height: calc(100vh - 250px);
  overflow-y: auto;
}
.ck-message-item {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
}
.ck-message-item-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
.ck-message-item-content {
  font-size: 14px;
  color: #666;
  padding: 10px;
}
.ck-message-item-time {
  font-size: 12px;
  color: #666;
  padding: 0 10px;
}
.ck-message-item-btn {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.ck-response-item {
  width: 100%;
  padding: 10px;
}
.ck-response-item-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
}
.ck-response-item-content {
  font-size: 12px;
  color: #666;
  padding: 10px;
}
.ck-response-item-time {
  font-size: 10px;
  color: #666;
  padding: 0 10px;
}
.ck-response-item-btn {
  width: 100%;
  display: flex;
  justify-content: center;
}
.dialogClass .el-dialog__body {
  padding: 10px 20px;
}
.pagination-container {
  height: 50px;
}
</style>
