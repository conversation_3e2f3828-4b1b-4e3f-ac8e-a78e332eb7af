<template>
    <div class="text-to-speech">
        <el-row :gutter="30">
            <el-col :span="16">
            <el-input type="textarea" v-model="text" :maxlength="maxLength" show-word-limit rows="8" placeholder="请输入文本"></el-input>
            <div class="char-count">您还可以输入 {{ maxLength - text.length }} 字</div>
            </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <div class="block">
              <span class="demonstration">语速</span>
              <el-slider v-model="speed" :max="10" show-input></el-slider>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
              <div v-show="showVoice" class="block">
                <span class="demonstration">语调</span>
                <el-slider v-model="pitch" :max="10" show-input></el-slider>
              </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
              <div v-show="showVoice" class="block">
                <span class="demonstration">音量</span>
                <el-slider v-model="volume" :max="10" show-input></el-slider>
              </div>
            </el-col>
        </el-row>

        <el-row>
          <el-col :span="20">
            <div class="controls">
              <h3>发言人</h3>
                <!-- 女声 -->
              <el-row>
                  <h3>女声</h3>
                  <el-radio-group v-model="per" @change="handleRadioChange">
                    <el-radio-button v-for="dict in femaleVoices" :key="dict.value" :label="dict.value">
                      {{ dict.label }}
                    </el-radio-button>
                  </el-radio-group>
              </el-row>
              <!-- 男声 -->
              <el-row>
                  <h3>男声</h3>
                  <el-radio-group v-model="per" @change="handleRadioChange">
                    <el-radio-button v-for="dict in maleVoices" :key="dict.value" :label="dict.value">
                      {{ dict.label }}
                    </el-radio-button>
                  </el-radio-group>
              </el-row>

              <el-row type="flex" justify="start" align="middle" style="margin-top: 30px;">
                <el-col>
                  <el-button type="primary" @click="synthesizeVoice">合成</el-button>
                </el-col>
                <el-col style="margin-left: 20px;">
                  <audio v-if="showAudio" :src="audioSrc" controls :playsinline="true" ></audio>
                </el-col>
              </el-row>

              <el-row style="margin-top: 30px;">
                <el-button type="primary" @click="saveVoiceRole">保存</el-button>
                <el-switch  style="margin-left: 30px;"
                            v-model="flag"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            active-text="自动语音播放"></el-switch>
              </el-row>
            </div>
          </el-col>
        </el-row>
    </div>
  </template>

  <script>
  import axios from "axios";
  import qs from "qs";
    import {
    getBaiDuToken,
  } from "@/api/explorationCenter/experience.js";
  import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
  import {
    saveUserVoiceRole,
    getUserVoiceRole,
    DBTTS,
    getSystemTTSChoose,
  } from "@/api/system/voiceRole.js";

  const ttsRecorder = new TtsRecorder();
  export default {
    dicts: ['sys_voice_role','sys_xf_voice_role','user_voice_choose','sys_doubao_voice_role'],
    data() {
      return {
        text: '“欢迎各位同学。我是AI·CAI大模型，很高兴能在这里陪伴大家一起学习。',
        maxLength: 60,
        selectedVoice: '',
        version: '标准',
        speed: 5,// 语速
        pitch: 5, // 音调
        volume: 5, // 音量
        per : 1,
        token : '',
        audioSrc: '',
        flag: false, // 全局语音标志
        isloaded: true,// 加载完成
        saveParams: {
          bdVoiceRoleId : '',
          voiceSpeed: '',
          voicePitch: '',
          voiceVolume: '',
          userPlayflag: '',
          xfVoiceRoleId: "",
          douBaoVoiceRoleId: "",
        },
        userVoiceChoose: "",
        showVoice: true,
        showAudio: true,
      };
    },
    created() {
      this.Token();
      this.getVoiceRole();
      this.getSystemTTSChoose();
    },
    computed: {
    // 过滤出女声
    femaleVoices() {
      for (const item of this.dict.type.user_voice_choose) {
        if (item.label === "baidu") {
          return this.dict.type.sys_voice_role.filter(dict => dict.raw.dictSort === 0);
        }
        if (item.label === "xfSpark") {
          return this.dict.type.sys_xf_voice_role.filter(dict => dict.raw.dictSort === 0);
        }
        if(item.label === "doubao"){
          return this.dict.type.sys_doubao_voice_role.filter(dict => dict.raw.dictSort === 0);
        }
      }
    },
    // 过滤出男声
    maleVoices() {
      for (const item of this.dict.type.user_voice_choose) {
        if (item.label === "baidu") {
          return this.dict.type.sys_voice_role.filter(dict => dict.raw.dictSort === 1);
        }
        if (item.label === "xfSpark") {
          return this.dict.type.sys_xf_voice_role.filter(dict => dict.raw.dictSort === 1);
        }
        if(item.label === "doubao"){
          return this.dict.type.sys_doubao_voice_role.filter(dict => dict.raw.dictSort === 1);
        }
      }
    }
  },
    methods: {
      // 获取系统语音合成选择
      getSystemTTSChoose(){
        getSystemTTSChoose().then(res => {
          this.userVoiceChoose = res.msg;
          // 控制展示 语速 语调
          if(this.userVoiceChoose === "doubao"){
            this.showVoice = false;
          }
          // 控制展示 音频
          if(this.userVoiceChoose === "xfSpark"){
            this.showAudio = false;
          }
        })

      },
      getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          // 讯飞展示处理
          if(this.userVoiceChoose === "xfSpark"){
            this.speed = res.data.voiceSpeed / 10;
            this.pitch = res.data.voicePitch / 10;
            this.volume = res.data.voiceVolume / 10;
          }
          if(this.userVoiceChoose === "doubao"){
            this.speed = res.data.voiceSpeed * 5;
          }
        })
      },
      // 统一的语音合成接口
      synthesizeVoice() {
        switch(this.userVoiceChoose) {
          case "baidu": // 百度语音
            this.textToAudio();
            break;
          case "xfSpark": // 讯飞语音
            this.play();
            break;
          case "doubao": // 豆包语音
            this.synthesizeSpeech();
            break;
          default:
            this.$message.warning("未知的语音合成服务");
            break;
        }
      },
      handleRadioChange(value) {
        // 根据选中的 value 找到对应的 label
        const selectedDict = this.femaleVoices.find((dict) => dict.value === value) || this.maleVoices.find((dict) => dict.value === value);
        this.selectedLabel = selectedDict ? selectedDict.label : "";
      },
      saveVoiceRole(){
        // this.saveParams.voiceRoleId = this.per;
        this.saveParams.voiceSpeed = this.speed;
        this.saveParams.voicePitch = this.pitch;
        this.saveParams.voiceVolume = this.volume;
        this.saveParams.userPlayflag = this.flag;
        this.saveParams.bdVoiceRoleId = this.dict.type.sys_voice_role.filter(dict => dict.label === this.selectedLabel)[0].value
        this.saveParams.xfVoiceRoleId = this.dict.type.sys_xf_voice_role.filter(dict => dict.label === this.selectedLabel)[0].value
        this.saveParams.douBaoVoiceRoleId = this.dict.type.sys_doubao_voice_role.filter(dict => dict.label === this.selectedLabel)[0].value
        saveUserVoiceRole(this.saveParams).then(res => {
            if(res.code == 200){
                this.$message.success("保存成功")
            }else{
                this.$message.error("保存失败")
            }
        })
      },
      Token(){
        getBaiDuToken().then((result) => {
            // console.log(result.token)
            this.token = result.token;
            // console.log(this.token)
        }).catch((err) => {
            console.log(err);
        });
      },
      async textToAudio(){
        const option = {
            tex: this.text,
            tok: this.token,
            cuid: `${Math.floor(Math.random() * 1000000)}`,
            ctp: "1",
            lan: "zh",
            per: this.per,
            spd: this.speed,
            pit: this.pitch,
            vol: this.volume
        };
        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            responseType: "blob",
        });
        try{
            this.audioSrc = URL.createObjectURL(res.data);
            this.$message.success("合成成功");
        }catch(err){
            this.$message.error("合成失败");
        }
      },
      play() {
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: this.text,
        // 角色
         voiceName: this.per,
        // 语速
        speed: this.speed*10,
        // 音量
        voice: this.volume*10,
        // 音调
        pitch: this.pitch*10
      });
      ttsRecorder.start();
    },

      pause() {
        ttsRecorder.stop();
      },
      // 豆包接口语音合成
      synthesizeSpeech() {
        const params = {
          content: this.text,
          voiceType: this.per,
          speed: this.speed * 0.2,

        };

        DBTTS(params).then((response) => {
          if (response.data && response.data.audioData) {
            // 获取base64音频数据
            const audioBase64 = response.data.audioData;
            // 解码Base64为二进制
            const binaryString = window.atob(audioBase64);
            const len = binaryString.length;
            const bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            // 创建Blob对象
            const audioBlob = new Blob([bytes], { type: 'audio/mp3' });
            // 创建URL
            this.audioSrc = URL.createObjectURL(audioBlob);
            this.$message.success("合成成功");
          } else {
            this.$message.error("语音合成失败：未获取到音频数据");
          }
        }).catch(error => {
          this.$message.error(`语音合成请求失败: ${error.message}`);
          console.error("语音合成请求失败:", error);
        });
      },

    },
  };
  </script>

  <style scoped>
  .text-to-speech {
    max-width: 1000px;
    margin: 60px auto;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .char-count {
    margin-top: 10px;
    font-size: 14px;
    color: #888;
  }

  .controls {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .voice-list {
    margin-bottom: 20px;
  }

  .voice-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  </style>
