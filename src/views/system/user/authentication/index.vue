<template>
  <div class="app-container">
    <div class="tag-box">
      <el-tag v-if="authStatus=='0'">请填写下列信息并完成信息认证</el-tag>
      <el-tag v-else-if="authStatus == '2'" type="success">信息认证通过</el-tag>
      <el-tag v-else-if="authStatus == '1'" type="warning">信息认证中</el-tag>
      <el-tag v-else-if="authStatus == '3'" type="danger">信息认证未通过，请重新填写并认证</el-tag>
    </div>
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="用户名" prop="userName">
        <el-input disabled class="ck-input" v-model="form.userName" />
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.nickName" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-radio-group :disabled="isDisabled" v-model="form.sex">
          <el-radio label="0">男</el-radio>
          <el-radio label="1">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="角色" prop="roleId">
        <el-radio-group :disabled="isDisabled" v-model="form.roleId" @change="roleChange" v-if="!isDisabled">
          <el-radio v-for="dict in dict.type.role_id" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
        </el-radio-group>
        <el-checkbox-group v-model="roleIds" disabled v-if="isDisabled">
          <el-checkbox v-for="dict in dict.type.role_ids" :key="dict.value"
            :label="dict.value">{{dict.label}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="学号" prop="studentId" v-if="roleFlag ==1 || form.studentId">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.studentId" placeholder="请输入学号"
          @change="studentChange" />
      </el-form-item>
      <el-form-item label="工号" prop="jobId" v-if="roleFlag ==2 || form.jobId">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.jobId" placeholder="请输入工号"
           />
      </el-form-item>
      <el-form-item label="所属单位" prop="univerName" v-if="roleFlag ==2 || roleFlag==1">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.univerName" placeholder="请输入单位"/>
      </el-form-item>
      <el-form-item label="所属院系" prop="collegeName" v-if="roleFlag ==2 || roleFlag==1">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.collegeName" placeholder="请输入院系"/>
      </el-form-item>
      <el-form-item label="所属专业" prop="majorName" v-if="roleFlag==1">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.majorName" placeholder="请输入专业"/>
      </el-form-item>
      <el-form-item label="手机号" prop="phonenumber">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.phonenumber" placeholder="请输入手机号" />
      </el-form-item>
      <!-- 添加验证码部分 -->
      <el-form-item label="验证码" prop="code" v-if="!isDisabled">
        <el-input :disabled="isDisabled" class="ck-input" v-model="form.code" placeholder="请输入验证码" />
      </el-form-item>
      <el-form-item v-if="!isDisabled">
        <!-- 假设使用一个按钮来请求验证码，点击时调用getCaptcha方法 -->
        <el-button type="info" :disabled="isDisabled" @click="getCaptcha" :loading="captchaLoading">获取验证码</el-button>
      </el-form-item>
      <!-- <el-form-item label="年级" prop="grade" v-if="studentFlag">
        <el-date-picker class="ck-input" v-model="form.grade" value-format="yyyy" :disabled="isDisabled" type="year"
          placeholder="选择年级">
        </el-date-picker>
      </el-form-item> -->
    </el-form>
    <el-row class="step-btn-box" v-if="!isDisabled">
      <el-button type="primary" :disabled="isDisabled" @click="handleSubmint" :loading="btnLoad">确定</el-button>
    </el-row>
  </div>
</template>
<script>
import { getAuthenticationInfo, saveAuthentication, getUniversity, send, getCollByStuId, getCollByTeaId } from "@/api/system/authentication.js";
export default {
  name: "Authentication",
  dicts: ['role_id', 'role_ids'],
  data() {
    return {
      form: {
        roleId: '102'
      },
      btnLoad: false,
      rules: {},
      isDisabled: false,
      authStatus: "1",
      options: [],
      roleFlag: 2,//1学生，2教师，3体验
      code: '', // 用户输入的验证码
      captchaLoading: false, // 控制验证码按钮加载状态\
      roleIds: []
    };
  },
  created() {
    this.getAuthenticationInfo();
  },
  mounted() {
  },
  methods: {
    getCaptcha() {
      this.captchaLoading = true; // 开始加载
      if (this.form.phonenumber && this.form.phonenumber.length > 0) {
        const phoneRegex = /^1[3-9]\d{9}$/; // 简单的中国大陆手机号正则表达式
        if (!phoneRegex.test(this.form.phonenumber)) {
          this.$message.error('请输入有效的手机号码');
          this.captchaLoading = false;
          return;
        } else {
          send({ phonenumber: this.form.phonenumber })
            .then((res) => {
              this.captchaLoading = false;
            }
            ).catch(() => {
              this.captchaLoading = false;
            })
        }
      } else {
        this.$message.error('请输入手机号码');
        this.captchaLoading = false;
      }
    },
    getAuthenticationInfo() {
      getAuthenticationInfo().then((res) => {
        this.form = res.data;
        this.roleIds = res.roleIds.map(String)
        // this.form.roleId = this.form.authStatus == '0' ? '102' : this.form.authRoleId.toString()
        this.form.roleId = this.form.authStatus == '0' ? '102' : this.form.roleId.toString()
        this.getUniversity(this.form.roleId)
        this.authStatus = res.data.authStatus
        if (this.authStatus == '1') {
          this.isDisabled = true
          this.roleIds.push(res.data.authRoleId.toString())
        } else if (this.authStatus == '2') {
          this.isDisabled = true
        } else {
          this.isDisabled = false
        }
        if ((this.form.roleId == 101 || this.form.roleId == 104) && !this.form.authRoleId) {
          this.roleFlag = 1
        } else if (this.form.roleId == 102 && !this.form.authRoleId) {
          this.roleFlag = 2
        } else {
          this.roleFlag = 3
        }
      });
    },
    getUniversity(roleId) {
      if (roleId == 102) {
        getUniversity(roleId).then((res) => {
          this.options = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                  children: null
                };
              }) : null
            };
          });
        })
      } else if (roleId == 104) {
        getUniversity(roleId).then((res) => {
          this.options = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                  children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                    return {
                      value: item.id,
                      label: item.name,
                      children: null
                    };
                  }) : null
                };
              }) : null
            };
          });
        })
      } else {
        getUniversity(roleId).then((res) => {
          this.options = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                  children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                    return {
                      value: item.id,
                      label: item.name,
                      children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                        return {
                          value: item.id,
                          label: item.name
                        };
                      }) : null
                    };
                  }) : null
                };
              }) : null
            };
          });
        })
      }

    },
    handleSubmint() {

      if (this.form.roleId === '102') {
        this.jobChange(this.form.jobId);
      }
      if (this.form.roleId === '101' || this.form.roleId === '104' ) {
        this.studentChange(this.form.studentId);
      }


      saveAuthentication(this.form).then((res) => {
        this.$modal.msgSuccess("已提交");
        this.isDisabled = true
        this.authStatus = '1'
        this.roleIds.push(this.form.roleId.toString())
      });
    },
    roleChange(val) {
      if (val == 101 || val == 104) {
        this.roleFlag = 1
        this.form.jobId = null
      } else if (val == 102) {
        this.roleFlag = 2
        this.form.studentId = null
      } else {
        this.roleFlag = 3
        this.form.jobId = null
        this.form.studentId = null
      }
      this.getUniversity(val)
    },
    studentChange(val) {
      if (this.roleFlag == 1 && val) {
        getCollByStuId({ studentId: val }).then(res => {
          if (res.code == 200) {
            this.form.affiliatedUnit = res.data
          }
        })
      }
    },
    jobChange(val) {
      if (this.roleFlag == 2 && val) {
        getCollByTeaId({ teacherId: val }).then(res => {
          if (res.code == 200) {
            this.form.affiliatedUnit = res.data
          }
        })
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: 10px auto;
}
.tag-box {
  width: 80%;
  margin: auto;
  .el-tag {
    width: 100%;
  }
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
</style>
