<template>
  <div >
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form"
             style="position: relative;" label-width="100px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="registerForm.username" type="text" auto-complete="off" placeholder="用户名" style="width: 40%" disabled>
        </el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="password">
        <el-input v-model="registerForm.password" type="password" auto-complete="new-password" placeholder="密码"
                  @keyup.enter.native="handleRegister" style="width: 40%">
        </el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="registerForm.confirmPassword" type="password" auto-complete="new-password" placeholder="确认密码"
                  @keyup.enter.native="handleRegister" style="width: 40%">
        </el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="code" v-if="captchaEnabled">
        <el-input v-model="registerForm.code" auto-complete="off" placeholder="请输入验证码" style="width: 40%"
                  @keyup.enter.native="handleRegister" >
        </el-input>
        <el-button :disabled="isSendingCode || countdown > 0" :loading="isSendingCode"
                   style="width: 25%; margin-left: 5%" @click="sendSmsCode">
          {{ countdown > 0 ? `${countdown} 秒后重试` : '发送验证码' }}
        </el-button>
      </el-form-item>

      <el-form-item style="width: 100%;align-items: center;">
        <el-button :loading="loading" size="medium" type="primary"
                   style="width: 30%;margin-bottom: 10px;margin-left: 155px;border-radius: 20px;"
                   @click.native.prevent="handleRegister">
          <span v-if="!loading">确定</span>
          <span v-else>重 置 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import { send, revise } from "@/api/login";
import { encrypt, decrypt } from "@/api/system/rsaUtil";

export default {
  name: "newPwd",
  props: {
    user: {
      type: Object
    }
  },
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      codeUrl: "",
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        code: "",
        uuid: "",
        icpNum: "鲁ICP备2022016453号",
        prodFLag: false,
      },
      registerRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的用户名" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
          {
            min: 8, // 修改最小长度为8
            max: 12,
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, // 添加正则表达式确保包含大小写字母和数字
            message: "密码8到12位,且包含大小写字母和数字,不能包含特殊字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      captchaEnabled: true,
      isSendingCode: false, // 标记验证码是否正在发送中
      countdown: 0, // 发送验证码后的倒计时计数
    };
  },
  created() {
    this.icpNum = window.location.hostname === 'www.aicaipap.com' ? '鲁ICP备2022016453号-3' : '鲁ICP备2022016453号-2'
    this.prodFLag = window.location.hostname === 'www.aicaipap.com' ? true : false
  },
  watch: {
    user: {
      handler(user) {
        if (user) {
          this.registerForm.username = user.userName
        }
      },
    },
  },
  methods: {
    sendSmsCode() {
      if (this.isSendingCode || this.countdown > 0) {
        return; // 如果正在发送中或倒计时未结束，直接返回
      }
      this.isSendingCode = true; // 开始发送，禁用按钮并显示加载状态
      var priKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu';
      const param = {
        userName: this.registerForm.username,
        dataNow: encrypt(Date.now().toString(), priKey),
      };
      send(param)
        .then((res) => {
            this.countdown = 60;
          }
        );
      // 发送成功后，开始倒计时
      this.startCountdown();
    },
    startCountdown() {
      const timer = setInterval(() => {
        if (this.countdown === 0) {
          clearInterval(timer);
          this.isSendingCode = false; // 发送完成，重置状态
        } else {
          this.countdown--; // 倒计时减1
        }
      }, 1000); // 每秒减少
    },
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          var priKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu';
          const param = {
            userName: this.registerForm.username,
            password: this.registerForm.password,
            code: this.registerForm.code,
            dataNow: encrypt(Date.now().toString(), priKey),
          };
          revise(param)
            .then((res) => {
              const username = this.registerForm.username;
              this.$alert(
                "<font color='red'>恭喜你，您的账号 " +
                username +
                " 密码修改成功！</font>",
                "系统提示",
                {
                  dangerouslyUseHTMLString: true,
                  type: "success",
                }
              )

            })
            .catch(() => {
              this.loading = false;
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        }
      });
    },
  },
};
</script>
