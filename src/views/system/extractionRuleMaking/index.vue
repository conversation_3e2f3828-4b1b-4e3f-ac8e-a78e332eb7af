<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="规则名称 " prop="rulesName">
        <el-input
          v-model="queryParams.rulesName"
          placeholder="请输入规则名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDeleteContent"
          >删除</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tabList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规则名称" prop="rulesName" width="120" />
      <el-table-column label="prompt" prop="prompt" width="180" />
      <el-table-column label="response" prop="response" width="180" />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateName(scope.row)"
            >编辑</el-button
          >

          <el-button
            size="mini"
            type="text"
            @click="handleDeleteContent(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="rulesName">
          <el-input v-model="form.rulesName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="prompt" prop="prompt">
          <el-input v-model="form.prompt" type="textarea" placeholder="请输入prompt" />
        </el-form-item>
        <el-form-item label="response" prop="response">
          <el-input v-model="form.response" type="textarea" placeholder="请输入response" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 内容列表 -->
    <el-dialog
      :title="dataSetRuleListContentTitle"
      :visible.sync="dataSetRuleContentListOpen"
      width="1200px"
      append-to-body
    >
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddDataSetRuleContent"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDeleteContent"
            >删除</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="dataSetRuleContentListLoading"
        :data="dataSetRuleContentList"
        @selection-change="handleSelectionChangeContent"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="标记内容"
          prop="tagContent"
          :show-overflow-tooltip="true"
          width="150"
        />
        <el-table-column
          label="标记类型"
          align="center"
          prop="tagType"
          width="180"
        >
          <template slot-scope="scope">
            <div
              v-for="dict in dict.type.tag_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <div v-if="scope.row.tagType == dict.value">{{ dict.label }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="内容方位"
          align="center"
          prop="contentPosition"
          width="180"
        >
          <template slot-scope="scope">
            <div
              v-for="dict in dict.type.content_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <div v-if="scope.row.contentPosition == dict.value">
                {{ dict.label }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleUpdateDataSetRuleContent(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="handleDataSetRuleContent(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="dataSetRuleContentTotal > 0"
        :total="dataSetRuleContentTotal"
        :page.sync="dataSetRuleContentQueryParams.pageNum"
        :limit.sync="dataSetRuleContentQueryParams.pageSize"
        @pagination="getDataSetRuleContentList"
      />
    </el-dialog>

    <!-- 数据集内容新增 -->
    <el-dialog
      :title="dataSetRuleContentTitle"
      :visible.sync="dataSetRuleContentOpen"
      width="800px"
      append-to-body
      @close="cancelDataSetRuleContentform"
    >
      <el-form
        ref="dataSetRuleContentform"
        :model="dataSetRuleContentform"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="标记内容" prop="tagContent">
          <el-input
            v-model="dataSetRuleContentform.tagContent"
            placeholder="请输入标记内容"
          />
        </el-form-item>
        <el-form-item label="标记类型" prop="tagType">
          <el-select
            v-model="dataSetRuleContentform.tagType"
            placeholder="请选择标记类型"
          >
            <el-option
              v-for="dict in dict.type.tag_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容方位" prop="contentPosition">
          <el-select
            v-model="dataSetRuleContentform.contentPosition"
            placeholder="请选择内容方位"
          >
            <el-option
              v-for="dict in dict.type.content_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDataSetRuleContentform"
          >确 定</el-button
        >
        <el-button @click="cancelDataSetRuleContentform">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

    <script>
import {
  getDataSetRulesList,
  getDataSetRules,
  addDataSetRules,
  updateDataSetRules,
  delDataSetRules,
  addDataSetRulesContent,
  getDataSetRulesContentById,
  updateDataSetRulesContent,
  delDataSetRulesContent,
  getDataSetRulesById,
} from "@/api/system/extractionRuleMaking";

export default {
  name: "extractionRuleMaking",
  dicts: ["tag_type", "content_position"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      // 显示搜索条件
      showSearch: true,
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      tabList: [],
      // 弹出层标题
      title: "",
      // 数据范围选项
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rulesName: undefined,
        prompt: undefined,
        response: undefined,
        tagType: undefined,

      },
      form: {},
      open: false,
      rules: {
        rulesName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" },
        ],
        prompt: [{ required: true, message: "prompt不能为空", trigger: "blur" }],
        response: [{ required: true, message: "response不能为空", trigger: "blur" }],
        // tagContent: [
        //   { required: true, message: "标记内容不能为空", trigger: "blur" },
        // ],
        // tagType: [
        //   { required: true, message: "标记类型不能为空", trigger: "blur" },
        // ],
        // contentPosition: [
        //   { required: true, message: "内容方位不能为空", trigger: "blur" },
        // ],
      },
      dataSetRuleContentListLoading: false,
      dataSetRuleContentListOpen: false,
      dataSetRuleContentOpen: false,
      dataSetRuleContentList: [],
      dataSetRuleContentTotal: 0,
      dataSetRuleContentTitle: "",
      dataSetRuleListContentTitle: "",
      dataSetRuleContentQueryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dataSetRuleContentform: {},
      rulesId: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getDataSetRulesList(this.queryParams).then((response) => {
        this.tabList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        rulesName: undefined,
        prompt: undefined,
        response: undefined,
        tagType: undefined,
      }),
        this.handleQuery();
    },
    reset() {
      this.form = {
        dataName: undefined,
        businessType: undefined,
      };
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    handleUpdateName(row) {
      this.reset();
      getDataSetRulesById(row.rulesId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.rulesId != undefined) {
            updateDataSetRules(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDataSetRules(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.$refs["form"].clearValidate();
      this.reset();
    },

    /** 编辑 */
    handleUpdate(row) {
      this.dataId = row.id;
      this.dataSetRuleListContentTitle = "编辑";
      this.dataSetRuleContentListOpen = true;
      this.rulesId = row.rulesId;
      this.getDataSetRuleContentList();
    },
    // 获取编辑内容列表
    getDataSetRuleContentList() {
      this.dataSetRuleContentListLoading = true;
      this.dataSetRuleContentQueryParams.rulesId = this.rulesId;
      getDataSetRules(this.dataSetRuleContentQueryParams).then((response) => {
        this.dataSetRuleContentList = response.rows;
        this.dataSetRuleContentTotal = response.total;
        this.dataSetRuleContentListLoading = false;
      });
    },
    handleAddDataSetRuleContent() {
      this.dataSetRuleContentOpen = true;
      this.resetDataSetRuleContentform();
      this.dataSetRuleContentTitle = "新增";
    },
    // 编辑内容编辑
    handleUpdateDataSetRuleContent(row) {
      this.resetDataSetRuleContentform();
      getDataSetRulesContentById(row.id).then((response) => {
        this.dataSetRuleContentform = response.data;
        this.dataSetRuleContentOpen = true;
        this.dataSetRuleContentTitle = "修改";
      });
    },
    // 内容保存
    submitDataSetRuleContentform() {
      this.form.rulesId = this.rulesId;
      this.$refs["dataSetRuleContentform"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateDataSetRulesContent(this.form).then(
              (response) => {
                this.$modal.msgSuccess("修改成功");
                this.dataSetRuleContentOpen = false;
                this.getDataSetRuleContentList();
                this.resetDataSetRuleContentform();
              }
            );
          } else {
            addDataSetRulesContent(this.form).then(
              (response) => {
                this.$modal.msgSuccess("新增成功");
                this.dataSetRuleContentOpen = false;
                this.getDataSetRuleContentList();
                this.resetDataSetRuleContentform();
              }
            );
          }
        }
      });
    },
    resetDataSetRuleContentform() {
      this.dataSetRuleContentform = {};
    },
    // 数据集内容取消
    cancelDataSetRuleContentform() {
      this.dataSetRuleContentOpen = false;
      this.$refs["dataSetRuleContentform"].clearValidate();
      this.resetDataSetRuleContentform();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.rulesId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 删除数据集内容按钮操作 */
    handleDeleteContent(row) {
      const ids = row.rulesId || this.ids;
      this.$modal
        .confirm("是否确认删除本条训练记录？")
        .then(function () {
          return delDataSetRules(ids);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        })
        .catch(() => {});
    },
    handleSelectionChangeContent(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 删除数据集内容按钮操作 */
    handleDataSetRuleContent(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除本条训练记录？")
        .then(function () {
          return delDataSetRulesContent(ids);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getDataSetRuleContentList();
        })
        .catch(() => {});
    },
  },
};
</script>
