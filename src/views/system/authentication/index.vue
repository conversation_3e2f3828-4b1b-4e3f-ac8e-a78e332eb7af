<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="用户ID" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="认证记录ID" prop="authId" min-width="80" />
      <el-table-column label="用户ID" prop="userId" min-width="80" />
      <el-table-column label="姓名" prop="nickName" min-width="80" />
      <el-table-column label="角色" prop="roleName" min-width="80" />
      <el-table-column label="有效期" prop="roleName" min-width="80">
        <template slot-scope="scope">
          <span>{{scope.row.expTimeStart?getYMD(scope.row.expTimeStart):'-'}} 至
            {{scope.row.expTimeEnd?getYMD(scope.row.expTimeEnd):'-'}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="所属院系" min-width="180">
        <template slot-scope="scope">
          <span
            v-if="scope.row.universityName && scope.row.collegeName && scope.row.majorName && scope.row.className">{{scope.row.universityName+'/'+scope.row.collegeName+'/'+scope.row.majorName+'/'+scope.row.className}}</span>
          <span
            v-else-if="scope.row.universityName && scope.row.collegeName && scope.row.majorName">{{scope.row.universityName+'/'+scope.row.collegeName+'/'+scope.row.majorName}}</span>
          <span
            v-else-if="scope.row.universityName && scope.row.collegeName">{{scope.row.universityName+'/'+scope.row.collegeName}}</span>
          <span v-else-if="scope.row.universityName">{{scope.row.universityName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="工号" prop="jobId" min-width="80">
        <template slot-scope="scope">
          <span>{{scope.row.jobId? scope.row.jobId:'-'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="学号" prop="studentId" min-width="80">
        <template slot-scope="scope">
          <span>{{scope.row.studentId? scope.row.studentId:'-'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="年级" prop="grade" min-width="80">
        <template slot-scope="scope">
          <span>{{scope.row.grade? scope.row.grade:'-'}}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="认证审核状态" prop="auditStatus" min-width="100">
        <template slot-scope="scope">
          <span>{{auditStatusChange(scope.row.auditStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" :disabled="scope.row.auditStatus == 1"
            @click="handlePass(scope.row,1)">通过</el-button>
          <el-button size="mini" type="text" :disabled="scope.row.auditStatus == 1"
            @click="handleAudit(scope.row,2)">驳回</el-button>
          <el-button size="mini" type="text" :disabled="scope.row.auditStatus != 1"
            @click="handlePostpone(scope.row,3)">延期</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="有效期" :visible.sync="dialogVisible" width="450px" :before-close="handleClose">
      <span class="demonstration">体验结束日期</span>
      <el-date-picker v-model="expTimeEnd" type="date" :picker-options="pickerOptions" placeholder="选择体验结束日期"
        value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAuthenticationList, audit,
  postponeStatus
} from "@/api/system/authentication.js";
export default {
  name: "Authentication",
  dicts: ['audit_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dialogVisible: false,
      params: {},
      expTime: '',
      expTimeEnd: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= Date.now();
        },
      }
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getAuthenticationList(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 审批按钮操作 */
    handleAudit(row, type) {
      const params = {
        ...row,
        auditStatus: type
      }
      audit(params).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      }
      );
    },
    handlePass(row, type) {
      this.dialogVisible = true
      this.params = {
        ...row,
        auditStatus: type
      }
    },
    handleSubmit() {
      var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
      var startTime = date1.getFullYear() + "-" + ((date1.getMonth() + 1) < 10 ? "0" + (date1.getMonth() + 1) : (date1.getMonth() + 1)) + "-" + (date1.getDate() < 10 ? "0" + date1.getDate() : date1.getDate()) + " " + (date1.getHours() < 10 ? "0" + date1.getHours() : date1.getHours()) + ":" + (date1.getMinutes() < 10 ? "0" + date1.getMinutes() : date1.getMinutes()) + ":" + (date1.getSeconds() < 10 ? "0" + date1.getSeconds() : date1.getSeconds());
      if (this.expTimeEnd.length == 0) {
        this.$modal.msgError("请先选择有效期");
      } else {
        this.params = {
          ...this.params,
          expTimeStart: startTime,
          expTimeEnd: this.expTimeEnd
        }
        if (this.params.auditStatus == 1) {
          audit(this.params).then(response => {
            this.$modal.msgSuccess("操作成功");
            this.handleClose()
          }
          );
        } else {
          delete this.params.auditStatus
          postponeStatus(this.params).then(response => {
            this.$modal.msgSuccess("操作成功");
            this.handleClose()
          })
        }
      }
    },
    handlePostpone(row, type) {
      this.dialogVisible = true
      this.params = {
        userId: row.userId,
        auditStatus: type
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.params = {}
      this.expTimeEnd = ''
      this.getList();
    },
    auditStatusChange(auditStatus) {
      return this.selectDictLabelByVal(this.dict.type.audit_status, auditStatus)
    },
    getYMD(dateStr) {
      const date = new Date(dateStr);
      // 使用toISOString提取年月日，然后截取前10个字符
      return dateStr.substring(0, 10);
    }
  }
};
</script>