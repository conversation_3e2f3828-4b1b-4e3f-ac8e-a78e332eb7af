<template>
  <div align="center" id="qqq">
    <div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="路由配置" name="routing">
          <component :is="currentComponent"></component>
        </el-tab-pane>
        <el-tab-pane label="专业配置" name="speciality">
          <component :is="currentComponent"></component>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script>
  import routing from "./routing";
  import speciality from "./speciality";


  export default {
    components: {
      'routing': routing,
      'speciality': speciality,
    },

    data() {
      return {
        activeName: 'routing',
        currentComponent: 'routing',
      };
    },
    watch: {
      activeName(newName) {
        this.currentComponent = newName;
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event);
      },
    },
  };

</script>
<style type="text/css">
  #qqq {
    margin-left: 2%;
    margin-right: 2%;
    margin-bottom: 8%;
    margin-top: 1%;
  }

  .no-border {
    border: none;
    outline: none;
    font-size: 120%;
    text-align: center;
  }

  .logo-image2 {
    width: 50px;
    height: auto;
  }
</style>
