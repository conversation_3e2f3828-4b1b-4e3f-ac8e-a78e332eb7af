<template>
  <div class="app-container">
<!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--      <el-form-item label="知识库appId" prop="appId">-->
<!--        <el-input-->
<!--          v-model="queryParams.appId"-->
<!--          placeholder="请输入知识库appId"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="知识库secretKey" prop="secretKey">-->
<!--        <el-input-->
<!--          v-model="queryParams.secretKey"-->
<!--          placeholder="请输入知识库secretKey"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="学校id" prop="universityId">-->
<!--        <el-input-->
<!--          v-model="queryParams.universityId"-->
<!--          placeholder="请输入学校id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="学院id" prop="collegeId">-->
<!--        <el-input-->
<!--          v-model="queryParams.collegeId"-->
<!--          placeholder="请输入学院id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="专业id" prop="majorId">-->
<!--        <el-input-->
<!--          v-model="queryParams.majorId"-->
<!--          placeholder="请输入专业id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['create:speciality:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['create:speciality:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['create:speciality:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="specialityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false"/>
      <el-table-column label="知识库appId" align="center" prop="appId" />
      <el-table-column label="知识库secretKey" align="center" prop="secretKey" />
      <el-table-column label="学校" align="center" prop="universityName" />
      <el-table-column label="学院" align="center" prop="collegeName" />
      <el-table-column label="专业" align="center" prop="majorName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['create:speciality:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['create:speciality:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库专业配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="知识库appId" prop="appId">
          <el-input v-model="form.appId" placeholder="请输入知识库appId" />
        </el-form-item>
        <el-form-item label="知识库secretKey" prop="secretKey">
          <el-input v-model="form.secretKey" placeholder="请输入知识库secretKey" />
        </el-form-item>
        <el-form-item label="所属院系" prop="affiliatedUnit" >
          <el-cascader  class="ck-input" v-model="form.affiliatedUnit"
                       :options="options"></el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listSpeciality, getSpeciality, delSpeciality, addSpeciality, updateSpeciality } from "@/api/system/speciality";
  import { getAuthenticationInfo,  getUniversity } from "@/api/system/authentication.js";

  export default {
    name: "Speciality",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 知识库专业配置表格数据
        specialityList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        options: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          appId: null,
          secretKey: null,
          universityId: null,
          collegeId: null,
          majorId: null,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
        }
      };
    },
    created() {
      this.getList();
      this.getAuthenticationInfo();
    },
    methods: {
      /** 查询知识库专业配置列表 */
      getList() {
        this.loading = true;
        listSpeciality(this.queryParams).then(response => {
          this.specialityList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      getAuthenticationInfo() {
        getAuthenticationInfo().then((res) => {
          this.form = res.data;
          this.roleIds = res.roleIds.map(String)
          // this.form.roleId = this.form.authStatus == '0' ? '102' : this.form.authRoleId.toString()
          this.form.roleId = this.form.authStatus == '0' ? '102' : this.form.roleId.toString()
          this.getUniversity(this.form.roleId)
          this.authStatus = res.data.authStatus
          if (this.authStatus == '1') {
            this.isDisabled = true
            this.roleIds.push(res.data.authRoleId.toString())
          } else if (this.authStatus == '2') {
            this.isDisabled = true
          } else {
            this.isDisabled = false
          }
          if ((this.form.roleId == 101 || this.form.roleId == 104) && !this.form.authRoleId) {
            this.roleFlag = 1
          } else if (this.form.roleId == 102 && !this.form.authRoleId) {
            this.roleFlag = 2
          } else {
            this.roleFlag = 3
          }
        });
      },
      getUniversity(roleId) {
        getUniversity(roleId).then((res) => {
          this.options = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                  children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                    return {
                      value: item.id,
                      label: item.name,
                    };
                  }) : null
                };
              }) : null
            };
          });
        })
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          appId: null,
          secretKey: null,
          universityId: null,
          collegeId: null,
          majorId: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加知识库专业配置";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids
        getSpeciality(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改知识库专业配置";
        });
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateSpeciality(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addSpeciality(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除知识库专业配置编号为"' + ids + '"的数据项？').then(function() {
          return delSpeciality(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('create/speciality/export', {
          ...this.queryParams
        }, `speciality_${new Date().getTime()}.xlsx`)
      }
    }
  };
</script>
