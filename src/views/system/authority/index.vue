<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="菜单路由" prop="menuRouting">
        <el-input
          v-model="queryParams.menuRouting"
          placeholder="请输入菜单路由"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['create:infor:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['create:infor:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['create:infor:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inforList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" v-if="false"/>
      <el-table-column label="菜单路由" align="center" prop="menuRouting"/>
      <el-table-column label="应用apiAk" align="center" prop="apiKey"/>
      <el-table-column label="应用apiSK" align="center" prop="secretKey"/>
      <el-table-column label="服务apiURL" align="center" prop="apiUrl"/>
      <el-table-column label="百度千帆ak" align="center" prop="ak"/>
      <el-table-column label="百度千帆sk" align="center" prop="sk"/>
      <el-table-column label="BOS域名" align="center" prop="domainName"/>
      <el-table-column label="Bucket名称" align="center" prop="bosBucketName"/>
      <el-table-column label="备注" align="center" prop="remarks"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['create:infor:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['create:infor:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改鉴权信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="菜单路由" prop="menuRouting">
          <el-input v-model="form.menuRouting" placeholder="请输入菜单路由"/>
        </el-form-item>
        <el-form-item label="应用apiAk" prop="apiKey">
          <el-input v-model="form.apiKey" placeholder="请输入应用apiAk"/>
        </el-form-item>
        <el-form-item label="应用apiSK" prop="secretKey">
          <el-input v-model="form.secretKey" placeholder="请输入应用apiSK"/>
        </el-form-item>
        <el-form-item label="服务apiURL" prop="apiUrl">
          <el-input v-model="form.apiUrl" placeholder="请输入服务apiURL"/>
        </el-form-item>
        <el-form-item label="百度千帆ak" prop="ak">
          <el-input v-model="form.ak" placeholder="请输入百度千帆ak"/>
        </el-form-item>
        <el-form-item label="百度千帆sk" prop="sk">
          <el-input v-model="form.sk" placeholder="请输入百度千帆sk"/>
        </el-form-item>
        <el-form-item label="BOS域名" prop="domainName">
          <el-input v-model="form.domainName" placeholder="请输入BOS域名"/>
        </el-form-item>
        <el-form-item label="Bucket名称" prop="bosBucketName">
          <el-input v-model="form.bosBucketName" placeholder="请输入Bucket名称"/>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {listInfor, getInfor, delInfor, addInfor, updateInfor} from "@/api/system/authority";
  import {encrypt, decrypt} from "@/api/system/rsaUtil";

  export default {
    name: "Infor",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 鉴权信息表格数据
        inforList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          menuRouting: null,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询鉴权信息列表 */
      getList() {
        this.loading = true;
        listInfor(this.queryParams).then(response => {
          this.inforList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          menuRouting: null,
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加鉴权信息";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids
        getInfor(id).then(response => {
          var priKey = 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu';
          const param = {
            id:  response.data.id,
            menuRouting:  response.data.menuRouting,
            apiKey: decrypt(response.data.apiKey, priKey),
            secretKey: decrypt(response.data.secretKey, priKey),
            apiUrl: decrypt(response.data.apiUrl, priKey),
            ak: decrypt(response.data.ak, priKey),
            sk: decrypt(response.data.sk, priKey),
            domainName: decrypt(response.data.domainName, priKey),
            bosBucketName: decrypt(response.data.bosBucketName, priKey),
            remarks: response.data.remarks,
          };
          this.form = param;
          this.open = true;
          this.title = "修改鉴权信息";
        });
      },
      /** 提交按钮 */
      submitForm() {
        //公钥
        var pubKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCy9so3ptBDaGWs9gBT0jDQE6LOu3ocCA6iQrgZgJiDnfafoimHbFgGw1Cayns7u4XSQV3ic0923i4Qrbs7dBKi+wxZfIuUYEMoDvYXHk8rfO3q64TzqaI1Q0z7PYQwc0k2N96LiOeYzx9iPvBG5Smspy7NPHl5o0xl7A6jOV2MmQIDAQAB';

        //公钥加密
        const param = {
          id: this.form.id,
          menuRouting: this.form.menuRouting,
          apiKey: encrypt(this.form.apiKey, pubKey),
          secretKey: encrypt(this.form.secretKey, pubKey),
          apiUrl: encrypt(this.form.apiUrl, pubKey),
          ak: encrypt(this.form.ak, pubKey),
          sk: encrypt(this.form.sk, pubKey),
          domainName: encrypt(this.form.domainName, pubKey),
          bosBucketName: encrypt(this.form.bosBucketName, pubKey),
          remarks: this.form.remarks,
        };
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateInfor(param).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addInfor(param).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除鉴权信息编号为"' + ids + '"的数据项？').then(function () {
          return delInfor(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {
        });
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('create/infor/export', {
          ...this.queryParams
        }, `infor_${new Date().getTime()}.xlsx`)
      }
    }
  };
</script>
