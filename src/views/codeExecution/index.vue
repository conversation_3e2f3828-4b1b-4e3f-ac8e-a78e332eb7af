
<template>
  <div class="code-execution-container">
    <!-- 头部控制栏 -->
    <div class="header-controls">
      <div class="left-controls">
        <el-select v-model="selectedLanguage" placeholder="选择语言" style="width: 120px;">
          <el-option label="Python" value="python"></el-option>
        </el-select>
        <el-button
          type="primary"
          :loading="isExecuting"
          @click="executeCode"
          :disabled="!code.trim()"
        >
          <i class="el-icon-video-play"></i>
          {{ isExecuting ? '执行中...' : '运行代码' }}
        </el-button>
      </div>
      <div class="right-controls">
        <el-button size="small" @click="clearCode">清空</el-button>
        <el-button size="small" @click="resetCode">重置</el-button>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧代码编辑器 -->
      <div class="editor-panel">
        <div class="panel-header">
          <span>代码编辑器</span>
          <div class="editor-controls">
            <el-button size="mini" @click="changeTheme">
              {{ isDarkTheme ? '浅色主题' : '深色主题' }}
            </el-button>
          </div>
        </div>
        <div class="editor-container" ref="editorContainer"></div>
      </div>

      <!-- 右侧结果展示 -->
      <div class="result-panel">
        <div class="panel-header">
          <span>执行结果</span>
          <div class="result-controls">
            <el-button size="mini" @click="clearResult">清空结果</el-button>
          </div>
        </div>

        <!-- 执行状态 -->
        <div class="execution-status" v-if="executionResult">
          <el-tag
            :type="executionResult.status === 'SUCCESS' ? 'success' : 'danger'"
            size="small"
          >
            {{ getStatusText(executionResult.status) }}
          </el-tag>
          <span class="execution-time" v-if="executionResult.executionTime">
            执行时间: {{ executionResult.executionTime }}ms
          </span>
          <span class="exit-code" v-if="executionResult.exitCode !== undefined">
            退出码: {{ executionResult.exitCode }}
          </span>
        </div>

        <!-- 输出结果 -->
        <div class="output-container">
          <!-- 标准输出 -->
          <div class="output-section" v-if="executionResult && executionResult.stdout">
            <div class="output-header">
              <i class="el-icon-success"></i>
              <span>标准输出</span>
            </div>
            <pre class="output-content success">{{ formatOutput(executionResult.stdout) }}</pre>
          </div>

          <!-- 错误输出 -->
          <div class="output-section" v-if="executionResult && executionResult.stderr">
            <div class="output-header error">
              <i class="el-icon-error"></i>
              <span>错误输出</span>
            </div>
            <pre class="output-content error">{{ executionResult.stderr }}</pre>
          </div>

          <!-- 错误信息 -->
          <div class="output-section" v-if="executionResult && executionResult.errorMessage">
            <div class="output-header error">
              <i class="el-icon-warning"></i>
              <span>错误信息</span>
            </div>
            <pre class="output-content error">{{ executionResult.errorMessage }}</pre>
          </div>

          <!-- 空状态 -->
          <div class="empty-result" v-if="!executionResult && !isExecuting">
            <i class="el-icon-document"></i>
            <p>点击"运行代码"查看执行结果</p>
          </div>

          <!-- 执行中状态 -->
          <div class="executing-state" v-if="isExecuting">
            <i class="el-icon-loading"></i>
            <span>代码执行中，请稍候...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as monaco from 'monaco-editor'
import { executeCode } from '@/api/codeExecution'
import { setupMonacoEnvironment, pythonEditorOptions } from '@/utils/monacoConfig'

export default {
  name: 'CodeExecution',
  data() {
    return {
      // 编辑器相关
      editor: null,
      code: `# 欢迎使用在线代码执行平台
# 这里是一个简单的Python示例

import math

# 计算圆的面积
radius = 5
area = math.pi * radius ** 2

print(f'半径为 {radius} 的圆的面积是: {area:.2f}')

# 计算斐波那契数列
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(f'斐波那契数列前10项:')
for i in range(10):
    print(f'F({i}) = {fibonacci(i)}')`,
      selectedLanguage: 'python',
      isDarkTheme: false,

      // 执行相关
      isExecuting: false,
      executionResult: null
    }
  },
  mounted() {
    setupMonacoEnvironment()
    this.initMonacoEditor()
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.dispose()
    }
  },
  methods: {
    // 初始化Monaco编辑器
    initMonacoEditor() {
      this.$nextTick(() => {
        if (this.$refs.editorContainer) {
          this.editor = monaco.editor.create(this.$refs.editorContainer, {
            ...pythonEditorOptions,
            value: this.code,
            theme: this.isDarkTheme ? 'vs-dark' : 'vs'
          })

          // 监听代码变化
          this.editor.onDidChangeModelContent(() => {
            this.code = this.editor.getValue()
          })
        }
      })
    },

    // 执行代码
    async executeCode() {
      if (!this.code.trim()) {
        this.$message.warning('请输入要执行的代码')
        return
      }

      this.isExecuting = true
      this.executionResult = null

      try {
        const response = await executeCode({
          language: this.selectedLanguage,
          code: this.code
        })

        if (response.code === 200) {
          this.executionResult = response.data
          this.$message.success('代码执行完成')
        } else {
          this.$message.error(response.msg || '代码执行失败')
        }
      } catch (error) {
        console.error('代码执行错误:', error)
        this.$message.error('代码执行失败，请检查网络连接')
      } finally {
        this.isExecuting = false
      }
    },

    // 切换主题
    changeTheme() {
      this.isDarkTheme = !this.isDarkTheme
      if (this.editor) {
        monaco.editor.setTheme(this.isDarkTheme ? 'vs-dark' : 'vs')
      }
    },

    // 清空代码
    clearCode() {
      this.code = ''
      if (this.editor) {
        this.editor.setValue('')
      }
    },

    // 重置代码
    resetCode() {
      const defaultCode = `# 欢迎使用在线代码执行平台
# 这里是一个简单的Python示例

import math

# 计算圆的面积
radius = 5
area = math.pi * radius ** 2

print(f'半径为 {radius} 的圆的面积是: {area:.2f}')

# 计算斐波那契数列
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(f'斐波那契数列前10项:')
for i in range(10):
    print(f'F({i}) = {fibonacci(i)}')`

      this.code = defaultCode
      if (this.editor) {
        this.editor.setValue(defaultCode)
      }
    },

    // 清空结果
    clearResult() {
      this.executionResult = null
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'SUCCESS': '执行成功',
        'ERROR': '执行失败',
        'TIMEOUT': '执行超时',
        'RUNNING': '执行中'
      }
      return statusMap[status] || status
    },

    // 格式化输出
    formatOutput(output) {
      if (!output) return ''
      // 移除时间戳标记
      return output
        .replace(/\[\$\(date \+%Y-%m-%d %H:%M:%S\)\] === 开始执行代码 ===\n/, '')
        .replace(/\[\$\(date \+%Y-%m-%d %H:%M:%S\)\] === 代码执行完成 ===\n/, '')
        .trim()
    }
  }
}
</script>

<style scoped lang="scss">
.code-execution-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .left-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .right-controls {
      display: flex;
      gap: 8px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px 20px;
    min-height: 0;

    .editor-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 500;
        color: #303133;
      }

      .editor-container {
        flex: 1;
        min-height: 400px;
      }
    }

    .result-panel {
      width: 400px;
      display: flex;
      flex-direction: column;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 500;
        color: #303133;
      }

      .execution-status {
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 12px;

        .execution-time, .exit-code {
          color: #606266;
        }
      }

      .output-container {
        flex: 1;
        overflow-y: auto;

        .output-section {
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .output-header {
            padding: 8px 16px;
            background: #f8f9fa;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;

            &.error {
              color: #f56c6c;
              background: #fef0f0;
            }
          }

          .output-content {
            margin: 0;
            padding: 12px 16px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
            background: #fafafa;

            &.success {
              color: #67c23a;
              background: #f0f9ff;
            }

            &.error {
              color: #f56c6c;
              background: #fef0f0;
            }
          }
        }

        .empty-result {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 12px;
          }
        }

        .executing-state {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100px;
          color: #409eff;
          gap: 8px;

          i {
            animation: rotate 1s linear infinite;
          }
        }
      }
    }
  }
}

// 动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .code-execution-container .main-content {
    flex-direction: column;

    .result-panel {
      width: 100%;
      height: 300px;
    }
  }
}
</style>
