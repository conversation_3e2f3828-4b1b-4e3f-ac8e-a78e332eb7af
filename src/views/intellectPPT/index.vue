<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="PPT要求" prop="query">
        <template>
          <el-radio v-model="requestType" label="text">输入文本</el-radio>
          <el-radio v-model="requestType" label="file">上传文件</el-radio>
        </template>
        <div v-if="requestType=='text'">
          <el-input type="textarea" v-model="form.query" placeholder="请输入PPT要求" maxlength="8000" show-word-limit
                    :autosize="{ minRows: 4, maxRows: 6}" />
        </div>
        <div v-if="requestType=='file'">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers"
                     multiple :limit="1" :on-success="handleUploadSuccess" :on-remove="handleRemove" accept=".txt,.doc,.docx">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">支持txt、doc、docx格式文件上传，仅能上传一个文件</div>
          </el-upload>
        </div>
      </el-form-item>
      <!-- <el-form-item label="PPT生成类型" prop="createModel">
        <el-select class="ck-input" v-model="form.createModel" placeholder="请选择PPT生成类型">
          <el-option v-for="dict in dict.type.create_model" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="PPT生成主题" prop="theme">
        <el-select class="ck-input" v-model="form.theme" placeholder="请选择PPT生成主题">
          <el-option v-for="dict in dict.type.theme" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="业务ID" prop="businessId">
        <el-input class="ck-input" v-model="form.businessId" placeholder="请输入业务ID" />
      </el-form-item> -->
      <el-form-item label="PPT作者名" prop="author">
        <el-input class="ck-input" v-model="form.author" placeholder="请输入PPT作者名" />
      </el-form-item>
      <el-form-item label="是否生成PPT演讲备注" prop="isCardNote">
        <el-select class="ck-input" v-model="form.isCardNote" placeholder="请选择是否生成PPT演讲备注">
          <el-option v-for="dict in dict.type.is_card_note" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint" :loading="btnLoad">确定</el-button>
    </el-row>
  </div>
</template>
<script>
  import { getToken } from "@/utils/auth";
  import { createPPT } from "@/api/intellectPPT/intellectPPT.js";
  export default {
    name: 'IntellectPPT',
    dicts: ['theme', 'create_model', 'is_card_note'],
    data() {
      return {
        uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
        uploadData: { modeltype: 'smartPPT' },
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        form: {
          isCardNote: "false",
          theme: "auto"
        },
        btnLoad: false,
        rules: {
          query: [
            { required: true, message: '请输入PPT要求', trigger: ['blur', 'change'] },
          ],
        },
        requestType: 'text',
        fileId: ''
      }
    },
    created() {
      this.form.query =  this.$route.query && this.$route.query.content ;
    },
    methods: {
      handleSubmint() {
        this.btnLoad = true
        if (this.requestType == 'text') {
          this.form.requestType = 'text'
        } else {
          this.form = {
            requestType: 'file',
            fileId: this.fileId
          }
        }
        createPPT(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('生成成功')
            const link = document.createElement('a');
            link.href = res.msg;
            link.download = 'ppt';
            link.click();
          }
          this.btnLoad = false
        }).catch(err => {
          this.btnLoad = false
        })
      },
      handleUploadSuccess(res, file,) {
        file.id = res.data.id;
        this.fileId = res.data.id;
      },
      handleRemove(file, fileList) {
        this.fileId = ''
      },
    }
  }
</script>
<style lang="scss" scoped>
  .ck-form {
    width: 80%;
    margin: auto;
  }
  .ck-input {
    width: 50%;
  }
  .step-btn-box {
    text-align: center;
    height: 67px;
    line-height: 67px;
  }
</style>

