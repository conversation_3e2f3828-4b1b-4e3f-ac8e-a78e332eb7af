<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程名称" prop="course">
        <el-input v-model="queryParams.course" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="courseList">
      <el-table-column label="学院" prop="collageName" min-width="100" />
      <el-table-column label="专业" prop="majorName" min-width="100" />
      <el-table-column label="课程" prop="course" min-width="100">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="handleReview(scope.row)">
            {{ scope.row.course }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否联盟课" prop="isAllianceCourse" :show-overflow-tooltip="true" min-width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.isAllianceCourse=="N"?'是':'否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="scope.row.isAllianceCourse=='N'">
            <el-button size="mini" type="text" icon="el-icon-share"
              @click="handleToKnowledgeMapping(scope.row)">图谱</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getCourseList,
} from "@/api/teachingMaterials/teachingMaterials.js";

export default {
  name: "TeachingMaterials",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      courseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getCourseList(this.queryParams).then((response) => {
        this.courseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: "/teachingMaterials/addMaterials",
      });
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/teachingMaterials/coursewareManagement",
        query: { course: row.course, major: row.major },
      });
    },
    /**跳转知识图谱 */
    handleToKnowledgeMapping(row) {
      this.$router.push({
        path: "/knowledgeBase/knowledgeDomain",
        query: { courseName: row.course },
      });
    },
  },
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
