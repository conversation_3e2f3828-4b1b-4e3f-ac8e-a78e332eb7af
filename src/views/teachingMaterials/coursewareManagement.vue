<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课件名称" prop="presentationName">
        <el-input v-model="queryParams.presentationName" placeholder="请输入课件名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="presentationList">
      <el-table-column label="课程" prop="course" min-width="100" />
      <el-table-column label="课件名称" prop="presentationName" min-width="100" />
      <el-table-column label="课件教材" prop="textBookName" min-width="100" />
      <el-table-column label="课件章节" prop="chapter" min-width="100" >
        <template slot-scope="scope">
          {{ scope.row.chapter == "" ? "无" : scope.row.chapter }}
        </template>
      </el-table-column>
<!--      <el-table-column label="课件编号" prop="presentationId" min-width="100" />-->
<!--      <el-table-column label="审核状态" prop="isExamine" min-width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <span v-if="scope.row.isExamine === 0">未审核</span>-->
<!--          <span v-if="scope.row.isExamine === 1">审核通过</span>-->
<!--          <el-popover placement="right" width="300" trigger="hover" v-if="scope.row.isExamine === 2">-->
<!--            {{'审核意见：'+ scope.row.suggestion }}-->
<!--            <el-button type="text" size="small" slot="reference"-->
<!--              style="overflow: hidden;white-space: nowrap;text-overflow:ellipsis;text-align: left;color: #606266;">-->
<!--              审核不通过</el-button>-->
<!--          </el-popover>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button v-if="scope.row.authority ==0" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)">修改</el-button>
<!--          <el-button v-if="checkPermi(['teach:materials:check'])" :disabled="scope.row.isExamine !== 0" size="mini"-->
<!--            type="text" icon="el-icon-circle-check" @click="handleCheck(scope.row)">审核</el-button>-->
          <el-button v-if="scope.row.authority ==0" size="mini" type="text" icon="el-icon-delete"
            @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="审核" :visible.sync="dialogVisible">
      <el-form :model="form">
        <el-form-item label="">
          <el-radio-group v-model="form.isExamine">
            <el-radio :label="1">同意</el-radio>
            <el-radio :label="0">不同意</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input type="textarea" placeholder="请输入内容" v-model="form.suggestion" autosize />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPresentationList,
  delPresentation,
  checkCourse
} from "@/api/teachingMaterials/teachingMaterials.js";
import { checkPermi } from "@/utils/permission";
export default {
  name: "CoursewareManagement",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      presentationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        course: this.$route.query && this.$route.query.course,
        major: this.$route.query && this.$route.query.major,
      },
      dialogVisible: false,
      form: {
        isExamine: 1,
      },
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    this.queryParams.course="";
    this.queryParams.major="";
  },
  activated() {
    this.queryParams.course=this.$route.query && this.$route.query.course
    this.queryParams.major=this.$route.query && this.$route.query.major
    this.getList();
  },
  methods: {
    checkPermi,
    /** 查询列表 */
    getList() {
      this.loading = true;
      getPresentationList(this.queryParams).then((response) => {
        this.presentationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: "/teachingMaterials/addMaterials",
        query: { course: this.$route.query && this.$route.query.course, major: this.$route.query && this.$route.query.major, courseflag: true },
      });
    },
    /** 查看按钮操作 */
    handleReview(row) {
      this.$router.push({
        path: "/teachingMaterials/reviewMaterials",
        query: { id: row.id },
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: "/teachingMaterials/updateMaterials",
        query: { id: row.id, course: this.$route.query && this.$route.query.course, major: this.$route.query && this.$route.query.major, courseflag: true },
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delPresentation(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    // releaseStatusChange(releaseStatus) {
    //   return this.selectDictLabelByVal(
    //     this.dict.type.release_status,
    //     releaseStatus
    //   );
    // },
    handleCheck(row) {
      this.dialogVisible = true;
      this.form.presentionId = row.id;
    },
    handleSubmit() {
      console.log(this.form.suggestion)
      if ((this.form.isExamine == 0 && !this.form.suggestion) || (this.form.isExamine == 0 && this.form.suggestion == '')) {
        this.$message.warning("请填写审核意见");
        return;
      } else {
        checkCourse(this.form).then((res) => {
          if (res.code == 200) {
            this.$message.success("提交审核成功");
            this.handleClose()
          }
        })
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.getList();
      this.resetForm("form");
    }
  },
};
</script>
