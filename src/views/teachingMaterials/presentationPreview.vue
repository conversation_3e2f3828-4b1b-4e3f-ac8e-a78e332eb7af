<template>
  <div class="app-container ck-container">


    <div class="pagination">

      <div class="button-container">
        <button @click="changePage(-1)" class="styled-button">上一页</button>
        <button @click="changePage(1)" class="styled-button">下一页</button>
      </div>

      <label>
        {{ '页码:'+this.pptIndex+' / '+this.presentationAllpage}}
      </label>
      <div>
        <el-input type="number" placeholder="请输入页码" v-model="goPages" style="width:200px;">
          <el-button slot="append" @click="goPage">跳转</el-button>
        </el-input>
      </div>

    </div>

    <div class="pptbox" id="pptbox">
      <iframe :src="src" ref="iframe" width="100%" height="100%" style="border:0px;"></iframe>
    </div>


  </div>

</template>

<script>
import "../../../public/ppt/css/pptxjs.css";
import "../../../public/ppt/css/nv.d3.min.css";

import {
  getpptInfo,
  getppt,
} from "@/api/explorationCenter/experience.js";


export default {
  name: "wisdomSchools",
  data() {
    return {
      v: 0,
      pptIndex: 2,
      // 遮罩层
      src: '/ppt/pptx.html',
      iframeWin: {},
      pptInfo: {},
      ppts: '',
      goPages: '',
      pptPath: '',
      presentationAllpage: 0,//ppt总页数
    };
  },

  created() {
    this.getpptInfo();
  },
  mounted() {
    this.iframeWin = this.$refs.iframe.contentWindow;
    //window.addEventListener("boforeunload", this.beforeunloadHandler());
    var _this = this
    setTimeout(function () {
      _this.iframeWin.cresssated(_this.ppts);
    }, 2000)
  },
  beforeDestroy() {

  },


  methods: {

    getpptInfo() {
      const params = {
        presentationId: this.$route.query && this.$route.query.presentationId
      }
      getpptInfo(params).then((response) => {
        this.pptInfo = response.data
        this.pptIndex = 1
        this.presentationAllpage = this.pptInfo.presentationAllpage
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + ((this.currentPage && this.currentPage != this.presentationAllpage) ? this.currentPage : 1) + this.pptInfo.suffix
        this.getppt(this.pptPath)
      });
    },
    getppt(pptPath) {
      const paramss = {
        filePath: pptPath
      }
      getppt(paramss).then((response) => {
        this.ppts = response;
      });
    },


    goPage() {
      if(this.goPages <= this.presentationAllpage && this.goPages!=""){
        this.pptIndex = this.goPages;
        this.goPages="";
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex+ this.pptInfo.suffix
        this.getppt(this.pptPath)
        const _this = this
        setTimeout(function () {
          _this.iframeWin.change(_this.ppts);
        }, 1000)
      }else {
        this.$message.warning('请勿跳转错误页面');
      }

    },
    changePage(page) {
      if ((Number(this.pptIndex) + Number(page)) <= this.presentationAllpage && (Number(this.pptIndex) + Number(page)) >= 1) {
        this.pptIndex = Number(this.pptIndex) + Number(page);
        this.pptPath = this.pptInfo.presentationHttp + this.pptInfo.prefix + this.pptIndex + this.pptInfo.suffix;
        this.getppt(this.pptPath);
        const _this = this;
        setTimeout(function () {
          _this.iframeWin.change(_this.ppts);
        }, 1000);
      } else {
        this.$message.warning("没有这一页");
      }
    },
  }
};
</script>
<style lang="scss" scoped>

.button-container {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
}

.styled-button {
  background-color: #4CAF50; /* Green background */
  border: none;
  color: white;
  padding: 12px 24px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.styled-button:hover {
  background-color: #45a049; /* Darker green on hover */
}

.styled-button:active {
  background-color: #3e8e41;
}

.styled-button:focus {
  outline: none;
  box-shadow: 0 0 5px #4CAF50;
}



.ck-container {
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 84px);
  background-size: 100% 100%;
  display: flex;
  .pause-button {
    position: absolute;
    top: 600px;
    left: 170px;
    z-index: 10;
  }
  .start-button {
    position: absolute;
    top: 600px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .dialogue-button {
    position: absolute;
    top: 650px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .pagination {
    position: absolute;
    top: 700px;
    left: 50px;
    z-index: 10;
    margin: 0px;
  }
  .imgbox {
    width: 17%;
    height: 60%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 130px;
    left: 20px;
    .img {
      height: 80%;
    }
  }
  .pptbox {
    position: absolute;
    height: 100%;
    width: 80%;
    top: 30px;
    right: 0px;
  }
  .right-container {
    background: #dbe9f740;
    flex: 1;
    border-radius: 10px;
    margin-left: 20px;
    .answer-box {
      height: 72%;
      overflow-y: auto;
      border-bottom: 1px solid #e8e8e8;
      padding: 10px;
      .answer {
        width: 100%;
        height: 150px;
        overflow: auto;
      }
    }
    .ask-box {
      height: 28%;
      overflow-y: auto;
      padding: 8px;
      border-radius: 5px;
      .ask-button {
        position: absolute;
        bottom: 8px;
        right: 10px;
        margin: auto;
      }
      .talk-button {
        position: absolute;
        bottom: 8px;
        right: 60px;
        margin: auto;
      }
    }
  }
}

.subtitlesStyles {
  position: fixed; /* 相对于 pptbox 定位 */
  bottom: 3vh; /* 距离底部一定距离 */
  width: 70%; /* 宽度为全屏 */
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  background-color: rgba(0, 0, 0, 0.5); /* 灰色透明背景 */
  padding: 25px 0; /* 上下内边距 */
  margin-left: 0%;
}
.svg-icon {
  width: 16px;
  height: 16px;
}
.subtitle-content {
  color: white; /* 字幕字体颜色为白色 */
  font-size: 26px; /* 字体大小 */
  text-align: center; /* 文字居中 */
  max-width: 90% /* 限制最大宽度 */
}
</style>

