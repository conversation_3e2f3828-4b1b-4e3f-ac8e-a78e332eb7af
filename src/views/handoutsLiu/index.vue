<template>
  <div class="app-container ck-container">
    <div class="left-sidebar">
      <div class="left-sidebar-title">
        <img v-if="logo" :src="logo" class="left-sidebar-logo" />
        <h1 class="left-sidebar-lable">大模型</h1>
        <el-button class="left-sidebar-btn" icon="el-icon-plus" type="text" @click="handleAdd">新建</el-button>
      </div>
      <div class="left-sidebar-search">
        <!--        <span>调用</span>-->
        <el-select v-model="invocation" @change="getDialogueList">
          <el-option v-for="dict in dict.type.call_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </div>
      <div class="left-sidebar-his">
        <span>历史记录</span>
        <div class="left-sidebar-his-list">
          <el-table ref="multipleTable" :data="hisList" height="630" style="width: 100%" :show-header="false"
            @row-click="handleReview">
            <el-table-column label="内容" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center" width="50">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-delete" :disabled="isClickable" @click.native.stop="handleDel(scope.row)" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="right-container">

      <div class="right-container-top">
        <el-row style="margin-top: 20px" v-for="(item, index) in dialogueList" :key="index" type="flex"
          justify="center">
          <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
          <div v-if="item.issue == 'assistant'" style="position:relative; width:100%;">
            <div style="background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap;">
              <!--              {{item.content}}-->
              <div class="markdown-content" v-html="markedContent(item.content)"></div>
              <div v-show="item.imageUrls">
                <img v-for="(imageUrl, index) in item.imageUrls" :key="index" :src="imageUrl" alt="Dialogue Image"
                  style="max-width: 100%; height: auto; margin-top: 5px;">
              </div>
              <div style="height: 40px;width: 100%">
                <el-button style="float: right;margin: 5px;" size="mini" round
                @click="handleAudioControl()">停止播放
                  </el-button>
                  <el-button style="float: right;margin: 5px;" size="mini" round
                  @click="synthesizeVoice(item.content)">开始播放
                  </el-button>
                <el-button style="float: right;margin: 5px;;padding-top:5px;padding-bottom: 5px;" size="mini" round
                  @click="likeOrStomp(item,index,'2')">
                  <svg-icon :icon-class="item.likeStomp=='2'?'bad':'thumbs-down'" class-name="card-panel-icon" />
                </el-button>
                <el-button style="float: right;margin: 5px;padding-top:5px;padding-bottom: 5px;" size="mini" round
                  @click="likeOrStomp(item,index,'1')">
                  <svg-icon :icon-class="item.likeStomp=='1'?'good':'thumbs-up'" class-name="card-panel-icon" />
                </el-button>
              </div>
            </div>
            <div style="position:absolute; bottom:-23px; left:5px;"> <!-- 调整bottom值以靠近内容框 -->
              <!-- 重新生成按钮 -->
              <el-button v-if="!loadSendBtn && index == dialogueList.length-1" size="mini" type="text"
                class="copy-button" @click="handleRegen(index)"
                style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 5px;">重新生成</el-button>
            </div>
            <!-- 按钮容器放置在右下角，使用更精确的定位 -->
            <div style="position:absolute; bottom:-23px; right:5px;"> <!-- 调整bottom值以靠近内容框 -->
              <!-- 复制内容按钮 -->
              <el-button size="mini" type="text" icon="el-icon-document-copy" class="copy-button"
                @click="copyContent(item.content)"
                style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-right: 5px;">复制</el-button>
            </div>
          </div>
          <div v-if="item.issue == 'user'" style="width:100%;">
            <div
              style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}
            </div>
          </div>
          <!-- <el-input v-model=" item.content" type="textarea" autosize readonly /> -->
          <img v-if="item.issue == 'user'" :src="user" style="width: 33px; height: 33px; margin-left: 10px;" />
        </el-row>

        <!-- 相关问题展示 -->
        <div
          v-if="relatedIssuesList && relatedIssuesList.length && dialogueList[dialogueList.length - 1].issue === 'assistant'"
          style="margin-top: 30px;">
          <span style="padding-left:40px;color:#7d83c5;font-size: 14px;line-height: 26px;"> 你可以继续问我： </span>
          <ul>
            <li v-for="(issue, index) in relatedIssuesList" :key="index" style="list-style-type: none; cursor: pointer;"
              @click="askNewQuestion(issue,dialogueList.length-1)">
              <span class="issue-text">{{issue}}</span>
            </li>
          </ul>
        </div>

        <div class="toggle-textbox" @click="toggleTextBox">
          <div class="icon-wrapper">
            <i v-if="!showTextBox" class="el-icon-plus"></i>
            <i v-else class="el-icon-minus"></i>
          </div>
        </div>

        <!-- 遮罩层 -->
        <div v-show="showTextBox" class="overlay"></div>
        <!-- 添加大文本框及操作按钮，使用v-show控制显示 -->
        <div v-show="showTextBox" class="full-textbox">
          <el-input v-model="fullTextContent" type="textarea" :autosize="{ minRows: 10, maxRows: 20 }" maxlength="4000"
            show-word-limit />
          <div class="textbox-actions">
            <el-button size="small" type="primary" class="copy-button"
              @click="copyContent(fullTextContent)">复制</el-button>
            <el-button size="small" type="info" @click="downloadAsTXT(fullTextContent)">下载</el-button>
            <el-button size="small" type="warning" @click="preserveFullTextContent(fullTextContent)">保存</el-button>
            <el-button size="small" v-if="getExplicitImplicit()" type="success"
              @click="generatePpt(fullTextContent)">生成ppt</el-button>
            <!-- 这里需要实现保存逻辑 -->
          </div>
        </div>

      </div>

      <div class=" right-container-bottom">
        <div
          style="border:1px solid #efefff;margin-top: 10px;padding: 10px 10px 2px 10px;background-color: #fff;border-radius: 12px ;">
          <el-tooltip class="item" effect="light" :content="translationFlag?'点击停止说话':'点击说话'" placement="top">
            <el-button style="margin: 5px;" size="mini" round
                :icon="translationFlag?'el-icon-microphone':'el-icon-turn-off-microphone'"
                @click="voiceASRType === 1 ? baiduASR() : translation()">
                {{translationFlag ? '停止' : '语音'}}
              </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="light" :content="playFlag ?  '点击停止播放': '点击播放'" placement="top">
            <el-button style="margin: 5px;" size="mini" round @click="autoPlay">
              <svg-icon :icon-class="playFlag ?  'voice': 'voiceClose'" />
              {{playFlag ?  ' 停播': ' 播放'}}
            </el-button>
          </el-tooltip>
          <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
            show-word-limit @keydown.native="handleKeyCode($event)" @blur="saveEditedContent" placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
          <el-button style="float: right" type="primary" size="mini" round icon="el-icon-s-promotion" @click="ask"
            :loading="loadSendBtn" />
        </div>
      </div>

      <audio :src="computedAudioSrc" controls :playsinline="true" style="display: none;" ref="audio"
        @ended="continuePlay">
        您的浏览器不支持音频播放。
      </audio>

    </div>
  </div>
</template>

<script>
import logoImg from "@/assets/logo/logo1.png";
import asks from "@/assets/logo/asks.png";
import user from "@/assets/logo/user.png";
import IatRecorder from '@/assets/js/IatRecorder.js'
import TtsRecorder from "@/assets/js/tts_xunfei/audio.js";
import { AudioRecorder } from "@/assets/js/BaiDuASR.js";
import axios from "axios";
import qs from "qs";
import marked from 'marked';
import { getToken } from "@/utils/auth";
import speechRecognitionService from '@/utils/speechRecognitionService';
import { eventBus } from '@/utils/eventBus';
import {
  getDialogueList,
  addDialogue,
  updateDialogue,
  delDialogue,
  getDialogue,
  preserveFullTextContent,
  getBaiDuToken,
  getId,
  likeOrStomp,
  relatedIssues
} from "@/api/explorationCenter/experience.js";
import {
  getUserVoiceRole,
  DBTTS,
  getSystemTTSChoose,
} from "@/api/system/voiceRole.js";
import { mapState, mapActions } from 'vuex';
import Cookies from "js-cookie";
const iatRecorder = new IatRecorder('zh_cn', 'mandarin')
const ttsRecorder = new TtsRecorder();
export default {
  dicts: ['call_type','system_websocket_param','user_voiceasr_choose','user_voice_choose','sys_qa_wakeup',],
  name: "ExplorationCenter",
  data() {
    return {
      logo: logoImg,
      asks: asks,
      user: user,
      showTextBox: false, // 控制文本框是否显示
      fullTextContent: '', // 用于存储大文本框的内容，初始化为空
      promptOptions: [],
      hisList: [],
      invocation: "model",
      menuRouting: "",
      relatedIssuesList: [],
      dialogueList: [],
      dialogueNum: true,
      dialogueResult: { content: '正在回答，请稍等···' }, //
      isClickable: false,
      loadSendBtn: false,
      content: "",
      id: "",
      translationFlag: false,

      per: "1",
      playFlag: false,//自动播放
      isPlayAudio: false,//正在播放
      token: "", //使用ai的accessToken
      audioArr: [], //音频数组
      segments: [], //
      currentIndex: 0, //音频播放第几条
      isPlayFinish: false,
      isStopFinsh: true,
      speed: 5, // 语速
      pitch: 5, // 语调
      volum: 5,  // 音量
      pid: '',
      AsrintervalId: null,
      baiduASRParam:{
        appId: '',
        appKey: '',
        dev_pid: '',
        name: this.$route.path
      },
      contenMark:0,
      finalText: "", // 累加的最终结果
      timer: null, // 定时器
      voiceASRType: 1, // 识别服务商
      voiceType: 1, // 语音合成服务商
      userVoiceChoose: "",// 语音合成服务商
      isUnsupported: false,
      selectedLanguage: 'zh-CN',
      autoRestart: true,
      
      audioContext: null,        // 音频上下文
      audioAnalyser: null,       // 音频分析器
      audioDataArray: null,      // 音频数据数组
      silenceTimer: null,        // 静音计时器
      silenceThreshold: 10,      // 静音阈值（0-255之间的值）
      silenceDuration: 2000,     // 静音持续时间（毫秒）
      isMonitoringVolume: false, // 是否正在监控音量
      lastVolumeTime: 0,         // 上次检测到声音的时间
      autoSendEnabled: true      // 是否启用自动发送
    };
  },
  created() {
    this.getDialogueList();

    this.getToken();
    this.getVoiceRole();
    this.initRecorder();
    this.getSystemTTSChoose();
  },
  beforeDestroy() {
    //关闭页面前停止
    this.clearAudio();
  },


  // -----------------------------------------------------------------------
  computed: {
    computedAudioSrc() {
      return this.audioArr[this.currentIndex];
    },
    // 用于将vuex存储中的状态 (state) 映射到组件的计算属性中
    // speechRecognition 表示vuex中的模块名称 数组是映射的状态名称
    ...mapState('speechRecognition', [
      'isListening',
      'transcript',
      'interimTranscript'
    ])
  },


  watch: {
    computedAudioSrc(newSrc) {
      if (newSrc && this.isPlayAudio && this.playFlag) {
        this.$refs.audio.addEventListener(
          'loadeddata',
          () => {
            this.$refs.audio.play();
            this.isplaying = true;
          },
          { once: true }
        );
      }
    },
    translationFlag(newVal,oldVal){
      if (newVal){
        this.contenMark=1;
        this.contentArray=[];
      }
      if (!newVal){
        this.contenMark=0;
      }
    }
  },

  activated() { },
  mounted(){
    setTimeout(() => {
      this.getVoiceASRChoose()
      // 注册语音命令
      this.registerSpeechCommands();
    }, 1500);
  },

  methods: {

    ...mapActions('speechRecognition', [
      'registerCommands',
      'clearTranscript'
    ]),

    // 语音唤醒
    registerSpeechCommands() {
      if (!this.dict || !this.dict.type || !this.dict.type.sys_qa_wakeup) {
        console.warn('字典中未找到唤醒词配置');
        return;
      }
      console.log('从字典中加载唤醒词...');
      // 创建命令对象
      const wakeupCommands = {};
      // 遍历字典中的唤醒词
      this.dict.type.sys_qa_wakeup.forEach(item => {
        // 将每个唤醒词注册为命令
        wakeupCommands[item.label] = () => {
          console.log(`检测到唤醒词: ${item.label}`);
          // 根据字典中的值执行不同的方法
          if (item.value === 'handleAdd') {
            // 执行新建对话方法
            this.handleAdd();
            if(this.voiceASRType === 1) {
              this.baiduASR();
            } else {
              this.translation();
            }
          } else if (item.value === 'init') {
            // 执行初始化操作 - 设置内容并开启语音识别
            this.content = item.label; // 使用唤醒词作为内容
            if(this.voiceASRType === 1) {
              this.baiduASR();
            } else {
              this.translation();
            }
          }
          // 开始监控麦克风音量
          if (this.autoSendEnabled) {
            this.startVolumeMonitoring();
          }
        };
      });
      
      // 注册所有唤醒词命令
      if (Object.keys(wakeupCommands).length > 0) {
        this.registerCommands({
          context: this.$route.name || 'explorationCenter',
          commands: wakeupCommands
        });
        
        console.log(`成功注册 ${Object.keys(wakeupCommands).length} 个唤醒词`);
      } else {
        console.warn('未找到有效的唤醒词配置');
      }
    },
    // 获取系统语音合成选择
    getSystemTTSChoose(){
      getSystemTTSChoose().then(res => {
        this.userVoiceChoose = res.msg;
      })

    },
    // 获取语音识别调用
    getVoiceASRChoose() {
      // 通用函数：根据字典值设置类型
      const setTypeFromDict = (dict, key) => {
        dict.forEach(item => {
          if (item.label === "baidu") {
            this[key] = 1;
          } else if (item.label === "xfSpark") {
            this[key] = 0;
          }
        });
      };
      // 设置 voiceASRType
      setTypeFromDict(this.dict.type.user_voiceasr_choose, "voiceASRType");
      // 设置 voiceType
      setTypeFromDict(this.dict.type.user_voice_choose, "voiceType");
    },
    markedContent(content) {
      const htmlContent = marked.parse(content);
      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div');
      tempElement.innerHTML = htmlContent;
      // 递归函数来移除空白文本节点
      function removeEmptyTextNodes(node) {
        if (node.nodeType === Node.TEXT_NODE && !node.textContent.trim()) {
          node.parentNode.removeChild(node);
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          for (let i = node.childNodes.length - 1; i >= 0; i--) {
            removeEmptyTextNodes(node.childNodes[i]);
          }
        }
      }
      // 遍历所有块级元素并移除空白文本节点
      const blockElements = ['p', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'blockquote', 'hr'];
      blockElements.forEach(tagName => {
        const elements = tempElement.querySelectorAll(tagName);
        elements.forEach(element => {
          removeEmptyTextNodes(element);
        });
      });
      // 返回处理后的HTML内容
      return tempElement.innerHTML;
    },
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/addLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      this.isClickable = true;
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            this.getDialogueList();
            if (this.playFlag) {
              this.playAudio(s);
            }
            this.isClickable = false;
            this.getHeight()
          } else {
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight()
          }

        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },
    //相关问题
    askNewQuestion(issue, index) {
      this.isClickable = true;
      this.stopAudio()
      this.loadSendBtn = true;
      this.dialogueList.push({ content: issue, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      this.getHeight()
      const param = {
        invocation: this.invocation,
        //promptId: this.promptId,
        content: issue,
        id: this.id,
        menuRouting: this.$route.path,
        language: Cookies.get("voiceType"),
      };

      this.content = '';
      this.relatedIssuesList = [];
      //
      this.updateDialogue2(param).then(() => {
        // 确保对话列表中至少有两个元素
        if (this.dialogueList.length >= 2) {
          const quae = {
            content: this.dialogueList[this.dialogueList.length - 2].content,
          };

          // 调用 relatedIssues 函数并传递 quae 对象
          relatedIssues(quae).then((res) => {
            if (res.code === 200) {
              this.relatedIssuesList = res.data;
            }
          });
        }
      })
        .catch(error => {
          this.isClickable = false;
          console.error('Error:', error)});
    },
    getImgUrl(pptPath) {
      let baseUrl = window.location.origin
      var imgData;
      if (pptPath.includes('/ruoyi/')) {
        // 替换路径中的 ruoyi/ 之前的部分
        const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

        if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
          imgData = 'http://127.0.0.1:9215' + finalPath
          // console.log('Final baseUrl:', baseUrl)
        } else {
          imgData = baseUrl + finalPath
        }
      }
      return imgData;

    },
    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      this.isClickable = true;
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlag) {
              this.playAudio(s);
            }
            this.isClickable = false;
            this.handleReview({ id: this.id })
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight()
          }
        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },

    // 获取发言人
    getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          this.speed = res.data.voiceSpeed;
          this.pitch = res.data.voicePitch;
          this.volume = res.data.voiceVolume;
        })
      },
    getExplicitImplicit() {
      return this.$route.path === '/explorationCenter6/explorationCenter01';
    },
    generatePpt(content) {
      this.$router.push({
        path: '/intellectSmartPpt/index',
        query: { content: content },
      });
    },
    handlePlay() {
      if (this.playFlag) {
        this.playFlag = !this.playFlag
        this.pause()
      } else {
        this.playFlag = !this.playFlag
      }
    },
    play(content) {
      let resText = this.markdownToPlainText(content)
      //要合成的文本
      ttsRecorder.setParams({
        // 文本内容
        text: resText,
        // 角色
        voiceName: this.per,
        // 语速
        speed: this.speed,
        // 音量
        voice: this.volume,
        // 音高
        pitch: this.pitch,
      });
      ttsRecorder.start();
    },
    pause() {
      ttsRecorder.stop();
    },


    initRecorder(){
      speechRecognitionService.initRecorder((blob, encTime) => {
        if (speechRecognitionService.websocket && speechRecognitionService.isRecording) {
          speechRecognitionService.websocket.send(blob);
        }
      });
    },
    baiduASROptions(){
      const type = Cookies.get("voiceType")
      if(type === 'CN'){
        this.baiduASRParam.dev_pid = 1537
      }else if(type === 'EN'){
        this.baiduASRParam.dev_pid = 1737
      }else{
        this.baiduASRParam.dev_pid = 1537
      }
      this.dict.type.system_websocket_param.forEach(item => {
        console.log(item)
        if(item.label === 'appId'){
          this.baiduASRParam.appId = Number(item.value)
        }
        if(item.label === 'appKey'){
          this.baiduASRParam.appKey = item.value
        }
      })
    },
    closeASR(){
      this.translationFlag = false;
      this.finalText = "";
      // 停止音量监控
      this.stopVolumeMonitoring();
      if(this.voiceASRType === 1){
        speechRecognitionService.closeWebsocket();
        eventBus.$off(this.baiduASRParam.name);
        if (this.timer) {
          clearTimeout(this.timer); // 清除定时器
          this.timer = null;
        }
        return;
      }
      iatRecorder.stop();
    },
    baiduASR(){
      this.baiduASROptions();
      if(this.translationFlag){
        this.closeASR();
      }else{
        this.translationFlag = true;
        speechRecognitionService.startSpeechRecognition(error => {
        if (error) {
          this.$message.error('麦克风未打开！');
          switch (error.message || error.name) {
            case 'PERMISSION_DENIED':
            case 'PermissionDeniedError':
              console.info('用户拒绝提供信息。');
              break;
            case 'NOT_SUPPORTED_ERROR':
            case 'NotSupportedError':
              console.info('浏览器不支持硬件设备。');
              break;
            case 'MANDATORY_UNSATISFIED_ERROR':
            case 'MandatoryUnsatisfiedError':
              console.info('无法发现指定的硬件设备。');
              break;
            default:
              console.info('无法打开麦克风。异常信息:' + (error.code || error.name));
              break;
          }
        }
      }, this.baiduASRParam);
      eventBus.$on(this.baiduASRParam.name, (result) => {
        console.log("识别结果:", result);

        if (result.type === "MID_TEXT"){
          this.content = this.finalText + result.text;
        }
        if (result.type === "FIN_TEXT"){
          console.log("最终结果:", this.finalText);
           // 累加最终结果
          this.finalText += result.text;
          this.content = this.finalText;
          console.log("最终结果:", this.content);
        }
      });
      // 设置 60 秒后自动关闭录音
      this.timer = setTimeout(() => {
        speechRecognitionService.closeWebsocket(); // 调用关闭方法
        eventBus.$off(this.baiduASRParam.name) // 清除监听器
        this.translationFlag = false; // 更新标识
        this.finalText = "";
        this.$message.info('录音已自动停止。');
      }, 60000);
      }
    },

    saveEditedContent() {
      // 用户编辑完成后同步最终内容
      this.finalText = this.content;
      console.log("保存用户编辑后的结果:", this.finalText);
    },

    // baiduASR() {
    //   // 停止录音后结果返回
    //   clearInterval(this.AsrintervalId);
    //   this.AsrintervalId = setInterval(() => {
    //     if (this.translationFlag) {
    //       AudioRecorder.monitorVolume().then(result => {
    //         // console.log(result);
    //         this.content += result.content;
    //         this.translationFlag = result.flag;
    //         clearInterval(this.AsrintervalId);
    //       })
    //     }
    //   }, 500);
    //   // 开始录音
    //   if (!this.translationFlag) {
    //     this.translationFlag = true;
    //     AudioRecorder.startRecording().then(result => {
    //       this.content += result.content;
    //       this.translationFlag = result.flag;
    //     })
    //   } else {
    //     this.translationFlag = false;
    //     AudioRecorder.stopRecording().then(result => {
    //       this.content += result.content;  // 将识别结果赋值给 Vue 的 content
    //     })
    //   }
    // },

    translation() {
      if (this.translationFlag) {
        iatRecorder.stop();
        this.translationFlag = false;
      } else {
        let language = ""
        const type = Cookies.get("voiceType")
        if(type === 'CN'){
          language = "zh_cn"
        }else if(type === 'EN'){
          language = "en_us"
        }else{
          language = "zh_cn"
        }
        iatRecorder.setParams({
          language: language
        })
        iatRecorder.start()
        this.translationFlag = true;
        iatRecorder.onTextChange = (text) => {
          let inputText = text;
          this.content = inputText.substring(0, inputText.length - 1); //文字处理，因为不知道为什么识别输出的后面都带‘。’，这个方法是去除字符串最后一位
        };
      }
    },

    toggleTextBox() {
      this.showTextBox = !this.showTextBox;
    },
    getDialogueList() {
      const param = {
        menuRouting: this.$route.path,
        invocation: this.invocation,
      };
      getDialogueList(param).then((res) => {
        this.hisList = res.data;
      });
    },

    handleAdd() {
      this.dialogueNum = true;
      this.dialogueList = [];
      this.content = "";
      this.id = "";
      this.relatedIssuesList = [];
    },
    async getId() {
      await getId()
        .then((res) => {
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    //请求后端
    async ask() {
      if(this.translationFlag){
        this.closeASR();
      } // 关闭语音输入
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }
      this.isClickable = true;
      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.getHeight()
      //首次发请求
      if (this.dialogueNum) {
        await this.getId();
        //后端ai询问
        const param = {
          invocation: this.invocation,
          content: this.content,
          menuRouting: this.$route.path,
          id: this.id,
          language: Cookies.get("voiceType"),
        };

        this.token = getBaiDuToken().then(res => {
          this.token = res.token;
        }).catch(error => {
          console.error('Failed to get token:', error);
        });

        //置空content
        this.content = ''
        this.relatedIssuesList = [];
        //发送请求
        await this.addDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };

            // 调用 relatedIssues 函数并传递 quae 对象
            relatedIssues(quae).then((res) => {
              if (res.code === 200) {
                this.relatedIssuesList = res.data;
              }
            });
          }
        }).catch(error => {
          this.isClickable = false;
          console.error('添加对话时出错:', error);
        });
      } else {
        //
        this.stopAudio()
        const param = {
          invocation: this.invocation,
          //promptId: this.promptId,
          content: this.content,
          id: this.id,
          menuRouting: this.$route.path,
          language: Cookies.get("voiceType"),
        };

        this.content = ''
        //
        this.relatedIssuesList = [];
        //
        this.updateDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };

            // 调用 relatedIssues 函数并传递 quae 对象
            relatedIssues(quae).then((res) => {
              if (res.code === 200) {
                this.relatedIssuesList = res.data;
              }
            });
          }
        })
          .catch(error => {
            this.isClickable = false;
            console.error('Error:', error)});
      }

    },


    //获取token
    async getToken() {
      //console.log("获取token ")
      const res = await getBaiDuToken();
      // console.log("12"+res.token)
      this.token = res.token;
      // console.log("13"+this.token)
    },

    // 添加一个统一的语音合成方法
    synthesizeVoice(content) {
      // 停止当前正在播放的音频
      this.stopAudio();

      // 根据 userVoiceChoose 选择不同的语音合成方法
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.playAudio(content);
          break;
        case "xfSpark": // 讯飞语音
          this.play(content);
          break;
        case "doubao": // 豆包语音
          this.playAudioFromUrl(content);
          break;
        default:
          // 默认使用百度语音
          this.playAudio(content);
          break;
      }
    },
    // 统一的暂停方法
    handleAudioControl() {
      switch(this.userVoiceChoose) {
        case "baidu": // 百度语音
          this.stopAudio();
          break;
        case "xfSpark": // 讯飞语音
          this.pause();
          break;
        case "doubao": // 豆包语音
          this.stopAudio();
          break;
        default:
          // 默认使用百度语音
          this.stopAudio();
          break;
      }
    },
    // 自动播放
    autoPlay() {
      //console.log("自动播放 ")
      //如果已是自动播放  关闭播放 改变状态
      if (this.playFlag) {
        this.playFlag = !this.playFlag;

        this.isPlayAudio = false
        this.$refs.audio.pause();
        //清除音频src []
        this.clearAudio();
        // this.isplaying = false
      } else {
        this.playFlag = !this.playFlag;
        this.isPlayAudio = false
      }
    },

    //停止播放
    async stopAudio() {
      if (this.isStopFinsh) {
        this.isStopFinsh = false;
        this.$refs.audio.pause();
        // this.isplaying = false;
        this.isPlayAudio = false;
        this.isPlayFinish = true;
        this.clearAudio();
      } else {
        setTimeout(() => {
          this.isStopFinsh = true;
        }, 500);
      }
    },


    //封装异步  文本转音频   播放音频
    async playAudio(content) {
      if (this.isPlayAudio) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true

      //提前获取token 并赋值
      const res = await getBaiDuToken();
      this.token = res.token;

      this.isPlayFinish = false
      this.textToAudio2(content, this.token)

      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isplaying = true;
      this.isStopFinsh = true
      //this.isplaying = true;
    },

    async playAudioFromUrl(content) {
      if (this.isPlayAudio && !this.txtToImage) {
        return;
      }
      this.clearAudio()
      this.isPlayAudio = true
      this.isPlayFinish = false
      this.synthesizeSpeech(content)
      this.currentIndex = 0
      await this.$refs.audio.play()
      this.isStopFinsh = true
    },

    // 每段音频结束后调用此函数播放下一段
    continuePlay() {
      //console.log("每段音频结束后调用此函数播放下一段  ")
      this.currentIndex++;
      if (this.currentIndex < this.audioArr.length) {
        setTimeout(() => {
          this.$refs.audio.load();
          this.$refs.audio.play().catch((error) => {
            console.error('Failed to play:', error);
          });
        }, 100);
      } else {
        this.isplaying = false;
        this.isPlayAudio = false;
      }
    },

    // 文本转语音  提供文本转语音
    async textToAudio2(text, token) {
      const tex = this.markdownToPlainText(text);
      this.segments = this.splitTextByPunctuation(tex, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: this.per,
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("https://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          responseType: "blob",
        });
        // console.log(res.data)
        if (this.isPlayFinish) {
          // console.log("结束00赋值")
          this.clearAudio()
          return;
        }
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    // 豆包语音合成方法，使其接收内容参数
    synthesizeSpeech(content) {
      const tex = this.markdownToPlainText(content);
      this.segments = this.splitTextByPunctuation(tex, 55);
      this.isPlayFinish = false;
      let Index = 0; // 添加一个索引来跟踪当前处理的段落
      const processSegment = () => {
        if (Index >= this.segments.length || this.isPlayFinish) {
          return; // 如果处理完所有段落或者标记为完成，则停止处理
        }
        const text = this.segments[Index].trim();
        if (text === "") {
          Index++;
          processSegment(); // 如果文本为空，跳过当前段落
          return;
        }

        if (!this.isAudioLoading) {
          this.isAudioLoading = true; // 设置音频正在加载的标志

          const params = {
            content: text,
            voiceType: this.per,
            speed: this.speed,
          };

          DBTTS(params).then((response) => {
            if (response.data && response.data.audioData) {
              const audioBase64 = response.data.audioData;
              const binaryString = window.atob(audioBase64);
              const len = binaryString.length;
              const bytes = new Uint8Array(len);
              for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              const audioBlob = new Blob([bytes], { type: 'audio/mp3' });
              const audioUrl = URL.createObjectURL(audioBlob);
              this.audioArr.push(audioUrl); // 将生成的音频 URL 添加到播放列表

              Index++; // 移动到下一个段落
              this.isAudioLoading = false; // 重置音频加载标志
              processSegment(); // 递归调用处理下一个段落
            } else {
              throw new Error("网络不佳请稍后再试");
            }
          }).catch(error => {
            this.$message.error(`网络不佳请稍后再试: ${error.message}`);
            console.error("语音合成请求失败:", error);
            Index++;
            this.isAudioLoading = false;
            processSegment(); // 即使出错也继续处理下一个段落
          });
        }
      };

      processSegment(); // 开始处理第一个段落
    },

    clearAudio() {
      // console.log("clearAudio  ")
      this.$refs.audio.load(); // 重新加载音频以反映清除后的状态
      this.currentIndex = 0
      this.audioArr = []
    },


    markdownToPlainText(markdown) {
    if (!markdown) return '';

    // 移除 Markdown 标题
    markdown = markdown.replace(/^#+\s(.+)/gm, '$1');

    // 移除 Markdown 图片和链接
    markdown = markdown.replace(/!\[.*?\]\(.*?\)/g, '');
    markdown = markdown.replace(/\[.*?\]\(.*?\)/g, '');

    // 移除 Markdown 粗体和斜体
    markdown = markdown.replace(/\*\*(.*?)\*\*/g, '$1'); // 粗体
    markdown = markdown.replace(/\*(.*?)\*/g, '$1');     // 斜体
    markdown = markdown.replace(/__(.*?)__/g, '$1');     // 粗体
    markdown = markdown.replace(/_(.*?)_/g, '$1');       // 斜体

    // 移除 Markdown 代码块和行内代码
    markdown = markdown.replace(/```[\s\S]*?```/g, '');
    markdown = markdown.replace(/`(.*?)`/g, '$1');

    // 移除 Markdown 分割线
    markdown = markdown.replace(/-{3,}/g, '');

    // 移除 Markdown 列表
    markdown = markdown.replace(/^\s*[-*+]\s+/gm, '');
    markdown = markdown.replace(/^\d+\.\s+/gm, '');

    // 移除 Markdown 引用
    markdown = markdown.replace(/^>\s+/gm, '');

    // 移除 Markdown 表格
    markdown = markdown.replace(/\|.*?\|/g, '');
    markdown = markdown.replace(/-\|/g, '');

    // 移除多余的换行和空格
    markdown = markdown.replace(/\n{2,}/g, '\n');
    markdown = markdown.trim();

    return markdown;
},


    // 拆分文本
    splitTextByPunctuation(text, maxLength) {
      // 定义标点符号和次级标点符号的正则表达式
      const punctuation = /[。！？；]/;
      const secondaryPunctuation = /[ ，]/;

      let result = [];

      while (text.length > 0) {
        // 匹配文本中的主要标点符号
        let match = punctuation.exec(text);

        // 如果找到标点符号
        if (match) {
          let segment = text.slice(0, match.index + 1);

          // 如果当前片段长度不超过最大长度
          if (segment.length <= maxLength) {
            if (segment.trim().length > 0) {
              result.push(segment.trim());
            }
            text = text.slice(match.index + 1);
          } else {
            // 如果当前片段长度超过最大长度
            while (segment.length > maxLength) {
              // 查找次级标点符号
              let secondaryMatch = secondaryPunctuation.exec(segment);

              if (secondaryMatch && secondaryMatch.index < maxLength) {
                let subSegment = segment.slice(0, secondaryMatch.index + 1);
                if (subSegment.trim().length > 0) {
                  result.push(subSegment.trim());
                }
                segment = segment.slice(secondaryMatch.index + 1);
              } else {
                let toAdd = segment.slice(0, maxLength).trim();
                if (toAdd.length > 0) {
                  result.push(toAdd);
                }
                segment = segment.slice(maxLength);
              }
            }

            // 处理最后剩下的部分
            if (segment.trim().length > 0) {
              result.push(segment.trim());
            }
            text = text.slice(match.index + 1);
          }
        } else {
          // 如果没有找到标点符号，直接按最大长度分割文本
          while (text.length > maxLength) {
            let part = text.slice(0, maxLength).trim();
            if (part.length > 0) {
              result.push(part);
            }
            text = text.slice(maxLength);
          }
          // 处理最后剩下的部分
          if (text.trim().length > 0) {
            result.push(text.trim());
          }
          text = "";
        }
      }

      return result;
    },




    //根据已有的文本转语音   dialogueResult
    async textToAudioBytxt(token) {
      //console.log("根据已有的文本转语音   dialogueResult")
      // console.log(this.dialogueResult.content)

      this.segments = this.splitTextByPunctuation(this.dialogueResult.content, 55)

      for (const text of this.segments) {
        const option = {
          tex: text,
          tok: token,
          cuid: `${Math.floor(Math.random() * 1000000)}`,
          ctp: "1",
          lan: "zh",
          per: "1",
          spd: this.speed,
          pit: this.pitch,
          vol: this.volume
        };

        const res = await axios.post("http://tsn.baidu.com/text2audio", qs.stringify(option), {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          responseType: "blob",
        });
        this.audioArr.push(URL.createObjectURL(res.data));
        //   audioSrc.value = URL.createObjectURL(res.data);
      }
    },

    preserveFullTextContent() {
      const param = {
        id: this.id,
        fullTextContent: this.fullTextContent,
      };
      preserveFullTextContent(param).then(res => {
        if (res.code === 200) {
          this.$message.success('保存成功')
        }
      })
    },

    handleReview(row) {
      if (this.isClickable) {
        return;
      }
      this.relatedIssuesList = [];
      getDialogue(row.id).then((res) => {
        this.id = row.id;
        this.fullTextContent = res.data.fullTextContent;
        this.dialogueList = res.data.dialogueDetailsList;
        for (let i = 0; i < this.dialogueList.length; i++) {
          let imageUrls = [];
          const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
          let match;
          let content = this.dialogueList[i].content;

          // 解析图片路径
          while ((match = imageRegex.exec(content)) !== null) {
            const imagePath = match[2] || match[1]; // 获取匹配的图片路径
            imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
          }

          // 更新对话内容，排除图片路径
          const contentWithoutImages = content.replace(imageRegex, (match, p1, p2) => {
            return ''; // 替换匹配的部分为空字符串
          });

          // 移除多余的符号
          const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');

          this.$nextTick(() => {
            this.$set(this.dialogueList, i, { ...this.dialogueList[i], content: cleanedContent });
            // 如果有图片URL，则更新对话项的imageUrls属性
            if (imageUrls.length > 0) {
              this.$set(this.dialogueList, i, { ...this.dialogueList[i], imageUrls });
              console.log("imageUrls set:", this.dialogueList[i].imageUrls);
            }
          });

        }
        this.dialogueNum = false;
        this.getHeight()
        this.haveImage = true
      });
    },
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除对话记录？")
        .then(function () {
          return delDialogue(row.id);
        })
        .then(() => {
          this.getDialogueList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
    },
    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    },



    /** 字符串处理 */
    getSecondSlash(str) {
      // 使用lastIndexOf找到最后一个'/'的位置
      var lastSlashIndex = str.lastIndexOf("/");

      // 如果找到了'/'
      if (lastSlashIndex !== -1) {
        // 提取从开始到'/'前的内容
        return str.substring(0, lastSlashIndex);
      } else {
        // 如果没有找到'/'，返回原字符串
        return str;
      }
    },

    // 下载内容为TXT文件
    downloadAsTXT(content) {
      const blob = new Blob([content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = '教学讲义.txt';
      a.click();
      window.URL.revokeObjectURL(url);
    },

    copyContent(content) {
      let cleanedContent = this.cleanMarkdown(content);
      const textarea = document.createElement('textarea');
      textarea.value = cleanedContent;
      document.body.appendChild(textarea);
      textarea.select();

      try {
        // 尝试执行复制操作
        const success = document.execCommand('copy');
        if (success) {
          this.$message.success('复制成功')
        } else {
          this.$message.error('复制失败')
        }
      } catch (error) {
        this.$message.error('复制失败:', error);
      }

      document.body.removeChild(textarea);
    },
    cleanMarkdown(markdown) {
      // 移除Markdown格式
      return markdown
        .replace(/\*\*(.*?)\*\*/g, '$1')  // 移除粗体
        .replace(/\*(.*?)\*/g, '$1')       // 移除斜体
        .replace(/^(#+)\s/gm, '')          // 移除标题
        .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，只保留链接文字
        // 添加更多正则表达式以处理其他Markdown格式
        ;
    },
    handleRegen(index) {
      this.isClickable = true;
      this.stopAudio()
      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.dialogueList[index - 1].content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.getHeight()
      const param = {
        invocation: this.invocation,
        content: this.dialogueList[index - 1].content,
        id: this.id,
        menuRouting: this.$route.path,
        language: Cookies.get("voiceType"),
      };

      this.content = ''
      this.relatedIssuesList = [];
      //
      this.updateDialogue2(param).then(() => {
        // 确保对话列表中至少有两个元素
        if (this.dialogueList.length >= 2) {
          const quae = {
            content: this.dialogueList[this.dialogueList.length - 2].content,
          };

          // 调用 relatedIssues 函数并传递 quae 对象
          relatedIssues(quae).then((res) => {
            if (res.code === 200) {
              this.relatedIssuesList = res.data;
            }
          });
        }
      })
        .catch(error => {
          this.isClickable = false;
          console.error('Error:', error)});
    },
    likeOrStomp(item, index, type) {
      const param = {
        id: item.id,
        likeStomp: type
      }
      likeOrStomp(param).then(res => {
        if (res.code == 200) {
          this.dialogueList[index].likeStomp = type
        }
      })
    },
    getHeight() {
      this.$nextTick(() => {
        var container = document.querySelector('.right-container-top');
        container.scrollTop = container.scrollHeight;
      })
    },
    // 开始监控麦克风音量
    startVolumeMonitoring() {
      if (!navigator.mediaDevices || this.isMonitoringVolume) return;
      
      console.log("开始监控麦克风音量");
      this.isMonitoringVolume = true;
      
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // 请求麦克风权限
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          // 创建媒体流源
          const source = this.audioContext.createMediaStreamSource(stream);
          
          // 创建分析器
          this.audioAnalyser = this.audioContext.createAnalyser();
          this.audioAnalyser.fftSize = 256;
          
          // 连接源到分析器
          source.connect(this.audioAnalyser);
          
          // 创建数据数组
          this.audioDataArray = new Uint8Array(this.audioAnalyser.frequencyBinCount);
          
          // 开始分析音量
          this.analyzeVolume();
          
          // 保存流的引用，以便稍后停止
          this.microphoneStream = stream;
        })
        .catch(err => {
          console.error("获取麦克风权限失败:", err);
          this.isMonitoringVolume = false;
        });
    },
    
    // 停止监控麦克风音量
    stopVolumeMonitoring() {
      if (!this.isMonitoringVolume) return;
      
      console.log("停止监控麦克风音量");
      
      // 清除静音检测定时器
      if (this.silenceTimer) {
        clearTimeout(this.silenceTimer);
        this.silenceTimer = null;
      }
      
      // 停止音频分析
      if (this.requestAnimationId) {
        cancelAnimationFrame(this.requestAnimationId);
        this.requestAnimationId = null;
      }
      
      // 关闭麦克风
      if (this.microphoneStream) {
        this.microphoneStream.getTracks().forEach(track => track.stop());
        this.microphoneStream = null;
      }
      
      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close().catch(e => console.error(e));
        this.audioContext = null;
      }
      
      this.audioAnalyser = null;
      this.audioDataArray = null;
      this.isMonitoringVolume = false;
    },
    
    // 分析麦克风音量
    analyzeVolume() {
      if (!this.isMonitoringVolume || !this.audioAnalyser) return;
      
      // 获取当前音频数据
      this.audioAnalyser.getByteFrequencyData(this.audioDataArray);
      
      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < this.audioDataArray.length; i++) {
        sum += this.audioDataArray[i];
      }
      const average = sum / this.audioDataArray.length;
      
      // 处理音量数据
      this.handleVolumeData(average);
      
      // 继续请求下一帧分析
      this.requestAnimationId = requestAnimationFrame(() => this.analyzeVolume());
    },
    
    // 处理音量数据
    handleVolumeData(volume) {
      const now = Date.now();
      
      // 如果音量超过阈值，认为有人在说话
      if (volume > this.silenceThreshold) {
        // 更新最后声音时间
        this.lastVolumeTime = now;
        
        // 如果有静音定时器，清除它
        if (this.silenceTimer) {
          clearTimeout(this.silenceTimer);
          this.silenceTimer = null;
        }
      } 
      // 如果音量低于阈值且没有静音定时器，开始计时
      else if (!this.silenceTimer && this.lastVolumeTime > 0) {
        // 设置静音定时器
        this.silenceTimer = setTimeout(() => {
          // 如果自动发送功能开启且有内容
          if (this.autoSendEnabled && this.content.trim() !== '') {
            console.log("检测到静音，自动发送内容");
            
            // 停止语音识别和音量监控
            this.closeASR();
            this.stopVolumeMonitoring();
            
            // 发送内容
            this.ask();
          }
          
          this.silenceTimer = null;
        }, this.silenceDuration);
      }
    },

  }
};
</script>
<style lang="scss" scoped>
.ck-container {
  padding: 0;
  display: flex;
  height: calc(100vh - 84px);
  .left-sidebar {
    width: 280px;
    border-right: 1px solid #e8e8e8;
    padding: 12px;
    .left-sidebar-title {
      height: 50px;
      border: 1px solid #e8e8e8;
      border-radius: 30px;
      line-height: 50px;
      .left-sidebar-logo {
        width: 36px;
        height: 36px;
        vertical-align: middle;
        margin: 0 12px;
        border-radius: 30px;
      }
      .left-sidebar-lable {
        font-size: 16px;
        font-weight: 600;
        line-height: 50px;
        display: inline-block;
        vertical-align: middle;
        margin: 0;
      }
      .left-sidebar-btn {
        float: right;
        line-height: 50px;
        padding: 0;
        margin-right: 12px;
      }
    }
    .left-sidebar-search {
      height: 86px;
      margin-top: 12px;
      .left-sidebar-select {
        width: 100%;
        margin: 12px 0;
      }
    }
    .left-sidebar-his {
      height: calc(100vh - 220px);
      .left-sidebar-input {
        width: 100%;
        margin: 12px 0;
      }
      .left-sidebar-his-list {
        height: calc(100vh - 330px);
      }
    }
  }
  .right-container {
    background: #dbe9f740;
    flex: 1;
    .right-container-top {
      height: 75%;
      overflow-y: auto;
      border-bottom: 1px solid #e8e8e8;
      padding: 0 20%;
    }
    .right-container-bottom {
      height: 20%;
      overflow-y: auto;
      margin-top: 20px;
      padding: 0 80px;
    }
  }

  .right-container-top {
    position: relative; /* 使得内部绝对定位的元素（如.toggle-textbox）能够相对于它定位 */
  }

  .toggle-textbox {
    position: fixed; /* 使按钮固定在屏幕上的某个位置 */
    /*position: absolute;*/
    top: 100px; /* 调整顶部距离以适应你的布局 */
    right: 20px; /* 保持在右侧 */
    cursor: pointer;
    z-index: 2; /* 确保按钮在其他内容之上 */
  }

  .full-textbox {
    /*position: absolute;*/
    position: fixed;
    top: 140px; /* 调整与加号按钮的垂直距离 */
    right: 20px; /* 文本框同样出现在右侧 */
    width: 800px; /* 举例：设定文本框宽度，根据实际需求调整 */
    background-color: white; /* 示例：背景颜色，可根据设计调整 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 示例：阴影效果 */
    padding: 10px;
    border-radius: 5px;
    z-index: 2; /* 确保文本框在其他内容之上 */
  }

  .textbox-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .icon-wrapper {
    display: inline-block; /* 使背景只包裹内容 */
    width: 32px; /* 圆形的直径 */
    height: 32px;
    line-height: 32px; /* 保证图标垂直居中 */
    text-align: center; /* 图标水平居中 */
    border-radius: 50%; /* 制作圆形背景 */
    background-color: #5a9dd8; /* 背景颜色 */
  }

  /* 增大图标尺寸 */
  .icon-wrapper .el-icon-plus,
  .icon-wrapper .el-icon-minus {
    font-size: 25px; /* 根据需要调整图标大小 */
    color: white; /* 图标颜色，假设为白色以对比蓝色背景 */
  }
}
.svg-icon {
  width: 16px;
  height: 16px;
}

.issue-text {
  display: inline-block;
  padding: 5px 10px;
  border: 1px solid #ebebeb;
  background-color: #fff;
  color: #7d83c5;
  border-radius: 8px;
  transition: border-color 0.3s ease;
  margin-bottom: 5px;
}

.issue-text:hover {
  border-color: blue;
}

/* 添加遮罩层的样式 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* 黑色半透明背景 */
  z-index: 1; /* 确保遮罩层位于所有内容之上 */
}
</style>
<style lang="scss" >
  /* 重置所有元素的默认外边距和内边距 */
  .markdown-content  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box; /* 确保盒模型计算包括边框和内边距 */
  }

  /* 为整个Markdown内容容器设置基本样式，并添加宽度限制 */
  .markdown-content {
    font-size: 16px; /* 根据需要调整字体大小 */
    line-height: 1.5; /* 根据需要调整行高 */
    max-width: 800px; /* 设置最大宽度，可以根据实际情况调整 */
    margin: 0 auto; /* 自动水平居中 */
    padding: 1em; /* 为内容四周增加一些空间 */
    word-wrap: break-word; /* 强制文本在容器内换行 */
    overflow-wrap: break-word; /* 确保长单词或URL能够换行 */
    white-space: pre-wrap; /* 保留空白符序列，但允许自动换行 */
  }

  /* 对于长链接或其他可能溢出的内容，进一步确保它们不会超出容器 */
  .markdown-content a,
  .markdown-content code {
    word-break: break-word; /* 允许任意字符间断开以适应容器 */
    white-space: pre-wrap; /* 保留空白符序列，但允许自动换行 */
  }

  /* 针对代码块进行特殊处理，确保它们不会超出容器 */
  .markdown-content pre {
    overflow-x: auto; /* 如果内容太宽，则启用水平滚动条 */
    white-space: pre; /* 保留空白符和换行符 */
    padding: 1em; /* 为代码块增加一些内边距 */
    background-color: #f8f8f8; /* 可选：为代码块添加背景色 */
    border: 1px solid #ddd; /* 可选：为代码块添加边框 */
  }

  /* 针对代码块内的代码进行特殊处理 */
  .markdown-content pre code {
    white-space: pre; /* 保留空白符和换行符 */
  }

  /* 为段落设置适当的间距 */
  .markdown-content p {
    margin: 0.1em 0.1em 0;
  }

  /* 为列表项设置适当的间距 */
  .markdown-content li {
    margin: 0.25em 0; /* 根据需要调整 */
    padding-left: 1em; /* 如果需要缩进 */
  }

  /* 为有序列表和无序列表设置适当的间距 */
  .markdown-content ol, .markdown-content ul {
    margin: 0.5em 0; /* 根据需要调整 */
    padding-left: 1em; /* 如果需要缩进 */
  }

  /* 为标题设置适当的间距 */
  .markdown-content h1, .markdown-content h2, .markdown-content h3,
  .markdown-content h4, .markdown-content h5, .markdown-content h6 {
    margin: 1em 0 0.5em; /* 根据需要调整 */
  }

  /* 为块级元素（如代码块）设置适当的间距 */
  .markdown-content pre, .markdown-content blockquote {
    margin: 1em 0; /* 根据需要调整 */
    padding: 0.5em; /* 根据需要调整 */
  }

  /* 为水平线设置适当的间距 */
  .markdown-content hr {
    margin: 1em 0; /* 根据需要调整 */
  }

  /* 确保段落和列表之间没有额外的间距 */
  .markdown-content p + ol, .markdown-content p + ul, .markdown-content ol + p, .markdown-content ul + p {
    margin-top: 0.5em; /* 根据需要调整 */
  }

  /* 确保段落和标题之间没有额外的间距 */
  .markdown-content p + h1, .markdown-content p + h2, .markdown-content p + h3,
  .markdown-content p + h4, .markdown-content p + h5, .markdown-content p + h6,
  .markdown-content h1 + p, .markdown-content h2 + p, .markdown-content h3 + p,
  .markdown-content h4 + p, .markdown-content h5 + p, .markdown-content h6 + p {
    margin-top: 0.5em; /* 根据需要调整 */
  }

  /* 去除段落间的多余空行 */
  .markdown-content > * + * {
    margin-top: 0.5em; /* 根据需要调整 */
  }
</style>
