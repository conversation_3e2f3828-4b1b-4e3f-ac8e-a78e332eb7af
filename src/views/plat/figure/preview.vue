<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="尺寸" prop="size">
        <el-input
          v-model="queryParams.size"
          placeholder="请输入尺寸"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动作名称" prop="motionName">
        <el-input
          v-model="queryParams.motionName"
          placeholder="请输入动作名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动作类型" prop="motionType">
        <el-select placeholder="请选择动作类型" v-model="queryParams.motionType" clearable size="small">
          <el-option
            v-for="dict in typeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="动作编号" prop="motionNo">
        <el-input
          v-model="queryParams.motionNo"
          placeholder="请输入动作编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['plat:motion:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plat:motion:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="motionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="尺寸" align="center" prop="size" />
      <el-table-column label="动作名称" align="center" prop="motionName" />
      <el-table-column label="动作类型" align="center" prop="motionType" :formatter="typeFormat" />
      <el-table-column label="动作编号" align="center" prop="motionNo" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plat:motion:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['plat:motion:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handlePreview(scope.row)"
            v-hasPermi="['plat:motion:edit']"
          >预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改动作信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="动作名称" prop="motionName">
          <el-input v-model="form.motionName" placeholder="请输入动作名称" />
        </el-form-item>
        <el-form-item label="尺寸" prop="size">
          <el-input v-model="form.size" placeholder="请输入尺寸" />
        </el-form-item>
        <el-form-item label="动作类型" prop="motionType">
          <el-select placeholder="请选择动作类型" v-model="form.motionType" clearable>
            <el-option
              v-for="dict in typeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="动作编号" prop="motionNo">
          <el-input v-model="form.motionNo" placeholder="请输入动作编号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 数字人预览 -->
    <el-dialog title="预览动作信息" :visible.sync="previewVisible" width="500px" append-to-body>
      <div class="imgbox">
        <img class="img" alt="" :src="'/szr' + item" v-for="(item, index) in imgList" :key="index" v-show="currentIndex==index?true:false">
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetFrames" :disabled="!isLastFrame">重 放</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMotion, getMotion, delMotion, addMotion, updateMotion, preview } from "@/api/plat/motion";

export default {
  name: "preview",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 动作信息表格数据
      motionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 预览是否显示弹出层
      previewVisible: false,
      // 动作类型字典项
      typeOptions: [{
          value: 0,
          label: '口型'
        }, {
          value: 1,
          label: '手势'
        }],
      imgList:[],
      currentImage: '',
      currentIndex: 0,
      intervalId: null,
      //当前动作是否播放完成
      isLastFrame: false,
      //动作播放速度
      interval_time: 300,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        motionName: null,
        figureId: null,
        size: null,
        motionType: null,
        motionNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        motionName: [
          { required: true, message: "动作名称不能为空", trigger: "blur" }
        ],
        size: [
          { required: true, message: "尺寸不能为空", trigger: "blur" }
        ],
        motionType: [
          { required: true, message: "动作类型", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询动作信息列表 */
    getList() {
      this.queryParams.figureId = this.$router.currentRoute.query && this.$router.currentRoute.query.figureId;
      this.loading = true;
      listMotion(this.queryParams).then(response => {
        this.motionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 动作类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.motionType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.previewVisible = false;
      this.reset();
      this.imgList = [],
      this.currentImage = '',
      this.currentIndex = 0,
      this.intervalId = null,
      //当前动作是否播放完成
      this.isLastFrame = false
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 预览按钮操作 */
    handlePreview(row) {
      this.imgList = [],
      this.currentImage = '',
      this.currentIndex = 0,
      this.intervalId = null,
      //当前动作是否播放完成
      this.isLastFrame = false
      this.reset();
      const id = row.id
      preview(id).then(response => {
        this.imgList = response.data;
        this.currentImage = this.imgList[0];
        this.startAnimation();
        this.previewVisible = true;
      });
    },
    startAnimation() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
      this.intervalId = setInterval(() => {
        this.nextFrame();
      }, this.interval_time); // 每秒切换一次图片
    },
    nextFrame() {
      if (this.currentIndex < this.imgList.length - 1) {
        this.currentIndex++;
        this.currentImage = this.imgList[this.currentIndex];
      } else {
        this.isLastFrame = true; // 最后一帧，禁用按钮
      }
    },
    resetFrames() {
      this.currentIndex = 0;
      this.currentImage = this.imgList[0];
      this.isLastFrame = false; // 重置为可以继续播放
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMotion(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改动作信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.motionType == 1 && !this.form.motionNo) {
            this.$message.error(`当动作类型为口型时，动作编号必填！`);
            return false;
          }
          if (this.form.id != null) {
            updateMotion(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMotion(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除动作信息编号为"' + ids + '"的数据项？').then(function() {
        return delMotion(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
<style lang="scss" scoped>
  /* 修改 img 的样式 */
  .img {
    max-width: 100%; /* 图片的最大宽度为容器宽度 */
    height: auto; /* 保持原始宽高比 */
  }
</style>
