<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="形象名称" prop="figureName">
        <el-input
          v-model="queryParams.figureName"
          placeholder="请输入形象名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入版本号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleUpload"
          v-hasPermi="['plat:figure:upload']"
        >上传</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['plat:figure:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plat:figure:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="figureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="形象名称" align="center" prop="figureName" />
      <el-table-column label="版本号" align="center" prop="version" />
      <el-table-column label="预览图" align="center" >
        <template slot-scope="scope">
          <img alt="" :src="'/szr' + scope.row.previewUrl" style="max-width: 30%; height: auto;" v-if="scope.row.previewUrl">
        </template>
      </el-table-column>
      <!-- <el-table-column label="预览图" align="center" prop="previewBase64" /> -->
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plat:figure:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['plat:figure:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleIterms(scope.row)"
          >明细</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
          >预览</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数字人形象对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="形象名称" prop="figureName">
          <el-input v-model="form.figureName" placeholder="请输入形象名称" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 上传数字人形象 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="upload.uploadData" :rules="upload.rules">
        <el-form-item label="形象名称" prop="figureName">
          <el-input v-model="upload.uploadData.figureName" placeholder="请输入形象名称" />
        </el-form-item>
        <el-form-item label="形象描述" prop="remark">
          <el-input v-model="upload.uploadData.remark" placeholder="请输入形象描述" />
        </el-form-item>
      </el-form>
      <el-upload class="upload-demo ck-input" drag :action="upload.uploadUrl" :data="upload.uploadData" :headers="upload.headers"
                 multiple :limit="1" :on-success="handleUploadSuccess" :on-remove="handleRemove" accept=".zip" :file-list="fileList">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">支持zip格式文件上传，仅能上传一个文件</div>
      </el-upload>
      <el-row class="step-btn-box">
        <el-button type="primary" @click="handleSubmint" :loading="upload.btnLoad">确定</el-button>
      </el-row>
    </el-dialog>

    <el-dialog title="预览数字人形象" :visible.sync="previewVisible" width="500px" append-to-body>
      <div class="imgbox">
        <img class="img" alt="" :src="'/szr' + item" v-for="(item, index) in imgList" :key="index" v-show="currentIndex==index?true:false">
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetFrames" :disabled="!isLastFrame">重 放</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listFigure, getFigure, delFigure, addFigure, updateFigure, uploadFile, preview } from "@/api/plat/figure";
  import { getToken } from "@/utils/auth";

  export default {
    name: "Figure",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 数字人形象表格数据
        figureList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 状态字典项
        statusOptions: [{
          value: 0,
          label: '正常'
        }, {
          value: 1,
          label: '停用'
        }],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          figureName: null,
          version: null,
          status: null,
        },
        // 信息导入参数
        upload: {
          // 是否显示弹出层（信息导入）
          open: false,
          // 弹出层标题（信息导入）
          title: "",
          // 设置上传的请求头部
          headers: {
            Authorization: "Bearer " + getToken(),
            timeout: 300000
          },
          // 上传的地址
          uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
          uploadData: {
            modeltype: 'plat',
            figureName: null,
            remark: null
          },
          btnLoad: false,
          // 表单校验
          rules: {
            figureName: [
              { required: true, message: "形象名称不能为空", trigger: "blur" }
            ],
          }
        },
        uploadForm: {},
        fileId: '',
        fileList: [],
        previewVisible: false,
        imgList:[],
        currentImage: '',
        currentIndex: 0,
        intervalId: null,
        //当前动作是否播放完成
        isLastFrame: false,
        //动作播放速度
        interval_time: 300,
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          figureName: [
            { required: true, message: "形象名称不能为空", trigger: "blur" }
          ],
          status: [
            { required: true, message: "状态不能为空", trigger: "change" }
          ],
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询数字人形象列表 */
      getList() {
        this.loading = true;
        listFigure(this.queryParams).then(response => {
          this.figureList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      // 状态字典翻译
      statusFormat(row, column) {
        return this.selectDictLabel(this.statusOptions, row.status);
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
        this.previewVisible = false,
          this.imgList = [],
          this.currentImage = '',
          this.currentIndex = 0,
          this.intervalId = null,
          //当前动作是否播放完成
          this.isLastFrame = false,
          this.fileList = [];
        this.fileId = "";
        this.uploadForm = {};
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          figureName: null,
          status: 0,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          remark: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids
        getFigure(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改数字人形象";
        });
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateFigure(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addFigure(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids;
        this.$modal.confirm('是否确认删除数字人形象编号为"' + ids + '"的数据项？').then(function() {
          return delFigure(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
      /** 明细按钮操作 */
      handleIterms(row) {
        this.$router.push({
          path: '/plat/motion',
          query: { figureId: row.id },
        });
      },
      /** 预览按钮操作 */
      handlePreview(row) {
        this.imgList = [],
          this.currentImage = '',
          this.currentIndex = 0,
          this.intervalId = null,
          //当前动作是否播放完成
          this.isLastFrame = false
        this.reset();
        const data = {
          figureId: row.id,
          size: '1215 x 2160',
          motionNoList: ['n', 'i', 'h', 'ao', '@action:wave_left', '@action:wave_right', '@action:love']
        };
        preview(data).then(response => {
          this.imgList = response.data;
          this.currentImage = this.imgList[0];
          this.startAnimation();
          this.previewVisible = true;
        });
      },
      startAnimation() {
        if (this.intervalId) {
          clearInterval(this.intervalId);
        }
        this.intervalId = setInterval(() => {
          this.nextFrame();
        }, this.interval_time); // 每秒切换一次图片
      },
      nextFrame() {
        if (this.currentIndex < this.imgList.length - 1) {
          this.currentIndex++;
          this.currentImage = this.imgList[this.currentIndex];
        } else {
          this.isLastFrame = true; // 最后一帧，禁用按钮
        }
      },
      resetFrames() {
        this.currentIndex = 0;
        this.currentImage = this.imgList[0];
        this.isLastFrame = false; // 重置为可以继续播放
      },
      /** 上传按钮操作 */
      handleUpload() {
        this.reset();
        this.upload.open = true;
        this.upload.title = "上传数字人形象";
        this.fileList = [];
      },
      handleSubmint() {
        this.upload.btnLoad = true

        if (!this.upload.uploadData.figureName) {
          this.$message.error(`请输入形象名称！`);
          return false;
        }

        this.uploadForm = {
          fileId: this.fileId,
          figureName: this.upload.uploadData.figureName,
          remark: this.upload.uploadData.remark
        }
        uploadFile(this.uploadForm).then(res => {
          if (res.code === 200) {
            this.upload.open = false;
            this.$message.success('上传成功')
            this.fileList = [];
            this.fileId = "";
            this.uploadForm = {};
            this.getList();
          }
          this.upload.btnLoad = false
        }).catch(err => {
          this.upload.btnLoad = false
        })
      },
      handleUploadSuccess(res, file,) {
        file.id = res.data.id;
        this.fileId = res.data.id;
      },
      handleRemove(file, fileList) {
        this.fileId = ''
        this.fileList = [];
      },
    }
  };
</script>
<style lang="scss" scoped>
  /* 修改 img 的样式 */
  .img {
    max-width: 100%; /* 图片的最大宽度为容器宽度 */
    height: auto; /* 保持原始宽高比 */
  }
</style>
