<template>
  <div class="app-container">
    <!-- 表格样式的描述列表 -->
    <el-descriptions
      title="注册信息"
      :column="3"
      border
      class="ck-form"
    >
      <!-- 姓名 -->
      <el-descriptions-item label="姓名">
        <el-input
          :disabled="isDisabled"
          v-model="form.name"
          placeholder="请输入姓名"
        />
      </el-descriptions-item>

      <!-- 学号 -->
      <el-descriptions-item
        label="学号"
        v-if="isStudent"
      >
        <el-input
          :disabled="isDisabled"
          v-model="form.studentId"
          placeholder="请输入学号"
          @blur="jobChangeS"
        />
      </el-descriptions-item>

      <!-- 工号 -->
      <el-descriptions-item
        label="工号"
        v-if="isTeacher"
      >
        <el-input
          :disabled="isDisabled"
          v-model="form.teacherId"
          placeholder="请输入工号"
          @blur="jobChangeT"
        />
      </el-descriptions-item>

      <!-- 性别 -->
      <el-descriptions-item label="性别">
        <el-radio-group
          :disabled="isDisabled"
          v-model="form.sexMark"
        >
          <el-radio label="0">男</el-radio>
          <el-radio label="1">女</el-radio>
        </el-radio-group>
      </el-descriptions-item>

      <!-- 角色 -->
      <el-descriptions-item label="角色">
        <el-radio-group
          v-model="form.isStuOrTea"
          :disabled="isDisabled"
          @change="roleChange"
        >
          <el-radio
            v-for="dict in dict.type.role_id"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-descriptions-item>

      <!-- 学校 -->
      <el-descriptions-item label="学校">
        <el-input
          :disabled="isDisabled"
          v-model="form.univerName"
          placeholder="请输入学校"
        />
      </el-descriptions-item>

      <!-- 院系 -->
      <el-descriptions-item label="院系">
        <el-input
          :disabled="isDisabled"
          v-model="form.colleName"
          placeholder="请输入院系"
        />
      </el-descriptions-item>

      <!-- 专业 -->
      <el-descriptions-item
        label="专业"
        v-if="isStudent"
      >
        <el-input
          :disabled="isDisabled"
          v-model="form.majorName"
          placeholder="请输入专业"
        />
      </el-descriptions-item>

      <!-- 班级 -->
      <el-descriptions-item
        label="班级"
        v-if="isStudent"
      >
        <el-input
          :disabled="isDisabled"
          v-model="form.className"
          placeholder="请输入班级"
        />
      </el-descriptions-item>
    </el-descriptions>

    <!-- 提交按钮 -->
    <el-row
      class="step-btn-box"
      v-if="!isDisabled"
    >
<!--      <el-button-->
<!--        type="primary"-->
<!--        :loading="btnLoad"-->
<!--        @click="handleSubmit"-->
<!--      >-->
<!--        确定-->
<!--      </el-button>-->
      <el-tooltip effect="light" content="拖动注册" placement="right-end">
      <div class="outer-container">
        <!-- 新容器根据圆的位置和半圆重叠填充颜色 -->
        <div
          class="color-fill"
          :style="{ width: circlePosition + circleSize / 2 + 'px', background: fillColor }"
        ></div>
        <div
          class="circle"
          :style="{ left: circlePosition + 'px' }"
          @mousedown="startDrag"
        ><i :class="isAtRightEdge ? 'el-icon-circle-check' : 'el-icon-d-arrow-right' " style="margin-top: 10px;color: #00afff"/></div>
      </div>
      </el-tooltip>
    </el-row>


  </div>
</template>

<script>
import { externalCertification,checkStuOrTea } from "@/api/system/authentication.js";

export default {
  data() {
    return {
      roleFlag: 1, // 当前角色：1 - 学生, 2 - 老师
      isDisabled: false, // 表单是否禁用
      btnLoad: false, // 提交按钮加载状态
      circlePosition: 0,
      containerWidth: 200, // 容器宽度
      circleSize: 40, // 圆的直径
      isDragging: false,
      isDraggable: true, // 是否允许拖动
      isAtRightEdge: false, // 添加标志，防止多次调用
      form: {
        name: "",
        teacherId: "",
        studentId:"",
        sexMark: "0",
        isStuOrTea: 1,
        univerName: "",
        colleName: "",
        majorName: "",
        className: "",
      },
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        teacherId: [{ required: true, message: "工号不能为空", trigger: "blur" }],
        studentId: [{ required: true, message: "学号不能为空", trigger: "blur" }],
        sexMark: [{ required: true, message: "请选择性别", trigger: "change" }],
        univerName: [{ required: true, message: "学校不能为空", trigger: "blur" }],
        colleName: [{ required: true, message: "院系不能为空", trigger: "blur" }],
        majorName: [{ required: true, message: "专业不能为空", trigger: "blur" }],
        className: [{ required: true, message: "班级不能为空", trigger: "blur" }],
      },
      roleRules: {
        student: {
          studentId: [{ required: true, message: "学号不能为空", trigger: "blur" }],
          majorName: [{ required: true, message: "专业不能为空", trigger: "blur" }],
          className: [{ required: true, message: "班级不能为空", trigger: "blur" }],
        },
        teacher: {
          teacherId: [{ required: true, message: "工号不能为空", trigger: "blur" }],
        },
      },
      dict: {
        type: {
          role_id: [
            { value: 1, label: "学生" },
            { value: 0, label: "老师" },
          ],
        },
      },
    };
  },
  computed: {
    isStudent() {
      return this.roleFlag === 1;
    },
    isTeacher() {
      return this.roleFlag === 0;
    },
    // 固定浅蓝色圆的颜色和变化的深蓝色背景色
    fillColor() {
      const percentage = this.circlePosition / (this.containerWidth - this.circleSize);
      // 根据圆的位置调节背景色的深浅，使用相对浅蓝色的深色调
      const r = 173 + (percentage * 82);  // 从浅蓝色(173)到深蓝色(255)
      const g = 216 + (percentage * 39);  // 从浅蓝色(216)到深蓝色(255)
      const b = 255 - (percentage * 90); // 从浅蓝色(255)到深蓝色(165)
      return `rgb(${r}, ${g}, ${b})`;  // 渐变的颜色
    },
  },
  watch: {
    // roleFlag(newVal) {
    //   // 动态更新校验规则
    //   if (newVal === 1) {
    //     this.rules.majorName = [{ required: true, message: "专业不能为空", trigger: "blur" }];
    //     this.rules.className = [{ required: true, message: "班级不能为空", trigger: "blur" }];
    //     this.rules.teacherId = [];
    //     this.rules.studentId = [{ required: true, message: "学号不能为空", trigger: "blur" }];
    //   } else if (newVal === 0) {
    //     this.rules.majorName = [];
    //     this.rules.className = [];
    //     this.rules.teacherId = [{ required: true, message: "工号不能为空", trigger: "blur" }];
    //     this.rules.studentId = [];
    //   }
    // },
    roleFlag(newVal) {
      this.rules = newVal === 1 ? { ...this.roleRules.student } : { ...this.roleRules.teacher };
    },
  },
  methods: {

    startDrag(event) {
      if (!this.isDraggable) return; // 如果禁用拖动，直接返回

      this.isDragging = true;
      const startX = event.clientX;
      const initialPosition = this.circlePosition;

      const onMouseMove = (e) => {
        if (!this.isDragging) return;
        const deltaX = e.clientX - startX;
        let newPosition = initialPosition + deltaX;

        // 限制圆只能在容器范围内移动
        if (newPosition < 0) newPosition = 0;
        if (newPosition > this.containerWidth - this.circleSize) {
          newPosition = this.containerWidth - this.circleSize;
          if (!this.isAtRightEdge) { // 如果圆还没有到达右边界
            console.log("圆已到达右边界！");
            this.toggleDraggableStop();  // 停止拖动
            this.handleSubmit();         // 提交操作
            this.isDragging = false; // 失去焦点，禁止继续拖动
            this.isAtRightEdge = true;  // 设置标志，防止重复触发
          }
        } else {
          this.isAtRightEdge = false; // 如果圆没有到达右边界，重置标志
        }
        this.circlePosition = newPosition;
      };

      const onMouseUp = () => {
        this.isDragging = false;
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
      };

      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
    },
    // 控制是否允许拖动
    toggleDraggableStop() {
      this.isDraggable = false;
      console.log("拖动已" + (this.isDraggable ? "启用" : "禁用"));
    },
    toggleDraggableStart() {
      this.isDraggable = true;
      console.log("拖动已" + (this.isDraggable ? "启用" : "禁用"));
    },
    // 重置圆的位置
    resetCirclePosition() {
      this.circlePosition = 0;  // 将圆的位置重置为最左边
      this.toggleDraggableStart();
      this.isAtRightEdge = false;
    },
    validateRole(data) {
      console.log('1');
      return checkStuOrTea(data).then(res => {
        if (res.code === 200) {
          console.log('验证成功');
          return true;
        } else {
          this.$message.error(res.msg);
          setTimeout(() => {
            this.resetCirclePosition();
          }, 500);
          return false;
        }
      }).catch(err => {
        this.$message.error('验证异常');
        console.error(err);
        this.$message.error(res.msg);
        setTimeout(() => {
          this.resetCirclePosition();
        }, 500);
        return false;
      });
    },


    roleChange() {
      // 根据角色切换时动态调整表单内容
      this.roleFlag = this.form.isStuOrTea; // 角色切换
    },
    resetForm() {
      // 重置表单数据
      this.form = {
        name: "",
        teacherId: "",
        studentId: "",
        sexMark: "0",
        isStuOrTea: 1,
        univerName: "",
        colleName: "",
        majorName: "",
        className: "",
      };
      // 重置表单校验
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // handleSubmit() {
    //   if (this.form.isStuOrTea === 0){
    //     const dataT={
    //       isStuOrTea:0,
    //       teacherId:this.form.teacherId
    //     }
    //     checkStuOrTea(dataT).then(res => {
    //       if (res.code === 200) {
    //         console.log('成功')
    //         this.handleCheckSubmit();
    //       }else {
    //         this.$message.error(res.msg)
    //       }
    //     })
    //   }else {
    //     const dataS={
    //       isStuOrTea:1,
    //       studentId:this.form.studentId
    //     }
    //     checkStuOrTea(dataS).then(res => {
    //       if (res.code === 200) {
    //         console.log('成功')
    //         this.handleCheckSubmit();
    //       }else {
    //         this.$message.error(res.msg)
    //       }
    //     })
    //   }
    // },

    handleSubmit() {

      // 检查表单每个字段是否为空
      const missingFields = Object.entries(this.form).filter(([key, value]) => {
        // 对不需要验证的字段，跳过检查
        if ((this.roleFlag === 1 && key === "teacherId") || (this.roleFlag === 0 && (key === "majorName" || key === "className" || key === "studentId"))) {
          return false;
        }
        // 确保字段值是字符串，再检查是否为空
        return value === null || value === undefined || (typeof value === "string" && value.trim() === "") || value === "";
      });

      if (missingFields.length > 0) {
        // 提示用户第一个未填写的字段
        const [firstMissingField] = missingFields;
        const fieldName = this.getFieldLabel(firstMissingField[0]); // 获取字段的中文名称
        this.$message.error(`${fieldName}不能为空`);
        setTimeout(() => {
          this.resetCirclePosition();
        }, 500);
        return;
      }

      // 校验通过后继续执行后续逻辑
      const data = this.form.isStuOrTea === 0
        ? { isStuOrTea: 0, teacherId: this.form.teacherId }
        : { isStuOrTea: 1, studentId: this.form.studentId };

      this.validateRole(data).then((isValidRole) => {
        if (isValidRole) {
          this.handleCheckSubmit();
        }
      });
    },

    handleCheckSubmit(){
          externalCertification(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('提交成功');
              // 清空表单数据
              this.resetForm();
              setTimeout(() => {
                this.resetCirclePosition();
              }, 500);
            } else {
              this.$message.error('提交失败：' + res.msg);
            }
          }).catch(err => {
            this.$message.error('提交异常');
            console.error(err);
          });
    },

    // 获取字段的中文名称
    getFieldLabel(fieldKey) {
      const fieldLabels = {
        name: "姓名",
        teacherId: "工号",
        studentId: "学号",
        sexMark: "性别",
        isStuOrTea: "角色",
        univerName: "学校",
        colleName: "院系",
        majorName: "专业",
        className: "班级",
      };
      return fieldLabels[fieldKey] || "未知字段";
    },

    jobChangeT() {
      setTimeout(() => {
        const dataT={
          isStuOrTea:0,
          teacherId:this.form.teacherId
        }
        checkStuOrTea(dataT).then(res => {
          if (res.code === 200) {
            console.log('成功')
          }else {
            this.$message.error(res.msg)
          }
        })
      }, 500);

    },
    jobChangeS() {
      setTimeout(() => {
        const dataS={
          isStuOrTea:1,
          studentId:this.form.studentId
        }
        checkStuOrTea(dataS).then(res => {
          if (res.code === 200) {
            console.log('成功')
          }else {
            this.$message.error(res.msg)
          }
        })
      }, 500);

    },
  },
};
</script>

<style>


.outer-container {
  left: 40vw;
  width: 200px;
  height: 40px;
  border: 2px solid #ccc;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  background-color: #e5e5e5; /* 背景颜色 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
}

.color-fill {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: width 0.1s ease; /* 平滑过渡 */
}

.circle {
  width: 40px;
  height: 40px;
  background-color: #add8e6; /* 浅蓝色 */
  border-radius: 50%;
  position: absolute;
  cursor: pointer;
  transition: left 0.1s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 给圆添加阴影 */
}



.ck-form {
  margin: 20px auto;
  width: 80%;
}
.step-btn-box {
  text-align: center;
  margin-top: 20px;
}

.el-descriptions-item {
  font-size: 16px; /* 修改为你需要的字体大小 */
}

.el-descriptions-item__label {
  font-size: 16px; /* 修改标签部分字体大小 */
}

.el-descriptions-item__content {
  font-size: 16px; /* 修改内容部分字体大小 */
}

</style>
