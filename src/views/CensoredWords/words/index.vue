<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="敏感词" prop="word">
        <el-input
          v-model="queryParams.word"
          placeholder="请输入敏感词"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分组" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入分组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8"style="display: flex; justify-content: flex-start;">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['CensoredWords:words:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['CensoredWords:words:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['CensoredWords:words:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['CensoredWords:words:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5" >
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleAddUpload"
          v-hasPermi="['CensoredWords:words:export']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5" style="margin-left: auto;">
        <!-- 下载模板按钮 -->
        <el-button
          type="info"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate()"
        >下载模板</el-button>
      </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wordsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="敏感词" align="center" prop="word" />
      <el-table-column label="分组" align="center" prop="category" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" align="center" prop="updateBy" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['CensoredWords:words:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['CensoredWords:words:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改敏感词管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="敏感词" prop="word">
          <el-input v-model="form.word" placeholder="请输入敏感词" />
        </el-form-item>
        <el-form-item label="分组" prop="category">
          <el-input v-model="form.category" placeholder="请输入分组" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改敏感词管理对话框 -->
    <el-dialog :title="title" :visible.sync="openUpload" width="500px" append-to-body>
      <el-upload
        class="upload-demo"
        drag
        :headers="headers"
        :action="uploadUrl"
        :file-list="fileList"
        :limit="1"
        :on-success="handleUploadSuccess"
        :on-remove="handleRemove"
        :data="uploadData"
        accept=".xls, .xlsx"

        >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传excel文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormUpload">确 定</el-button>
        <el-button @click="cancelUpload">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWords, getWords, delWords, addWords, updateWords,addWordsByExcel } from "@/api/CensoredWords/words";
import {getToken} from "@/utils/auth";

export default {
  name: "Words",
  data() {
    return {
      uploadData: { modeltype: 'mgc' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // 上传的图片服务器地址
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 敏感词管理表格数据
      wordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（导入）
      openUpload: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        word: null,
        category: null,
      },
      // 表单参数
      form: {},
      formExcel:{},
      // 表单校验
      rules: {
        word: [
          { required: true, message: "敏感词不能为空", trigger: "blur" }
        ],
        category: [
          { required: true, message: "分组不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleUploadSuccess(res, file) {

      console.log("res")
      // this.formExcel.fileId =res.data.id

      console.log(res)
      console.log(res.data.id)
      this.formExcel.fileId =res.data.id
      console.log(this.formExcel.fileId )

      this.fileList.push(file)
    },
    handleRemove() {
      this.formExcel.fileId = ''
      this.formExcel.fileName = ''
      this.fileList = []
    },
    /** 查询敏感词管理列表 */
    getList() {
      this.loading = true;
      listWords(this.queryParams).then(response => {
        this.wordsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    cancelUpload() {
      this.openUpload = false;
      this.formExcel.fileId = ''
      this.formExcel.fileName = ''
      this.fileList = []
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        word: null,
        category: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加敏感词管理";
    },
    /** 新增按钮操作 */
    handleAddUpload() {
      this.reset();
      console.log("handleAddUpload")
      this.openUpload = true;
      this.title = "批量导入敏感词";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWords(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改敏感词管理";
      });
    },
    /** 提交按钮 */
    submitFormUpload() {
      //先检查是否上传了文件

      console.log(this.formExcel.fileId)
      if (!this.formExcel.fileId || this.formExcel.fileId.length === 0) {
        this.$modal.msgError("请先上传文件");
        return;
      }
      addWordsByExcel(this.formExcel.fileId).then(response => {
        // 判断返回的response对象中的code字段，200表示成功
        if (response.code === 200) {
          this.$modal.msgSuccess("导入成功");
          this.openUpload = false;
          this.getList();
        } else {
          this.$modal.msgError("导入失败: " + (response.msg || '未知错误'));
        }
      }).catch(error => {
        // 捕获异常情况
        this.$modal.msgError("导入失败: " + error.message);
        console.error("导入过程中发生错误:", error);
})



      // updateWords(this.form).then(response => {
      //   this.$modal.msgSuccess("导入成功");
      //   this.openUpload = false;
      //   this.getList();
      // });


    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWords(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWords(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除敏感词管理编号为"' + ids + '"的数据项？').then(function() {
        return delWords(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/system/words/export', {
        ...this.queryParams
      }, `words_${new Date().getTime()}.xlsx`)
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      this.download('/system/words/export/template', {
        ...this.queryParams
      }, `words_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
