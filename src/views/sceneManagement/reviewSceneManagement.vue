<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="场景名称" prop="sceneName">
        <el-input class="ck-input" v-model="form.sceneName" placeholder="请输入场景名称" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="场景图标" prop="deiDesc">
        <template slot-scope="scope">
          <img :src="getImgUrl(form.sceneIcon)" alt="场景图标" style="max-width: 30%; height: auto;">
        </template>
      </el-form-item>
      <el-form-item label="选择数字人形象" prop="figureId">
        <el-select v-model="form.figureId" placeholder="选择数字人形象" size="mini"  style="width: 50%;height: 50px;" :disabled="true">
          <!-- 下拉选项 -->
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
            <!-- 使用slot自定义选项内容 -->
            <span style="float: left">{{ item.label }}</span>
            <img :src="'/szr'+item.image" style="width: 24px; height: 24px; margin-left: 10px;" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="模型名称" prop="modelName">
        <el-input class="ck-input" v-model="form.modelName" placeholder="请输入模型名称" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="模型ak" prop="modelAk">
        <el-input class="ck-input" v-model="form.modelAk" placeholder="请输入模型ak" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="模型sk" prop="modelSk">
        <el-input class="ck-input" v-model="form.modelSk" placeholder="请输入模型sk" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="模型url" prop="modelUrl">
        <el-input class="ck-input" v-model="form.modelUrl" placeholder="请输入模型url" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="知识库名称" prop="knowledgeBaseName">
        <el-input class="ck-input" v-model="form.knowledgeBaseName" placeholder="请输入知识库" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="知识库sk" prop="knowledgeBaseSk">
        <el-input class="ck-input" v-model="form.knowledgeBaseSk" placeholder="请输入知识库sk" maxlength="50" show-word-limit :disabled="true" />
      </el-form-item>
      <el-form-item label="知识库appId" prop="knowledgeBaseAppid">
        <el-input class="ck-input" v-model="form.knowledgeBaseAppid" placeholder="请输入知识库appId" maxlength="50" show-word-limit  :disabled="true"/>
      </el-form-item>
      <div class="text-to-speech">
        <el-row :gutter="30">
          <el-col :span="16">
            <el-input type="textarea" v-model="text" :maxlength="maxLength" show-word-limit rows="8" placeholder="请输入文本"></el-input>
            <div class="char-count">您还可以输入 {{ maxLength - text.length }} 字</div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <div class="block">
              <span class="demonstration">语速</span>
              <el-slider v-model="form.voiceSpeed" :max="10" show-input :disabled="true"></el-slider>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <div class="block">
              <span class="demonstration">语调</span>
              <el-slider v-model="form.voicePitch" :max="10" show-input :disabled="true"></el-slider>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <div class="block">
              <span class="demonstration">音量</span>
              <el-slider v-model="form.voiceVolume" :max="10" show-input :disabled="true"></el-slider>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="20">
            <div class="controls">
              <h3>发言人</h3>
              <!-- 女声 -->
              <el-row>
                <h3>女声</h3>
                <el-radio-group v-model="form.voiceRoleId" @change="handleRadioChange">
                  <el-radio-button v-for="dict in femaleVoices" :key="dict.value" :label="dict.value" :disabled="true">
                    {{ dict.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-row>
              <!-- 男声 -->
              <el-row>
                <h3>男声</h3>
                <el-radio-group v-model="form.voiceRoleId" @change="handleRadioChange">
                  <el-radio-button v-for="dict in maleVoices" :key="dict.value" :label="dict.value" :disabled="true">
                    {{ dict.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-row>

              <el-row style="margin-top: 30px;">
                <el-switch  style="margin-left: 30px;"
                            v-model="form.userPlayflag"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            active-text="自动语音播放" :disabled="true"></el-switch>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
<script>
  import { getToken } from "@/utils/auth";
  import { getManagement  } from "@/api/sceneManagement/sceneManagement.js";
  import {getPlatlist} from '@/api/explorationCenter/experience.js'
  import {
    getBaiDuToken,
  } from "@/api/explorationCenter/experience.js";
  import {
    saveUserVoiceRole,
    getUserVoiceRole,
  } from "@/api/system/voiceRole.js";
  import axios from "axios";
  import qs from "qs";
  export default {
    dicts: ['sys_voice_role','sys_xf_voice_role','user_voice_choose'],
    name: 'FormContent',
    // props: {
    //   flag: {
    //     type: String,
    //     default: ''
    //   }
    // },
    data() {
      return {
        uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", // 上传的图片服务器地址
        uploadData: { modeltype: 'mk1' },
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        fileList: [],
        text: '“欢迎各位同学。我是AI·CAI大模型，很高兴能在这里陪伴大家一起学习。',
        maxLength: 60,
        options: [],
        token : '',
        audioSrc: '',
        isloaded: true,// 加载完成
        form: {
          userPlayflag: false,
        },

        rules: {
          sceneName: [
            { required: true, message: '请输入场景名称', trigger: ['blur', 'change'] },
          ],
          figureId: [
            { required: true, message: '请选择数字人形象', trigger: ['blur', 'change'] },
          ],
          modelName: [
            { required: true, message: '请输入模型名称', trigger: ['blur', 'change'] },
          ],
          modelAk: [
            { required: true, message: '请输入模型ak', trigger: ['blur', 'change'] },
          ],
          modelSk: [
            { required: true, message: '请输入模型sk', trigger: ['blur', 'change'] },
          ],
          modelUrl: [
            { required: true, message: '请输入模型url', trigger: ['blur', 'change'] },
          ],
          knowledgeBaseName: [
            { required: true, message: '请输入知识库名称', trigger: ['blur', 'change'] },
          ],
          knowledgeBaseSk: [
            { required: true, message: '请输入知识库sk', trigger: ['blur', 'change'] },
          ],
          knowledgeBaseAppid: [
            { required: true, message: '请输入知识库appId', trigger: ['blur', 'change'] },
          ],
          bdVoiceRoleId : [],
          voiceSpeed: [],
          voicePitch: [],
          voiceVolume: [],
          xfVoiceRoleId: [],
          voiceRoleId: [],
          userPlayflag: [],
        },
        userVoiceChoose: 1,
      }
    },

    computed: {
      // 过滤出女声
      femaleVoices() {
        for (const item of this.dict.type.user_voice_choose) {
          if (item.label === "baidu") {
            this.userVoiceChoose = 0;
            return this.dict.type.sys_voice_role.filter(dict => dict.raw.dictSort === 0);
          }
          if (item.label === "xfSpark") {
            this.userVoiceChoose = 1;
            return this.dict.type.sys_xf_voice_role.filter(dict => dict.raw.dictSort === 0);
          }
        }
      },
      // 过滤出男声
      maleVoices() {
        for (const item of this.dict.type.user_voice_choose) {
          if (item.label === "baidu") {
            return this.dict.type.sys_voice_role.filter(dict => dict.raw.dictSort === 1);
          }
          if (item.label === "xfSpark") {
            return this.dict.type.sys_xf_voice_role.filter(dict => dict.raw.dictSort === 1);
          }
        }
      }
    },

    created() {
      this.getInfo();
      this.getVoiceRole();
      this.handleSelect();
    },

    methods: {

      getInfo() {
        this.id = this.$route.query && this.$route.query.id
        this.loading = true;
        getManagement(this.id).then(response => {
          this.form = response.data;
          this.form.voiceRoleId = response.data.bdVoiceRoleId;
          this.loading = false;
        });
      },
      getImgUrl(pptPath) {
        let baseUrl = window.location.origin
        var imgData;
        if (pptPath.includes('/ruoyi/')) {
          // 替换路径中的 ruoyi/ 之前的部分
          const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

          if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
            imgData = 'http://127.0.0.1:9215' + finalPath
            // console.log('Final baseUrl:', baseUrl)
          } else {
            imgData = baseUrl + finalPath
          }
        }
        return imgData;

      },

      handleUploadSuccess(res, file,) {
        file.id = res.data.id;
        this.fileList.push(res.data.id);

      },
      handleSelect() {
        getPlatlist().then(res => {
          this.options = res.data.map(item => ({
            value: item.id, // 使用id作为value
            label: `${item.figureName} - ${item.version}`, // 组合figureName和version作为label
            image: item.previewUrl // 使用previewUrl作为图像URL
          }));
        });
      },
      handleRemove(file, fileList) {
        const findex = this.fileList.map(f => f.indexOf(file.id));
        if (findex > -1) {
          this.fileList.splice(findex, 1);
        }
      },

      beforeUpload(file) {
        // 定义允许的文件类型
        const allowedTypes = ['image/jpeg', 'image/png'];
        const fileType = file.type;
        const isValidType = allowedTypes.includes(fileType);

        // 检查文件类型
        if (!isValidType) {
          this.$message.error('上传文件只能是 jpg, png 格式!');
          return false;
        }

        return isValidType;
      },
      handleBack() {
        this.$store.dispatch('tagsView/delView', this.$route)
        this.$router.push(this.$route.query.menuRouting)
      },
      handleChange(type) {
        this.form.projectType = type
      },


      getVoiceRole(){
        getUserVoiceRole().then(res => {
          this.per = res.data.voiceRoleId;
          this.flag = res.data.userPlayflag;
          if(this.userVoiceChoose === 1){
            this.speed = res.data.voiceSpeed / 10;
            this.pitch = res.data.voicePitch / 10;
            this.volume = res.data.voiceVolume / 10;
          }
        })
      },

      handleRadioChange(value) {
        // 根据选中的 value 找到对应的 label
        const selectedDict = this.femaleVoices.find((dict) => dict.value === value) || this.maleVoices.find((dict) => dict.value === value);
        this.selectedLabel = selectedDict ? selectedDict.label : "";
      },

    }
  }
</script>
<style lang="scss" scoped>
  .ck-form {
    width: 80%;
    margin: auto;
  }
  .ck-input {
    width: 50%;
  }
  .step-btn-box {
    text-align: center;
    height: 67px;
    line-height: 67px;
  }
  .is-choose {
    color: #1890ff;
  }

  .text-to-speech {
    max-width: 1000px;
    margin: 60px auto;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .char-count {
    margin-top: 10px;
    font-size: 14px;
    color: #888;
  }

  .controls {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
</style>

