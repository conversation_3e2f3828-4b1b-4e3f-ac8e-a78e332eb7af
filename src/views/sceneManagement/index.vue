<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="场景名称" prop="fileName">
        <el-input v-model="queryParams.sceneName" placeholder="请输入场景名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">创建场景</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataMakeList">
      <el-table-column label="场景名称" prop="sceneName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="场景图标" min-width="180">
        <template slot-scope="scope">
          <img :src="getImgUrl(scope.row.sceneIcon)" alt="场景图标" style="max-width: 30%; height: auto;">
        </template>
      </el-table-column>
      <el-table-column label="数字人形象" align="center" >
        <template slot-scope="scope">
          <img alt="" :src="'/szr' + scope.row.previewUrl" style="max-width: 50%; height: auto;" v-if="scope.row.previewUrl">
        </template>
      </el-table-column>      <el-table-column label="模型名称" prop="modelName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="知识库名称" prop="knowledgeBaseName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="语音发言人" prop="voiceRoleName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="创建人" prop="createBy" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-bank-card" @click="handleReview(scope.row)">详情</el-button>
          <el-button  size="mini" type="text" icon="el-icon-edit"
                     @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />

  </div>
</template>

<script>
  import { getManagementList,delManagement } from "@/api/sceneManagement/sceneManagement.js";
  import { getToken } from "@/utils/auth";

  export default {
    name: "SceneManagement",
    dicts: ["s_data_make"],
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 数据集管理表格数据
        dataMakeList: [],
        //上传图片弹窗显隐
        upload: {
          // 是否显示弹出层
          open: false,
          // 弹出层标题
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 是否更新已经存在的用户数据
          dataId: 0,
          // 设置上传的请求头部
          headers: { Authorization: "Bearer " + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/test/storage/upload",
          imageNumber: ''
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          sceneName: undefined,
        },
      };
    },
    created() {
      this.getList();
    },
    activated() {
      this.getList();
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true;
        getManagementList(this.queryParams).then((response) => {
          this.dataMakeList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },



      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.queryParams.sceneName = undefined,
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.$router.push({
          path: "/sceneManagement/addSceneManagement",
        });
      },
      /** 查看按钮操作 */
      handleReview(row) {
        this.$router.push({
          path: "/sceneManagement/reviewSceneManagement",
          query: { id: row.id },
        });
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.$router.push({
          path: "/sceneManagement/updateSceneManagement",
          query: { id: row.id },
        });
      },
      /** 删除按钮操作 */
      handleDel(row) {
        this.$modal
          .confirm("是否确认删除？")
          .then(function () {
            return delManagement(row.id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => { });
      },

      getImgUrl(pptPath) {
        let baseUrl = window.location.origin
        var imgData;
        if (pptPath.includes('/ruoyi/')) {
          // 替换路径中的 ruoyi/ 之前的部分
          const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

          if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
            imgData = 'http://127.0.0.1:9215' + finalPath
            // console.log('Final baseUrl:', baseUrl)
          } else {
            imgData = baseUrl + finalPath
          }
        }
        return imgData;

      },

      /** 字符串处理 */
      getSecondSlash(str) {
        // 使用lastIndexOf找到最后一个'/'的位置
        var lastSlashIndex = str.lastIndexOf("/");

        // 如果找到了'/'
        if (lastSlashIndex !== -1) {
          // 提取从开始到'/'前的内容
          return str.substring(0, lastSlashIndex);
        } else {
          // 如果没有找到'/'，返回原字符串
          return str;
        }
      },
      extractContent(str) {
        // 找到第二个和第三个 '/' 的位置
        let index1 = str.indexOf('/', str.indexOf('/') + 1); // 第二个 '/'
        let index2 = str.indexOf('/', index1 + 1); // 第三个 '/'

        // 提取这两个索引之间的子字符串
        if (index1 !== -1 && index2 !== -1) {
          return str.substring(index1 + 1, index2);
        } else {
          return null; // 如果没有找到符合要求的 '/'，返回null或其他默认值
        }
      },


    },
  };
</script>
