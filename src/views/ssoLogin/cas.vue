<template>
  <div>
    <el-button
        type="text"
        v-loading.fullscreen.lock="fullscreenLoading"
    >
    </el-button>

    <CenteredMessage :customMessage="customMessageData" v-if="showCenterMessage"/>

    <ssoLoad v-if="!showCenterMessage"></ssoLoad>
  </div>
</template>

<script>

import Cookies from 'js-cookie'
import { getConfigKey } from '@/api/system/config'
import { casLogin, getTokenByUserCode, loginByNameAndCardNo } from '@/api/login'
import CenteredMessage from '@/views/ssoLogin/CenteredMessage.vue'
import ssoLoad from '@/views/ssoLogin/ssoLoad.vue'
import { getToken } from '@/utils/auth'

export default {
  components: {
    CenteredMessage, ssoLoad
  },
  data() {
    return {
      fullscreenLoading: false,
      callbackAddress: '',
      TokenKey: 'Admin-Token',
      ExpiresInKey: 'Admin-Expires-In',
      accessToken: '',
      expiresIn: '',
      callbackUrl: '',
      customMessageData: {
        title: '',
        msg: '',
        aiCaiLoginUrl: '',
        loginRedirectUrl: ''
      },
      casLogout: '',
      userCode: '',
      routePptV1: '',
      userId: '',
      userName: '',
      showCenterMessage: false
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getUserCode()
  },
  mounted() {
  },
  methods: {
    getUserCode() {
      // 在 mounted 生命周期钩子中获取 query 参数
      this.userCode = this.$route.query.userCode
      console.log('User Code:', this.userCode)
      this.getTokenData()
    },

    // 获取token以及相关数据
    getTokenData() {
      const data = {
        userCode: this.userCode
      }
      try {
        getTokenByUserCode(data).then((res) => {
          console.log('res Data:', res)
          if (res.data.title === '身份合法') {
            this.showCenterMessage = false
          } else {
            this.showCenterMessage = true
          }
          this.customMessageData.title = res.data.title
          this.customMessageData.msg = res.data.msg
          this.customMessageData.aiCaiLoginUrl = res.data.aiCaiLoginUrl
          this.customMessageData.loginRedirectUrl = res.data.loginRedirectUrl

          this.casLogout = res.data.casLogout
          this.accessToken = res.data.access_token
          this.expiresIn = res.data.expires_in

          this.userId = res.data.userId
          this.userName = res.data.userName

          this.routePptV1 = res.data.routePptV1
          // 处理cache数据
          this.dealDataCache()
        })
      } catch (e) {
        console.error(e)
        this.$notify.error({ title: '错误', message: e })
      }

    },

    dealDataCache() {
      // 存储 Token 和 ExpiresIn 到 Cookies
      if (this.accessToken != null && this.accessToken !== '') {
        Cookies.set(this.TokenKey, this.accessToken)
        Cookies.set('isCasLogin', 'true')
      }
      if (this.expiresIn != null && this.expiresIn !== '') {
        Cookies.set(this.ExpiresInKey, this.expiresIn, { expires: parseInt(this.expiresIn) })
      }
      if (this.casLogout != null && this.casLogout !== '') {
        Cookies.set('casLogout', this.casLogout)
      }
      if (this.customMessageData.loginRedirectUrl != null && this.customMessageData.loginRedirectUrl !== '') {
        Cookies.set('loginRedirectUrl', this.customMessageData.loginRedirectUrl)
      }
      if (this.userId != null && this.userId !== '') {
        Cookies.set('userId', this.userId)
      }
      if (this.userName != null && this.userName !== '') {
        Cookies.set('userName', this.userName)
      }
      let routeUrlLabel = Cookies.get('route_url_label')
      console.log('routeUrlLabel', routeUrlLabel)
      switch (routeUrlLabel) {
        case '/pptV1':
          console.log('准备跳转:', routeUrlLabel)
          this.$router.push({ path: routeUrlLabel })
          break
        case 'index':
        default:
          this.$router.push({ path: '/index' })
          break
      }
    }
  }
}

</script>

<style scoped>

</style>
