<template>
  <div v-if="visible" class="overlay">
    <div class="message-box">
      <!-- 使用 customMessage 传递 title 和 msg -->
      <img src="../../assets/icons/hint.png" alt="" class="hint-image">
      <p class="message1">{{ customMessage.title }}</p>
      <p class="message2">{{ customMessage.msg }}</p>

      <!-- 按钮区域，绑定传递过来的链接 -->
      <div class="buttonBox">
        <el-button type="success" class="button1" plain @click=clickBack1>
          <a>返回认证登录页面</a>
        </el-button>
        <el-button type="info" class="button1" plain @click=clickBack2>
          <a>返回AI才系统页面</a>
        </el-button>
      </div>

    </div>
  </div>
</template>

<script>

export default {
  name: "CenteredMessage",
  data() {
    return {
      visible: true, // 控制组件是否显示
    };
  },
  props: {
    // 允许父组件传递自定义的消息内容 (默认值为 title 和 msg)
    customMessage: {
      type: Object,
      default() {
        return {
          title: "加载中，请稍候...",
          msg: "",
          loginRedirectUrl: "",
          aiCaiLoginUrl: ""
        };
      }
    }
  },
  watch: {
    // 如果需要监听 customMessage 更新，可以通过 watch 来处理
    customMessage(newVal) {
      // 可根据需要做一些处理
    }
  },
  created() {
    console.log("自定义消息内容：", this.customMessage);
  },
  methods: {
    clickBack1() {
      // 返回认证登录页面
      console.log(this.customMessage.loginRedirectUrl)
      window.location.href = this.customMessage.loginRedirectUrl;
    },
    clickBack2() {
      // 返回AI才系统页面
      console.log(this.customMessage.aiCaiLoginUrl)
      window.location.href = this.customMessage.aiCaiLoginUrl;

    },
  }
};
</script>

<style scoped>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* 层级确保在最上层 */

  /* 背景设置 */
  background-image: url("../../assets/404_images/404.gif");
  background-size: cover; /* 背景图片保持覆盖整个屏幕 */
  background-position: center center; /* 背景图居中对齐 */
  background-repeat: no-repeat; /* 背景不重复 */
}


/* 消息框样式 */
.message-box {
  background: #fff;
  padding: 20px 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 100%;
  min-height: 500px;
  min-width: 500px;
}

/* 消息文本 */
.message1 {
  font-size: 22px;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
  margin: 0;
}

.message2 {
  font-size: 20px;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
  margin: 10px;
}

.hint-image {
  width: 100%; /* 宽度占满父容器 */
  height: auto; /* 高度按比例缩放 */
  max-width: 200px; /* 可选：限制最大宽度 */
}

.buttonBox {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.button1 {
  margin-left: 10px;
}

</style>
