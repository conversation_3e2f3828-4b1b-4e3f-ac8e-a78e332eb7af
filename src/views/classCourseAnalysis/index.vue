<template>
  <div class="app-container background-container">
    <div>
      <div class="button-container">
        <el-button
          type="primary"
          size="small"
          @click="handleShowAnalysisView('showSingleCourseAnalysis')"
          :class="{ active: activeButton === 'showSingleCourseAnalysis' }"
        >
          单门课程分析统计
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleShowAnalysisView('showClassCourseAnalysis')"
          :class="{ active: activeButton === 'showClassCourseAnalysis' }"
        >
          班级课程分析统计
        </el-button>
        <!--        <el-button-->
        <!--          type="primary"-->
        <!--          size="small"-->
        <!--          v-if="showCloseView"-->
        <!--          @click="handleShowAnalysisView('close')"-->
        <!--        >-->
        <!--          关闭-->
        <!--        </el-button>-->

        <div class="box1ElSelect">
          <div class="ElSelect" v-show="showSingleCourseAnalysis">
            <el-select v-model="selectData.selectedStudent" filterable size="medium" placeholder="请选择学生"
                       @change="changeSelectStudent" @blur="changeSelectStudent">
              <el-option
                v-for="item in studentOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </div>

          <div class="ElSelect" v-show="showClassCourseAnalysis">
            <el-select v-model="selectData.selectedClass" filterable size="medium" placeholder="请选择班级"
                       @change="changeSelectClass" @blur="changeSelectClass">
              <el-option
                v-for="item in classOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>


    <div class="box1">
      <div v-show="showSingleCourseAnalysis">
        <div>
          <singleCourseAnalysis :studentData="studentData"></singleCourseAnalysis>
        </div>

      </div>
      <div v-show="showClassCourseAnalysis">
        <classCourseAnalysis :classData="classData"></classCourseAnalysis>
      </div>
    </div>
  </div>
</template>

<script>
import singleCourseAnalysis from '@/views/classCourseAnalysis/components/singleCourseAnalysis.vue'
import classCourseAnalysis from '@/views/classCourseAnalysis/components/classCourseAnalysis.vue'

export default {
  name: 'studentCourseAnalysis',
  components: { singleCourseAnalysis, classCourseAnalysis },
  data() {
    return {
      showPdfView: false,
      pdfSrc: '',
      loading: false,
      fullscreenLoading: false, // 全局加载状态
      lable: '',
      activeButton: '1', // 默认激活的按钮
      showSingleCourseAnalysis: true, // 展示单门课程分析
      showClassCourseAnalysis: false, // 展示班级课程分析
      showCloseView: false, // 展示关闭分析按钮

      studentOptions: [{
        id: '1',
        name: '陌小杰'
      }],
      classOptions: [{
        id: '1',
        name: '管理学1班'
      }],
      selectData: {
        selectedStudent: '1',
        selectedClass: '1'
      },
      studentData: { id: null },
      classData: { id: null }
    }
  },
  created() {
  }
  ,
  methods: {
    openFullScreen1() {
      this.fullscreenLoading = true // 开始加载
    }
    ,
    closeFullScreen() {
      this.fullscreenLoading = false // 关闭加载
    }
    ,
    closeShowEmbedPdf() {
      this.fullscreenLoading = false // 关闭加载
      this.showPdfView = false // 关闭显示pdf

    }
    ,
    handleShowAnalysisView(label) {
      // 重置显示状态
      this.showSingleCourseAnalysis = false
      this.showClassCourseAnalysis = false
      this.showCloseView = false // 关闭按钮默认为 false

      // 根据选择的标签更新状态
      if (label === 'showSingleCourseAnalysis') {
        this.showSingleCourseAnalysis = true
        this.activeButton = 'showSingleCourseAnalysis'
        this.showCloseView = true // 显示关闭按钮
      } else if (label === 'showClassCourseAnalysis') {
        this.showClassCourseAnalysis = true
        this.activeButton = 'showClassCourseAnalysis'
        this.showCloseView = true // 显示关闭按钮
      } else if (label === 'close') {
        this.showCloseView = false // 点击关闭按钮隐藏关闭按钮
      }
    },
    /**
     * 选择学生
     * @param data
     */
    changeSelectStudent(data) {
      // 选择的学生id
      this.studentData.id = data
      console.log('选择的学生', data)
      console.log(this.selectData)
    },
    /**
     * 选择的班级
     * @param data
     */
    changeSelectClass(data) {
      this.classData.id = data
      // 选择的班级id
      console.log('选择的班级', data)
      console.log(this.selectData)
    }

  }
}
</script>

<style scoped>

.app-container {
  padding: 20px;

}

.background-container {
  /* 设置盒子的大小 */
  width: 100%;
  height: auto; /* 让盒子高度为整个视窗高度 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.button-container {
  position: absolute; /* 使用绝对定位 */
  top: 10px; /* 距离顶部（可以根据需要调整） */
  left: 20px; /* 距离左侧（可以根据需要调整） */
  z-index: 1000; /* 确保在其他元素之上 */

}

.content {
  padding: 10px;
  min-height: 200px; /* 设置最小高度以便加载效果明显 */
}

.pdf-wrapper {
  display: flex; /* 使用 flexbox 布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  width: 100%; /* 父容器宽度 */
  height: 100vh; /* 父容器高度为视口高度 */
}

.pdf-container {
  width: 90%; /* 父容器宽度 */
  height: 100vh; /* 父容器高度为视口高度 */
}

.showPdfIframe {
  position: relative; /* 相对定位 */
  top: 0; /* 顶部距离为 0 */
  left: 0; /* 左侧距离为 0 */
  width: 100%; /* 宽度为 100% */
  height: auto; /* 高度自动调整 */
  min-height: 100vh; /* 最小高度为视口高度的一半 */
  border: none; /* 无边框 */
}


.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {

}

.bg-purple {

}

.bg-gray {
  background-color: #f0f0f0; /* 浅灰色背景 */
}

.bg-purple-light {

}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
  padding: 10px; /* 内边距，保持内容距离盒子边框 */
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.title {
  /* 字体居中 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 设置盒子的高度为一行 */
  height: 30px; /* 可以根据需要调整高度 */

  /* 设置字体大小 */
  font-size: 24px;

  /* 可选的样式 */
  font-weight: bold; /* 加粗 */
}

.title1 {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #4a4a4a; /* 设置字体颜色为深灰色 */
  font-style: italic; /* 设置字体为斜体 */
}

.litTitle {
  font-size: 16px; /* 设置字体为 18px */
  text-align: left; /* 标题靠左 */
  margin-bottom: 10px; /* 在标题和内容之间留一点距离 */
  margin-top: 20px;
  color: #3e3f41;
}

.box-center {
  /* 使用 flex 布局 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */

  /* 确保盒子有一定的高度 */
  height: 20px; /* 根据需要调整 */

  /* 可选的样式 */
  font-size: 16px;
}

.box-left {
  display: flex;
  justify-content: left; /* 左对齐 */
  align-items: center; /* 垂直居中 */

  height: 20px; /* 保持高度 */
  font-size: 16px;

  padding-left: 20px; /* 设置缩进 */
}

.box1 {

  width: 90%;
  height: auto;

  /* 背景图片 */
  background-image: url('../../views/studentCourseAnalysis/1.png'); /* 替换为实际图片路径 */

  /* 设置背景图片铺满整个盒子 */
  background-size: cover;

  /* 图片居中 */
  background-position: center;

  /* 不重复图片 */
  background-repeat: no-repeat;

  /* 确保背景图片不会跟随页面滚动 */
  background-attachment: fixed;
}

.textDataStyle {
  font-size: 16px;
  color: #666666; /* 设置字体颜色为更灰色 */
}

.box1ElSelect {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  width: 70%;
  gap: 20px;
}

.active {
  color: rgb(12, 12, 12); /* 高亮按钮的字体颜色 */
}
</style>
