<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="课程" prop="lessonName">
        <el-input v-model="queryParams.lessonName" placeholder="请输入课程名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="课程名称" prop="lessonName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="作业名称" prop="hmName" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="任课老师" prop="createBy" :show-overflow-tooltip="true" min-width="180" />
      <el-table-column label="创建时间" align="center" prop="publishTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="commitTime" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.commitTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button  :disabled="scope.row.commitStatus == 1 || scope.row.cutoffLabel" size="mini" type="text" icon="el-icon-position"
            @click="handleWork(scope.row)">写作业</el-button>
          <el-button v-if="scope.row.cutoffLabel" size="mini" type="text" icon="el-icon-position"
                     >已过期</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  getHomeworkList
} from "@/api/jobCorrectionCreation/homework.js";

export default {
  name: "Homework",
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [{}],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lessonName: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      getHomeworkList(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 写作业操作 */
    handleWork(row) {
      this.$router.push({
        path: "/homework/doHomework",
        query: { id: row.hmId },
      });
    },
  },
};
</script>
