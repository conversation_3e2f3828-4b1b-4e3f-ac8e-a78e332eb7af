<template>
  <div class="app-container ck-container">
    <div class="VSTD_box" v-if="homeworkQuestions.length !== 0">
      <el-form :model="form" ref="form">
        <div v-for="(item, index) in homeworkQuestions" :key="item.id">
          <div>
            <p style="font-weight:500">
              第{{ index + 1 }}题：{{ item.question
              }}
              <span v-if="item.questionType == 'single'">（单选）</span>
              <span v-if="item.questionType == 'multiple'">（多选）</span>
              <span v-if="item.questionType == 'blank'">（填空）</span>
              <span v-if="item.questionType == 'shortAnswer'">（简答）</span>
            </p>
          </div>
          <div v-if="item.questionType == 'single'">
            <el-form-item label="">
              <el-radio-group v-model="form[item.id]">
                <el-radio v-for="items in item.homeworkQuestionOptions" :key="items.id"
                  :label="items.optionMark">{{items.optionMark+'.'+items.optionText}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div v-if="item.questionType == 'multiple'">
            <el-form-item label="">
              <el-checkbox-group v-model="form[item.id]">
                <el-checkbox v-for="items in item.homeworkQuestionOptions" :key="items.id"
                  :label="items.optionMark">{{items.optionMark+'.'+items.optionText}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div v-if="item.questionType == 'blank'">
            <el-form-item label="">
              <el-input v-model="form[item.id]"></el-input>
            </el-form-item>
          </div>
          <div v-if="item.questionType == 'shortAnswer'">
            <el-form-item label="">
              <el-input type="textarea" v-model="form[item.id]"></el-input>
            </el-form-item>
          </div>
          <div v-if="item.questionType == 'Essay'">
            <el-form-item label="">
              <el-input type="textarea" v-model="form[item.id]"></el-input>
            </el-form-item>
          </div>
        </div>
        <div>
        </div>
      </el-form>
    </div>
    <div v-else>
      <div class="none" style="margin-left: 0px">
        <div class="none_img"></div>
        <h3>暂无试题</h3>
      </div>
    </div>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint">提交</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { getHomeWork } from "@/api/jobCorrectionCreation/jobCorrectionCreation.js";
import { addDetailList } from "@/api/jobCorrectionCreation/homework.js";
export default {
  name: 'FormContent',
  data() {
    return {
      homeworkQuestions: [],
      form: {},
    }
  },
  created() {
    this.getHomeWork()
  },
  methods: {
    getHomeWork() {
      if (this.$route.query && this.$route.query.id) {
        getHomeWork(this.$route.query.id).then(res => {
          this.homeworkQuestions = res.data.homeworkQuestions
          this.homeworkQuestions.map(item => {
            if (item.questionType == 'multiple') {
              this.$set(this.form, item.id, [])
            }
          })
        })
      }
    },
    handleSubmint() {
      const key = Object.keys(this.form)
      this.homeworkQuestions.filter(e => {
        key.find(i => {
          if (e.id == i) {
            return e.userAnswer = this.form[i]
          }
        })
      })
      this.homeworkQuestions.map(item => {
        item.questionId = item.id
        if (item.questionType == 'multiple') {
          item.userAnswer = item.userAnswer.join('、')
        }
      })
      addDetailList(this.homeworkQuestions).then(res => {
        if (res.code === 200) {
          this.$message.success('提交成功')
          this.handleBack()
        }
      })
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push('/explorationCenter7/homework')
    },
  }
}
</script>
<style lang="scss" scoped>
.ck-container {
  height: calc(100vh - 84px);
  overflow-y: auto;
}
.ck-form {
  width: 80%;
  margin: auto;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
</style>
