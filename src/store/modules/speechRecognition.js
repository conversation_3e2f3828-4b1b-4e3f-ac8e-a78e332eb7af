import SpeechWakeUp from '@/utils/voiceweakup/SpeechWakeUp';
import commandProcessor from '@/utils/voiceweakup/commandProcessor';

// 创建语音识别服务实例
const speechService = SpeechWakeUp();

const state = {
  isListening: false,
  transcript: '',
  interimTranscript: '',
  commands: {},  // 命令映射表
  activeContext: 'global'  // 当前活跃上下文
};

const mutations = {
  SET_LISTENING_STATE(state, isListening) {
    state.isListening = isListening;
  },
  
  SET_TRANSCRIPT(state, transcript) {
    state.transcript = transcript;
  },
  
  SET_INTERIM_TRANSCRIPT(state, transcript) {
    state.interimTranscript = transcript;
  },
  
  REGISTER_COMMANDS(state, { context, commands }) {
    // 使用新对象以触发响应式更新
    state.commands = {
      ...state.commands,
      [context]: { ...(state.commands[context] || {}), ...commands }
    };
  },
  
  SET_ACTIVE_CONTEXT(state, context) {
    state.activeContext = context;
  }
};

const actions = {
  // 初始化语音识别服务
  initSpeechService({ commit, dispatch }) {
    // 设置回调函数
    speechService.setCallbacks({
      onStart: () => {
        commit('SET_LISTENING_STATE', true);
        // console.log('语音识别已启动');
      },
      onEnd: () => {
        commit('SET_LISTENING_STATE', false);
        // console.log('语音识别已停止');
      },
      onResult: (result) => {
        if (result.interim) {
          commit('SET_INTERIM_TRANSCRIPT', result.interim);
        }
        
        if (result.final) {
          commit('SET_TRANSCRIPT', result.final);
          
          // 使用命令处理器处理识别结果
          dispatch('processTranscript', result.final);
        }
      },
      onError: (error) => {
        // console.error('语音识别错误:', error);
      }
    });
    
    // 检查是否支持语音识别
    const status = speechService.getStatus();
    if (status.isUnsupported) {
      console.error('当前浏览器不支持语音识别');
    }
  },
  
  // 开始语音识别
  startListening({ commit }) {
    if (!speechService) return;
    
    speechService.start();
    console.log('开始语音识别');
  },
  
  // 停止语音识别
  stopListening({ commit }) {
    if (!speechService) return;
    
    speechService.stop();
    console.log('停止语音识别');
  },
  
  // 注册语音命令
  registerCommands({ commit, state }, { context, commands }) {
    // 更新 Vuex 状态
    commit('REGISTER_COMMANDS', { context, commands });
    
    // 同时使用命令处理器注册命令
    commandProcessor.registerCommands(context, commands);
    
    console.log(`已为上下文 ${context} 注册 ${Object.keys(commands).length} 个命令`);
  },
  
  // 设置活跃上下文
  setActiveContext({ commit }, context) {
    commit('SET_ACTIVE_CONTEXT', context);
    
    // 同时更新命令处理器的活跃上下文
    commandProcessor.setActiveContext(context);
    
    console.log('当前语音识别上下文:', context);
  },
  
  // 处理识别结果
  processTranscript({ state, commit }, transcript) {
    if (!transcript) return;
    
    // 使用命令处理器处理识别结果
    const commandProcessed = commandProcessor.processTranscript(transcript);
    
    // 如果命令已处理，清空识别结果
    if (commandProcessed) {
      commit('SET_TRANSCRIPT', '');
      commit('SET_INTERIM_TRANSCRIPT', '');
    }
  },
  
  // 清空识别结果
  clearTranscript({ commit }) {
    commit('SET_TRANSCRIPT', '');
    commit('SET_INTERIM_TRANSCRIPT', '');
    speechService.clearTranscript();
  }
};

const getters = {
  // 获取当前是否正在监听
  isListening: state => state.isListening,
  
  // 获取当前识别结果
  transcript: state => state.transcript,
  
  // 获取中间识别结果
  interimTranscript: state => state.interimTranscript,
  
  // 获取指定上下文的命令
  getCommandsByContext: state => context => state.commands[context] || {},
  
  // 获取当前活跃上下文的命令
  activeCommands: state => state.commands[state.activeContext] || {},
  
  // 获取全局命令
  globalCommands: state => state.commands['global'] || {},
  
  // 获取所有可用命令（全局+当前上下文）
  availableCommands: state => {
    const globalCommands = state.commands['global'] || {};
    const contextCommands = state.commands[state.activeContext] || {};
    return { ...globalCommands, ...contextCommands };
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
