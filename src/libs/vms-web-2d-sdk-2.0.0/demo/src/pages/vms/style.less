.vms-page {
  background-color: #fff;
  font-family: '微软雅黑';

  h2 {
    color: #000;
  }
  .remote-stream-area {
    position: absolute;
    width: 420px;
    height: 100%;
    color: #333;
    text-align: center;
    border: 1px solid #ddd;
  }
  .vms-setting-area {
    padding-left: 430px;
    font-size: 12px;
    .record-button {
      width: 80px;
      height: 80px;
      border: 3px solid #c3d2ff;
      border-radius: 40px;
      background-color: #fff;
      cursor: pointer;

      &:hover {
        border: 3px solid #3d6fff;
      }
    }

    .record-button-stop {
      display: inline-block;
      position: relative;
      overflow: visible !important;
      width: 80px;
      height: 80px;
      margin: 0 auto;
      cursor: pointer;

      .record-button-circle {
        fill: #3d6fff;
        stroke: #3d6fff;
      }

      .record-button-circle-outline {
        fill: #3d6fff;
        opacity: 0.2;
        transition: 0.1s;
      }

      .record-time {
        position: absolute;
        bottom: 20px;
        width: 80px;
        color: #fff;
        font-size: 12px;
        text-align: center;
      }
    }
    .record-text {
      margin-top: 16px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #3d6fff;
    }
    .table-show {
      margin-top: 8px;

      td {
        padding: 2px 4px;
        font-size: 12px;
        color: #333;
      }
      th {
        padding: 4px;
        font-size: 12px;
        color: #000;
        font-weight: 500;
      }
    }
    .params-setting-item {
      margin-bottom: 12px;
      h4 {
        font-weight: 600;
      }
      .params-setting-item-list {
        display: flex;
        margin-top: 10px;
        & > label {
          line-height: 32px;
        }
        .params-setting-item-slider {
          flex-grow: 1;
        }
      }
    }
    .upload-file-label {
      position: relative;
      cursor: pointer;
    }
    .excle-file {
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
    }
    .uploader-btn {
      cursor: pointer;
      padding: 5px 10px;
      background: #fff;
      color: #eea931;
      border: solid 1px #eea931;
      border-radius: 5px;
      height: 31px;
      // width: 81px;
    }
  }
  .vms-setting-area * {
    font-size: 12px;
  }
}
div,
p,
ul,
li {
  margin: 0;
  padding: 0;
}
