{"name": "web-3d-virtual-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8891", "build": "vite build", "preview": "vite preview", "build-sdk": "rollup -c"}, "dependencies": {"@loadable/component": "^5.15.2", "antd": "^4.22.3", "crypto-js": "^4.1.1", "js-base64": "^3.7.2", "md5": "^2.3.0", "react": "17.x", "react-dom": "17.x", "react-router": "^5.3.3", "react-router-config": "^5.1.1", "react-router-dom": "^5.3.3", "vconsole": "^3.15.0"}, "devDependencies": {"@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^2.0.0", "flv.js": "^1.6.2", "less": "^4.1.3", "three": "^0.145.0", "typescript": "^4.6.4", "vite": "^3.0.0"}}