/*!
 * vms.js v2.0.0
 * (c) 2022-2023 
 * Released under the MIT License in iflytek.
 */
import e from"crypto";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function i(e,t,i){return e(i={path:t,exports:{},require:function(e,t){return r()}},i.exports),i.exports}function r(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var n=i((function(i,r){var n;i.exports=n=n||function(i,r){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==t&&t.crypto&&(n=t.crypto),!n)try{n=e}catch(e){}var o=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function e(){}return function(t){var i;return e.prototype=t,i=new e,e.prototype=null,i}}(),a={},c=a.lib={},u=c.Base={extend:function(e){var t=s(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},d=c.WordArray=u.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,i=e.words,r=this.sigBytes,n=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<n;o++)t[r+o>>>2]|=(i[o>>>2]>>>24-o%4*8&255)<<24-(r+o)%4*8;else for(var s=0;s<n;s+=4)t[r+s>>>2]=i[s>>>2];return this.sigBytes+=n,this},clamp:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=i.ceil(t/4)},clone:function(){var e=u.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],i=0;i<e;i+=4)t.push(o());return new d.init(t,e)}}),l=a.enc={},h=l.Hex={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],n=0;n<i;n++){var o=t[n>>>2]>>>24-n%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r+=2)i[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new d.init(i,t/2)}},p=l.Latin1={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],n=0;n<i;n++)r.push(String.fromCharCode(t[n>>>2]>>>24-n%4*8&255));return r.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r++)i[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new d.init(i,t)}},f=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(p.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return p.parse(unescape(encodeURIComponent(e)))}},m=c.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new d.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(e){var t,r=this._data,n=r.words,o=r.sigBytes,s=this.blockSize,a=o/(4*s),c=(a=e?i.ceil(a):i.max((0|a)-this._minBufferSize,0))*s,u=i.min(4*c,o);if(c){for(var l=0;l<c;l+=s)this._doProcessBlock(n,l);t=n.splice(0,c),r.sigBytes-=u}return new d.init(t,u)},clone:function(){var e=u.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});c.Hasher=m.extend({cfg:u.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){m.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,i){return new e.init(i).finalize(t)}},_createHmacHelper:function(e){return function(t,i){return new g.HMAC.init(e,i).finalize(t)}}});var g=a.algo={};return a}(Math)})),o=(i((function(e,t){var i,r,o,s,a;e.exports=(r=(i=(a=n).lib).Base,o=i.WordArray,(s=a.x64={}).Word=r.extend({init:function(e,t){this.high=e,this.low=t}}),s.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,i=[],r=0;r<t;r++){var n=e[r];i.push(n.high),i.push(n.low)}return o.create(i,this.sigBytes)},clone:function(){for(var e=r.clone.call(this),t=e.words=this.words.slice(0),i=t.length,n=0;n<i;n++)t[n]=t[n].clone();return e}}),a)})),i((function(e,t){var i;e.exports=(i=n,function(){if("function"==typeof ArrayBuffer){var e=i.lib.WordArray,t=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var i=e.byteLength,r=[],n=0;n<i;n++)r[n>>>2]|=e[n]<<24-n%4*8;t.call(this,r,i)}else t.apply(this,arguments)}).prototype=e}}(),i.lib.WordArray)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i.lib.WordArray,t=i.enc;function r(e){return e<<8&4278255360|e>>>8&16711935}t.Utf16=t.Utf16BE={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],n=0;n<i;n+=2)r.push(String.fromCharCode(t[n>>>2]>>>16-n%4*8&65535));return r.join("")},parse:function(t){for(var i=t.length,r=[],n=0;n<i;n++)r[n>>>1]|=t.charCodeAt(n)<<16-n%2*16;return e.create(r,2*i)}},t.Utf16LE={stringify:function(e){for(var t=e.words,i=e.sigBytes,n=[],o=0;o<i;o+=2){var s=r(t[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(s))}return n.join("")},parse:function(t){for(var i=t.length,n=[],o=0;o<i;o++)n[o>>>1]|=r(t.charCodeAt(o)<<16-o%2*16);return e.create(n,2*i)}}}(),i.enc.Utf16)})),i((function(e,t){var i,r;e.exports=(i=(r=n).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,i=e.sigBytes,r=this._map;e.clamp();for(var n=[],o=0;o<i;o+=3)for(var s=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<i;a++)n.push(r.charAt(s>>>6*(3-a)&63));var c=r.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return function(e,t,r){for(var n=[],o=0,s=0;s<t;s++)if(s%4){var a=r[e.charCodeAt(s-1)]<<s%4*2,c=r[e.charCodeAt(s)]>>>6-s%4*2;n[o>>>2]|=(a|c)<<24-o%4*8,o++}return i.create(n,o)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},r.enc.Base64)})),i((function(e,t){var i,r;e.exports=(i=(r=n).lib.WordArray,r.enc.Base64url={stringify:function(e,t=!0){var i=e.words,r=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var o=[],s=0;s<r;s+=3)for(var a=(i[s>>>2]>>>24-s%4*8&255)<<16|(i[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|i[s+2>>>2]>>>24-(s+2)%4*8&255,c=0;c<4&&s+.75*c<r;c++)o.push(n.charAt(a>>>6*(3-c)&63));var u=n.charAt(64);if(u)for(;o.length%4;)o.push(u);return o.join("")},parse:function(e,t=!0){var r=e.length,n=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var s=0;s<n.length;s++)o[n.charCodeAt(s)]=s}var a=n.charAt(64);if(a){var c=e.indexOf(a);-1!==c&&(r=c)}return function(e,t,r){for(var n=[],o=0,s=0;s<t;s++)if(s%4){var a=r[e.charCodeAt(s-1)]<<s%4*2,c=r[e.charCodeAt(s)]>>>6-s%4*2;n[o>>>2]|=(a|c)<<24-o%4*8,o++}return i.create(n,o)}(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},r.enc.Base64url)})),i((function(e,t){var i;e.exports=(i=n,function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,s=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=s.MD5=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var i=0;i<16;i++){var r=t+i,n=e[r];e[r]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var o=this._hash.words,s=e[t+0],c=e[t+1],p=e[t+2],f=e[t+3],m=e[t+4],g=e[t+5],v=e[t+6],b=e[t+7],y=e[t+8],S=e[t+9],E=e[t+10],_=e[t+11],C=e[t+12],I=e[t+13],T=e[t+14],R=e[t+15],k=o[0],O=o[1],w=o[2],A=o[3];k=u(k,O,w,A,s,7,a[0]),A=u(A,k,O,w,c,12,a[1]),w=u(w,A,k,O,p,17,a[2]),O=u(O,w,A,k,f,22,a[3]),k=u(k,O,w,A,m,7,a[4]),A=u(A,k,O,w,g,12,a[5]),w=u(w,A,k,O,v,17,a[6]),O=u(O,w,A,k,b,22,a[7]),k=u(k,O,w,A,y,7,a[8]),A=u(A,k,O,w,S,12,a[9]),w=u(w,A,k,O,E,17,a[10]),O=u(O,w,A,k,_,22,a[11]),k=u(k,O,w,A,C,7,a[12]),A=u(A,k,O,w,I,12,a[13]),w=u(w,A,k,O,T,17,a[14]),k=d(k,O=u(O,w,A,k,R,22,a[15]),w,A,c,5,a[16]),A=d(A,k,O,w,v,9,a[17]),w=d(w,A,k,O,_,14,a[18]),O=d(O,w,A,k,s,20,a[19]),k=d(k,O,w,A,g,5,a[20]),A=d(A,k,O,w,E,9,a[21]),w=d(w,A,k,O,R,14,a[22]),O=d(O,w,A,k,m,20,a[23]),k=d(k,O,w,A,S,5,a[24]),A=d(A,k,O,w,T,9,a[25]),w=d(w,A,k,O,f,14,a[26]),O=d(O,w,A,k,y,20,a[27]),k=d(k,O,w,A,I,5,a[28]),A=d(A,k,O,w,p,9,a[29]),w=d(w,A,k,O,b,14,a[30]),k=l(k,O=d(O,w,A,k,C,20,a[31]),w,A,g,4,a[32]),A=l(A,k,O,w,y,11,a[33]),w=l(w,A,k,O,_,16,a[34]),O=l(O,w,A,k,T,23,a[35]),k=l(k,O,w,A,c,4,a[36]),A=l(A,k,O,w,m,11,a[37]),w=l(w,A,k,O,b,16,a[38]),O=l(O,w,A,k,E,23,a[39]),k=l(k,O,w,A,I,4,a[40]),A=l(A,k,O,w,s,11,a[41]),w=l(w,A,k,O,f,16,a[42]),O=l(O,w,A,k,v,23,a[43]),k=l(k,O,w,A,S,4,a[44]),A=l(A,k,O,w,C,11,a[45]),w=l(w,A,k,O,R,16,a[46]),k=h(k,O=l(O,w,A,k,p,23,a[47]),w,A,s,6,a[48]),A=h(A,k,O,w,b,10,a[49]),w=h(w,A,k,O,T,15,a[50]),O=h(O,w,A,k,g,21,a[51]),k=h(k,O,w,A,C,6,a[52]),A=h(A,k,O,w,f,10,a[53]),w=h(w,A,k,O,E,15,a[54]),O=h(O,w,A,k,c,21,a[55]),k=h(k,O,w,A,y,6,a[56]),A=h(A,k,O,w,R,10,a[57]),w=h(w,A,k,O,v,15,a[58]),O=h(O,w,A,k,I,21,a[59]),k=h(k,O,w,A,m,6,a[60]),A=h(A,k,O,w,_,10,a[61]),w=h(w,A,k,O,p,15,a[62]),O=h(O,w,A,k,S,21,a[63]),o[0]=o[0]+k|0,o[1]=o[1]+O|0,o[2]=o[2]+w|0,o[3]=o[3]+A|0},_doFinalize:function(){var t=this._data,i=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;i[n>>>5]|=128<<24-n%32;var o=e.floor(r/4294967296),s=r;i[15+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),i[14+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(i.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var d=c[u];c[u]=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,i,r,n,o,s){var a=e+(t&i|~t&r)+n+s;return(a<<o|a>>>32-o)+t}function d(e,t,i,r,n,o,s){var a=e+(t&r|i&~r)+n+s;return(a<<o|a>>>32-o)+t}function l(e,t,i,r,n,o,s){var a=e+(t^i^r)+n+s;return(a<<o|a>>>32-o)+t}function h(e,t,i,r,n,o,s){var a=e+(i^(t|~r))+n+s;return(a<<o|a>>>32-o)+t}t.MD5=o._createHelper(c),t.HmacMD5=o._createHmacHelper(c)}(Math),i.MD5)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib,r=t.WordArray,n=t.Hasher,o=[],s=e.algo.SHA1=n.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],n=i[1],s=i[2],a=i[3],c=i[4],u=0;u<80;u++){if(u<16)o[u]=0|e[t+u];else{var d=o[u-3]^o[u-8]^o[u-14]^o[u-16];o[u]=d<<1|d>>>31}var l=(r<<5|r>>>27)+c+o[u];l+=u<20?1518500249+(n&s|~n&a):u<40?1859775393+(n^s^a):u<60?(n&s|n&a|s&a)-1894007588:(n^s^a)-899497514,c=a,a=s,s=n<<30|n>>>2,n=r,r=l}i[0]=i[0]+r|0,i[1]=i[1]+n|0,i[2]=i[2]+s|0,i[3]=i[3]+a|0,i[4]=i[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(i/4294967296),t[15+(r+64>>>9<<4)]=i,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA1=n._createHelper(s),e.HmacSHA1=n._createHmacHelper(s)}(),i.SHA1)})),i((function(e,t){var i;e.exports=(i=n,function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,s=t.algo,a=[],c=[];!function(){function t(t){for(var i=e.sqrt(t),r=2;r<=i;r++)if(!(t%r))return!1;return!0}function i(e){return 4294967296*(e-(0|e))|0}for(var r=2,n=0;n<64;)t(r)&&(n<8&&(a[n]=i(e.pow(r,.5))),c[n]=i(e.pow(r,1/3)),n++),r++}();var u=[],d=s.SHA256=o.extend({_doReset:function(){this._hash=new n.init(a.slice(0))},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],n=i[1],o=i[2],s=i[3],a=i[4],d=i[5],l=i[6],h=i[7],p=0;p<64;p++){if(p<16)u[p]=0|e[t+p];else{var f=u[p-15],m=u[p-2];u[p]=((f<<25|f>>>7)^(f<<14|f>>>18)^f>>>3)+u[p-7]+((m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10)+u[p-16]}var g=r&n^r&o^n&o,v=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&d^~a&l)+c[p]+u[p];h=l,l=d,d=a,a=s+v|0,s=o,o=n,n=r,r=v+(((r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22))+g)|0}i[0]=i[0]+r|0,i[1]=i[1]+n|0,i[2]=i[2]+o|0,i[3]=i[3]+s|0,i[4]=i[4]+a|0,i[5]=i[5]+d|0,i[6]=i[6]+l|0,i[7]=i[7]+h|0},_doFinalize:function(){var t=this._data,i=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return i[n>>>5]|=128<<24-n%32,i[14+(n+64>>>9<<4)]=e.floor(r/4294967296),i[15+(n+64>>>9<<4)]=r,t.sigBytes=4*i.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(d),t.HmacSHA256=o._createHmacHelper(d)}(Math),i.SHA256)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.WordArray,r=e.algo,n=r.SHA256,o=r.SHA224=n.extend({_doReset:function(){this._hash=new t.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=n._doFinalize.call(this);return e.sigBytes-=4,e}});e.SHA224=n._createHelper(o),e.HmacSHA224=n._createHmacHelper(o)}(),i.SHA224)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.Hasher,r=e.x64,n=r.Word,o=r.WordArray,s=e.algo;function a(){return n.create.apply(n,arguments)}var c=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],u=[];!function(){for(var e=0;e<80;e++)u[e]=a()}();var d=s.SHA512=t.extend({_doReset:function(){this._hash=new o.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],n=i[1],o=i[2],s=i[3],a=i[4],d=i[5],l=i[6],h=i[7],p=r.high,f=r.low,m=n.high,g=n.low,v=o.high,b=o.low,y=s.high,S=s.low,E=a.high,_=a.low,C=d.high,I=d.low,T=l.high,R=l.low,k=h.high,O=h.low,w=p,A=f,P=m,L=g,D=v,x=b,M=y,U=S,N=E,V=_,B=C,F=I,j=T,W=R,H=k,G=O,z=0;z<80;z++){var J,K,Y=u[z];if(z<16)K=Y.high=0|e[t+2*z],J=Y.low=0|e[t+2*z+1];else{var q=u[z-15],X=q.high,$=q.low,Q=($>>>1|X<<31)^($>>>8|X<<24)^($>>>7|X<<25),Z=u[z-2],ee=Z.high,te=Z.low,ie=(te>>>19|ee<<13)^(te<<3|ee>>>29)^(te>>>6|ee<<26),re=u[z-7],ne=u[z-16],oe=ne.low;Y.high=K=(K=(K=((X>>>1|$<<31)^(X>>>8|$<<24)^X>>>7)+re.high+((J=Q+re.low)>>>0<Q>>>0?1:0))+((ee>>>19|te<<13)^(ee<<3|te>>>29)^ee>>>6)+((J+=ie)>>>0<ie>>>0?1:0))+ne.high+((J+=oe)>>>0<oe>>>0?1:0),Y.low=J}var se,ae=N&B^~N&j,ce=V&F^~V&W,ue=w&P^w&D^P&D,de=(A>>>28|w<<4)^(A<<30|w>>>2)^(A<<25|w>>>7),le=c[z],he=le.low,pe=H+((N>>>14|V<<18)^(N>>>18|V<<14)^(N<<23|V>>>9))+((se=G+((V>>>14|N<<18)^(V>>>18|N<<14)^(V<<23|N>>>9)))>>>0<G>>>0?1:0),fe=de+(A&L^A&x^L&x);H=j,G=W,j=B,W=F,B=N,F=V,N=M+(pe=(pe=(pe=pe+ae+((se+=ce)>>>0<ce>>>0?1:0))+le.high+((se+=he)>>>0<he>>>0?1:0))+K+((se+=J)>>>0<J>>>0?1:0))+((V=U+se|0)>>>0<U>>>0?1:0)|0,M=D,U=x,D=P,x=L,P=w,L=A,w=pe+(((w>>>28|A<<4)^(w<<30|A>>>2)^(w<<25|A>>>7))+ue+(fe>>>0<de>>>0?1:0))+((A=se+fe|0)>>>0<se>>>0?1:0)|0}f=r.low=f+A,r.high=p+w+(f>>>0<A>>>0?1:0),g=n.low=g+L,n.high=m+P+(g>>>0<L>>>0?1:0),b=o.low=b+x,o.high=v+D+(b>>>0<x>>>0?1:0),S=s.low=S+U,s.high=y+M+(S>>>0<U>>>0?1:0),_=a.low=_+V,a.high=E+N+(_>>>0<V>>>0?1:0),I=d.low=I+F,d.high=C+B+(I>>>0<F>>>0?1:0),R=l.low=R+W,l.high=T+j+(R>>>0<W>>>0?1:0),O=h.low=O+G,h.high=k+H+(O>>>0<G>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(r+128>>>10<<5)]=Math.floor(i/4294967296),t[31+(r+128>>>10<<5)]=i,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(d),e.HmacSHA512=t._createHmacHelper(d)}(),i.SHA512)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.x64,r=t.Word,n=t.WordArray,o=e.algo,s=o.SHA512,a=o.SHA384=s.extend({_doReset:function(){this._hash=new n.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=s._doFinalize.call(this);return e.sigBytes-=16,e}});e.SHA384=s._createHelper(a),e.HmacSHA384=s._createHmacHelper(a)}(),i.SHA384)})),i((function(e,t){var i;e.exports=(i=n,function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,s=t.x64.Word,a=t.algo,c=[],u=[],d=[];!function(){for(var e=1,t=0,i=0;i<24;i++){c[e+5*t]=(i+1)*(i+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)u[e+5*t]=t+(2*e+3*t)%5*5;for(var n=1,o=0;o<24;o++){for(var a=0,l=0,h=0;h<7;h++){if(1&n){var p=(1<<h)-1;p<32?l^=1<<p:a^=1<<p-32}128&n?n=n<<1^113:n<<=1}d[o]=s.create(a,l)}}();var l=[];!function(){for(var e=0;e<25;e++)l[e]=s.create()}();var h=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var i=this._state,r=this.blockSize/2,n=0;n<r;n++){var o=e[t+2*n],s=e[t+2*n+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),(P=i[n]).high^=s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),P.low^=o}for(var a=0;a<24;a++){for(var h=0;h<5;h++){for(var p=0,f=0,m=0;m<5;m++)p^=(P=i[h+5*m]).high,f^=P.low;var g=l[h];g.high=p,g.low=f}for(h=0;h<5;h++){var v=l[(h+4)%5],b=l[(h+1)%5],y=b.high,S=b.low;for(p=v.high^(y<<1|S>>>31),f=v.low^(S<<1|y>>>31),m=0;m<5;m++)(P=i[h+5*m]).high^=p,P.low^=f}for(var E=1;E<25;E++){var _=(P=i[E]).high,C=P.low,I=c[E];I<32?(p=_<<I|C>>>32-I,f=C<<I|_>>>32-I):(p=C<<I-32|_>>>64-I,f=_<<I-32|C>>>64-I);var T=l[u[E]];T.high=p,T.low=f}var R=l[0],k=i[0];for(R.high=k.high,R.low=k.low,h=0;h<5;h++)for(m=0;m<5;m++){var O=l[E=h+5*m],w=l[(h+1)%5+5*m],A=l[(h+2)%5+5*m];(P=i[E]).high=O.high^~w.high&A.high,P.low=O.low^~w.low&A.low}var P,L=d[a];(P=i[0]).high^=L.high,P.low^=L.low}},_doFinalize:function(){var t=this._data,i=t.words,r=8*t.sigBytes,o=32*this.blockSize;i[r>>>5]|=1<<24-r%32,i[(e.ceil((r+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*i.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,u=[],d=0;d<c;d++){var l=s[d],h=l.high,p=l.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),u.push(p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8)),u.push(h)}return new n.init(u,a)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),i=0;i<25;i++)t[i]=t[i].clone();return e}});t.SHA3=o._createHelper(h),t.HmacSHA3=o._createHmacHelper(h)}(Math),i.SHA3)})),i((function(e,t){var i;e.exports=(i=n,
/** @preserve
	(c) 2012 by Cédric Mesnil. All rights reserved.

	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
	    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*/
function(e){var t=i,r=t.lib,n=r.WordArray,o=r.Hasher,s=t.algo,a=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),u=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),d=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),l=n.create([0,1518500249,1859775393,2400959708,2840853838]),h=n.create([1352829926,1548603684,1836072691,2053994217,0]),p=s.RIPEMD160=o.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var i=0;i<16;i++){var r=t+i,n=e[r];e[r]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var o,s,p,S,E,_,C,I,T,R,k,O=this._hash.words,w=l.words,A=h.words,P=a.words,L=c.words,D=u.words,x=d.words;for(_=o=O[0],C=s=O[1],I=p=O[2],T=S=O[3],R=E=O[4],i=0;i<80;i+=1)k=o+e[t+P[i]]|0,k+=i<16?f(s,p,S)+w[0]:i<32?m(s,p,S)+w[1]:i<48?g(s,p,S)+w[2]:i<64?v(s,p,S)+w[3]:b(s,p,S)+w[4],k=(k=y(k|=0,D[i]))+E|0,o=E,E=S,S=y(p,10),p=s,s=k,k=_+e[t+L[i]]|0,k+=i<16?b(C,I,T)+A[0]:i<32?v(C,I,T)+A[1]:i<48?g(C,I,T)+A[2]:i<64?m(C,I,T)+A[3]:f(C,I,T)+A[4],k=(k=y(k|=0,x[i]))+R|0,_=R,R=T,T=y(I,10),I=C,C=k;k=O[1]+p+T|0,O[1]=O[2]+S+R|0,O[2]=O[3]+E+_|0,O[3]=O[4]+o+C|0,O[4]=O[0]+s+I|0,O[0]=k},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,o=n.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return n},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t,i){return e^t^i}function m(e,t,i){return e&t|~e&i}function g(e,t,i){return(e|~t)^i}function v(e,t,i){return e&i|t&~i}function b(e,t,i){return e^(t|~i)}function y(e,t){return e<<t|e>>>32-t}t.RIPEMD160=o._createHelper(p),t.HmacRIPEMD160=o._createHmacHelper(p)}(),i.RIPEMD160)})),i((function(e,t){var i,r;e.exports=(r=(i=n).enc.Utf8,void(i.algo.HMAC=i.lib.Base.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var i=e.blockSize,n=4*i;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),s=this._iKey=t.clone(),a=o.words,c=s.words,u=0;u<i;u++)a[u]^=1549556828,c[u]^=909522486;o.sigBytes=s.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,i=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(i))}})))})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib,r=t.Base,n=t.WordArray,o=e.algo,s=o.HMAC,a=o.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:o.SHA1,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var i=this.cfg,r=s.create(i.hasher,e),o=n.create(),a=n.create([1]),c=o.words,u=a.words,d=i.keySize,l=i.iterations;c.length<d;){var h=r.update(t).finalize(a);r.reset();for(var p=h.words,f=p.length,m=h,g=1;g<l;g++){m=r.finalize(m),r.reset();for(var v=m.words,b=0;b<f;b++)p[b]^=v[b]}o.concat(h),u[0]++}return o.sigBytes=4*d,o}});e.PBKDF2=function(e,t,i){return a.create(i).compute(e,t)}}(),i.PBKDF2)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib,r=t.Base,n=t.WordArray,o=e.algo,s=o.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:o.MD5,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var i,r=this.cfg,o=r.hasher.create(),s=n.create(),a=s.words,c=r.keySize,u=r.iterations;a.length<c;){i&&o.update(i),i=o.update(e).finalize(t),o.reset();for(var d=1;d<u;d++)i=o.finalize(i),o.reset();s.concat(i)}return s.sigBytes=4*c,s}});e.EvpKDF=function(e,t,i){return s.create(i).compute(e,t)}}(),i.EvpKDF)})),i((function(e,t){var i;e.exports=void((i=n).lib.Cipher||function(e){var t=i,r=t.lib,n=r.Base,o=r.WordArray,s=r.BufferedBlockAlgorithm,a=t.enc,c=a.Base64,u=t.algo.EvpKDF,d=r.Cipher=s.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,i){this.cfg=this.cfg.extend(i),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?y:v}return function(t){return{encrypt:function(i,r,n){return e(r).encrypt(t,i,r,n)},decrypt:function(i,r,n){return e(r).decrypt(t,i,r,n)}}}}()});r.StreamCipher=d.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var l=t.mode={},h=r.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=l.CBC=function(){var e=h.extend();function t(e,t,i){var r,n=this._iv;n?(r=n,this._iv=void 0):r=this._prevBlock;for(var o=0;o<i;o++)e[t+o]^=r[o]}return e.Encryptor=e.extend({processBlock:function(e,i){var r=this._cipher,n=r.blockSize;t.call(this,e,i,n),r.encryptBlock(e,i),this._prevBlock=e.slice(i,i+n)}}),e.Decryptor=e.extend({processBlock:function(e,i){var r=this._cipher,n=r.blockSize,o=e.slice(i,i+n);r.decryptBlock(e,i),t.call(this,e,i,n),this._prevBlock=o}}),e}(),f=(t.pad={}).Pkcs7={pad:function(e,t){for(var i=4*t,r=i-e.sigBytes%i,n=r<<24|r<<16|r<<8|r,s=[],a=0;a<r;a+=4)s.push(n);var c=o.create(s,r);e.concat(c)},unpad:function(e){e.sigBytes-=255&e.words[e.sigBytes-1>>>2]}};r.BlockCipher=d.extend({cfg:d.cfg.extend({mode:p,padding:f}),reset:function(){var e;d.reset.call(this);var t=this.cfg,i=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,i&&i.words):(this._mode=e.call(r,this,i&&i.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var m=r.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,i=e.salt;return(i?o.create([1398893684,1701076831]).concat(i).concat(t):t).toString(c)},parse:function(e){var t,i=c.parse(e),r=i.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=o.create(r.slice(2,4)),r.splice(0,4),i.sigBytes-=16),m.create({ciphertext:i,salt:t})}},v=r.SerializableCipher=n.extend({cfg:n.extend({format:g}),encrypt:function(e,t,i,r){r=this.cfg.extend(r);var n=e.createEncryptor(i,r),o=n.finalize(t),s=n.cfg;return m.create({ciphertext:o,key:i,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,i,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(i,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),b=(t.kdf={}).OpenSSL={execute:function(e,t,i,r){r||(r=o.random(8));var n=u.create({keySize:t+i}).compute(e,r),s=o.create(n.words.slice(t),4*i);return n.sigBytes=4*t,m.create({key:n,iv:s,salt:r})}},y=r.PasswordBasedCipher=v.extend({cfg:v.cfg.extend({kdf:b}),encrypt:function(e,t,i,r){var n=(r=this.cfg.extend(r)).kdf.execute(i,e.keySize,e.ivSize);r.iv=n.iv;var o=v.encrypt.call(this,e,t,n.key,r);return o.mixIn(n),o},decrypt:function(e,t,i,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var n=r.kdf.execute(i,e.keySize,e.ivSize,t.salt);return r.iv=n.iv,v.decrypt.call(this,e,t,n.key,r)}})}())})),i((function(e,t){var i;e.exports=((i=n).mode.CFB=function(){var e=i.lib.BlockCipherMode.extend();function t(e,t,i,r){var n,o=this._iv;o?(n=o.slice(0),this._iv=void 0):n=this._prevBlock,r.encryptBlock(n,0);for(var s=0;s<i;s++)e[t+s]^=n[s]}return e.Encryptor=e.extend({processBlock:function(e,i){var r=this._cipher,n=r.blockSize;t.call(this,e,i,n,r),this._prevBlock=e.slice(i,i+n)}}),e.Decryptor=e.extend({processBlock:function(e,i){var r=this._cipher,n=r.blockSize,o=e.slice(i,i+n);t.call(this,e,i,n,r),this._prevBlock=o}}),e}(),i.mode.CFB)})),i((function(e,t){var i,r,o;e.exports=((o=n).mode.CTR=(i=o.lib.BlockCipherMode.extend(),r=i.Encryptor=i.extend({processBlock:function(e,t){var i=this._cipher,r=i.blockSize,n=this._iv,o=this._counter;n&&(o=this._counter=n.slice(0),this._iv=void 0);var s=o.slice(0);i.encryptBlock(s,0),o[r-1]=o[r-1]+1|0;for(var a=0;a<r;a++)e[t+a]^=s[a]}}),i.Decryptor=r,i),o.mode.CTR)})),i((function(e,t){var i;e.exports=(
/** @preserve
	 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
	 * derived from CryptoJS.mode.CTR
	 * <NAME_EMAIL>
	 */
(i=n).mode.CTRGladman=function(){var e=i.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,i=e>>8&255,r=255&e;255===t?(t=0,255===i?(i=0,255===r?r=0:++r):++i):++t,e=0,e+=t<<16,e+=i<<8,e+=r}else e+=1<<24;return e}var r=e.Encryptor=e.extend({processBlock:function(e,i){var r=this._cipher,n=r.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),function(e){0===(e[0]=t(e[0]))&&(e[1]=t(e[1]))}(s);var a=s.slice(0);r.encryptBlock(a,0);for(var c=0;c<n;c++)e[i+c]^=a[c]}});return e.Decryptor=r,e}(),i.mode.CTRGladman)})),i((function(e,t){var i,r,o;e.exports=((o=n).mode.OFB=(i=o.lib.BlockCipherMode.extend(),r=i.Encryptor=i.extend({processBlock:function(e,t){var i=this._cipher,r=i.blockSize,n=this._iv,o=this._keystream;n&&(o=this._keystream=n.slice(0),this._iv=void 0),i.encryptBlock(o,0);for(var s=0;s<r;s++)e[t+s]^=o[s]}}),i.Decryptor=r,i),o.mode.OFB)})),i((function(e,t){var i,r;e.exports=((r=n).mode.ECB=((i=r.lib.BlockCipherMode.extend()).Encryptor=i.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),i.Decryptor=i.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),i),r.mode.ECB)})),i((function(e,t){var i;e.exports=((i=n).pad.AnsiX923={pad:function(e,t){var i=e.sigBytes,r=4*t,n=r-i%r,o=i+n-1;e.clamp(),e.words[o>>>2]|=n<<24-o%4*8,e.sigBytes+=n},unpad:function(e){e.sigBytes-=255&e.words[e.sigBytes-1>>>2]}},i.pad.Ansix923)})),i((function(e,t){var i;e.exports=((i=n).pad.Iso10126={pad:function(e,t){var r=4*t,n=r-e.sigBytes%r;e.concat(i.lib.WordArray.random(n-1)).concat(i.lib.WordArray.create([n<<24],1))},unpad:function(e){e.sigBytes-=255&e.words[e.sigBytes-1>>>2]}},i.pad.Iso10126)})),i((function(e,t){var i;e.exports=((i=n).pad.Iso97971={pad:function(e,t){e.concat(i.lib.WordArray.create([2147483648],1)),i.pad.ZeroPadding.pad(e,t)},unpad:function(e){i.pad.ZeroPadding.unpad(e),e.sigBytes--}},i.pad.Iso97971)})),i((function(e,t){var i;e.exports=((i=n).pad.ZeroPadding={pad:function(e,t){var i=4*t;e.clamp(),e.sigBytes+=i-(e.sigBytes%i||i)},unpad:function(e){var t=e.words,i=e.sigBytes-1;for(i=e.sigBytes-1;i>=0;i--)if(t[i>>>2]>>>24-i%4*8&255){e.sigBytes=i+1;break}}},i.pad.ZeroPadding)})),i((function(e,t){var i;e.exports=((i=n).pad.NoPadding={pad:function(){},unpad:function(){}},i.pad.NoPadding)})),i((function(e,t){var i,r,o;e.exports=(i=(o=n).lib.CipherParams,r=o.enc.Hex,o.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){var t=r.parse(e);return i.create({ciphertext:t})}},o.format.Hex)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.BlockCipher,r=e.algo,n=[],o=[],s=[],a=[],c=[],u=[],d=[],l=[],h=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var i=0,r=0;for(t=0;t<256;t++){var f=r^r<<1^r<<2^r<<3^r<<4;n[i]=f=f>>>8^255&f^99,o[f]=i;var m,g=e[i],v=e[g],b=e[v];s[i]=(m=257*e[f]^16843008*f)<<24|m>>>8,a[i]=m<<16|m>>>16,c[i]=m<<8|m>>>24,u[i]=m,d[f]=(m=16843009*b^65537*v^257*g^16843008*i)<<24|m>>>8,l[f]=m<<16|m>>>16,h[f]=m<<8|m>>>24,p[f]=m,i?(i=g^e[e[e[b^g]]],r^=e[e[r]]):i=r=1}}();var f=[0,1,2,4,8,16,32,64,128,27,54],m=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,i=e.sigBytes/4,r=4*((this._nRounds=i+6)+1),o=this._keySchedule=[],s=0;s<r;s++)s<i?o[s]=t[s]:(u=o[s-1],s%i?i>6&&s%i==4&&(u=n[u>>>24]<<24|n[u>>>16&255]<<16|n[u>>>8&255]<<8|n[255&u]):(u=n[(u=u<<8|u>>>24)>>>24]<<24|n[u>>>16&255]<<16|n[u>>>8&255]<<8|n[255&u],u^=f[s/i|0]<<24),o[s]=o[s-i]^u);for(var a=this._invKeySchedule=[],c=0;c<r;c++){if(s=r-c,c%4)var u=o[s];else u=o[s-4];a[c]=c<4||s<=4?u:d[n[u>>>24]]^l[n[u>>>16&255]]^h[n[u>>>8&255]]^p[n[255&u]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,c,u,n)},decryptBlock:function(e,t){var i=e[t+1];e[t+1]=e[t+3],e[t+3]=i,this._doCryptBlock(e,t,this._invKeySchedule,d,l,h,p,o),i=e[t+1],e[t+1]=e[t+3],e[t+3]=i},_doCryptBlock:function(e,t,i,r,n,o,s,a){for(var c=this._nRounds,u=e[t]^i[0],d=e[t+1]^i[1],l=e[t+2]^i[2],h=e[t+3]^i[3],p=4,f=1;f<c;f++){var m=r[u>>>24]^n[d>>>16&255]^o[l>>>8&255]^s[255&h]^i[p++],g=r[d>>>24]^n[l>>>16&255]^o[h>>>8&255]^s[255&u]^i[p++],v=r[l>>>24]^n[h>>>16&255]^o[u>>>8&255]^s[255&d]^i[p++],b=r[h>>>24]^n[u>>>16&255]^o[d>>>8&255]^s[255&l]^i[p++];u=m,d=g,l=v,h=b}m=(a[u>>>24]<<24|a[d>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^i[p++],g=(a[d>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&u])^i[p++],v=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[u>>>8&255]<<8|a[255&d])^i[p++],b=(a[h>>>24]<<24|a[u>>>16&255]<<16|a[d>>>8&255]<<8|a[255&l])^i[p++],e[t]=m,e[t+1]=g,e[t+2]=v,e[t+3]=b},keySize:8});e.AES=t._createHelper(m)}(),i.AES)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib,r=t.WordArray,n=t.BlockCipher,o=e.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],d=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=o.DES=n.extend({_doReset:function(){for(var e=this._key.words,t=[],i=0;i<56;i++){var r=s[i]-1;t[i]=e[r>>>5]>>>31-r%32&1}for(var n=this._subKeys=[],o=0;o<16;o++){var u=n[o]=[],d=c[o];for(i=0;i<24;i++)u[i/6|0]|=t[(a[i]-1+d)%28]<<31-i%6,u[4+(i/6|0)]|=t[28+(a[i+24]-1+d)%28]<<31-i%6;for(u[0]=u[0]<<1|u[0]>>>31,i=1;i<7;i++)u[i]=u[i]>>>4*(i-1)+3;u[7]=u[7]<<5|u[7]>>>27}var l=this._invSubKeys=[];for(i=0;i<16;i++)l[i]=n[15-i]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,i){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),h.call(this,1,1431655765);for(var r=0;r<16;r++){for(var n=i[r],o=this._lBlock,s=this._rBlock,a=0,c=0;c<8;c++)a|=u[c][((s^n[c])&d[c])>>>0];this._lBlock=s,this._rBlock=o^a}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,h.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var i=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=i,this._lBlock^=i<<e}function p(e,t){var i=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=i,this._rBlock^=i<<e}e.DES=n._createHelper(l);var f=o.TripleDES=n.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),i=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=l.createEncryptor(r.create(t)),this._des2=l.createEncryptor(r.create(i)),this._des3=l.createEncryptor(r.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=n._createHelper(f)}(),i.TripleDES)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.StreamCipher,r=e.algo,n=r.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,i=e.sigBytes,r=this._S=[],n=0;n<256;n++)r[n]=n;n=0;for(var o=0;n<256;n++){var s=n%i,a=r[n];r[n]=r[o=(o+r[n]+(t[s>>>2]>>>24-s%4*8&255))%256],r[o]=a}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,i=this._j,r=0,n=0;n<4;n++){var o=e[t=(t+1)%256];e[t]=e[i=(i+e[t])%256],e[i]=o,r|=e[(e[t]+e[i])%256]<<24-8*n}return this._i=t,this._j=i,r}e.RC4=t._createHelper(n);var s=r.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});e.RC4Drop=t._createHelper(s)}(),i.RC4)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.StreamCipher,r=[],n=[],o=[],s=e.algo.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,i=0;i<4;i++)e[i]=16711935&(e[i]<<8|e[i]>>>24)|4278255360&(e[i]<<24|e[i]>>>8);var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,i=0;i<4;i++)a.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(t){var o=t.words,s=o[0],c=o[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),d=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),l=u>>>16|4294901760&d,h=d<<16|65535&u;for(n[0]^=u,n[1]^=l,n[2]^=d,n[3]^=h,n[4]^=u,n[5]^=l,n[6]^=d,n[7]^=h,i=0;i<4;i++)a.call(this)}},_doProcessBlock:function(e,t){var i=this._X;a.call(this),r[0]=i[0]^i[5]>>>16^i[3]<<16,r[1]=i[2]^i[7]>>>16^i[5]<<16,r[2]=i[4]^i[1]>>>16^i[7]<<16,r[3]=i[6]^i[3]>>>16^i[1]<<16;for(var n=0;n<4;n++)r[n]=16711935&(r[n]<<8|r[n]>>>24)|4278255360&(r[n]<<24|r[n]>>>8),e[t+n]^=r[n]},blockSize:4,ivSize:2});function a(){for(var e=this._X,t=this._C,i=0;i<8;i++)n[i]=t[i];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<n[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<n[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<n[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<n[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<n[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<n[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<n[6]>>>0?1:0)|0,this._b=t[7]>>>0<n[7]>>>0?1:0,i=0;i<8;i++){var r=e[i]+t[i],s=65535&r,a=r>>>16;o[i]=((s*s>>>17)+s*a>>>15)+a*a^((4294901760&r)*r|0)+((65535&r)*r|0)}e[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,e[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,e[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,e[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,e[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,e[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,e[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,e[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.Rabbit=t._createHelper(s)}(),i.Rabbit)})),i((function(e,t){var i;e.exports=(i=n,function(){var e=i,t=e.lib.StreamCipher,r=[],n=[],o=[],s=e.algo.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)a.call(this);for(n=0;n<8;n++)r[n]^=i[n+4&7];if(t){var o=t.words,s=o[0],c=o[1],u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),d=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),l=u>>>16|4294901760&d,h=d<<16|65535&u;for(r[0]^=u,r[1]^=l,r[2]^=d,r[3]^=h,r[4]^=u,r[5]^=l,r[6]^=d,r[7]^=h,n=0;n<4;n++)a.call(this)}},_doProcessBlock:function(e,t){var i=this._X;a.call(this),r[0]=i[0]^i[5]>>>16^i[3]<<16,r[1]=i[2]^i[7]>>>16^i[5]<<16,r[2]=i[4]^i[1]>>>16^i[7]<<16,r[3]=i[6]^i[3]>>>16^i[1]<<16;for(var n=0;n<4;n++)r[n]=16711935&(r[n]<<8|r[n]>>>24)|4278255360&(r[n]<<24|r[n]>>>8),e[t+n]^=r[n]},blockSize:4,ivSize:2});function a(){for(var e=this._X,t=this._C,i=0;i<8;i++)n[i]=t[i];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<n[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<n[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<n[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<n[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<n[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<n[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<n[6]>>>0?1:0)|0,this._b=t[7]>>>0<n[7]>>>0?1:0,i=0;i<8;i++){var r=e[i]+t[i],s=65535&r,a=r>>>16;o[i]=((s*s>>>17)+s*a>>>15)+a*a^((4294901760&r)*r|0)+((65535&r)*r|0)}e[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,e[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,e[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,e[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,e[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,e[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,e[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,e[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.RabbitLegacy=t._createHelper(s)}(),i.RabbitLegacy)})),i((function(e,t){e.exports=n})));const s="function"==typeof atob,a="function"==typeof Buffer,c="function"==typeof TextDecoder?new TextDecoder:void 0,u=("function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),d=(e=>{let t={};return u.forEach((e,i)=>t[e]=i),t})(),l=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,h=String.fromCharCode.bind(String),p="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):(e,t=(e=>e))=>new Uint8Array(Array.prototype.slice.call(e,0).map(t)),f=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),m=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,g=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return h(55296+(t>>>10))+h(56320+(1023&t));case 3:return h((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return h((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},v=e=>e.replace(m,g),b=e=>{if(e=e.replace(/\s+/g,""),!l.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,i,r,n="";for(let o=0;o<e.length;)t=d[e.charAt(o++)]<<18|d[e.charAt(o++)]<<12|(i=d[e.charAt(o++)])<<6|(r=d[e.charAt(o++)]),n+=64===i?h(t>>16&255):64===r?h(t>>16&255,t>>8&255):h(t>>16&255,t>>8&255,255&t);return n},y=s?e=>atob(f(e)):a?e=>Buffer.from(e,"base64").toString("binary"):b,S=a?e=>p(Buffer.from(e,"base64")):e=>p(y(e),e=>e.charCodeAt(0)),E=a?e=>Buffer.from(e,"base64").toString("utf8"):c?e=>c.decode(S(e)):e=>v(y(e)),_=e=>f(e.replace(/[-_]/g,e=>"-"==e?"+":"/")),C=e=>E(_(e)),I=C;
/*!
 * xrtc.js v4.2.1
 * (c) 2020-2023 
 * Released under the MIT License in iflytek.
 */
function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function R(e,t){if(e){if("string"==typeof e)return T(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?T(e,t):void 0}}function k(e){return function(e){if(Array.isArray(e))return T(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||R(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var i=[],r=!0,n=!1,o=void 0;try{for(var s,a=e[Symbol.iterator]();!(r=(s=a.next()).done)&&(i.push(s.value),!t||i.length!==t);r=!0);}catch(e){n=!0,o=e}finally{try{r||null==a.return||a.return()}finally{if(n)throw o}}return i}}(e,t)||R(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,t,i,r,n,o,s){try{var a=e[o](s),c=a.value}catch(e){return void i(e)}a.done?t(c):Promise.resolve(c).then(r,n)}function P(e){return function(){var t=this,i=arguments;return new Promise((function(r,n){var o=e.apply(t,i);function s(e){A(o,r,n,s,a,"next",e)}function a(e){A(o,r,n,s,a,"throw",e)}s(void 0)}))}}function L(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function D(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function x(e,t,i){return t&&D(e.prototype,t),i&&D(e,i),e}function M(e){var t={exports:{}};return e(t,t.exports),t.exports}var U=M((function(e){var t=function(e){var t,i=Object.prototype,r=i.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function c(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,i){return e[t]=i}}function u(e,t,i,r){var n=Object.create((t&&t.prototype instanceof g?t:g).prototype),o=new O(r||[]);return n._invoke=function(e,t,i){var r=l;return function(n,o){if(r===p)throw new Error("Generator is already running");if(r===f){if("throw"===n)throw o;return A()}for(i.method=n,i.arg=o;;){var s=i.delegate;if(s){var a=T(s,i);if(a){if(a===m)continue;return a}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(r===l)throw r=f,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);r=p;var c=d(e,t,i);if("normal"===c.type){if(r=i.done?f:h,c.arg===m)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(r=f,i.method="throw",i.arg=c.arg)}}}(e,i,o),n}function d(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l="suspendedStart",h="suspendedYield",p="executing",f="completed",m={};function g(){}function v(){}function b(){}var y={};y[o]=function(){return this};var S=Object.getPrototypeOf,E=S&&S(S(w([])));E&&E!==i&&r.call(E,o)&&(y=E);var _=b.prototype=g.prototype=Object.create(y);function C(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function i(n,o,s,a){var c=d(e[n],e,o);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?t.resolve(l.__await).then((function(e){i("next",e,s,a)}),(function(e){i("throw",e,s,a)})):t.resolve(l).then((function(e){u.value=e,s(u)}),(function(e){return i("throw",e,s,a)}))}a(c.arg)}var n;this._invoke=function(e,r){function o(){return new t((function(t,n){i(e,r,t,n)}))}return n=n?n.then(o,o):o()}}function T(e,i){var r=e.iterator[i.method];if(r===t){if(i.delegate=null,"throw"===i.method){if(e.iterator.return&&(i.method="return",i.arg=t,T(e,i),"throw"===i.method))return m;i.method="throw",i.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var n=d(r,e.iterator,i.arg);if("throw"===n.type)return i.method="throw",i.arg=n.arg,i.delegate=null,m;var o=n.arg;return o?o.done?(i[e.resultName]=o.value,i.next=e.nextLoc,"return"!==i.method&&(i.method="next",i.arg=t),i.delegate=null,m):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,m)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function w(e){if(e){var i=e[o];if(i)return i.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,s=function i(){for(;++n<e.length;)if(r.call(e,n))return i.value=e[n],i.done=!1,i;return i.value=t,i.done=!0,i};return s.next=s}}return{next:A}}function A(){return{value:t,done:!0}}return v.prototype=_.constructor=b,b.constructor=v,v.displayName=c(b,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,a,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},C(I.prototype),I.prototype[s]=function(){return this},e.AsyncIterator=I,e.async=function(t,i,r,n,o){void 0===o&&(o=Promise);var s=new I(u(t,i,r,n),o);return e.isGeneratorFunction(i)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},C(_),c(_,a,"Generator"),_[o]=function(){return this},_.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var i in e)t.push(i);return t.reverse(),function i(){for(;t.length;){var r=t.pop();if(r in e)return i.value=r,i.done=!1,i}return i.done=!0,i}},e.values=w,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var i=this;function n(r,n){return a.type="throw",a.arg=e,i.next=r,n&&(i.method="next",i.arg=t),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return n("end");if(s.tryLoc<=this.prev){var c=r.call(s,"catchLoc"),u=r.call(s,"finallyLoc");if(c&&u){if(this.prev<s.catchLoc)return n(s.catchLoc,!0);if(this.prev<s.finallyLoc)return n(s.finallyLoc)}else if(c){if(this.prev<s.catchLoc)return n(s.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return n(s.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),k(i),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var r=i.completion;if("throw"===r.type){var n=r.arg;k(i)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,i,r){return this.delegate={iterator:w(e),resultName:i,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}(e.exports);try{regeneratorRuntime=t}catch(e){Function("r","regeneratorRuntime = r")(t)}})),N=function(){function e(t){L(this,e),this.events={},this.logger=t}return x(e,[{key:"on",value:function(e,t){var i=this.events[e]||[];return i.push(t),this.events[e]=i,this}},{key:"once",value:function(e,t){var i=this;this.on(e,(function r(){for(var n=arguments.length,o=new Array(n),s=0;s<n;s++)o[s]=arguments[s];t.apply(null,o),i.off(e,r)}))}},{key:"off",value:function(e,t){if("*"===e)return this.events={},this;var i=this.events[e];return this.events[e]=i&&i.filter((function(e){return e!==t})),this}},{key:"emit",value:function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];"network-quality"!==e&&"audio-volume"!==e&&"mic-volume"!==e&&this.logger&&this.logger.info("Emit event name:",e);var n=this.events[e];return n&&n.forEach((function(e){return e.apply(null,i)})),this}}]),e}(),V=new Map;V.set("anchor",{publish:{audio:!0,video:!0},subscribe:{audio:!0,video:!0},control:!0}),V.set("audience",{publish:{audio:!1,video:!1},subscribe:{audio:!0,video:!0},control:!1});var B,F,j,W;!function(e){e[e.New=0]="New",e[e.Joining=1]="Joining",e[e.Joined=2]="Joined",e[e.Leaving=3]="Leaving",e[e.Leaved=4]="Leaved"}(B||(B={})),function(e){e[e.Create=0]="Create",e[e.Publishing=1]="Publishing",e[e.Published=2]="Published",e[e.Unpublished=3]="Unpublished"}(F||(F={})),function(e){e[e.Create=0]="Create",e[e.Subscribing=1]="Subscribing",e[e.Subscribed=2]="Subscribed",e[e.Unsubscribed=3]="Unsubscribed"}(j||(j={})),function(e){e[e.Invalid=0]="Invalid",e[e.AudioOnly=1]="AudioOnly",e[e.VideoOnly=2]="VideoOnly",e[e.AudioVideo=3]="AudioVideo"}(W||(W={}));var H="auxiliary",G="error";function z(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function J(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?z(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):z(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var K=J(J(J(J(J(J({},{INVALID_PARAMETER:4096,INVALID_OPERATION:4097,NOT_SUPPORTED:4098}),{JOIN_ROOM_FAILED:16388,CREATE_OFFER_FAILED:16389,LEAVE_ROOM_FAILED:16390,PUBLISH_STREAM_FAILED:16391,UNPUBLISH_STREAM_FAILED:16392,SUBSCRIBE_FAILED:16393,UNSUBSCRIBE_FAILED:16400,SWITCH_ROLE_ERROR:16401,INVALID_TRANSPORT_STATA:16402,LOCAL_AUDIO_STATA_ERROR:16403,LOCAL_VIDEO_STATA_ERROR:16404,REMOTE_AUDIO_STATA_ERROR:16405,REMOTE_VIDEO_STATA_ERROR:16406,LOCAL_SWITCH_SIMULCAST:16407,REMOTE_SWITCH_SIMULCAST:16408,SUBSCRIPTION_TIMEOUT:16450,UNKNOWN:"0xFFFF"}),{INIT_STREAM_FAILED:12289,PLAY_STREAM_ERROR:12290,SET_AUDIO_OUTPUT_FAILED:12291,SET_VIDEO_PROFILE_ERROR:12292,SET_SCREEN_SHARE_FAILED:12293,SWITCH_DEVICE_FAILED:12294,ADD_TRACK_FAILED:12295,REMOVE_TRACK_FAILED:12296,REPLACE_TRACK_FAILED:12297,PLAY_NOT_ALLOWED:16451,DEVICE_AUTO_RECOVER_FAILED:16452,CANDIDATE_COLLECT_FAILED:16453,RTCPEERCONNECTION_SATE_FAILED:16480}),{DEVICE_NOT_FOUND:256,H264_NOT_SUPPORTED:257,CAMERAS_NOT_FOUND:258,MICROPHONES_NOT_FOUND:259,SPEAKERS_NOT_FOUND:260,OS_NOT_SUPPORTED:261,WEBRTC_NOT_SUPPORTED:262,BROWSER_NOT_SUPPORTED:263}),{SIGNAL_CHANNEL_SETUP_FAILED:20481,SIGNAL_CHANNEL_RECONNECTION_FAILED:20482,SERVER_TIMEOUT:20483}),{SERVER_UNKNOWN_ERROR:-10011,AUTHORIZATION_FAILED:-10013,GET_SERVER_NODE_FAILED:-10015,REQUEST_TIMEOUT:-10020});function Y(e,t){return(Y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function q(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Y(e,t)}function X(e){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Q(e,t){return!t||"object"!==X(t)&&"function"!=typeof t?$(e):t}function Z(e){return(Z=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ee(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function te(e,t,i){return(te=ee()?Reflect.construct:function(e,t,i){var r=[null];r.push.apply(r,t);var n=new(Function.bind.apply(e,r));return i&&Y(n,i.prototype),n}).apply(null,arguments)}function ie(e){var t="function"==typeof Map?new Map:void 0;return(ie=function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,i)}function i(){return te(e,arguments,Z(this).constructor)}return i.prototype=Object.create(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Y(i,e)})(e)}var re,ne=function(e){q(i,ie(Error));var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,r=Z(e);if(t){var n=Z(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return Q(this,i)}}(i);function i(e){var r;L(this,i),(r=t.call(this)).code=e.code,e.name&&(r.name=e.name);var n=e.message instanceof Error?e.message.message:e.message;return r.message=n,r}return x(i,[{key:"getCode",value:function(){return this.code}}]),i}(),oe=function(){function e(){var t=this;L(this,e),this.context=new(window.AudioContext||window.webkitAudioContext),this.instant=0,this.slow=0,this.clip=0,this.script=this.context.createScriptProcessor(2048,1,1),this.script.onaudioprocess=function(e){var i,r=e.inputBuffer.getChannelData(0),n=0,o=0;for(i=0;i<r.length;++i)n+=r[i]*r[i],Math.abs(r[i])>.99&&(o+=1);t.instant=Math.sqrt(n/r.length),t.slow=.95*t.slow+.05*t.instant,t.clip=o/r.length}}return x(e,[{key:"connectToSource",value:function(e){try{var t=new MediaStream;t.addTrack(e),this.mic=this.context.createMediaStreamSource(t),this.mic.connect(this.script),this.script.connect(this.context.destination)}catch(e){console.error("soundMeter connectoToSource error: "+e)}}},{key:"stop",value:function(){this.mic.disconnect(),this.script.disconnect()}},{key:"resume",value:function(){this.context&&this.context.resume()}},{key:"getVolume",value:function(){return this.instant.toFixed(2)}}]),e}(),se=function(){function e(t){L(this,e),this.stream=t.stream,this.userId=t.stream.userId,this.log=t.stream.logger,this.track=t.track,this.div=t.div,this.muted=t.muted,this.outputDeviceId=t.deviceId,this.volume=t.volume,this.element=null,this.state="NONE",this.pausedRetryCount=5,this._emitter=new N,this.handleEleEventPlaying=this.eleEventPlaying.bind(this),this.handleEleEventEnded=this.eleEventEnded.bind(this),this.handleEleEventPause=this.eleEventPause.bind(this),this.handleTrackEventEnded=this.trackEventEnded.bind(this),this.handleTrackEventMute=this.trackEventMute.bind(this),this.handleTrackEventUnmute=this.trackEventUnmute.bind(this)}var t;return x(e,[{key:"play",value:(t=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,i){var r=new MediaStream;r.addTrack(t.track);var n=document.createElement("audio");n.srcObject=r,n.muted=t.muted,n.setAttribute("id","audio_".concat(t.stream.getId(),"_").concat(Date.now())),n.setAttribute("autoplay","autoplay"),n.setAttribute("playsinline","playsinline"),t.div.appendChild(n),t.outputDeviceId&&"function"==typeof(null==n?void 0:n.setSinkId)&&n.setSinkId(t.outputDeviceId),t.element=n,t.setVolume(t.volume),t.handleEvents(),n.addEventListener("canplay",(function(){t.element.play().then((function(){e()})).catch((function(e){i(e)}))}))})));case 1:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})},{key:"handleEvents",value:function(){var e,t,i;null===(e=this.element)||void 0===e||e.addEventListener("playing",this.handleEleEventPlaying),null===(t=this.element)||void 0===t||t.addEventListener("ended",this.handleEleEventEnded),null===(i=this.element)||void 0===i||i.addEventListener("pause",this.handleEleEventPause),this.trackHandleEvents()}},{key:"trackHandleEvents",value:function(){var e,t,i;null===(e=this.track)||void 0===e||e.addEventListener("ended",this.handleTrackEventEnded),null===(t=this.track)||void 0===t||t.addEventListener("mute",this.handleTrackEventMute),null===(i=this.track)||void 0===i||i.addEventListener("unmute",this.handleTrackEventUnmute)}},{key:"trackRemoveEvents",value:function(){var e,t,i;null===(e=this.track)||void 0===e||e.removeEventListener("ended",this.handleTrackEventEnded),null===(t=this.track)||void 0===t||t.removeEventListener("mute",this.handleTrackEventMute),null===(i=this.track)||void 0===i||i.removeEventListener("unmute",this.handleTrackEventUnmute)}},{key:"removeEvents",value:function(){var e,t,i;null===(e=this.element)||void 0===e||e.removeEventListener("playing",this.handleEleEventPlaying),null===(t=this.element)||void 0===t||t.removeEventListener("ended",this.handleEleEventEnded),null===(i=this.element)||void 0===i||i.removeEventListener("pause",this.handleEleEventPause),this.trackRemoveEvents()}},{key:"setSinkId",value:function(e){var t;this.outputDeviceId!==e&&(this.outputDeviceId=e,"function"==typeof(null===(t=this.element)||void 0===t?void 0:t.setSinkId)&&this.element.setSinkId(e))}},{key:"setVolume",value:function(e){this.log.info("stream - audioElement setVolume to : ".concat(e.toString())),this.element.volume=e}},{key:"getAudioLevel",value:function(){return this.soundMeter||(this.soundMeter=new oe,this.soundMeter.connectToSource(this.track)),this.soundMeter.getVolume()}},{key:"stop",value:function(){this.removeEvents(),this.div.removeChild(this.element),this.element.srcObject=null,this.element=null,this.soundMeter&&(this.soundMeter.stop(),this.soundMeter=null)}},{key:"resume",value:function(){var e;return null===(e=this.element)||void 0===e?void 0:e.play()}},{key:"getAudioElement",value:function(){return this.element}},{key:"setAudioTrack",value:function(e){this.trackRemoveEvents();var t=new MediaStream;t.addTrack(e),this.track=e,this.trackHandleEvents(),this.soundMeter=null,this.log.info("setAudioTrack",e),this.element&&(this.element.srcObject=t,this.element.play())}},{key:"eleEventPlaying",value:function(){this.log.info("stream ".concat(this.userId," - audio player is starting playing")),this.state="PLAYING",this._emitter.emit("player-state-changed",{state:this.state,reason:"playing"})}},{key:"eleEventEnded",value:function(){this.log.info("stream ".concat(this.userId," - audio player is ended")),"STOPPED"!==this.state&&(this.state="STOPPED",this._emitter.emit("player-state-changed",{state:this.state,reason:"ended"}))}},{key:"eleEventPause",value:function(){if(this.log.info("stream ".concat(this.userId," - audio player is paused")),this.state="PAUSED",this._emitter.emit("player-state-changed",{state:this.state,reason:"pause"}),this.div&&document.getElementById(this.div.id)){var e=(window.navigator&&window.navigator.userAgent||"").match(/Chrome\/(\d+)/),t=e&&e[1]?Number(e[1]):null;this.pausedRetryCount>0&&"number"==typeof t&&t<70&&(this.resume(),this.pausedRetryCount--)}else this.log.warn("audio container is not in DOM")}},{key:"trackEventEnded",value:function(){this.log.info("stream ".concat(this.userId," - audio player track is ended")),"STOPPED"!==this.state&&(this.state="STOPPED",this._emitter.emit("player-state-changed",{state:this.state,reason:"ended",type:"track"}))}},{key:"trackEventMute",value:function(){this.log.info("stream ".concat(this.userId," - audio track is muted")),"PAUSED"!==this.state&&(this.state="PAUSED",this._emitter.emit("player-state-changed",{state:this.state,reason:"mute",type:"track"}))}},{key:"trackEventUnmute",value:function(){this.log.info("stream ".concat(this.userId," - audio track is unmuted")),"PAUSED"===this.state&&(this.state="PLAYING",this._emitter.emit("player-state-changed",{state:this.state,reason:"unmute",type:"track"}))}}]),e}(),ae=(re=function(e,t){for(var i=["webgl","experimental-webgl","webkit-3d","moz-webgl"],r=null,n=0;n<i.length;++n){try{r=e.getContext(i[n],t)}catch(e){}if(r)break}return r},function(e,t,i){i=i||function(e){var t=document.getElementsByTagName("body")[0];if(t){var i=window.WebGLRenderingContext?'It doesn\'t appear your computer can support WebGL.<br/><a href="http://get.webgl.org">Click here for more information.</a>':'This page requires a browser that supports WebGL.<br/><a href="http://get.webgl.org">Click here to upgrade your browser.</a>';e&&(i+="<br/><br/>Status: "+e),t.innerHTML=function(e){return'<div style="margin: auto; width:500px;z-index:10000;margin-top:20em;text-align:center;"> '.concat(e," </div>")}(i)}},e.addEventListener&&e.addEventListener("webglcontextcreationerror",(function(e){i(e.statusMessage)}),!1);var r=re(e,t);return r||i(""),r});function ce(e,t,i){var r=e.createShader(t);if(null==r)return console.log("unable to create shader"),null;if(e.shaderSource(r,i),e.compileShader(r),!e.getShaderParameter(r,e.COMPILE_STATUS)){var n=e.getShaderInfoLog(r);return console.log("Failed to compile shader: "+n),e.deleteShader(r),null}return r}window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e,t){window.setTimeout(e,1e3/60)}),window.cancelAnimationFrame||(window.cancelAnimationFrame=window.cancelRequestAnimationFrame||window.webkitCancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelAnimationFrame||window.mozCancelRequestAnimationFrame||window.msCancelAnimationFrame||window.msCancelRequestAnimationFrame||window.oCancelAnimationFrame||window.oCancelRequestAnimationFrame||window.clearTimeout);var ue=function(){function e(t,i){L(this,e),this.div=t.div,this.video=t.video,this.virtualBackgroundMix=t.virtualBackgroundMix||null,this.textures=[],this.canCopyVideo=!1,this.canCopyBackground=!1,this.log=i,this.track=t.track,this.initVirtualBackground(t.virtualBackground)}return x(e,[{key:"play",value:function(){var e=this;if(this.initCanvas(),this.gl=ae(this.canvas,{preserveDrawingBuffer:!0,alpha:!0,antialias:!0})||null,this.gl){var t=this.initXShaderSource();if(function(e,t,i){var r=function(e,t,i){var r=ce(e,e.VERTEX_SHADER,t),n=ce(e,e.FRAGMENT_SHADER,i);if(!r||!n)return null;var o=e.createProgram();if(!o)return null;if(e.attachShader(o,r),e.attachShader(o,n),e.linkProgram(o),!e.getProgramParameter(o,e.LINK_STATUS)){var s=e.getProgramInfoLog(o);return console.log("Failed to link program: "+s),e.deleteProgram(o),e.deleteShader(n),e.deleteShader(r),null}return o}(e,t,i);return r?(e.useProgram(r),e.program=r,!0):(console.log("Failed to create program"),!1)}(this.gl,t.VSHADER,t.FSHADER))if(this.initVertexBuffers(this.gl)<0)this.log.warn("Failed to initialize shaders");else{var i=[];this.video?(this.setupVideo(this.video,"canCopyVideo"),i.push(this.video),this.virtualBackground&&this.virtualBackgroundMix&&i.push(this.virtualBackground),i.forEach((function(t,i){var r=e.initTextures(e.gl,i);r?e.textures.push(r):e.log.warn("Failed to initialize texture")})),this.gl.clearColor(0,0,0,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT),this.render()):this.log.warn("Failed to get video")}else this.log.warn("Failed to initialize shaders")}else this.log.warn("Failed to get the rendering context for webgl")}},{key:"stop",value:function(){this.canCopyVideo=!1,this.canCopyBackground=!1,this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=null,this.virtualBackground=null,this.virtualBackgroundMix=!1,this.observer.unobserve(this.div),this.canvas.remove(),delete this.canvas,delete this.gl}},{key:"unmute",value:function(){this.render()}},{key:"mute",value:function(){this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=null,this.gl.clearColor(0,0,0,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT),this.canvas.style.backgroundImage=""}},{key:"render",value:function(){var e,t,i=this,r=Date.now();!function n(){var o=i.track.getSettings();i.frameRate=o.frameRate||15;var s=1e3/i.frameRate;i.rafId&&cancelAnimationFrame(i.rafId),i.rafId=requestAnimationFrame(n),e=Date.now(),(t=e-r)>s&&(r=e-t/s,i.canCopyVideo&&(i.updateTexture(i.gl,i.textures[0],i.video),i.setCanvasBgImage(),i.canUpdateBackground()&&i.updateTexture(i.gl,i.textures[1],i.virtualBackground)),i.gl.clear(i.gl.COLOR_BUFFER_BIT),i.gl.viewport(0,0,i.canvas.width,i.canvas.height),i.gl.drawArrays(i.gl.TRIANGLE_STRIP,0,4))}()}},{key:"initVirtualBackground",value:function(e){if(e){if("IMG"===e.nodeName){var t=new Image;t.src=e.src,this.virtualBackground=t}else if("VIDEO"===e.nodeName){var i=document.createElement("video");i.playsInline=!0,i.muted=!0,i.loop=!0,i.src=e.src,i.play(),this.virtualBackground=i,this.setupVideo(this.video,"canCopyBackground")}}else this.virtualBackground=null}},{key:"initCanvas",value:function(){this.canvas=document.createElement("canvas"),this.canvas?(this.canvas.width=this.div.clientWidth,this.canvas.height=this.div.clientHeight,this.canvas.style.objectFit=this.video.style.objectFit,this.canvas.style.width="100%",this.canvas.style.height="100%",this.addDivListener(),this.div.appendChild(this.canvas)):this.log.warn("Failed to retrieve the <canvas> element")}},{key:"addDivListener",value:function(){var e=this;this.observer=new ResizeObserver((function(){var t=e.canvas.width,i=e.div.clientHeight,r=e.div.clientWidth;i!==e.canvas.height&&(e.canvas.height=i),r!==t&&(e.canvas.width=r)})),this.observer.observe(this.div)}},{key:"setCanvasBgImage",value:function(){this.canvas.style.backgroundImage||this.virtualBackground&&!this.virtualBackgroundMix&&(this.canvas.style.backgroundImage="url(".concat(this.virtualBackground.src,")"),this.canvas.style.backgroundRepeat="no-repeat",this.canvas.style.backgroundSize="cover")}},{key:"setupVideo",value:function(e,t){var i=this,r=!1,n=!1;e.addEventListener("playing",(function(){r=!0,o()}),!0),e.addEventListener("timeupdate",(function(){n=!0,o()}),!0);var o=function(){r&&n&&(i[t]=!0)}}},{key:"canUpdateBackground",value:function(){return!(!this.virtualBackground||!this.virtualBackgroundMix)&&("IMG"===this.virtualBackground.nodeName?this.virtualBackground.complete:this.canCopyBackground)}},{key:"initVertexBuffers",value:function(e){var t=new Float32Array([-1,1,0,1,-1,-1,0,0,1,1,1,1,1,-1,1,0]),i=t.BYTES_PER_ELEMENT,r=e.createBuffer();if(!r)return this.log.warn("Failed to create vertex buffer"),-1;e.bindBuffer(e.ARRAY_BUFFER,r),e.bufferData(e.ARRAY_BUFFER,t,e.STATIC_DRAW);var n=e.getAttribLocation(e.program,"a_Position");if(n<0)return this.log.warn("Failed to get the storage location of a_Position"),-1;e.vertexAttribPointer(n,2,e.FLOAT,!1,4*i,0),e.enableVertexAttribArray(n);var o=e.getAttribLocation(e.program,"a_TexCoord");return o<0?(this.log.warn("Failed to get the storage location of a_TexCoord"),-1):(e.vertexAttribPointer(o,2,e.FLOAT,!1,4*i,2*i),e.enableVertexAttribArray(o),4)}},{key:"initTextures",value:function(e,t){var i,r,n=e.createTexture();if(!n)return this.log.warn("Failed to create the texture object"),null;if(0===t){if(!(i=e.getUniformLocation(e.program,"u_Sampler0")))return this.log.warn("Failed to get the storage location of u_Sampler"),null}else if(!(r=e.getUniformLocation(e.program,"u_Sampler1")))return this.log.warn("Failed to get the storage location of u_Sampler"),null;return e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,1),e.activeTexture(0===t?e.TEXTURE0:e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),0===t?e.uniform1i(i,0):e.uniform1i(r,1),n}},{key:"updateTexture",value:function(e,t,i){var r=e.RGBA,n=e.RGBA,o=e.UNSIGNED_BYTE;e.bindTexture(e.TEXTURE_2D,t),e.texImage2D(e.TEXTURE_2D,0,r,n,o,i)}},{key:"initXShaderSource",value:function(){return{VSHADER:"attribute vec4 a_Position;\nattribute vec2 a_TexCoord;\nvarying vec2 v_TexCoord;\nvoid main() {\ngl_Position = a_Position;\nv_TexCoord = a_TexCoord;\n}\n",FSHADER:this.virtualBackground&&this.virtualBackgroundMix?"precision mediump float;\nuniform sampler2D u_Sampler0;\nuniform sampler2D u_Sampler1;\nvarying vec2 v_TexCoord;\nvoid main() {\nvec2 true_pixel_coord = vec2(v_TexCoord.x, (0.5 + (v_TexCoord.y / 2.))); \nvec2 mask_pexel_coord = vec2(v_TexCoord.x, v_TexCoord.y / 2.); \nfloat alpha = texture2D(u_Sampler0, mask_pexel_coord).r; \nvec3 rgb = texture2D(u_Sampler0, true_pixel_coord).rgb*alpha;\nvec4 videoColor = vec4(rgb, alpha);\nvec4 imgColor = vec4(texture2D(u_Sampler1, v_TexCoord).rgb*(1.0-alpha),1.0-alpha);\ngl_FragColor = imgColor + videoColor;\n}\n":"precision mediump float;\nuniform sampler2D u_Sampler0;\nvarying vec2 v_TexCoord;\nvoid main() {\nvec2 true_pixel_coord = vec2(v_TexCoord.x, (0.5 + (v_TexCoord.y / 2.))); \nvec2 mask_pexel_coord = vec2(v_TexCoord.x, v_TexCoord.y / 2.); \nfloat alpha = texture2D(u_Sampler0, mask_pexel_coord).r; \nvec3 rgb = texture2D(u_Sampler0, true_pixel_coord).rgb*alpha;\nvec4 videoColor = vec4(rgb, alpha);\ngl_FragColor = videoColor;\n}\n"}}}]),e}(),de=function(){function e(t){L(this,e),this.stream=t.stream,this.userId=t.stream.userId,this.log=t.stream.logger,this.track=t.track,this.div=t.div,this.muted=t.muted,this.objectFit=t.objectFit,this.mirror=t.mirror,this.element=null,this.state="NONE",this.pausedRetryCount=5,this._emitter=new N,this.handleEleEventPlaying=this.eleEventPlaying.bind(this),this.handleEleEventEnded=this.eleEventEnded.bind(this),this.handleEleEventPause=this.eleEventPause.bind(this),this.handleTrackEventEnded=this.trackEventEnded.bind(this),this.handleTrackEventMute=this.trackEventMute.bind(this),this.handleTrackEventUnmute=this.trackEventUnmute.bind(this),this.isAlphaChannels=t.isAlphaChannels||!1,this.virtualBackground=t.virtualBackground,this.virtualBackgroundMix=t.virtualBackgroundMix,this.initializeElement(),this.isAlphaChannels&&this.startTexturesPlayer()}var t;return x(e,[{key:"initializeElement",value:function(){var e=new MediaStream;e.addTrack(this.track);var t=document.createElement("video");t.srcObject=e,t.muted=!0;var i="width: 100%; height: 100%; object-fit: ".concat(this.objectFit,";");this.mirror&&(i+="transform: rotateY(180deg);"),t.setAttribute("id","video_".concat(this.stream.getId(),"_").concat(Date.now())),t.setAttribute("style",i),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.div.style.lineHeight="0",this.div.appendChild(t),this.element=t,this.handleEvents()}},{key:"play",value:(t=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,i){t.element.play().then((function(){t.element.pause(),e()})).catch((function(e){i(e)}))})));case 1:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})},{key:"handleEvents",value:function(){var e,t,i;null===(e=this.element)||void 0===e||e.addEventListener("playing",this.handleEleEventPlaying),null===(t=this.element)||void 0===t||t.addEventListener("ended",this.handleEleEventEnded),null===(i=this.element)||void 0===i||i.addEventListener("pause",this.handleEleEventPause),this.trackHandleEvents()}},{key:"trackHandleEvents",value:function(){var e,t,i;null===(e=this.track)||void 0===e||e.addEventListener("ended",this.handleTrackEventEnded),null===(t=this.track)||void 0===t||t.addEventListener("mute",this.handleTrackEventMute),null===(i=this.track)||void 0===i||i.addEventListener("unmute",this.handleTrackEventUnmute)}},{key:"trackRemoveEvents",value:function(){var e,t,i;null===(e=this.track)||void 0===e||e.removeEventListener("ended",this.handleTrackEventEnded),null===(t=this.track)||void 0===t||t.removeEventListener("mute",this.handleTrackEventMute),null===(i=this.track)||void 0===i||i.removeEventListener("unmute",this.handleTrackEventUnmute)}},{key:"removeEvents",value:function(){var e,t,i;null===(e=this.element)||void 0===e||e.removeEventListener("playing",this.handleEleEventPlaying),null===(t=this.element)||void 0===t||t.removeEventListener("ended",this.handleEleEventEnded),null===(i=this.element)||void 0===i||i.removeEventListener("pause",this.handleEleEventPause),this.trackRemoveEvents()}},{key:"stop",value:function(){this.removeEvents(),this.div.removeChild(this.element),this.element.srcObject=null,this.element=null,this.isAlphaChannels&&this.texturesPlayer.stop(),this.isAlphaChannels=!1}},{key:"resume",value:function(){var e;return null===(e=this.element)||void 0===e?void 0:e.play()}},{key:"getVideoFrame",value:function(){var e,t,i=document.createElement("canvas");i.width=null===(e=this.element)||void 0===e?void 0:e.videoWidth,i.height=null===(t=this.element)||void 0===t?void 0:t.videoHeight;var r=null;return this.isAlphaChannels?(r=this.texturesPlayer.canvas,i.height=i.height/2,i.getContext("2d").drawImage(r,0,0,r.width,r.height,0,0,i.width,i.height)):(r=this.element,i.getContext("2d").drawImage(r,0,0)),i.toDataURL("image/png")}},{key:"getVideoElement",value:function(){return this.element}},{key:"setVideoTrack",value:function(e){this.trackRemoveEvents();var t=new MediaStream;t.addTrack(e),this.track=e,this.trackHandleEvents(),this.log.info("setVideoTrack",e),this.element&&(this.element.srcObject=t,this.element.play())}},{key:"unmute",value:function(){this.isAlphaChannels&&this.texturesPlayer.unmute()}},{key:"mute",value:function(){this.isAlphaChannels&&this.texturesPlayer.mute()}},{key:"startTexturesPlayer",value:function(){this.element.style.position="absolute",this.element.style.width="0",this.element.style.height="0",this.element.style.zIndex="-1",this.texturesPlayer=new ue({div:this.div,video:this.element,track:this.track,virtualBackground:this.virtualBackground,virtualBackgroundMix:this.virtualBackgroundMix},this.log),this.texturesPlayer.play()}},{key:"eleEventPlaying",value:function(){this.log.info("stream ".concat(this.userId," - video player is starting playing")),this.state="PLAYING",this._emitter.emit("player-state-changed",{state:this.state,reason:"playing"})}},{key:"eleEventEnded",value:function(){this.log.info("stream ".concat(this.userId," - video player is ended")),"STOPPED"!==this.state&&(this.state="STOPPED",this._emitter.emit("player-state-changed",{state:this.state,reason:"ended"}))}},{key:"eleEventPause",value:function(){this.log.info("stream ".concat(this.userId," - video player is paused")),this.state="PAUSED",this._emitter.emit("player-state-changed",{state:this.state,reason:"pause"}),this.div&&document.getElementById(this.div.id)?this.pausedRetryCount>0&&(this.log.info("auto resume when video paused"),this.resume(),this.pausedRetryCount--):this.log.warn("video container is not in DOM")}},{key:"trackEventEnded",value:function(){this.log.info("stream ".concat(this.userId," - video player track is ended")),"STOPPED"!==this.state&&(this.state="STOPPED",this._emitter.emit("player-state-changed",{state:this.state,reason:"ended",type:"track"}))}},{key:"trackEventMute",value:function(){this.log.info("stream ".concat(this.userId," - video track is muted")),"PAUSED"!==this.state&&(this.state="PAUSED",this._emitter.emit("player-state-changed",{state:this.state,reason:"mute",type:"track"}))}},{key:"trackEventUnmute",value:function(){this.log.info("stream ".concat(this.userId," - video track is unmuted")),"PAUSED"===this.state&&(this.state="PLAYING",this._emitter.emit("player-state-changed",{state:this.state,reason:"unmute",type:"track"}))}}]),e}();function le(){var e=navigator.userAgent,t=navigator.connection,i=e.match(/NetType\/\w+/)?e.match(/NetType\/\w+/)[0]:"";"3gnet"===(i=i.toLowerCase().replace("nettype/",""))&&(i="3g");var r=t&&t.type&&t.type.toLowerCase(),n=t&&t.effectiveType&&t.effectiveType.toLowerCase();"slow-2"===n&&(n="2g");var o=i||"unknown";if(r)switch(r){case"cellular":case"wimax":o=n||"unknown";break;case"wifi":o="wifi";break;case"ethernet":o="wired";break;case"none":case"other":case"unknown":o="unknown"}return o}var he={Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry|BB10/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},specil:function(){return navigator.userAgent.match(/MicroMessenger/i)},any:function(){return he.BlackBerry()||he.iOS()||he.Opera()||he.Windows()},getOsName:function(){var e="Unknown OS";return he.Android()&&(e="Android"),he.BlackBerry()&&(e="BlackBerry"),he.iOS()&&(e="iOS"),he.Opera()&&(e="Opera Mini"),he.Windows()&&(e="Windows"),{osName:e,type:"mobile"}}};function pe(){var e,t,i=navigator.userAgent.toLocaleLowerCase();if(-1!=i.indexOf("firefox"))e="Firefox";else if(-1!=i.indexOf("trident"))e="IE",-1==i.indexOf("ie")&&(t=11);else if(-1!=i.indexOf("opr"))e="OPR";else if(-1!=i.indexOf("edge"))e="Edge";else if(-1!=i.indexOf("chrome"))e="Chrome";else if(-1!=i.indexOf("safari")){e="Safari";var r=i.match(/(version).*?([\d.]+)/);t=r?r[2]:""}else e="未知浏览器";if(void 0===t){var n=i.match(/(firefox|trident|opr|chrome|safari).*?([\d.]+)/);t=n?n[2]:""}return{browser:e,version:t}}var fe,me,ge,ve,be,ye,Se,Ee,_e,Ce,Ie,Te,Re,ke=pe(),Oe=ke.browser,we=ke.version;function Ae(e){switch(e){case Ee.ForwardStream:return"forward";case Ee.MixedStream:return"mixed"}return""}function Pe(e){return"forward"==e?Ee.ForwardStream:"mixed"==e?Ee.MixedStream:Ee.Invalid}function Le(e){switch(e){case Ie.Microphone:return"mic";case Ie.ScreenShare:return"screen";case Ie.File:return"file"}return""}function De(e){return"mic"==e?Ie.Microphone:"screen"==e?Ie.ScreenShare:"file"==e?Ie.File:Ie.Unknown}function xe(e){switch(e){case Te.Camera:return"camera";case Te.ScreenShare:return"screen";case Te.File:return"file"}return""}function Me(e){return"camera"==e?Te.Camera:"screen"==e?Te.ScreenShare:"file"==e?Te.File:Te.Unknown}function Ue(e){switch(e){case Re.BigStream:return"h";case Re.MiddleStream:return"m";case Re.SmallStream:return"l"}return""}function Ne(e){return"h"==e?Re.BigStream:"m"==e?Re.MiddleStream:"l"==e?Re.SmallStream:Re.Invalid}function Ve(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function Be(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?Ve(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Ve(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}!function(e){e[e.Failed=0]="Failed",e[e.Success=1]="Success",e[e.Timeout=2]="Timeout"}(fe||(fe={})),function(e){e[e.Unknown=0]="Unknown",e[e.ActivelyLeave=1]="ActivelyLeave",e[e.RoomDissolved=2]="RoomDissolved",e[e.RepeatLogin=3]="RepeatLogin"}(me||(me={})),function(e){e[e.Normal=0]="Normal",e[e.Timeout=1]="Timeout",e[e.Kick=2]="Kick",e[e.RepeatLogin=3]="RepeatLogin",e[e.RoomDissolved=4]="RoomDissolved"}(ge||(ge={})),function(e){e[e.Unknown=0]="Unknown",e[e.Kicked=1]="Kicked",e[e.RepeatLogin=2]="RepeatLogin",e[e.RoomDissolved=3]="RoomDissolved"}(ve||(ve={})),function(e){e[e.New=0]="New",e[e.ConnectionConnected=1]="ConnectionConnected",e[e.ConnectionLost=2]="ConnectionLost",e[e.ConnectionRetring=3]="ConnectionRetring",e[e.ConnectionRecovery=4]="ConnectionRecovery"}(be||(be={})),function(e){e[e.ParticipantJoin=0]="ParticipantJoin",e[e.ParticipantLeave=1]="ParticipantLeave",e[e.StreamAdd=2]="StreamAdd",e[e.StreamUpdate=3]="StreamUpdate",e[e.StreamRemove=4]="StreamRemove",e[e.Drop=5]="Drop",e[e.PermissionChange=6]="PermissionChange"}(ye||(ye={})),function(e){e[e.AudioMute=0]="AudioMute",e[e.VideoMute=1]="VideoMute",e[e.AudioUnmute=2]="AudioUnmute",e[e.VideoUnmute=3]="VideoUnmute",e[e.Kick=4]="Kick"}(Se||(Se={})),function(e){e[e.Invalid=0]="Invalid",e[e.ForwardStream=1]="ForwardStream",e[e.MixedStream=2]="MixedStream"}(Ee||(Ee={})),function(e){e[e.Invalid=0]="Invalid",e[e.AudioOnly=1]="AudioOnly",e[e.VideoOnly=2]="VideoOnly",e[e.AudioVideo=3]="AudioVideo"}(_e||(_e={})),function(e){e[e.Normal=0]="Normal",e[e.Shadow=1]="Shadow"}(Ce||(Ce={})),function(e){e[e.Unknown=0]="Unknown",e[e.Microphone=1]="Microphone",e[e.ScreenShare=2]="ScreenShare",e[e.File=3]="File"}(Ie||(Ie={})),function(e){e[e.Unknown=0]="Unknown",e[e.Camera=1]="Camera",e[e.ScreenShare=2]="ScreenShare",e[e.File=3]="File"}(Te||(Te={})),function(e){e[e.Invalid=0]="Invalid",e[e.BigStream=1]="BigStream",e[e.MiddleStream=2]="MiddleStream",e[e.SmallStream=3]="SmallStream"}(Re||(Re={}));var Fe=Be(Be({},{TOP_ERROR:8801,SET_LOG_LEVEL:3001,ENABLE_UPLOAD_LOG:3002,DISABLE_UPLOAD_LOG:3003,JOIN:3004,JOIN_FIRST:8001,JOIN_SUCCESS:1103,JOIN_FAILED:1104,LEAVE:3007,LEAVE_SUCCESS:3008,LEAVE_FAILED:3009,SWITCH_ROLE_ANCHOR:3010,SWITCH_ROLE_AUDIENCE:3011,SWITCH_ROLE_ANCHOR_SUCCESS:3012,SWITCH_ROLE_ANCHOR_FAILED:3013,SWITCH_ROLE_AUDIENCE_SUCCESS:3014,SWITCH_ROLE_AUDIENCE_FAILED:3015,PUBLISH_STREAM:3016,PUBLISH_STREAM_SCREEN:3017,PUBLISH_STREAM_SUCCESS:3018,PUBLISH_STREAM_FAILED:3019,PUBLISH_STREAM_SCREEN_SUCCESS:3020,PUBLISH_STREAM_SCREEN_FAILED:3021,UNPUBLISH_STREAM:3022,UNPUBLISH_STREAM_SCREEN:3023,UNPUBLISH_STREAM_SUCCESS:3024,UNPUBLISH_STREAM_FAILED:3025,UNPUBLISH_STREAM_SCREEN_SUCCESS:3026,UNPUBLISH_STREAM_SCREEN_FAILED:3027,SUBSCRIBE_STREAM:3028,SUBSCRIBE_STREAM_SCREEN:3029,SUBSCRIBE_STREAM_SUCCESS:3030,SUBSCRIBE_STREAM_FAILED:3031,SUBSCRIBE_STREAM_SCREEN_SUCCESS:3032,SUBSCRIBE_STREAM_SCREEN_FAILED:3033,UNSUBSCRIBE_STREAM:3034,UNSUBSCRIBE_STREAM_SCREEN:3035,UNSUBSCRIBE_STREAM_SUCCESS:3036,UNSUBSCRIBE_STREAM_FAILED:3037,UNSUBSCRIBE_STREAM_SCREEN_SUCCESS:3038,UNSUBSCRIBE_STREAM_SCREEN_FAILED:3039,HAS_PUBLISHED_STREAM:3040,GET_CLIENT_STATE:3041,GET_REMOTE_MUTED_STATE:3042,ENABLE_AUDIO_VOLUME_EVALUATION:3043,ENABLE_SMALL_STREAM:3044,DISABLE_SMALL_STREAM:3045,SET_SMALL_STREAM_PROFILE:3046,SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL:3047,SET_REMOTE_VIDEO_STREAM_TYPE_BIG:3048,SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL_SUCCESS:3049,SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL_FAILED:3050,SET_REMOTE_VIDEO_STREAM_TYPE_BIG_SUCCESSE:3051,SET_REMOTE_VIDEO_STREAM_TYPE_BIG_FAILED:3052,UPDATE_SIMULCAST:3053,UPDATE_SIMULCAST_SUCCESSE:3054,UPDATE_SIMULCAST_FAILED:3055,PLAY_LOCAL_VIDEO:3056,PLAY_LOCAL_AUDIO:3057,PLAY_LOCAL_VIDEO_SCREEN:3058,PLAY_LOCAL_AUDIO_SCREEN:3059,PLAY_REMOTE_VIDEO:3060,PLAY_REMOTE_AUDIO:3061,PLAY_REMOTE_VIDEO_SCREEN:3062,PLAY_REMOTE_AUDIO_SCREEN:3063,STOP_LOCAL_VIDEO:3064,STOP_LOCAL_AUDIO:3065,STOP_LOCAL_VIDEO_SCREEN:3066,STOP_LOCAL_AUDIO_SCREEN:3067,STOP_REMOTE_VIDEO:3068,STOP_REMOTE_AUDIO:3069,STOP_REMOTE_VIDEO_SCREEN:3070,STOP_REMOTE_AUDIO_SCREEN:3071,RESUME_LOCAL_VIDEO:3072,RESUME_LOCAL_AUDIO:3073,RESUME_LOCAL_VIDEO_SCREEN:3074,RESUME_LOCAL_AUDIO_SCREEN:3075,RESUME_REMOTE_VIDEO:3076,RESUME_REMOTE_AUDIO:3077,RESUME_REMOTE_VIDEO_SCREEN:3078,RESUME_REMOTE_AUDIO_SCREEN:3079,CLOSE_LOCAL_VIDEO:3080,CLOSE_LOCAL_AUDIO:3081,CLOSE_LOCAL_VIDEO_SCREEN:3082,CLOSE_LOCAL_AUDIO_SCREEN:3083,CLOSE_REMOTE_VIDEO:3084,CLOSE_REMOTE_AUDIO:3085,CLOSE_REMOTE_VIDEO_SCREEN:3086,CLOSE_REMOTE_AUDIO_SCREEN:3087,MUTE_LOCAL_AUDIO:3088,MUTE_LOCAL_AUDIO_SCREEN:3089,MUTE_REMOTE_AUDIO:3090,MUTE_REMOTE_AUDIO_SCREEN:3091,MUTE_LOCAL_VIDEO:3092,MUTE_LOCAL_VIDEO_SCREEN:3093,MUTE_REMOTE_VIDEO:3094,MUTE_REMOTE_VIDEO_SCREEN:3095,UNMUTE_LOCAL_AUDIO:3096,UNMUTE_LOCAL_AUDIO_SCREEN:3097,UNMUTE_REMOTE_AUDIO:3098,UNMUTE_REMOTE_AUDIO_SCREEN:3099,UNMUTE_LOCAL_VIDEO:3100,UNMUTE_LOCAL_VIDEO_SCREEN:3101,UNMUTE_REMOTE_VIDEO:3102,UNMUTE_REMOTE_VIDEO_SCREEN:3103,GET_LOCAL_ID:3104,GET_REMOTE_ID:3105,GET_LOCAL_USER_ID:3106,GET_REMOTE_USER_ID:3107,SET_AUDIO_OUTPUT:3108,SET_LOCAL_AUDIO_VOLUME:3109,SET_LOCAL_AUDIO_VOLUME_SCREEN:3110,SET_REMOTE_AUDIO_VOLUME:3111,SET_REMOTE_AUDIO_VOLUME_SCREEN:3112,GET_LOCAL_AUDIO_LEVEL:3113,GET_LOCAL_AUDIO_LEVEL_SCREEN:3114,GET_REMOTE_AUDIO_LEVEL:3115,GET_REMOTE_AUDIO_LEVEL_SCREEN:3116,HAS_LOCAL_AUDIO:3117,HAS_LOCAL_AUDIO_SCREEN:3118,HAS_REMOTE_AUDIO:3119,HAS_REMOTE_AUDIO_SCREEN:3120,HAS_LOCAL_VIDEO:3121,HAS_LOCAL_VIDEO_SCREEN:3122,HAS_REMOTE_VIDEO:3123,HAS_REMOTE_VIDEO_SCREEN:3124,GET_LCOAL_AUDIO_TRACK:3125,GET_LCOAL_AUDIO_TRACK_SCREEN:3126,GET_REMOTE_AUDIO_TRACK:3127,GET_REMOTE_AUDIO_TRACK_SCREEN:3128,GET_LCOAL_VIDEO_TRACK:3129,GET_LCOAL_VIDEO_TRACK_SCREEN:3130,GET_REMOTE_VIDEO_TRACK:3131,GET_REMOTE_VIDEO_TRACK_SCREEN:3132,GET_LCOAL_VIDEO_FRAME:3133,GET_LCOAL_VIDEO_FRAME_SCREEN:3134,GET_REMOTE_VIDEO_FRAME:3135,GET_REMOTE_VIDEO_FRAME_SCREEN:3136,GET_LCOAL_TYPE:3137,GET_REMOTE_TYPE:3138,GET_LOCAL_AUDIO_ELEMENT:3139,GET_LOCAL_AUDIO_ELEMENT_SCREEN:3140,GET_REMOTE_AUDIO_ELEMENT:3141,GET_REMOTE_AUDIO_ELEMENT_SCREEN:3142,GET_LOCAL_VIDEO_ELEMENT:3143,GET_LOCAL_VIDEO_ELEMENT_SCREEN:3144,GET_REMOTE_VIDEO_ELEMENT:3145,GET_REMOTE_VIDEO_ELEMENT_SCREEN:3146,SET_AUDIO_PROFILE:3147,SET_VIDEO_PROFILE:3148,SET_SCREEN_PROFILE:3149,SET_VIDEO_CONTENT_HINT:3150,SWITCH_DEVICE_AUDIO:3151,SWITCH_DEVICE_VIDEO:3152,ADD_AUDIO_TRACK:3153,ADD_AUDIO_TRACK_SCREEN:3154,ADD_VIDEO_TRACK:3155,ADD_VIDEO_TRACK_SCREEN:3156,REMOVE_TRACK:3157,REMOVE_TRACK_SCREEN:3158,REPLACE_AUDIO_TRACK:3159,REPLACE_AUDIO_TRACK_SCREEN:3160,REPLACE_VIDEO_TRACK:3161,REPLACE_VIDEO_TRACK_SCREEN:3162,GET_DEVICES_INFO_IN_USE:3163,ON_STREAM_ADDED:3164,ON_STREAM_ADDED_SCREEN:3165,ON_STREAM_REMOVED:3166,ON_STREAM_REMOVED_SCREEN:3167,ON_STREAM_UPDATED:3168,ON_STREAM_UPDATED_SCREEN:3169,ON_STREAM_SUBSCRIBED:3170,ON_STREAM_SUBSCRIBED_SCREEN:3171,ON_PEER_JOIN:3172,ON_PEER_LEVAE:3173,ON_MUTE_AUDIO:3174,ON_MUTE_AUDIO_SCREEN:3175,ON_MUTE_VIDEO:3176,ON_MUTE_VIDEO_SCREEN:3177,ON_UNMUTE_AUDIO:3178,ON_UNMUTE_AUDIO_SCREEN:3179,ON_UNMUTE_VIDEO:3180,ON_UNMUTE_VIDEO_SCREEN:3181,ON_CLIENT_BANNED:3182,ON_CAMERA_CHANGED:3183,ON_RECORDING_DEVICE_CHANGED:3184,ON_PLAYBACK_DEVICE_CHANGED:3185,ON_ERROR:3186,OFF_STREAM_ADDED:3187,OFF_STREAM_REMOVED:3188,OFF_STREAM_UPDATED:3189,OFF_STREAM_SUBSCRIBED:3190,OFF_CONNECTION_STATE_CHANGED:3191,OFF_PEER_JOIN:3192,OFF_PEER_LEVAE:3193,OFF_MUTE_AUDIO:3194,OFF_MUTE_VIDEO:3195,OFF_UNMUTE_VIDEO:3196,OFF_CLIENT_BANNED:3197,OFF_CAMERA_CHANGED:3198,OFF_RECORDING_DEVICE_CHANGED:3199,OFF_PLAYBACK_DEVICE_CHANGED:3200,OFF_NETWORK_QUALITY:3201,OFF_AUDIO_VOLUME:3202,OFF_ERROR:3203,ON_PLAYER_STATE_CHANGED:3204,ON_SCREEN_SHARING_STOPPED:3205,ON_STREAM_ERROR:3206,CONNECTIONLOST_CB:3207,TRY_TO_RECONNECT_CB:3208,CONNECTION_RECOVERY_CB:3209}),{VUBIT:2001,VDBIT:2002,AUBIT:2003,ADBIT:2004,VULOSS:2005,VDLOSS:2006,AULOSS:2007,ADLOSS:2008,VURTT:2009,VDRTT:2010,AURTT:2011,ADRTT:2012,VUFPS:2013,VDFPS:2014,AUFPS:2015,ADFPS:2016,VUBLOCK:2017,VDBLOCK:2018,AUBLOCK:2019,ADBLOCK:2020,VUWIDTHHEIGHT:2021,VDWIDTHHEIGHT:2022,APPCPU:2023,SYSCPU:2024});function je(){var e,t,i=navigator.userAgent.toLocaleLowerCase();if(-1!=i.indexOf("firefox"))e="Firefox";else if(-1!=i.indexOf("trident"))e="IE",-1==i.indexOf("ie")&&(t=11);else if(-1!=i.indexOf("opr"))e="OPR";else if(-1!=i.indexOf("edge"))e="Edge";else if(-1!=i.indexOf("chrome"))e="Chrome";else if(-1!=i.indexOf("safari")){var r;e="Safari",r=(r=i.indexOf("version"))+"version".length+1,t=parseInt(i.slice(r,r+3))}else e="未知浏览器";return void 0===t&&(r=(r=i.indexOf(e.toLocaleLowerCase()))+e.length+1,t=parseInt(i.slice(r,r+3))),{browser:e,version:t}}function We(){return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter((function(e){return e in window})).length>0}function He(){var e=function(){var e=je(),t=e.browser,i=e.version;return"Chrome"===t?i>=74:"Edge"===t?i>=80:"Firefox"===t?i>=66:"OPR"===t?i>=60:"Safari"===t&&i>=13}(),t=We(),i=function(){if(!navigator.mediaDevices)return!1;var e=["getUserMedia","enumerateDevices"];return e.filter((function(e){return e in navigator.mediaDevices})).length===e.length}();return new Promise((function(r,n){(function(){var e=this;return new Promise((function(t,i){if(We()){var r=new RTCPeerConnection;r.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0}).then((function(e){var i=!!e.sdp&&e.sdp.toLowerCase().indexOf("h264")>-1;r.close(),r=null,t(i)}),(function(){e.logger.onError({c:Fe.TOP_ERROR,v:K.H264_NOT_SUPPORTED}),i(new ne({code:K.H264_NOT_SUPPORTED,message:"h264 not supported"}))})).catch((function(t){e.logger.onError({c:Fe.TOP_ERROR,v:K.H264_NOT_SUPPORTED});var r=new ne({code:K.H264_NOT_SUPPORTED,message:t.message});i(r)}))}else t(!1)}))})().then((function(n){r({result:e&&t&&i&&n,detail:{isBrowserSupported:e,isWebRTCSupported:t,isMediaDevicesSupported:i,isH264Supported:n}})}),(function(e){return n(e)}))}))}function Ge(){var e=this;if(!navigator.mediaDevices)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND}),new ne({code:K.DEVICE_NOT_FOUND,message:"navigator.mediaDevices is undefined"});return new Promise((function(t,i){navigator.mediaDevices.enumerateDevices().then((function(e){var i=e.filter((function(e){return"audioinput"!==e.kind||"communications"!=e.deviceId})).map((function(e,t){var i=e.label;e.label||(i=e.kind+"_"+t);var r={label:i,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));t(i)}),(function(e){i(e)})).catch((function(t){e.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND});var r=new ne({code:K.DEVICE_NOT_FOUND,message:t.message});i(r)}))}))}function ze(){var e=this;if(!navigator.mediaDevices)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.CAMERAS_NOT_FOUND}),new ne({code:K.CAMERAS_NOT_FOUND,message:"navigator.mediaDevices is undefined"});return new Promise((function(t,i){navigator.mediaDevices.enumerateDevices().then((function(e){var i=e.filter((function(e){return"videoinput"===e.kind})).map((function(e,t){var i=e.label;e.label||(i="camera_"+t);var r={label:i,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));t(i)}),(function(e){i(e)})).catch((function(t){e.logger.onError({c:Fe.TOP_ERROR,v:K.CAMERAS_NOT_FOUND});var r=new ne({code:K.CAMERAS_NOT_FOUND,message:t.message});i(r)}))}))}function Je(){if(!navigator.mediaDevices)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.MICROPHONES_NOT_FOUND}),new ne({code:K.MICROPHONES_NOT_FOUND,message:"navigator.mediaDevices is undefined"});return new Promise((function(e,t){navigator.mediaDevices.enumerateDevices().then((function(t){var i=t.filter((function(e){return"audioinput"===e.kind&&"communications"!==e.deviceId})).map((function(e,t){var i=e.label;e.label||(i="microphone_"+t);var r={label:i,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));e(i)}),(function(e){t(e)})).catch((function(e){var i=new ne({code:K.MICROPHONES_NOT_FOUND,message:e.message});t(i)}))}))}function Ke(){var e=this;if(!navigator.mediaDevices)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.SPEAKERS_NOT_FOUND}),new ne({code:K.SPEAKERS_NOT_FOUND,message:"navigator.mediaDevices is undefined"});return new Promise((function(t,i){navigator.mediaDevices.enumerateDevices().then((function(e){var i=e.filter((function(e){return"audiooutput"===e.kind})).map((function(e,t){var i=e.label;e.label||(i="speaker_"+t);var r={label:i,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));t(i)}),(function(e){i(e)})).catch((function(t){e.logger.onError({c:Fe.TOP_ERROR,v:K.SPEAKERS_NOT_FOUND});var r=new ne({code:K.SPEAKERS_NOT_FOUND,message:t.message});i(r)}))}))}function Ye(){return!!("captureStream"in HTMLCanvasElement.prototype)}function qe(){return"Safari"!==je().browser}var Xe=function(){function e(t,i,r,n){L(this,e),this.logger=i,this.streamConfig=t,this.streamId=t.streamId||null,this.mediaStream=t.mediaStream||null,this.type=t.type?t.type:t.screen?H:null,this.info=t.info||null,this.mixedInfo=t.mixedInfo||null,this.constraints={audio:t.audio,video:t.video},this.roomId=r||null,this.xsigoClient=n||null,this.isPlaying=!1,this.objectFit="cover",this.muted=!1,this.mirror=t.mirror||!1,this.audioPlayer=null,this.videoPlayer=null,this.audioOutputDeviceId="",this.audioOutputGroundId="",this.audioVolume=1,this.isRemote=!1,this.setUserId(t.userId),this.timer=null,this.waterStreamStream=null,this.waterMarkoptions=null,this.waterMarkVideo=null,this.isWaterMark=!1,this.localId="default",this._emitter=new N,this.hasAudioTrack=!1,this.hasVideoTrack=!1,this.audioStreamId="",this.videoStreamId="",this.peerConnections=[],this.audioTrackEnabled=!0,this.videoTrackEnabled=!0,this.pcFailedCount=0,this.audioMuted=!0,this.videoMuted=!0,this.backgroundColor="#000000",t.audio&&this.setAudioOutput("default"),this.isAlphaChannels=!1,this.virtualBackground=null,this.virtualBackgroundMix=!1,this.waterMarkImage=null}var t,i,r,n,o,s,a;return x(e,[{key:"setPlayBackground",value:function(e){this.backgroundColor=e}},{key:"play",value:(a=P(U.mark((function e(t,i){var r,n,o;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isPlaying){e.next=3;break}return this.logger.warn("duplicated play() call observed, please stop() firstly"),e.abrupt("return");case 3:if(this.isPlaying=!0,this.logger.info("stream start to play with options: ".concat(JSON.stringify(i))),n="string"==typeof t?document.getElementById(t):t,document.getElementById("player_".concat(this.userId))?r=document.getElementById("player_".concat(this.userId)):((r=document.createElement("div")).setAttribute("id","player_".concat(this.userId)),r.setAttribute("style","width: 100%; height: 100%; position: relative; background-color:".concat(this.backgroundColor,"; overflow: hidden;")),null===(o=n)||void 0===o||o.appendChild(r)),this.div=r,this.isRemote||(this.muted=!0),i&&void 0!==i.muted&&(this.muted=i.muted),this.isRemote&&this.info.video&&"screen"===this.info.video.source&&(this.objectFit="contain"),i&&void 0!==i.objectFit&&(this.objectFit=i.objectFit),!this.hasVideo()||!this.hasAudio()){e.next=16;break}return this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.PLAY_REMOTE_VIDEO_SCREEN:Fe.PLAY_REMOTE_VIDEO:this.type===H?Fe.PLAY_LOCAL_VIDEO_SCREEN:Fe.PLAY_LOCAL_VIDEO,v:this.addUid()}),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.PLAY_REMOTE_AUDIO_SCREEN:Fe.PLAY_REMOTE_AUDIO:this.type===H?Fe.PLAY_LOCAL_AUDIO_SCREEN:Fe.PLAY_LOCAL_AUDIO,v:this.addUid()}),e.abrupt("return",(this.playVideo(),this.playAudio()));case 16:if(!this.hasVideo()){e.next=19;break}return this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.PLAY_REMOTE_VIDEO_SCREEN:Fe.PLAY_REMOTE_VIDEO:this.type===H?Fe.PLAY_LOCAL_VIDEO_SCREEN:Fe.PLAY_LOCAL_VIDEO,v:this.addUid()}),e.abrupt("return",this.playVideo());case 19:if(!this.hasAudio()){e.next=22;break}return this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.PLAY_REMOTE_AUDIO_SCREEN:Fe.PLAY_REMOTE_AUDIO:this.type===H?Fe.PLAY_LOCAL_AUDIO_SCREEN:Fe.PLAY_LOCAL_AUDIO,v:this.addUid()}),e.abrupt("return",this.playAudio());case 22:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"stop",value:function(){this.logger.info("is playing:"+this.isPlaying),this.isPlaying&&(this.logger.info("Stop playing audio and video"),this.audioPlayer&&this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.STOP_REMOTE_AUDIO_SCREEN:Fe.STOP_REMOTE_AUDIO:this.type===H?Fe.STOP_LOCAL_AUDIO_SCREEN:Fe.STOP_LOCAL_AUDIO,v:this.addUid()}),this.videoPlayer&&this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.STOP_REMOTE_VIDEO_SCREEN:Fe.STOP_REMOTE_VIDEO:this.type===H?Fe.STOP_LOCAL_VIDEO_SCREEN:Fe.STOP_LOCAL_VIDEO,v:this.addUid()}),this.isPlaying=!1,this.stopAudio(),this.stopVideo(),this.div.parentNode&&this.div.parentNode.removeChild(this.div))}},{key:"resume",value:(s=P(U.mark((function e(){return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.logger.info("is playing:"+this.isPlaying),this.isPlaying&&(this.logger.info("stream - resume"),this.audioPlayer&&(this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.RESUME_REMOTE_AUDIO_SCREEN:Fe.RESUME_REMOTE_AUDIO:this.type===H?Fe.RESUME_LOCAL_AUDIO_SCREEN:Fe.RESUME_LOCAL_AUDIO,v:this.addUid()}),this.audioPlayer.resume()),this.videoPlayer&&(this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.RESUME_REMOTE_VIDEO_SCREEN:Fe.RESUME_REMOTE_VIDEO:this.type===H?Fe.RESUME_LOCAL_VIDEO_SCREEN:Fe.RESUME_LOCAL_VIDEO,v:this.addUid()}),this.videoPlayer.resume()));case 2:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"close",value:function(){this.logger.info("is playing:"+this.isPlaying),this.isPlaying&&this.stop(),this.mediaStream&&(this.mediaStream.getTracks().forEach((function(e){e.stop()})),this.mediaStream=null)}},{key:"muteAudio",value:function(){return!(!this.mediaStream||!this.audioTrackEnabled)&&(this.logger.info("mute audio"),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.MUTE_REMOTE_AUDIO_SCREEN:Fe.MUTE_REMOTE_AUDIO:this.type===H?Fe.MUTE_LOCAL_AUDIO_SCREEN:Fe.MUTE_LOCAL_AUDIO,v:this.addUid()}),this.addRemoteEvent(Se.AudioMute),this.doEnableTrack("audio",!1))}},{key:"muteVideo",value:function(){return!(!this.mediaStream||!this.videoTrackEnabled)&&(this.logger.info("mute video"),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.MUTE_REMOTE_VIDEO_SCREEN:Fe.MUTE_REMOTE_VIDEO:this.type===H?Fe.MUTE_LOCAL_VIDEO_SCREEN:Fe.MUTE_LOCAL_VIDEO,v:this.addUid()}),this.isAlphaChannels&&this.videoPlayer.mute(),this.addRemoteEvent(Se.VideoMute),this.doEnableTrack("video",!1))}},{key:"unmuteAudio",value:function(){return!(!this.mediaStream||this.audioTrackEnabled)&&(this.logger.info("unmute audio"),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.UNMUTE_REMOTE_AUDIO_SCREEN:Fe.UNMUTE_REMOTE_AUDIO:this.type===H?Fe.UNMUTE_LOCAL_AUDIO_SCREEN:Fe.UNMUTE_LOCAL_AUDIO,v:this.addUid()}),this.addRemoteEvent(Se.AudioUnmute),this.doEnableTrack("audio",!0))}},{key:"unmuteVideo",value:function(){return!(!this.mediaStream||this.videoTrackEnabled)&&(this.logger.info("unmute video"),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.UNMUTE_REMOTE_VIDEO_SCREEN:Fe.UNMUTE_REMOTE_VIDEO:this.type===H?Fe.UNMUTE_LOCAL_VIDEO_SCREEN:Fe.UNMUTE_LOCAL_VIDEO,v:this.addUid()}),this.isAlphaChannels&&this.videoPlayer.unmute(),this.addRemoteEvent(Se.VideoUnmute),this.doEnableTrack("video",!0))}},{key:"updateTrack",value:function(e,t){var i;(i="audio"===e?this.getAudioTrack():this.getVideoTrack())&&this.mediaStream.removeTrack(i),this.mediaStream.addTrack(t)}},{key:"doEnableTrack",value:function(e,t){var i=!1;return"audio"===e?this.mediaStream.getAudioTracks().forEach((function(e){i=!0,e.enabled=t})):this.mediaStream.getVideoTracks().forEach((function(e){i=!0,e.enabled=t})),this.setEnableTrackFlag(e,t),i}},{key:"setEnableTrackFlag",value:function(e,t){"audio"===e?this.audioTrackEnabled=t:this.videoTrackEnabled=t}},{key:"addRemoteEvent",value:function(e,t){var i=this;return new Promise((function(r,n){if(!i.isRemote){var o=function(e,t,i){1===e&&r(!0),0===e&&n(!1)};if(i.xsigoClient)switch(e){case Se.AudioMute:i.xsigoClient.muteAudio(i.roomId,i.audioStreamId,o,t);break;case Se.VideoMute:i.xsigoClient.muteVideo(i.roomId,i.videoStreamId,o,t);break;case Se.AudioUnmute:i.xsigoClient.unmuteAudio(i.roomId,i.audioStreamId,o,t);break;case Se.VideoUnmute:i.xsigoClient.unmuteVideo(i.roomId,i.videoStreamId,o,t)}else i.logger.info("not xsigoClient")}}))}},{key:"getId",value:function(){return this.streamId||""}},{key:"getUserId",value:function(){return"main"===this.type?this.userId:this.userId.replace("share_","")}},{key:"setUserId",value:function(e){if(this.streamConfig.screen)return this.userId="share_".concat(e);this.userId=e}},{key:"setAudioOutput",value:(o=P(U.mark((function e(t){var i;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Ke();case 2:(i=e.sent.filter((function(e){return e.deviceId===t}))).length&&(this.audioOutputDeviceId=i[0].deviceId,this.audioOutputGroundId=i[0].groupId,this.logger.info("setAudioOutput deviceId",t),this.logger.buriedLog({c:Fe.SET_AUDIO_OUTPUT,v:"deviceId:".concat(t)}),this.audioPlayer&&this.audioPlayer.setSinkId(t));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"getInuseSpeaker",value:(n=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.logger.buriedLog({c:Fe.GET_DEVICES_INFO_IN_USE,v:"speaker:".concat(this.audioOutputDeviceId)}),!this.audioOutputDeviceId){e.next=6;break}return e.next=4,Ke();case 4:this.audioOutputGroundId=e.sent.filter((function(e){return e.deviceId===t.audioOutputDeviceId}))[0].groupId;case 6:return e.abrupt("return",{speaker:{deviceId:this.audioOutputDeviceId,groupId:this.audioOutputGroundId}});case 7:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"setAudioVolume",value:function(e){this.audioVolume=e,this.logger.info("setAudioVolume to ".concat(e.toString())),this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.SET_REMOTE_AUDIO_VOLUME_SCREEN:Fe.SET_REMOTE_AUDIO_VOLUME:this.type===H?Fe.SET_LOCAL_AUDIO_VOLUME_SCREEN:Fe.SET_LOCAL_AUDIO_VOLUME,v:"volume:".concat(e)}),this.audioPlayer&&this.audioPlayer.setVolume(e)}},{key:"getAudioLevel",value:function(){return this.audioPlayer?this.audioPlayer.getAudioLevel():0}},{key:"setHasAudio",value:function(e){this.hasAudioTrack=e}},{key:"hasAudio",value:function(){return!!this.checkMediaStream()&&this.hasAudioTrack}},{key:"setHasVideo",value:function(e){this.hasVideoTrack=e}},{key:"hasVideo",value:function(){return!!this.checkMediaStream()&&this.hasVideoTrack}},{key:"getAudioTrack",value:function(){var e=null;if(this.checkMediaStream()){var t=this.mediaStream.getAudioTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoTrack",value:function(){var e=null;if(this.checkMediaStream()){var t=this.mediaStream.getVideoTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoFrame",value:function(){return this.logger.buriedLog({c:this.isRemote?this.type===H?Fe.GET_REMOTE_VIDEO_FRAME_SCREEN:Fe.GET_REMOTE_VIDEO_FRAME:this.type===H?Fe.GET_LCOAL_VIDEO_FRAME_SCREEN:Fe.GET_LCOAL_VIDEO_FRAME}),this.videoPlayer?this.videoPlayer.getVideoFrame():null}},{key:"on",value:function(e,t){this._emitter.on(e,t)}},{key:"playAudio",value:(r=P(U.mark((function e(){var t,i=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getAudioTrack(),!this.audioPlayer&&t){e.next=3;break}return e.abrupt("return");case 3:return this.logger.info("stream - create AudioPlayer and play"),this.audioPlayer=new se({stream:this,track:t,div:this.div,muted:this.muted,volume:this.audioVolume,deviceId:this.audioOutputDeviceId}),this.audioPlayer._emitter.on("player-state-changed",(function(e){i._emitter.emit("player-state-changed",{type:"audio",state:e.state,reason:e.reason}),"track"===e.type&&i._emitter.emit("track-state-changed",{type:"audio",state:e.state,reason:e.reason})})),e.abrupt("return",new Promise((function(e,t){i.audioPlayer.play().then((function(){e()})).catch((function(e){if(i.logger.warn("<audio> play() error:"+e),(e.toString()+" <audio>").startsWith("NotAllowedError")){i.logger.onError({c:Fe.TOP_ERROR,v:K.PLAY_NOT_ALLOWED});var r=new ne({code:K.PLAY_NOT_ALLOWED,message:e.message});i.logger.buriedLog({c:Fe.ON_STREAM_ERROR,v:"code:".concat(K.PLAY_NOT_ALLOWED)}),i._emitter.emit(G,r),t(r)}}))})));case 7:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"getWaterStreamStream",value:(i=P(U.mark((function e(t){var i,r,n,o,s,a,c,u,d,l=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=this.mediaStream,this.waterMarkVideo=document.createElement("video"),this.waterMarkVideo.srcObject=i,e.next=5,this.waterMarkVideo.play();case 5:return r=i.getVideoTracks()[0],n=document.createElement("canvas"),o=n.getContext("2d"),s=r.getSettings(),this.logger.info("settings frameRate ====>",1e3/s.frameRate),a=Math.floor(1e3/15),u=Date.now(),n.width=s.width,n.height=s.height,function e(){var i=r.getSettings().frameRate;i&&(a=Math.floor(1e3/i)),a<1e3/15&&(a=Math.floor(1e3/15)),l.timer&&cancelAnimationFrame(l.timer),l.timer=requestAnimationFrame(e),c=Date.now(),(d=c-u)>a&&(u=c-d/a,o.drawImage(l.waterMarkVideo,0,0,n.width,n.height),o.drawImage(t,0,0,s.width,s.height))}(),this.waterStreamStream=n.captureStream(),e.abrupt("return",this.waterStreamStream);case 17:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"startWaterMark",value:function(e,t){var i=this;return this.logger.info(this.userId+" startWaterMark",e),new Promise(function(){var r=P(U.mark((function r(n,o){var s,a;return U.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!i.waterStreamStream){r.next=2;break}return r.abrupt("return",o("waterMark is starting"));case 2:if(i.waterMarkoptions=e,i.waterMarkImage=t,i.type===H&&i.isRemote){r.next=6;break}return r.abrupt("return",o("waterMark is only support remoteStream and screenShare"));case 6:if(!i.videoPlayer){r.next=17;break}return r.next=9,i.getWaterStreamStream(t);case 9:s=r.sent.getVideoTracks()[0],(a=new MediaStream).addTrack(s),i.getVideoElement().srcObject=a,r.next=18;break;case 17:i.isWaterMark=!0;case 18:n();case 19:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}())}},{key:"closeWaterMark",value:function(){if(this.waterStreamStream&&(this.logger.info(this.userId+" closeWaterMark"),this.isWaterMark=!1,this.waterStreamStream=null,this.waterMarkImage=null,this.waterMarkoptions=null,this.timer&&(cancelAnimationFrame(this.timer),this.timer=null),this.waterMarkVideo&&(this.waterMarkVideo.srcObject=null,this.waterMarkVideo=null),this.isPlaying)){var e=this.getVideoElement(),t=this.getVideoTrack(),i=new MediaStream;i.addTrack(t),e.srcObject=i}}},{key:"setLocalUserId",value:function(e){this.localId=e}},{key:"playVideo",value:(t=P(U.mark((function e(){var t,i=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getVideoTrack(),!this.videoPlayer&&t){e.next=3;break}return e.abrupt("return");case 3:if(!this.isWaterMark){e.next=8;break}return e.next=6,this.getWaterStreamStream(this.waterMarkImage);case 6:t=e.sent.getVideoTracks()[0];case 8:return this.logger.info("stream - create VideoPlayer and play"),this.videoPlayer=new de({stream:this,track:t,div:this.div,muted:this.muted,objectFit:this.objectFit,mirror:this.mirror,isAlphaChannels:this.isAlphaChannels,virtualBackground:this.virtualBackground,virtualBackgroundMix:this.virtualBackgroundMix}),this.videoPlayer._emitter.on("player-state-changed",(function(e){i._emitter.emit("player-state-changed",{type:"video",state:e.state,reason:e.reason}),"track"===e.type&&i._emitter.emit("track-state-changed",{type:"video",state:e.state,reason:e.reason})})),e.abrupt("return",new Promise((function(e,t){i.videoPlayer.play().then((function(){e()})).catch((function(e){if(i.logger.warn("<video> play() error:"+e),(e.toString()+" <video>").startsWith("NotAllowedError")){i.logger.onError({c:Fe.TOP_ERROR,v:K.PLAY_NOT_ALLOWED});var r=new ne({code:K.PLAY_NOT_ALLOWED,message:e.message});i.logger.buriedLog({c:Fe.ON_STREAM_ERROR,v:"code:".concat(K.PLAY_NOT_ALLOWED)}),i._emitter.emit(G,r),t(r)}}))})));case 12:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"stopAudio",value:function(){this.audioPlayer&&(this.logger.info("stream - stop AudioPlayer"),this.audioPlayer.stop(),this.audioPlayer=null)}},{key:"stopVideo",value:function(){this.videoPlayer&&(this.logger.info("stream - stop VideoPlayer"),this.videoPlayer.stop(),this.videoPlayer=null)}},{key:"checkMediaStream",value:function(){return!!this.mediaStream}},{key:"restartAudio",value:function(){this.isPlaying&&(this.stopAudio(),this.playAudio())}},{key:"restartVideo",value:function(){this.isPlaying&&(this.stopVideo(),this.playVideo())}},{key:"setMediaStream",value:function(e){this.mediaStream=e}},{key:"setSimulcasts",value:function(e){this.info.video.simulcast=e}},{key:"getSimulcasts",value:function(){try{return this.info.video.simulcast||[]}catch(e){return[]}}},{key:"setInfo",value:function(e){this.info=e}},{key:"getType",value:function(){return this.type||"main"}},{key:"getAudioElement",value:function(){return this.audioPlayer&&this.audioPlayer.getAudioElement()}},{key:"getVideoElement",value:function(){return this.videoPlayer&&this.videoPlayer.getVideoElement()}},{key:"setAudioTrack",value:function(e){e.enabled=this.audioTrackEnabled,this.audioPlayer?this.audioPlayer.setAudioTrack(e):this.playAudio()}},{key:"setVideoTrack",value:function(e){e.enabled=this.videoTrackEnabled,this.videoPlayer?this.videoPlayer.setVideoTrack(e):this.playVideo()}},{key:"setAudioStreamId",value:function(e){this.audioStreamId=e}},{key:"setVideoStreamId",value:function(e){this.videoStreamId=e}},{key:"addUid",value:function(){if(this.isRemote)return"uid:".concat(this.getUserId())}},{key:"getAudioMuted",value:function(){return this.getAudioTrack()&&!this.audioTrackEnabled||this.audioMuted}},{key:"getVideoMuted",value:function(){return this.getVideoTrack()&&!this.videoTrackEnabled||this.videoMuted}},{key:"updatePeerConnectionFailed",value:function(e){if("failed"===e){this.pcFailedCount++,this.logger.onError({c:Fe.TOP_ERROR,v:K.RTCPEERCONNECTION_SATE_FAILED});var t=new ne({code:K.RTCPEERCONNECTION_SATE_FAILED,message:'{"count":'.concat(this.pcFailedCount,"}")});this._emitter.emit(G,t),this.logger.warn("updatePeerConnectionFailed,count:".concat(this.pcFailedCount))}else"connected"===e&&0!==this.pcFailedCount&&(this.pcFailedCount=0)}},{key:"setMutedState",value:function(e,t){"audio"===e?this.audioMuted=t:"video"===e&&(this.videoMuted=t)}},{key:"setIsAlphaChannels",value:function(e){this.logger.info("set isAlphaChannels",e),this.isAlphaChannels=e}},{key:"hasAlphaChannels",value:function(){return this.isAlphaChannels}},{key:"setVirtualBackground",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.virtualBackground)return this.logger.warn("The virtual background has already been set"),!1;if(!this.isAlphaChannels)return this.logger.warn("Please start the stream containing alpha channel first"),!1;var i=["IMG","VIDEO"];return i.includes(e.nodeName)?(this.logger.info("set virtual background,element:".concat(e.nodeName,",mix:").concat(t)),this.virtualBackground=e,this.virtualBackgroundMix="VIDEO"===e.nodeName||t,!0):(this.logger.warn("Element must be img or video"),!1)}}]),e}(),$e=new Map;$e.set("standard",{sampleRate:48e3,channelCount:1,bitrate:40}),$e.set("high",{sampleRate:48e3,channelCount:1,bitrate:128});var Qe=new Map;Qe.set("120p",{width:160,height:120,frameRate:15,bitrate:200}),Qe.set("180p",{width:320,height:180,frameRate:15,bitrate:350}),Qe.set("240p",{width:320,height:240,frameRate:15,bitrate:400}),Qe.set("360p",{width:640,height:360,frameRate:15,bitrate:800}),Qe.set("480p",{width:640,height:480,frameRate:15,bitrate:900}),Qe.set("720p",{width:1280,height:720,frameRate:15,bitrate:1500}),Qe.set("1080p",{width:1920,height:1080,frameRate:15,bitrate:2e3}),Qe.set("1440p",{width:2560,height:1440,frameRate:30,bitrate:4860}),Qe.set("4K",{width:3840,height:2160,frameRate:30,bitrate:9e3});var Ze=new Map;function et(e){return("function"==typeof Symbol&&"symbol"==X(Symbol.iterator)?function(e){return X(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":X(e)})(e)}Ze.set("480p",{width:640,height:480,frameRate:5,bitrate:900}),Ze.set("480p_2",{width:640,height:480,frameRate:30,bitrate:1e3}),Ze.set("720p",{width:1280,height:720,frameRate:5,bitrate:1200}),Ze.set("720p_2",{width:1280,height:720,frameRate:30,bitrate:3e3}),Ze.set("1080p",{width:1920,height:1080,frameRate:5,bitrate:1600}),Ze.set("1080p_2",{width:1920,height:1080,frameRate:30,bitrate:4e3});var tt,it=window.navigator&&window.navigator.userAgent||"",rt=/Edge\//i.test(it),nt=(tt=it.match(/Chrome\/(\d+)/))&&tt[1]?parseFloat(tt[1]):null;function ot(e,t){return new Promise((function(i,r){var n=document.createElement("canvas"),o=n.getContext("2d");n.width=1920,n.height=1080;var s=document.createElement("canvas");s.width=400,s.height=200,s.style.border="1px solid";var a=s.getContext("2d");a.rotate(-20*Math.PI/180),a.font="".concat(e.fontSize,"px ").concat(e.fontType),a.fillStyle=e.fontColor,a.textBaseline="middle",a.fillText(t,0,90);var c=new Image;c.src=s.toDataURL("image/png"),c.onload=function(){o.fillStyle=o.createPattern(c,"repeat"),o.fillRect(0,0,n.width,n.height);var e=new Image;return e.src=n.toDataURL("image/png"),i(e)}}))}function st(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function at(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?st(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):st(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}!function(e){q(s,Xe);var t,i,r,n,o=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,r=Z(e);if(t){var n=Z(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return Q(this,i)}}(s);function s(e,t){var i;return L(this,s),(i=o.call(this,e,t)).screen=e.screen,i.audioProfile=$e.get("standard"),i.videoProfile=Qe.get("480p"),i.screenProfile=Ze.get("1080p"),i.bitrate={audio:i.audioProfile.bitrate,video:i.screen?i.screenProfile.bitrate:i.videoProfile.bitrate},i.cameraId_=e.cameraId||"",i.cameraGroupId_="",i.microphoneId_=e.microphoneId||"",i.microphoneGroupId_="",i.cameraLabel_="",i.microphoneLabel_="",i.recoverCaptureCount_=0,i.published=!1,i.audioPubState=F.Create,i.videoPubState=F.Create,i._emitter.on("track-state-changed",i.onTrackStopped.bind($(i))),i}x(s,[{key:"initialize",value:(n=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.logger.info("initialize stream audio: ".concat(this.constraints.audio," video: ").concat(this.constraints.video)),e.abrupt("return",new Promise((function(e,i){return t.screen?t.initShareStream({audio:t.streamConfig.audio,screenAudio:t.streamConfig.screenAudio,microphoneId:t.microphoneId_,width:t.screenProfile.width,height:t.screenProfile.height,frameRate:t.screenProfile.frameRate,sampleRate:t.audioProfile.sampleRate,channelCount:t.audioProfile.channelCount}).then((function(i){t.mediaStream=i;var r=i.getAudioTracks().length>0,n=i.getVideoTracks().length>0;t.setHasAudio(r),t.setHasVideo(n),t.setMutedState("audio",!r),t.setMutedState("video",!n),t.listenForScreenSharingStopped(i.getVideoTracks()[0]),t.setVideoContentHint("detail"),t.updateDeviceIdInUse(),e(i),t.logger.info("init share stream success")})).catch((function(e){t.logger.onError({c:Fe.TOP_ERROR,v:K.INIT_STREAM_FAILED},"init share stream failed,".concat(e.name,":").concat(e.message));var r=new ne({code:K.INIT_STREAM_FAILED,message:e.message,name:e.name});i(r)})):t.initAvStream({audio:t.streamConfig.audio,video:t.streamConfig.video,facingMode:t.streamConfig.facingMode,cameraId:t.cameraId_,microphoneId:t.microphoneId_,width:t.videoProfile.width,height:t.videoProfile.height,frameRate:t.videoProfile.frameRate,sampleRate:t.audioProfile.sampleRate,channelCount:t.audioProfile.channelCount}).then((function(i){t.mediaStream=i,window.initStream=i;var r=i.getAudioTracks().length>0,n=i.getVideoTracks().length>0;t.setHasAudio(r),t.setHasVideo(n),t.setMutedState("audio",!r),t.setMutedState("video",!n),t.updateDeviceIdInUse(),t.videoSetting=n&&i.getVideoTracks()[0].getSettings(),e(i),t.logger.info("init local stream success")})).catch((function(e){t.logger.onError({c:Fe.TOP_ERROR,v:K.INIT_STREAM_FAILED},"init localstream failed,".concat(e.name,":").concat(e.message));var r=new ne({code:K.INIT_STREAM_FAILED,message:e.message,name:e.name});i(r)})).finally(P(U.mark((function e(){return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Ge();case 2:t.logger.info("mediaDevices",JSON.stringify(e.sent,null,4));case 4:case"end":return e.stop()}}),e)}))))})));case 2:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"initAvStream",value:(r=P(U.mark((function e(t){var i,r,n,o,s,a;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaDevices){e.next=3;break}return this.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND},"navigator.mediaDevices is undefined"),e.abrupt("return",Promise.reject());case 3:if(r={audio:t.audio,video:t.video},!t.audio){e.next=19;break}return e.next=7,Je();case 7:if(0!==(i=e.sent).length){e.next=13;break}throw this.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND}),new ne({code:K.DEVICE_NOT_FOUND,message:"no microphone detected, but you are trying to get audio stream, please check your microphone and the configeration on XRTC.createStream."});case 13:i.filter((function(e){return"default"===e.deviceId})),(n=i.filter((function(e){return e.deviceId.length>0}))).length>0&&(o=n[0].deviceId),(s=i.filter((function(e){return"default"===e.deviceId}))).length>0&&(a=s[0].deviceId),r.audio={deviceId:{exact:t.microphoneId||a||o},echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0,sampleRate:t.sampleRate,channelCount:t.channelCount};case 19:if(!t.video){e.next=31;break}return e.next=22,ze();case 22:if(0!==e.sent.length){e.next=28;break}throw this.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND}),new ne({code:K.DEVICE_NOT_FOUND,message:"no camera detected, but you are trying to get video stream, please check your camera and the configeration on XRTC.createStream."});case 28:r.video={width:t.width,height:t.height,frameRate:t.frameRate},t.cameraId&&(r.video=at(at({},r.video),{},{deviceId:{exact:t.cameraId}})),t.facingMode&&(r.video=at(at({},r.video),{},{facingMode:t.facingMode}));case 31:return e.abrupt("return",navigator.mediaDevices.getUserMedia(r));case 32:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"initShareStream",value:function(e){if(!navigator.mediaDevices)return this.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_NOT_FOUND},"navigator.mediaDevices is undefined"),Promise.reject();if(e.screenAudio)rt||nt<74?this.logger.onError({c:Fe.TOP_ERROR,v:K.BROWSER_NOT_SUPPORTED},"Your browser not support capture system audio"):e.audioConstraints={echoCancellation:!0,noiseSuppression:!0,sampleRate:44100};else if(e.audio){var t={audio:void 0!==e.microphoneId?{deviceId:{exact:e.microphoneId},sampleRate:e.sampleRate,channelCount:e.channelCount}:{sampleRate:e.sampleRate,channelCount:e.channelCount},video:!1},i=this.setConstraints(e);return this.logger.info("getDisplayMedia with contraints: "+JSON.stringify(i)),new Promise(function(){var e=P(U.mark((function e(r,n){var o;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.mediaDevices.getDisplayMedia(i);case 2:o=e.sent,navigator.mediaDevices.getUserMedia(t).then((function(e){o.addTrack(e.getAudioTracks()[0]),r(o)})).catch((function(e){return n(e)}));case 4:case"end":return e.stop()}}),e)})));return function(t,i){return e.apply(this,arguments)}}())}var r=this.setConstraints(e);return this.logger.info("getDisplayMedia with contraints: "+JSON.stringify(r)),navigator.mediaDevices.getDisplayMedia(r)}},{key:"setAudioProfile",value:function(e){var t;this.mediaStream?this.logger.warn("Please set audio profile before initialize!"):("object"===et(e)?t=e:void 0===(t=$e.get(e))&&(t=$e.get("standard")),this.logger.info("setAudioProfile: "+JSON.stringify(t)),this.logger.buriedLog({c:Fe.SET_AUDIO_PROFILE,v:JSON.stringify(t)}),this.audioProfile=t,this.bitrate.audio=t.bitrate)}},{key:"setVideoProfile",value:(i=P(U.mark((function e(t){var i;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mediaStream){e.next=3;break}return this.logger.warn("Please set video profile before initialize!"),e.abrupt("return");case 3:"object"===et(t)?i=t:void 0===(i=Qe.get(t))&&(i=Qe.get("480p")),this.logger.info("setVideoProfile "+JSON.stringify(i)),this.logger.buriedLog({c:Fe.SET_VIDEO_PROFILE,v:JSON.stringify(i)}),this.videoProfile=i,this.bitrate.video=i.bitrate;case 8:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"setScreenProfile",value:function(e){var t;this.mediaStream?this.logger.warn("Please set screen profile before initialize!"):("object"===et(e)?t=e:void 0===(t=Ze.get(e))&&(t=Ze.get("1080p_2")),this.logger.info("setScreenProfile "+JSON.stringify(t)),this.logger.buriedLog({c:Fe.SET_SCREEN_PROFILE,v:JSON.stringify(t)}),this.screenProfile=t,this.bitrate.video=t.bitrate)}},{key:"setConstraints",value:function(e){var t={};return t.video={width:e.width,height:e.height,frameRate:e.frameRate},void 0!==e.audioConstraints&&(t.audio=e.audioConstraints),this.constraints=t,t}},{key:"getBitrate",value:function(){return this.bitrate}},{key:"addTrack",value:function(e){var t=this;if(!this.mediaStream)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.ADD_TRACK_FAILED}),new ne({code:K.ADD_TRACK_FAILED,message:"the local stream is not initialized yet"});if("audio"===e.kind&&this.getAudioTracks().length>0||"video"===e.kind&&this.getVideoTracks().length>0)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.ADD_TRACK_FAILED}),new ne({code:K.ADD_TRACK_FAILED,message:"A Stream has at most one audio track and one video track"});var i=e.getSettings();return"video"===e.kind&&i&&this.videoSetting&&(i.width!==this.videoSetting.width||i.height!==this.videoSetting.height)&&this.logger.warn("video resolution of the track ".concat(i.width," x ").concat(i.height," shall be kept the same as the previous: ").concat(this.videoSetting.width," x ").concat(this.videoSetting.height)),new Promise((function(r,n){t._emitter.once("stream-track-update-result",(function(e){var i=e.code,o=e.message;if(t.logger.info("add track response",i),1===i)r(!0);else{t.logger.onError({c:Fe.TOP_ERROR,v:K.ADD_TRACK_FAILED});var s=new ne({code:K.ADD_TRACK_FAILED,message:o||"add track failed"});n(s)}})),"video"===e.kind?(t.cameraId_=i.deviceId,t.cameraGroupId_=i.groupId):"audio"===e.kind&&(t.microphoneId_=i.deviceId,t.microphoneGroupId_=i.groupId),t.setEnableTrackFlag(e.kind,e.enabled),t.logger.buriedLog({c:"audio"===e.kind?t.type===H?Fe.ADD_AUDIO_TRACK_SCREEN:Fe.ADD_AUDIO_TRACK:t.type===H?Fe.ADD_VIDEO_TRACK_SCREEN:Fe.ADD_VIDEO_TRACK}),t._emitter.emit("stream-add-track",{track:e,streamId:t.streamId})}))}},{key:"removeTrack",value:function(e){var t=this;if(e&&"audio"===e.kind)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_PARAMETER}),new ne({code:K.INVALID_PARAMETER,message:"remove audio track is not supported"});if(!this.mediaStream)throw new ne({code:K.INVALID_OPERATION,message:"the local stream is not initialized yet"});if(-1===this.mediaStream.getTracks().indexOf(e))throw this.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_PARAMETER}),new ne({code:K.INVALID_PARAMETER,message:"the track to be removed is not being publishing"});if(!this.supportPC())throw new ne({code:K.INVALID_OPERATION,message:"removeTrack is not supported in this browser"});return this.logger.info("remove video track from current published stream"),new Promise((function(i,r){t._emitter.once("stream-track-update-result",(function(e){var n=e.code,o=e.message;if(t.logger.info("remove track response",n),1===n)i(!0);else{t.logger.onError({c:Fe.TOP_ERROR,v:K.REMOVE_TRACK_FAILED});var s=new ne({code:K.REMOVE_TRACK_FAILED,message:o||"remove track failed"});r(s)}})),t.logger.buriedLog({c:t.type===H?Fe.REMOVE_TRACK_SCREEN:Fe.REMOVE_TRACK}),t._emitter.emit("stream-remove-track",{track:e,streamId:t.streamId})}))}},{key:"replaceTrack",value:function(e){if(!this.mediaStream)throw new ne({code:K.INVALID_OPERATION,message:"the local stream is not initialized yet"});var t=e.getSettings();if("video"===e.kind&&t&&this.videoSetting&&(t.width!==this.videoSetting.width||t.height!==this.videoSetting.height)&&this.logger.warn("video resolution of the track ".concat(t.width," x ").concat(t.height," shall be kept the same as the previous: ").concat(this.videoSetting.width," x ").concat(this.videoSetting.height)),"audio"===e.kind?(this.mediaStream.removeTrack(this.getAudioTrack()),this.mediaStream.addTrack(e),e.enabled=!this.getAudioMuted(),this.restartAudio()):(this.mediaStream.removeTrack(this.getVideoTrack()),this.mediaStream.addTrack(e),e.enabled=!this.getVideoMuted(),this.restartVideo()),!this.isReplaceTrackAvailable()||!this.supportPC())throw new ne({code:K.INVALID_OPERATION,message:"replaceTrack is not supported in this browser, please use switchDevice or addTrack instead"});this.logger.buriedLog({c:"audio"===e.kind?this.type===H?Fe.REPLACE_AUDIO_TRACK_SCREEN:Fe.REPLACE_AUDIO_TRACK:this.type===H?Fe.REPLACE_VIDEO_TRACK_SCREEN:Fe.REPLACE_VIDEO_TRACK}),this._emitter.emit("stream-replace-track",{streamId:this.streamId,type:e.kind,track:e})}},{key:"setVideoContentHint",value:function(e){var t=this.getVideoTrack();t&&"contentHint"in t&&(this.logger.info("set video track contentHint to: "+e),t.contentHint=e,t.contentHint!==e&&this.logger.info("Invalid video track contentHint: "+e),this.logger.buriedLog({c:Fe.SET_VIDEO_CONTENT_HINT,v:"hint".concat(e)}))}},{key:"switchDevice",value:function(e,t){var i,r,n=this;if(this.screen)throw new ne({code:K.INVALID_OPERATION,message:"switch device is not supported in screen sharing"});if(!t||this.streamConfig.audioSource||this.streamConfig.videoSource)return Promise.reject();if("audio"===e&&this.microphoneId_===t||"video"===e&&this.cameraId_===t)return this.logger.warn("switch device is not supported same device"),Promise.reject("switch device is not supported same device");if(this.logger.info("switchDevice "+e+" to: "+t),"audio"===e&&this.microphoneId_!==t){if(!(i=this.getAudioTrack()))return this.microphoneId_=t,Promise.resolve();i&&i.stop(),this.microphoneId_=t,this.logger.buriedLog({c:Fe.SWITCH_DEVICE_AUDIO,v:"deviceId:".concat(t)})}if("video"===e&&this.cameraId_!==t){if(!(r=this.getVideoTrack()))return this.cameraId_=t,Promise.resolve();r&&r.stop(),this.cameraId_=t,this.logger.buriedLog({c:Fe.SWITCH_DEVICE_VIDEO,v:"deviceId:".concat(t)})}return new Promise((function(t,o){n.initAvStream({audio:"audio"===e,video:"video"===e,facingMode:n.streamConfig.facingMode,cameraId:n.cameraId_,microphoneId:n.microphoneId_,width:n.videoProfile.width,height:n.videoProfile.height,frameRate:n.videoProfile.frameRate,sampleRate:n.audioProfile.sampleRate,channelCount:n.audioProfile.channelCount}).then((function(o){"audio"===e&&(n.mediaStream.removeTrack(i),(i=o.getAudioTracks()[0])&&n.mediaStream.addTrack(i),i&&n.setHasAudio(!0),i&&n._emitter.emit("stream-switch-device",{streamId:n.streamId,type:e,track:i}),n.updateDeviceIdInUse("audio"),i.enabled=!n.getAudioMuted(),n.restartAudio()),"video"==e&&(n.mediaStream.removeTrack(r),(r=o.getVideoTracks()[0])&&n.mediaStream.addTrack(r),r&&n.setHasVideo(!0),r&&n._emitter.emit("stream-switch-device",{streamId:n.streamId,type:e,track:r}),n.updateDeviceIdInUse("video"),r.enabled=!n.getVideoMuted(),n.restartVideo()),t()})).catch((function(e){n.logger.onError({c:Fe.TOP_ERROR,v:K.SWITCH_DEVICE_FAILED}),o(new ne({code:K.SWITCH_DEVICE_FAILED,message:"init audio or video stream failed"})),n.logger.onError({c:Fe.TOP_ERROR,v:K.SWITCH_DEVICE_FAILED});var t=new ne({code:K.SWITCH_DEVICE_FAILED,message:e.message});o(t)}))}))}},{key:"updateStream",value:function(e){var t=this;if(this.screen||this.streamConfig.audioSource||this.streamConfig.videoSource)return Promise.reject();var i,r,n=e.audio,o=e.video,s=e.cameraId,a=e.microphoneId;return n&&(i=this.getAudioTrack()),o&&(r=this.getVideoTrack()),new Promise((function(e,c){t.initAvStream({audio:n,video:o,facingMode:t.streamConfig.facingMode,cameraId:s||"",microphoneId:a||"",width:t.videoProfile.width,height:t.videoProfile.height,frameRate:t.videoProfile.frameRate,sampleRate:t.audioProfile.sampleRate,channelCount:t.audioProfile.channelCount}).then((function(s){n&&(t.logger.info("updateStream audio"),i&&i.stop(),t.mediaStream.removeTrack(i),(i=s.getAudioTracks()[0])&&t.mediaStream.addTrack(i),i&&t.setHasAudio(!0),i&&t._emitter.emit("stream-switch-device",{streamId:t.streamId,type:"audio",track:i}),t.updateDeviceIdInUse("audio"),t.setAudioTrack(i)),o&&(t.logger.info("updateStream video"),r&&r.stop(),t.mediaStream.removeTrack(r),(r=s.getVideoTracks()[0])&&t.mediaStream.addTrack(r),r&&t.setHasVideo(!0),r&&t._emitter.emit("stream-switch-device",{streamId:t.streamId,type:"video",track:r}),t.updateDeviceIdInUse("video"),t.setVideoTrack(r)),e()})).catch((function(e){t.logger.warn("NotReadableError"===e.name?"getUserMedia NotReadableError observed":e.name,e.message),t.logger.onError({c:Fe.TOP_ERROR,v:K.DEVICE_AUTO_RECOVER_FAILED});var i=new ne({code:K.DEVICE_AUTO_RECOVER_FAILED,message:e});c(i),t._emitter.emit(G,i)}))}))}},{key:"getAudioTracks",value:function(){return this.mediaStream.getAudioTracks()}},{key:"getVideoTracks",value:function(){return this.mediaStream.getVideoTracks()}},{key:"isReplaceTrackAvailable",value:function(){return"RTCRtpSender"in window&&"replaceTrack"in window.RTCRtpSender.prototype}},{key:"supportPC",value:function(){return"RTCPeerConnection"in window&&"getSenders"in window.RTCPeerConnection.prototype}},{key:"listenForScreenSharingStopped",value:function(e){var t=this;e.addEventListener("ended",(function(e){t.logger.info("screen sharing was stopped because the video track is ended"),t.logger.buriedLog({c:Fe.ON_SCREEN_SHARING_STOPPED}),t._emitter.emit("screen-sharing-stopped")}),{once:!0})}},{key:"updateDeviceIdInUse",value:function(e){if(!this.mediaStream)return this.microphoneId_="",this.microphoneGroupId_="",this.cameraId_="",this.cameraGroupId_="",this.microphoneLabel_="",void(this.cameraLabel_="");for(var t=this.mediaStream.getTracks(),i=t.length,r=0;r<i;r++){var n=t[r].getSettings(),o=n.deviceId,s=n.groupId;if(e&&o){if(e===t[r].kind&&"audio"===t[r].kind){this.microphoneId_=o,this.microphoneGroupId_=s,this.microphoneLabel_=t[r].label;break}if(e===t[r].kind&&"video"===t[r].kind&&!this.screen){this.cameraId_=o,this.cameraGroupId_=s,this.cameraLabel_=t[r].label;break}}else o&&("audio"===t[r].kind?(this.microphoneId_=o,this.microphoneGroupId_=s,this.microphoneLabel_=t[r].label):"video"!==t[r].kind||this.screen||(this.cameraId_=o,this.cameraGroupId_=s,this.cameraLabel_=t[r].label))}var a=this.mediaStream.getAudioTracks(),c=this.mediaStream.getVideoTracks();a&&0===a.length&&(this.microphoneId_="",this.microphoneGroupId_="",this.microphoneLabel_=""),c&&0===c.length&&(this.cameraId_="",this.cameraGroupId_="",this.cameraLabel_=""),this.logger.info("update device id: microphoneId: ".concat(this.microphoneId_,",microphoneLabel:").concat(this.microphoneLabel_,", microphoneGroupId:").concat(this.microphoneGroupId_,",cameraId: ").concat(this.cameraId_,",cameraLabel:").concat(this.cameraLabel_,",cameraGroupId:").concat(this.cameraGroupId_))}},{key:"getDevicesInfoInUse",value:function(){return this.logger.buriedLog({c:Fe.GET_DEVICES_INFO_IN_USE,v:"microphone:".concat(this.microphoneId_,",camera:").concat(this.cameraId_)}),{camera:{deviceId:this.cameraId_,groupId:this.cameraGroupId_,label:this.cameraLabel_},microphone:{deviceId:this.microphoneId_,groupId:this.microphoneGroupId_,label:this.microphoneLabel_}}}},{key:"setPubState",value:function(e,t){"audio"===e?this.audioPubState=t:this.videoPubState=t}},{key:"getPubState",value:function(e){return"audio"===e?this.audioPubState:this.videoPubState}},{key:"onTrackAdd",value:function(e){this._emitter.on("stream-add-track",e)}},{key:"onTrackRemove",value:function(e){this._emitter.on("stream-remove-track",e)}},{key:"onSwitchDevice",value:function(e){this._emitter.on("stream-switch-device",e)}},{key:"onReplaceTrack",value:function(e){this._emitter.on("stream-replace-track",e)}},{key:"onTrackStopped",value:(t=P(U.mark((function e(t){var i,r,n,o,s=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.type,this.logger.info("onTrackStopped",r=t.reason,this.recoverCaptureCount_),"audio"!==i||"ended"!==r){e.next=11;break}if(!(this.recoverCaptureCount_<=10)){e.next=9;break}return e.next=6,Je();case 6:n=e.sent.findIndex((function(e){return e.deviceId===s.microphoneId_})),this.microphoneId_&&n>-1&&(this.logger.info("stat-local-audio-ended"),this.recoverCaptureCount_+=1,this.updateStream({audio:!0,video:!1,microphoneId:this.microphoneId_}));case 9:e.next=18;break;case 11:if("video"!==i||"ended"!==r){e.next=18;break}if(!(this.recoverCaptureCount_<=10)){e.next=18;break}return e.next=15,ze();case 15:o=e.sent.findIndex((function(e){return e.deviceId===s.cameraId_})),this.cameraId_&&o>-1&&(this.logger.info("stat-local-video-ended"),this.recoverCaptureCount_+=1,this.updateStream({audio:!1,video:!0,cameraId:this.cameraId_}));case 18:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})}])}();var ct,ut=function(e){q(i,Xe);var t=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,r=Z(e);if(t){var n=Z(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return Q(this,i)}}(i);function i(e,r){var n;return L(this,i),(n=t.call(this,e,r)).isRemote=!0,n.subscribed=!1,n.audio=!1,n.video=!1,n.subscriptionId=null,n.audioSubscriptionId=null,n.videoSubscriptionId=null,n.audioSubState=j.Create,n.videoSubState=j.Create,n.simulcastType=null,n}return x(i,[{key:"getUserSeq",value:function(){return this.userId}},{key:"setAudio",value:function(e){this.audio=e}},{key:"setVideo",value:function(e){this.video=e}},{key:"setAudioSubscriptionId",value:function(e){this.audioSubscriptionId=e}},{key:"setVideoSubscriptionId",value:function(e){this.videoSubscriptionId=e}},{key:"getStreamKind",value:function(e){return this.audioStreamId===this.videoStreamId?W.AudioVideo:this.audioStreamId===e?W.AudioOnly:this.videoStreamId===e?W.VideoOnly:void 0}},{key:"setSimulcastType",value:function(e){this.simulcastType=e}},{key:"getSimulcastType",value:function(){return this.simulcastType}},{key:"setSubState",value:function(e,t){"audio"===e?this.audioSubState=t:this.videoSubState=t}},{key:"getSubState",value:function(e){return"audio"===e?this.audioSubState:this.videoSubState}}]),i}();function dt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function lt(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?dt(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):dt(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}!function(e){e[e.Create=0]="Create",e[e.Publishing=1]="Publishing",e[e.Published=2]="Published",e[e.Unpublished=3]="Unpublished"}(ct||(ct={}));var ht=function(){function e(t){L(this,e),this.options=t,this.userId=t.userId,this.streamId=null,this.localStream=t.mediaStream,this.peerConnection=new RTCPeerConnection({bundlePolicy:"max-bundle",sdpSemantics:"unified-plan"}),this.logger=t.logger,this.xsigoClient=t.xsigoClient,this.roomId=t.roomId,this.state=ct.Create,this.transceiver=null,this._emitter=new N,this._interval=-1,this.audioBytesSentIs0Count=0,this.videoBytesSentIs0Count=0,this.recoverCaptureCount=0,this.times=2e3}var t,i;return x(e,[{key:"publish",value:function(){var e=this;return new Promise(function(){var t=P(U.mark((function t(i,r){var n,o,s;return U.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.state!==ct.Create&&(e.logger.warn("Stream already publishing or published"),r({message:"stream already publishing or published"})),e.logger.info("stream publishing"),e.state=ct.Publishing,t.next=6,e.createOffer(e.localStream);case 6:n=t.sent,e.logger.info("publishStream track",e.localStream.getTracks()),e.peerConnection.onconnectionstatechange=e.onConnectionstatechange.bind(e,"publish"),o="",s=[],e.peerConnection.onicecandidate=function(t){var a=t.candidate;e.logger.info("peerConnection publish ".concat(JSON.stringify(e.localStream.getTracks()[0].kind),"  IceCandidate data:\n ").concat((null==a?void 0:a.candidate)||"")),null!=a&&a.candidate&&(o=o+"a="+a.candidate+"\r\n"),s.push(a);var c=!1,u=window.setTimeout((function(){c=!0}),e.times);if(!a||c){window.clearTimeout(u),s[0]&&0!==s.length||r({code:K.CANDIDATE_COLLECT_FAILED,message:"candidate is null"}),s=[];var d=n.sdp;if(d.toLowerCase().includes("audio")&&!d.toLowerCase().includes("opus"))return e.logger.warn("=======publish offer========\n",d),r("opus not supported");if(d.toLowerCase().includes("video")&&!d.toLowerCase().includes("h264"))return e.logger.warn("=======publish offer========\n",d),r("H264 not supported");d.includes("a=candidate")||(d+=o);var l=e.buildPublishParams();l.params.offerSdp=d,e.logger.info("=======publish offer========\n",d),e.streamId=e.xsigoClient.publishStream(e.roomId,l),i(e.streamId)}},t.next=17;break;case 14:t.prev=14,t.t0=t.catch(0),r(t.t0);case 17:case"end":return t.stop()}}),t,null,[[0,14]])})));return function(e,i){return t.apply(this,arguments)}}())}},{key:"republish",value:function(){var e=this;return new Promise(function(){var t=P(U.mark((function t(i,r){var n,o;return U.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.close(),e.peerConnection=new RTCPeerConnection({bundlePolicy:"max-bundle",sdpSemantics:"unified-plan"}),e.state=ct.Publishing,t.next=5,e.createOffer(e.localStream);case 5:n=t.sent,e.peerConnection.onconnectionstatechange=e.onConnectionstatechange.bind(e,"republish"),o="",e.peerConnection.onicecandidate=function(t){var r=t.candidate;e.logger.info("peerConnection republish IceCandidate data:\n ".concat((null==r?void 0:r.candidate)||"")),null!=r&&r.candidate&&(o=o+"a="+r.candidate+"\r\n");var s=!1,a=window.setTimeout((function(){s=!0}),e.times);r&&!s||(window.clearTimeout(a),n.sdp.includes("a=candidate")||(n.sdp=n.sdp+o),e.logger.info("=======republish offer========\n",n.sdp),i(n))};case 9:case"end":return t.stop()}}),t)})));return function(e,i){return t.apply(this,arguments)}}())}},{key:"unpublish",value:function(e){var t=this;this.xsigoClient.unpublishStream(this.roomId,this.streamId,(function(i,r,n){1===i&&(t.state=ct.Unpublished,t.close()),e(i,r,n)}))}},{key:"updateSimulcast",value:function(e,t){this.xsigoClient.updateSimulcast(this.roomId,this.streamId,{simulcast:e},t)}},{key:"createOffer",value:function(e){var t=this;return new Promise(function(){var i=P(U.mark((function i(r,n){var o,s,a,c;return U.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,t.localStream.getTracks().forEach((function(i){var r=[];if("video"===i.kind){var n=t.options,o=n.isEnableSmallStream,s=n.smallStreamConfig,a=n.screen,c=i.getSettings();t.captureVideoWidth=c.width,t.captureVideoHeight=c.height,o&&!a&&(r.push({rid:"h",active:!0,scaleResolutionDownBy:1}),r.push({rid:"l",active:!0,scaleResolutionDownBy:t.captureVideoHeight/s.height,maxBitrate:1e3*s.bitrate}))}t.transceiver=t.peerConnection.addTransceiver(i.kind,{direction:"sendonly",sendEncodings:r}),t.peerConnection.addTrack(i,e),t.filterCodecs(i)})),i.next=4,t.peerConnection.createOffer();case 4:t.logger.info("======= pub old ========\n"+(o=i.sent).sdp),s=[],o.sdp.includes("video")&&((a=o.sdp.split("\r\n")).forEach((function(e){if(e.includes("a=rtpmap")&&!e.toLowerCase().includes("h264")){var t=e.indexOf(":")+1,i=e.indexOf(" ");s.push(e.slice(t,i))}})),s.length&&s.forEach((function(e){a=a.filter((function(t){return!(t.includes("a=rtpmap:"+e)||t.includes("a=fmtp:"+e)||t.includes("a=rtcp-fb:"+e))}))})),o.sdp=a.join("\r\n")),o.sdp.includes("audio")&&((c=o.sdp.split("\r\n")).forEach((function(e){if(e.includes("a=rtpmap")&&!e.toLowerCase().includes("opus")){var t=e.indexOf(":")+1,i=e.indexOf(" ");s.push(e.slice(t,i))}})),s.length&&s.forEach((function(e){c=c.filter((function(t){return!(t.includes("a=rtpmap:"+e)||t.includes("a=fmtp:"+e)||t.includes("a=rtcp-fb:"+e))}))})),o.sdp=c.join("\r\n")),r(o),t.logger.info("======= pub original ========\n"+o.sdp),t.peerConnection.setLocalDescription(o),i.next=19;break;case 14:throw i.prev=14,i.t0=i.catch(0),t.logger.onError({c:Fe.TOP_ERROR,v:K.CREATE_OFFER_FAILED},"code:".concat(K.CREATE_OFFER_FAILED,",create offer error!, ").concat(i.t0)),t.state=ct.Create,new ne({code:K.CREATE_OFFER_FAILED,message:"create offer error!,".concat(i.t0)});case 19:case"end":return i.stop()}}),i,null,[[0,14]])})));return function(e,t){return i.apply(this,arguments)}}())}},{key:"onConnectionstatechange",value:function(e){var t=this;["failed","connected"].includes(this.peerConnection.connectionState)&&this._emitter.emit("publish-ice-state",{state:this.peerConnection.connectionState,streamId:this.streamId}),this.logger.info("peerConnection ".concat(e," ICE State: ").concat(this.peerConnection.connectionState)),"connecting"===this.peerConnection.connectionState?-1===this._interval&&(this._interval=window.setInterval((function(){t.getRTCIceCandidatePairStats()}),this.times)):"connected"===this.peerConnection.connectionState?(this.localStream.getTracks().forEach((function(e){(t.peerConnection.getSenders()||[]).forEach((function(t){t.replaceTrack(e)}))})),clearInterval(this._interval)):clearInterval(this._interval)}},{key:"filterCodecs",value:function(e){if(RTCRtpSender.getCapabilities){var t=RTCRtpSender.getCapabilities(e.kind).codecs.filter((function(t){return"audio"===e.kind?-1!=t.mimeType.indexOf("opus"):-1!=t.mimeType.indexOf("H264")}));this.transceiver.setCodecPreferences&&"function"==typeof this.transceiver.setCodecPreferences&&this.transceiver.setCodecPreferences(t)}}},{key:"setRemoteDesc",value:function(e,t){var i=this;return new Promise((function(r,n){i.logger.info("=======publish answer========\n",e),i.state=ct.Published,i.streamId=t,i.peerConnection.setRemoteDescription({sdp:e,type:"answer"}).then((function(){i.setBandwidth(i.options.bitrate),r(!0)})).catch((function(e){i.logger.error("publish setRemoteDescription error",e),n(e)}))}))}},{key:"getPeerConnection",value:function(){return this.peerConnection}},{key:"close",value:function(){this.peerConnection&&(this.peerConnection.onicecandidate=null,this.peerConnection.onconnectionstatechange=null,this.peerConnection.close()),this.peerConnection=null,this.transceiver=null,this._interval&&clearInterval(this._interval),this.audioBytesSentIs0Count=0,this.videoBytesSentIs0Count=0,this.recoverCaptureCount=0,this.logger.info("close publish stream peerConnection,streamId",this.streamId)}},{key:"setBandwidth",value:function(e){var t=this,i=this.peerConnection.getSenders(),r=e.audio,n=e.video;i.forEach((function(e){var i;"video"===e.track.kind&&(i=n),"audio"===e.track.kind&&(i=r);var o=e.getParameters();o.encodings.length||(o.encodings=[{}]),o.encodings[0].maxBitrate=1e3*i,t.logger.info("encodings",JSON.stringify(o.encodings)),e.setParameters(o).then((function(){t.logger.info("".concat(e.track.kind," set bitrate to ").concat(1e3*i," success"))}),(function(i){t.logger.warn("".concat(e.track.kind," set bitrate error"),i)}))}))}},{key:"replaceMediaStreamTrack",value:function(e){var t=this;this.logger.info("replace mediaStream Track",e),this.peerConnection&&e&&(this.peerConnection.getSenders()||[]).forEach((function(i){if("audio"===e.kind&&i.track&&"audio"===i.track.kind){i.replaceTrack(e);var r=t.localStream.getAudioTracks()[0];r&&t.localStream.removeTrack(r),r&&t.localStream.addTrack(e)}if("video"===e.kind&&i.track&&"video"===i.track.kind){i.replaceTrack(e);var n=t.localStream.getVideoTracks()[0];n&&t.localStream.removeTrack(n),n&&t.localStream.addTrack(e)}}));var i="audio"===e.kind?this.localStream.getAudioTracks()[0]:this.localStream.getVideoTracks()[0];i&&this.localStream.removeTrack(i),i&&this.localStream.addTrack(e)}},{key:"buildPublishParams",value:function(){var e=this,t=this.options||{},i=t.hasAudio,r=t.hasVideo,n=t.screen,o={streamType:Ee.ForwardStream,streamKind:i&&r?_e.AudioVideo:i?_e.AudioOnly:r?_e.VideoOnly:_e.Invalid,params:{offerSdp:"",audioInfo:{source:n?Ie.ScreenShare:Ie.Microphone,muted:t.audioMuted,floor:!0},videoInfo:{source:n?Te.ScreenShare:Te.Camera,muted:t.videoMuted,floor:!0}},cb:function(t,i,r){1===t?e.setRemoteDesc(r.answer_sdp,r.streamId).then((function(){e.options.onPublish&&e.options.onPublish(t,i,r)})).catch((function(t){e.options.onPublish&&e.options.onPublish(0,t,r)})):e.options.onPublish&&e.options.onPublish(t,i,r)},updateCb:function(){}},s=this.options,a=s.smallStreamConfig,c=[];if(s.isEnableSmallStream&&!n){if(this.logger.info("publish width height",this.captureVideoWidth,this.captureVideoHeight),this.captureVideoWidth>0&&this.captureVideoHeight>0){var u={type:Re.SmallStream,maxWidth:a.width,maxHeight:a.height};c.push({type:Re.BigStream,maxWidth:this.captureVideoWidth,maxHeight:this.captureVideoHeight}),c.push(u)}o.params.videoInfo.simulcast=c}return o}},{key:"getTransportStats",value:(i=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,i){if(t.peerConnection){var r,n=(t.peerConnection.getSenders()||[])[0];n&&n.getStats().then((function(i){r=t.getSenderStats({send:i,mediaType:n.track.kind}),e(r)}),(function(e){t.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_TRANSPORT_STATA},"Get transport stats error, ".concat(e)),i(e.message)}))}})));case 1:case"end":return e.stop()}}),e)}))),function(){return i.apply(this,arguments)})},{key:"getLocalStats",value:(t=P(U.mark((function e(t){var i=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,r){if(i.peerConnection){var n,o=(i.peerConnection.getSenders()||[]).find((function(e){return e.track.kind===t}));o&&o.getStats().then((function(r){n=i.getSenderStats({send:r,mediaType:t}),e(n)})).catch((function(e){r(e.message)}))}})));case 1:case"end":return e.stop()}}),e)}))),function(e){return t.apply(this,arguments)})},{key:"getSenderStats",value:function(e){var t=this,i={audio:{bytesSent:0,packetsSent:0,retransmittedPacketsSent:0,audioLevel:0},video:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,retransmittedPacketsSent:0,framesPerSecond:0,rid:"h"},smallVideo:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,retransmittedPacketsSent:0,framesPerSecond:0,rid:"l"},rtt:0,timestamp:0};return e.send.forEach((function(r){if("outbound-rtp"===r.type)if(i.timestamp=r.timestamp,"video"===e.mediaType&&"l"===r.rid){if(0===r.bytesSent)return;i.smallVideo=lt(lt({},i.smallVideo),{},{bytesSent:r.bytesSent,packetsSent:r.packetsSent,framesEncoded:r.framesEncoded,retransmittedPacketsSent:r.retransmittedPacketsSent||0,framesPerSecond:r.framesPerSecond||0,frameWidth:r.frameWidth||0,frameHeight:r.frameHeight||0,framesSent:r.framesSent,rid:r.rid||"l"})}else if("video"===e.mediaType){if(0===r.bytesSent)return;i.video=lt(lt({},i.video),{},{bytesSent:r.bytesSent,packetsSent:r.packetsSent,framesEncoded:r.framesEncoded,retransmittedPacketsSent:r.retransmittedPacketsSent||0,rid:r.rid||"h"}),void 0!==r.framesPerSecond&&(i.video.framesPerSecond=r.framesPerSecond),void 0!==r.frameWidth&&(i.video.frameWidth=r.frameWidth),void 0!==r.frameHeight&&(i.video.frameHeight=r.frameHeight),void 0!==r.framesSent&&(i.video.framesSent=r.framesSent)}else"audio"===e.mediaType&&(i.audio=lt(lt({},i.audio),{},{bytesSent:r.bytesSent,packetsSent:r.packetsSent,retransmittedPacketsSent:r.retransmittedPacketsSent||0}));else if("candidate-pair"===r.type)"number"==typeof r.currentRoundTripTime&&(i.rtt=1e3*r.currentRoundTripTime);else if("track"===r.type){if(void 0!==r.frameWidth){var n=t.localStream.getVideoTracks();n.length&&n[0].id===r.trackIdentifier&&(i.video.frameWidth=r.frameWidth,i.video.frameHeight=r.frameHeight,i.video.framesSent=r.framesSent)}}else if("media-source"===r.type)if("video"===r.kind){var o=t.localStream.getVideoTracks();o.length&&o[0].id===r.trackIdentifier&&void 0!==r.framesPerSecond&&(i.video.framesPerSecond=r.framesPerSecond)}else"audio"===r.kind&&(i.audio.audioLevel=r.audioLevel||0)})),i}},{key:"onPublishPeerConnectionFailed",value:function(e){this._emitter.on("publish-ice-state",e)}},{key:"getRTCIceCandidatePairStats",value:function(){var e=this;this.peerConnection&&this.peerConnection.getStats().then((function(t){t.forEach((function(t){"candidate-pair"===t.type&&e.logger.warn("publish RTCIceCandidatePairStats",JSON.stringify(t,null,4))}))}))}},{key:"updateBytesSentIs0Count",value:function(e){var t=this;if("audio"===e){var i,r=this.localStream.getAudioTracks();r.length&&"connected"===(null===(i=this.peerConnection)||void 0===i?void 0:i.connectionState)&&(this.audioBytesSentIs0Count+=1,this.audioBytesSentIs0Count>=4&&this.recoverCaptureCount<=5&&(this.recoverCaptureCount+=1,this.audioBytesSentIs0Count=0,r.forEach((function(e){(t.peerConnection.getSenders()||[]).forEach((function(t){"audio"===t.track.kind&&t.replaceTrack(e)}))})),this.logger.info("replace the track because the audio bytes sent is 0,recover count:",this.recoverCaptureCount)))}else if("video"===e){var n,o=this.localStream.getVideoTracks();o.length&&"connected"===(null===(n=this.peerConnection)||void 0===n?void 0:n.connectionState)&&(this.videoBytesSentIs0Count+=1,this.videoBytesSentIs0Count>=4&&this.recoverCaptureCount<=5&&(this.recoverCaptureCount+=1,this.videoBytesSentIs0Count=0,o.forEach((function(e){(t.peerConnection.getSenders()||[]).forEach((function(t){"video"===t.track.kind&&t.replaceTrack(e)}))})),this.logger.info("replace the track because the video bytes sent is 0,recover count:",this.recoverCaptureCount)))}}}]),e}();function pt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function ft(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):pt(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var mt,gt=function(){function e(t){L(this,e),this.subscribedStreams=new Map,this.subscriptedOptions=new Map,this.subscriptedState=new Map,this.logger=t}return x(e,[{key:"addSubscriptionRecord",value:function(e,t){this.subscribedStreams.set(e,t)}},{key:"setSubscriptionOpts",value:function(e,t){this.logger.debug("set subscribe options:",t),this.subscriptedOptions.set(e,t)}},{key:"getSubscriptionOpts",value:function(e){return this.subscriptedOptions.get(e)||{audio:!0,video:!0,small:!1}}},{key:"updateSubscriptedState",value:function(e,t){var i=ft(ft({},this.getSubscriptedState(e)),t);this.subscriptedState.set(e,i),this.logger.info("-----\x3e update subscribe state <----------",e,JSON.stringify(i))}},{key:"getSubscriptedState",value:function(e){return this.subscriptedState.get(e)||{audio:!1,video:!1,small:!1}}},{key:"needSubscribeKind",value:function(e){var t=this.subscriptedState.get(e)||{audio:!1,video:!1},i=this.subscriptedOptions.get(e)||{audio:!1,video:!1};return this.logger.debug("subscribe state",t),this.logger.debug("subscribe options:",i),i.audio&&!t.audio&&i.video&&!t.video?W.AudioVideo:i.audio&&!t.audio?W.AudioOnly:i.video&&!t.video?W.VideoOnly:void 0}},{key:"reset",value:function(e){if(e)return this.subscriptedState.delete(e),this.subscribedStreams.delete(e),void this.subscriptedOptions.delete(e);this.subscriptedState.clear(),this.subscribedStreams.clear(),this.subscriptedOptions.clear()}}]),e}(),vt=M((function(e){var t=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),(t+=null!=e["network-id"]?" network-id %d":"%v")+(null!=e["network-cost"]?" network-cost %d":"%v")}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",(t+=null!=e.rateNumerator?" rate=%s":"")+(null!=e.rateDenominator?"/%s":"")}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(t).forEach((function(e){t[e].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}))})),bt=M((function(e,t){var i=function(e){return String(Number(e))===e?Number(e):e},r=function(e,t,r){var n=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:n&&!t[e.name]&&(t[e.name]={});var o=e.push?{}:n?t[e.name]:t;!function(e,t,r,n){if(n&&!r)t[n]=i(e[1]);else for(var o=0;o<r.length;o+=1)null!=e[o+1]&&(t[r[o]]=i(e[o+1]))}(r.match(e.reg),o,e.names,e.name),e.push&&t[e.push].push(o)},n=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},i=[],o=t;return e.split(/(\r\n|\r|\n)/).filter(n).forEach((function(e){var t=e[0],n=e.slice(2);"m"===t&&(i.push({rtp:[],fmtp:[]}),o=i[i.length-1]);for(var s=0;s<(vt[t]||[]).length;s+=1){var a=vt[t][s];if(a.reg.test(n))return r(a,o,n)}})),t.media=i,t};var o=function(e,t){var r=t.split(/=(.+)/,2);return 2===r.length?e[r[0]]=i(r[1]):1===r.length&&t.length>1&&(e[r[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],r=e.split(" ").map(i),n=0;n<r.length;n+=3)t.push({component:r[n],ip:r[n+1],port:r[n+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})}))},t.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var t,r=!1;return"~"!==e[0]?t=i(e):(t=i(e.substring(1,e.length)),r=!0),{scid:t,paused:r}}))}))}})),yt=/%[sdv%]/g,St=function(e){var t=1,i=arguments,r=i.length;return e.replace(yt,(function(e){if(t>=r)return e;var n=i[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(n);case"%d":return Number(n);case"%v":return""}}))},Et=function(e,t,i){var r=[e+"="+(t.format instanceof Function?t.format(t.push?i:i[t.name]):t.format)];if(t.names)for(var n=0;n<t.names.length;n+=1)r.push(t.name?i[t.name][t.names[n]]:i[t.names[n]]);else r.push(i[t.name]);return St.apply(null,r)},_t=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Ct=["i","c","b","a"],It=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var i=t.innerOrder||Ct,r=[];return(t.outerOrder||_t).forEach((function(t){vt[t].forEach((function(i){i.name in e&&null!=e[i.name]?r.push(Et(t,i,e)):i.push in e&&null!=e[i.push]&&e[i.push].forEach((function(e){r.push(Et(t,i,e))}))}))})),e.media.forEach((function(e){r.push(Et("m",vt.m[0],e)),i.forEach((function(t){vt[t].forEach((function(i){i.name in e&&null!=e[i.name]?r.push(Et(t,i,e)):i.push in e&&null!=e[i.push]&&e[i.push].forEach((function(e){r.push(Et(t,i,e))}))}))}))})),r.join("\r\n")+"\r\n"},Tt=bt.parse,Rt=bt.parsePayloads;function kt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function Ot(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?kt(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):kt(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function wt(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return At(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?At(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}function At(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}!function(e){e[e.Create=0]="Create",e[e.Subscribing=1]="Subscribing",e[e.Subscribed=2]="Subscribed",e[e.Unsubscribed=3]="Unsubscribed"}(mt||(mt={}));var Pt,Lt=function(){function e(t){L(this,e),this.options=t,this.userId=t.userId,this.logger=t.logger,this.xsigoClient=t.xsigoClient||null,this.roomId=t.roomId,this.peerConnection=new RTCPeerConnection({sdpSemantics:"unified-plan"}),this.peerConnection.onnegotiationneeded=this.onNegotiationNeeded.bind(this),this.peerConnection.ontrack=this.onTrack.bind(this),this.state=mt.Create,this.subscriptionId=null,this._emitter=new N,this._interval=-1,this.times=2e3,this.isAlphaChannels=!1}var t;return x(e,[{key:"getState",value:function(){return this.state}},{key:"subscribe",value:function(){var e=this;return new Promise((function(t,i){e.logger.info("start subscribing to the stream"),e.state=mt.Subscribing,e.addTransceiver(),e.createOffer(),e.peerConnection.onconnectionstatechange=e.onConnectionstatechange.bind(e,"subscribe");var r="";e.peerConnection.onicecandidate=function(n){var o=n.candidate;e.logger.info("peercConnection subscribe IceCandidate data:\n ".concat((null==o?void 0:o.candidate)||"")),null!=o&&o.candidate&&(r=r+"a="+o.candidate+"\r\n");var s=!1,a=window.setTimeout((function(){s=!0}),e.times);if(!o||s){window.clearTimeout(a);var c=e.peerConnection.pendingLocalDescription.sdp;if(c.toLocaleLowerCase().includes("video")&&!c.toLowerCase().includes("h264"))e.logger.warn("=======subscribe offer========\n",c),i("H264 not supported");else{c.includes("a=candidate")||(c+=r);var u=e.buildSubscribeParams();u.params.offerSdp=c,e.logger.info("=======subscribe offer========\n",c),e.subscriptionId=e.xsigoClient.subscribeStream(e.roomId,u),t(e.subscriptionId)}}}}))}},{key:"resubscribe",value:function(){var e=this;return new Promise((function(t,i){e.logger.info("resubscribe stream",e.subscriptionId),e.close(),e.peerConnection=new RTCPeerConnection({sdpSemantics:"unified-plan"}),e.state=mt.Subscribing,e.addTransceiver(),e.createOffer(),e.peerConnection.onconnectionstatechange=e.onConnectionstatechange.bind(e,"resubscribe"),e.peerConnection.ontrack=e.onTrack.bind(e);var r="";e.peerConnection.onicecandidate=function(i){var n=i.candidate;e.logger.info("peercConnection resubscribe IceCandidate data:\n ".concat((null==n?void 0:n.candidate)||"")),null!=n&&n.candidate&&(r=r+"a="+n.candidate+"\r\n");var o=!1,s=window.setTimeout((function(){o=!0}),e.times);if(!n||o){window.clearTimeout(s);var a=e.peerConnection.pendingLocalDescription;a.sdp.includes("a=candidate")||(a.sdp=a.sdp+r),e.logger.info("=======resubscribe offer========\n",a.sdp),t(a)}}}))}},{key:"unsubscribe",value:function(e){var t=this;this.logger.info("unsubscribe subscriptionId",this.subscriptionId),this.xsigoClient.unsubscribeStream(this.roomId,this.subscriptionId,(function(i,r,n){1===i&&(t.state=mt.Unsubscribed,t.close()),e(i,r,n)}))}},{key:"switchSimulcast",value:function(e,t){this.xsigoClient.switchSimulcast(this.roomId,this.subscriptionId,{type:e},t)}},{key:"setRemoteDescription",value:function(e){var t=this;return new Promise((function(i,r){t.logger.info("=======subscribe answer========\n"+e),t.isAlphaChannels=e.includes("a=xrtc-alpha"),t.peerConnection.setRemoteDescription({sdp:e,type:"answer"}).then((function(){t.state=mt.Subscribed,i(!0)})).catch((function(e){t.logger.error("subscribe setRemoteDescription error",e),r(e)}))}))}},{key:"onConnectionstatechange",value:function(e){var t=this;["failed","connected"].includes(this.peerConnection.connectionState)&&this._emitter.emit("subscribe-ice-state",{state:this.peerConnection.connectionState,subscriptionId:this.subscriptionId}),this.logger.info("peerConnection ".concat(e," ICE State: ").concat(this.peerConnection.connectionState)),"connecting"===this.peerConnection.connectionState?-1===this._interval&&(this._interval=window.setInterval((function(){t.getRTCIceCandidatePairStats()}),this.times)):clearInterval(this._interval)}},{key:"addTransceiver",value:function(){if(this.options.hasAudio){var e=this.peerConnection.addTransceiver("audio",{direction:"recvonly"});if(RTCRtpSender.getCapabilities){var t=RTCRtpSender.getCapabilities("audio");e.setCodecPreferences&&"function"==typeof e.setCodecPreferences&&e.setCodecPreferences(t.codecs)}}if(this.options.hasVideo){var i=this.peerConnection.addTransceiver("video",{direction:"recvonly"});if(RTCRtpSender.getCapabilities){var r=RTCRtpSender.getCapabilities("video");i.setCodecPreferences&&"function"==typeof i.setCodecPreferences&&i.setCodecPreferences(r.codecs)}}}},{key:"createOffer",value:function(){var e=this;this.peerConnection.createOffer().then(this.onGotOffer.bind(this)).catch((function(t){e.logger.error("create offer error",t),e.state=mt.Create}))}},{key:"onGotOffer",value:function(e){var t=this;this.logger.info("=======sub old ========\n"+e.sdp);var i,r=Tt(e.sdp),n=wt(r.media);try{for(n.s();!(i=n.n()).done;){var o,s=i.value,a=wt(Rt(s.payloads));try{for(a.s();!(o=a.n()).done;){var c=o.value;s.rtcpFb=s.rtcpFb?[].concat(k(s.rtcpFb),[{payload:c,type:"rrtr"}]):[{payload:c,type:"rrtr"}]}}catch(e){a.e(e)}finally{a.f()}}}catch(e){n.e(e)}finally{n.f()}var u={sdp:It(r),type:"offer"};this.peerConnection.setLocalDescription(u).then((function(){t.logger.info("Set local description success",t.subscriptionId)})).catch((function(e){t.logger.error("Set local description failure",e)}))}},{key:"onNegotiationNeeded",value:function(){this.logger.info("onNegotiationneeded--")}},{key:"onTrack",value:function(e){this.logger.debug("on track return");var t=this.options||{},i=t.hasAudio;t.audioStreamId||t.videoStreamId?this.options.onRemoteStream(e.streams[0],e.track,i&&t.hasVideo?_e.AudioVideo:i?_e.AudioOnly:_e.VideoOnly,this.isAlphaChannels):this.logger.info("not audio or video")}},{key:"getPeerConnection",value:function(){return this.peerConnection}},{key:"close",value:function(){this.peerConnection&&(this.peerConnection.onicecandidate=null,this.peerConnection.onnegotiationneeded=null,this.peerConnection.onconnectionstatechange=null,this.peerConnection.ontrack=null,this.peerConnection.close()),this.peerConnection=null,this._interval&&clearInterval(this._interval),this.logger.info("close subscribe stream peerConnection subscriptionId",this.subscriptionId)}},{key:"buildSubscribeParams",value:function(){var e=this,t=this.options||{},i=t.hasAudio,r=t.hasVideo,n=t.simulcast,o={publisherUserId:t.publisherUserId,streamId:i?t.audioStreamId:t.videoStreamId,streamKind:i&&r?_e.AudioVideo:i?_e.AudioOnly:_e.VideoOnly,params:{offerSdp:"",hasAudio:i,hasVideo:r,type:(null==n?void 0:n.length)>0&&n[0].type},cb:function(t,i,r){1===t?e.setRemoteDescription(r.answer_sdp).then((function(){e.options.onSubscribe&&e.options.onSubscribe(t,i,r)})).catch((function(t){e.options.onSubscribe&&e.options.onSubscribe(0,t,r)})):e.options.onSubscribe&&e.options.onSubscribe(t,i,r)},updateCb:function(){}};return t.small&&r&&((n||[]).find((function(e){return e.type===Re.SmallStream}))?o.params.type=Re.SmallStream:this.logger.warn("does not publish small stream")),o}},{key:"getTransportStats",value:(t=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,i){if(t.peerConnection){var r=(t.peerConnection.getReceivers()||[])[0];r&&r.getStats().then((function(i){var n=t.getReceiverStats({send:i,mediaType:r.track.kind});e(n.rtt)})).catch((function(e){i(e)}))}})));case 1:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})},{key:"getRemoteAudioOrVideoStats",value:function(e){var t=this;return new Promise((function(i,r){if(t.peerConnection){var n=t.peerConnection.getReceivers().find((function(t){return t.track.kind===e}));n&&n.getStats().then((function(r){var n=t.getReceiverStats({send:r,mediaType:e});i(n)})).catch((function(e){r(e)}))}}))}},{key:"getReceiverStats",value:function(e){var t={audio:{bytesReceived:0,packetsReceived:0,packetsLost:0,nackCount:0,audioLevel:0},video:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesDecoded:0,frameWidth:0,frameHeight:0,framesPerSecond:0,nackCount:0},rtt:0,timestamp:0};return e.send.forEach((function(i){if("inbound-rtp"===i.type)if(t.timestamp=i.timestamp,"audio"===e.mediaType)t.audio=Ot(Ot({},t.audio),{},{bytesReceived:i.bytesReceived,packetsReceived:i.packetsReceived,packetsLost:i.packetsLost}),void 0!==i.nackCount&&(t.audio.nackCount=i.nackCount),void 0!==i.audioLevel&&(t.audio.audioLevel=i.audioLevel);else{if(0===i.bytesReceived)return;t.video=Ot(Ot({},t.video),{},{bytesReceived:i.bytesReceived,packetsReceived:i.packetsReceived,packetsLost:i.packetsLost,framesDecoded:i.framesDecoded,framesPerSecond:i.framesPerSecond||0,nackCount:i.nackCount}),void 0!==i.frameWidth&&(t.video.frameWidth=i.frameWidth),void 0!==i.frameHeight&&(t.video.frameHeight=i.frameHeight)}else"track"===i.type?void 0!==i.frameWidth?t.video=Ot(Ot({},t.video),{},{frameWidth:i.frameWidth,frameHeight:i.frameHeight}):void 0!==i.audioLevel&&(t.audio.audioLevel=i.audioLevel||0):"candidate-pair"===i.type&&"number"==typeof i.currentRoundTripTime&&(t.rtt=1e3*i.currentRoundTripTime)})),t}},{key:"onSubscribePeerConnectionFailed",value:function(e){this._emitter.on("subscribe-ice-state",e)}},{key:"getRTCIceCandidatePairStats",value:function(){var e=this;this.peerConnection&&this.peerConnection.getStats().then((function(t){t.forEach((function(t){"candidate-pair"===t.type&&e.logger.warn("subscribe RTCIceCandidatePairStats",JSON.stringify(t,null,4))}))}))}}]),e}();function Dt(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function xt(e){var t,i=new Array,r=function(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return Dt(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Dt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}(e);try{for(r.s();!(t=r.n()).done;){var n=t.value,o={type:Ne(n.rid),maxWidth:n.maxWidth,maxHeight:n.maxHeight};i.push(o)}}catch(e){r.e(e)}finally{r.f()}return i}function Mt(e){var t={userId:e.userId,streamId:e.streamId,type:Pe(e.type)};return e.info&&(t.info={},e.info.audio&&(t.info.audio={source:De(e.info.audio.source),muted:e.info.audio.muted,floor:e.info.audio.floor}),e.info.video&&(t.info.video={source:Me(e.info.video.source),muted:e.info.video.muted,floor:e.info.video.floor},e.info.video.simulcast&&(t.info.video.simulcast=xt(e.info.video.simulcast)))),t}function Ut(e){var t={userId:e.userId,streamId:e.streamId,type:Pe(e.data.type)};return e.data.media.info&&(t.info={},e.data.media.info.audio&&(t.info.audio={source:De(e.data.media.info.audio.source),muted:e.data.media.info.audio.muted,floor:e.data.media.info.audio.floor}),e.data.media.info.video&&(t.info.video={source:Me(e.data.media.info.video.source),muted:e.data.media.info.video.muted,floor:e.data.media.info.video.floor},e.data.media.info.video.simulcast&&(t.info.video.simulcast=xt(e.data.media.info.video.simulcast)))),t}!function(e){e[e.Created=0]="Created",e[e.Entering=1]="Entering",e[e.EnterFailed=2]="EnterFailed",e[e.EnterTimeout=3]="EnterTimeout",e[e.Entered=4]="Entered",e[e.Exiting=5]="Exiting",e[e.ExitFailed=6]="ExitFailed",e[e.ExitTimeout=7]="ExitTimeout",e[e.Exited=8]="Exited",e[e.Destroyed=9]="Destroyed",e[e.StateMax=10]="StateMax"}(Pt||(Pt={}));var Nt,Vt=["Created","Entering","EnterFailed","EnterTimeout","Entered","Exiting","ExitFailed","ExitTimeout","Exited","Destroyed"],Bt=function(){function e(t){L(this,e),this.currentState=Pt.Created,this.stateTransformTable=new Array,this.logger=t,this.initStateTransformTable()}return x(e,[{key:"setState",value:function(e){return this.checkStateChange(this.currentState,e)?(this.logger.info("RoomState : state change from "+Vt[this.currentState]+" to "+Vt[e]),this.currentState=e,!0):(this.logger.error("RoomState : INVALID state change from "+Vt[this.currentState]+" to "+Vt[e]),!1)}},{key:"state",value:function(){return this.currentState}},{key:"checkStateChange",value:function(e,t){return this.stateTransformTable[e][t]}},{key:"initStateTransformTable",value:function(){for(var e=Pt.Created;e<Pt.StateMax;e++){this.stateTransformTable[e]=new Array;for(var t=Pt.Created;t<Pt.StateMax;t++)this.stateTransformTable[e][t]=!1}this.stateTransformTable[Pt.Created][Pt.Entering]=!0,this.stateTransformTable[Pt.Created][Pt.Destroyed]=!0,this.stateTransformTable[Pt.Entering][Pt.Entered]=!0,this.stateTransformTable[Pt.Entering][Pt.EnterFailed]=!0,this.stateTransformTable[Pt.Entering][Pt.EnterTimeout]=!0,this.stateTransformTable[Pt.Entering][Pt.Destroyed]=!0,this.stateTransformTable[Pt.EnterFailed][Pt.Destroyed]=!0,this.stateTransformTable[Pt.EnterTimeout][Pt.Destroyed]=!0,this.stateTransformTable[Pt.Entered][Pt.Exiting]=!0,this.stateTransformTable[Pt.Entered][Pt.Destroyed]=!0,this.stateTransformTable[Pt.Exiting][Pt.Exited]=!0,this.stateTransformTable[Pt.Exiting][Pt.ExitTimeout]=!0,this.stateTransformTable[Pt.Exiting][Pt.Destroyed]=!0,this.stateTransformTable[Pt.Exited][Pt.Destroyed]=!0}}]),e}();!function(e){e[e.New=0]="New",e[e.Logining=1]="Logining",e[e.LoginFailed=2]="LoginFailed",e[e.LoginTimeout=3]="LoginTimeout",e[e.Logined=4]="Logined",e[e.Relogining=5]="Relogining",e[e.Relogined=6]="Relogined",e[e.Logouting=7]="Logouting",e[e.LogoutTimeout=8]="LogoutTimeout",e[e.Logouted=9]="Logouted",e[e.Destroyed=10]="Destroyed",e[e.StateMax=11]="StateMax"}(Nt||(Nt={}));var Ft,jt,Wt=["New","Logining","LoginFailed","LoginTimeout","Logined","Relogining","Relogined","Logouting","LogoutTimeout","Logouted","Destroy"],Ht=function(){function e(t){L(this,e),this.currentState=Nt.New,this.stateTransformTable=new Array,this.logger=t,this.initStateTransformTable()}return x(e,[{key:"setState",value:function(e){return this.checkStateChange(this.currentState,e)?(this.logger.info("Login : state change from "+Wt[this.currentState]+" to "+Wt[e]),this.currentState=e,!0):(this.logger.error("Login : INVALID state change from "+Wt[this.currentState]+" to "+Wt[e]),!1)}},{key:"state",value:function(){return this.currentState}},{key:"checkStateChange",value:function(e,t){return this.stateTransformTable[e][t]}},{key:"initStateTransformTable",value:function(){for(var e=Nt.New;e<Nt.StateMax;e++){this.stateTransformTable[e]=new Array;for(var t=Nt.New;t<Nt.StateMax;t++)this.stateTransformTable[e][t]=!1}this.stateTransformTable[Nt.New][Nt.Logining]=!0,this.stateTransformTable[Nt.New][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Logining][Nt.Logined]=!0,this.stateTransformTable[Nt.Logining][Nt.LoginFailed]=!0,this.stateTransformTable[Nt.Logining][Nt.LoginTimeout]=!0,this.stateTransformTable[Nt.Logining][Nt.Destroyed]=!0,this.stateTransformTable[Nt.LoginFailed][Nt.Destroyed]=!0,this.stateTransformTable[Nt.LoginTimeout][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Logined][Nt.Logouting]=!0,this.stateTransformTable[Nt.Logined][Nt.Relogining]=!0,this.stateTransformTable[Nt.Logined][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Relogining][Nt.Relogining]=!0,this.stateTransformTable[Nt.Relogining][Nt.Relogined]=!0,this.stateTransformTable[Nt.Relogining][Nt.Logined]=!0,this.stateTransformTable[Nt.Relogining][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Relogined][Nt.Relogining]=!0,this.stateTransformTable[Nt.Relogined][Nt.Logouting]=!0,this.stateTransformTable[Nt.Relogined][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Logouting][Nt.Logouted]=!0,this.stateTransformTable[Nt.Logouting][Nt.LogoutTimeout]=!0,this.stateTransformTable[Nt.Logouting][Nt.Destroyed]=!0,this.stateTransformTable[Nt.Logouted][Nt.Destroyed]=!0}}]),e}();function Gt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function zt(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?Gt(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Gt(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}!function(e){e[e.LoginSuccess=0]="LoginSuccess",e[e.LoginTimeout=1]="LoginTimeout",e[e.LoginFailed=2]="LoginFailed"}(Ft||(Ft={})),function(e){e[e.LogoutSuccess=0]="LogoutSuccess",e[e.LogoutTimeout=1]="LogoutTimeout",e[e.LogoutFailed=2]="LogoutFailed"}(jt||(jt={}));var Jt,Kt=function(){function e(t){L(this,e),this.options=t,this.state=new Ht(t.logger),this.connectionStatus=be.New,this.timeout=1e4}return x(e,[{key:"login",value:function(){var e=this;if(this.state.setState(Nt.Logining)){this.options.logger.info("Login room: ".concat(this.options.roomId));var t=null;this.buildLoginReuqest();var i={method:"login",params:this.loginRequestParams};t||(t=setTimeout((function(){e.options.logger.info("login timeout: ".concat(e.options.roomId)),e.state.setState(Nt.LoginTimeout)&&(t&&clearTimeout(t),t=null,e.options.loginCb&&e.options.loginCb(Ft.LoginTimeout,null,"login timeout"))}),this.timeout)),this.options.rpcClient.sendRequest(i,(function(i){if(e.state.setState(Nt.Logined)&&(t&&clearTimeout(t),t=null,e.options.loginCb)){var r=i.result.room,n={room:zt(zt({},r),{},{roomUniqueId:r.roomUniqueId||r.roomId,participants:r.participants||[],streams:r.streams||[]})};e.options.loginCb(Ft.LoginSuccess,n)}}),(function(i){e.state.setState(Nt.LoginFailed)&&(t&&clearTimeout(t),t=null,e.options.loginCb&&e.options.loginCb(Ft.LoginFailed,null,i.error.message))}))||this.options.logger.error("Json Rpc Client send login request error")}}},{key:"logout",value:function(){var e=this;if(this.state.setState(Nt.Logouting)){this.options.logger.info("Logout room: ".concat(this.options.roomId));var t=null;this.options.rpcClient.sendRequest({method:"logout"},(function(i){e.state.setState(Nt.Logouted)&&(t&&clearTimeout(t),t=null,e.options.logoutCb&&e.options.logoutCb(jt.LogoutSuccess))}),(function(i){e.state.setState(Nt.LoginFailed)&&(t&&clearTimeout(t),t=null,e.options.logoutCb&&e.options.logoutCb(jt.LogoutFailed,i.error.message))}))||this.options.logger.error("Json Rpc Client send loginout request error"),t||(t=setTimeout((function(){e.options.logger.info("logout timeout: ".concat(e.options.roomId)),e.state.setState(Nt.LogoutTimeout)&&(t&&clearTimeout(t),t=null,e.options.logoutCb&&e.options.logoutCb(jt.LogoutTimeout,"logout timeout"))}),this.timeout))}}},{key:"relogin",value:function(){var e=this;this.state.setState(Nt.Relogining)&&(this.buildLoginReuqest(),this.options.logger.info("Relogin room: ".concat(this.options.roomId)),this.options.rpcClient.sendRequest({method:"login",params:this.loginRequestParams},(function(t){if(e.state.setState(Nt.Relogined)&&e.options.reloginCb){var i=t.result.room,r={room:zt(zt({},i),{},{roomUniqueId:i.roomUniqueId||i.roomId,participants:i.participants||[],streams:i.streams||[]})};e.options.reloginCb(!0,i.sessionTimeout,r)}}),(function(t){e.options.logger.info("relogining failed")}))||this.options.logger.error("Json Rpc Client send relogin request error"))}},{key:"updatePermission",value:function(e){this.options.permission=e}},{key:"onConnectionLost",value:function(){this.connectionStatus=be.ConnectionLost}},{key:"onConnectionRecovery",value:function(){this.connectionStatus=be.ConnectionRecovery,this.relogin()}},{key:"buildLoginReuqest",value:function(){this.loginRequestParams={appId:this.options.appId,userId:this.options.userId,type:this.options.userType,roomId:this.options.roomId,previousRoomId:this.options.previousRoomId,permission:this.options.permission,userAgent:this.options.userAgent,userData:this.options.userData,protocol:"1.0"}}}]),e}();!function(e){e[e.Create=0]="Create",e[e.Publishing=1]="Publishing",e[e.Published=2]="Published",e[e.Republishing=3]="Republishing",e[e.Republished=4]="Republished",e[e.Unpublishing=5]="Unpublishing",e[e.Unpublished=6]="Unpublished",e[e.Destroyed=7]="Destroyed",e[e.StateMax=8]="StateMax"}(Jt||(Jt={}));var Yt=["Create","Publishing","Published","Republishing","Republished","Unpublishing","Unpublished","Destroy"],qt=function(){function e(t){L(this,e),this.currentState=Jt.Create,this.stateTransformTable=new Array,this.logger=t,this.initStateTransformTable()}return x(e,[{key:"setState",value:function(e){return this.checkStateChange(this.currentState,e)?(this.logger.info("PublicationState : state change from "+Yt[this.currentState]+" to "+Yt[e]),this.currentState=e,!0):(this.logger.error("PublicationState : INVALID state change from"+Yt[this.currentState]+" to "+Yt[e]),!1)}},{key:"state",value:function(){return this.currentState}},{key:"checkStateChange",value:function(e,t){return this.stateTransformTable[e][t]}},{key:"initStateTransformTable",value:function(){for(var e=Jt.Create;e<Jt.StateMax;e++){this.stateTransformTable[e]=new Array;for(var t=Jt.Create;t<Jt.StateMax;t++)this.stateTransformTable[e][t]=!1}this.stateTransformTable[Jt.Create][Jt.Publishing]=!0,this.stateTransformTable[Jt.Create][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Publishing][Jt.Published]=!0,this.stateTransformTable[Jt.Publishing][Jt.Unpublishing]=!0,this.stateTransformTable[Jt.Publishing][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Publishing][Jt.Republishing]=!0,this.stateTransformTable[Jt.Published][Jt.Unpublishing]=!0,this.stateTransformTable[Jt.Published][Jt.Republishing]=!0,this.stateTransformTable[Jt.Published][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Republishing][Jt.Republishing]=!0,this.stateTransformTable[Jt.Republishing][Jt.Republished]=!0,this.stateTransformTable[Jt.Republishing][Jt.Unpublishing]=!0,this.stateTransformTable[Jt.Republishing][Jt.Published]=!0,this.stateTransformTable[Jt.Republishing][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Republished][Jt.Republishing]=!0,this.stateTransformTable[Jt.Republished][Jt.Unpublishing]=!0,this.stateTransformTable[Jt.Republished][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Unpublishing][Jt.Unpublished]=!0,this.stateTransformTable[Jt.Unpublishing][Jt.Destroyed]=!0,this.stateTransformTable[Jt.Unpublished][Jt.Destroyed]=!0}}]),e}();function Xt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function $t(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Xt(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var Qt=function(){function e(t){L(this,e),this.options=t,this.state=new qt(t.logger),this.connectionStatus=be.ConnectionConnected,t.stream.info&&(t.stream.info.audio&&t.stream.info.video?(this.streamKind=_e.AudioVideo,this.audioMuteWanted=this.options.stream.info.audio.muted,this.simulcastWanted=this.options.stream.info.video.simulcast,this.videoMuteWanted=this.options.stream.info.video.muted):t.stream.info.audio?(this.streamKind=_e.AudioOnly,this.audioMuteWanted=this.options.stream.info.audio.muted):t.stream.info.video?(this.streamKind=_e.VideoOnly,this.simulcastWanted=this.options.stream.info.video.simulcast,this.videoMuteWanted=this.options.stream.info.video.muted):this.options.logger.warn("now not support mix"))}return x(e,[{key:"publish",value:function(){if(this.options.logger.info("Publish stream: ".concat(this.options.stream.streamId)),this.state.setState(Jt.Publishing)){var e=this.options.rpcClient.getWsState().state;["CONNECTED","RECOVERY"].includes(e)&&this.doPublish()}}},{key:"unpublish",value:function(e){if(this.options.logger.info("Unpublish stream: "+this.options.stream.streamId),this.state.setState(Jt.Unpublishing)){this.unpublishCb=e;var t=this.options.rpcClient.getWsState().state;["CONNECTED","RECOVERY"].includes(t)?this.doUnpublish(e):this.options.logger.info("websocketState: ".concat(t,", unpublish has been cached"))}}},{key:"updateSimulcast",value:function(e,t){this.simulcastWanted=e,this.updateSimulcastCb=t;var i=this.options.rpcClient.getWsState().state,r=this.state.state();["CONNECTED","RECOVERY"].includes(i)?r===Jt.Republished||r===Jt.Published?this.doUpdateSimulcast(t):this.options.logger.info("publicationState: ".concat(i,",updateSimulcast has been cached")):this.options.logger.info("websocketState: ".concat(i,",updateSimulcast has been cached"))}},{key:"muteAudio",value:function(e,t,i){this.audioMuteWanted=!0,this.muteAudioOption={userId:e,cb:t,userData:i};var r=this.options.rpcClient.getWsState().state,n=this.state.state();["CONNECTED","RECOVERY"].includes(r)?n===Jt.Republished||n===Jt.Published?this.control(e,"mute",t,i):this.options.logger.info("publicationState: ".concat(r,",muteAudio has been cached")):this.options.logger.info("websocketState: ".concat(r,",muteAudio has been cached"))}},{key:"muteVideo",value:function(e,t,i){this.videoMuteWanted=!0,this.muteVideoOption={userId:e,cb:t,userData:i};var r=this.options.rpcClient.getWsState().state,n=this.state.state();["CONNECTED","RECOVERY"].includes(r)?n===Jt.Republished||n===Jt.Published?this.control(e,"vmute",t,i):this.options.logger.info("publicationState: ".concat(r,",muteVideo has been cached")):this.options.logger.info("websocketState: ".concat(r,",muteVideo has been cached"))}},{key:"unmuteAudio",value:function(e,t,i){this.audioMuteWanted=!1,this.unmuteAudioOption={userId:e,cb:t,userData:i};var r=this.options.rpcClient.getWsState().state,n=this.state.state();["CONNECTED","RECOVERY"].includes(r)?n===Jt.Republished||n===Jt.Published?this.control(e,"unmute",t,i):this.options.logger.info("publicationState: ".concat(r,",unmuteAudio has been cached")):this.options.logger.info("websocketState: ".concat(r,",unmuteAudio has been cached"))}},{key:"unmuteVideo",value:function(e,t,i){this.videoMuteWanted=!1,this.unmuteVideoOPtion={userId:e,cb:t,userData:i};var r=this.options.rpcClient.getWsState().state,n=this.state.state();["CONNECTED","RECOVERY"].includes(r)?n===Jt.Republished||n===Jt.Published?this.control(e,"unvmute",t,i):this.options.logger.info("publicationState: ".concat(r,",unmuteVideo has been cached")):this.options.logger.info("websocketState: ".concat(r,",unmuteVideo has been cached"))}},{key:"onConnectionLost",value:function(){}},{key:"onConnectionRecovery",value:function(e,t){if(this.options.logger.info("onConnectionRecovery streamId",this.options.stream.streamId,"sessionTimeout",e,"sdp",t),this.state.state()===Jt.Unpublishing)return this.doUnpublish(this.unpublishCb);e?t&&(this.options.offerSdp=t,this.republish()):this.recoveryOperations()}},{key:"republish",value:function(){this.options.logger.info("start republish: "+this.options.stream.streamId),this.state.setState(Jt.Republishing)&&this.doPublish()}},{key:"recoveryOperations",value:function(){var e,t,i,r,n,o;if(this.audioMuteWanted!==(null===(e=this.options.stream.info)||void 0===e||null===(t=e.audio)||void 0===t?void 0:t.muted))if(this.audioMuteWanted){var s=this.muteAudioOption;this.control(s.userId,"mute",s.cb,s.userData)}else{var a=this.unmuteAudioOption;this.control(a.userId,"unmute",a.cb,a.userData)}if(this.videoMuteWanted!==(null===(i=this.options.stream.info)||void 0===i||null===(r=i.video)||void 0===r?void 0:r.muted))if(this.videoMuteWanted){var c=this.muteVideoOption;this.control(c.userId,"vmute",c.cb,c.userData)}else{var u=this.unmuteVideoOPtion;this.control(u.userId,"unvmute",u.cb,u.userData)}this.simulcastWanted&&null!==(n=this.options.stream.info)&&void 0!==n&&null!==(o=n.video)&&void 0!==o&&o.simulcast&&JSON.stringify(this.simulcastWanted)!==JSON.stringify(this.options.stream.info.video.simulcast)&&this.doUpdateSimulcast(this.updateSimulcastCb)}},{key:"buildPublishParams",value:function(){if(this.streamKind===_e.AudioVideo){var e={streamId:this.options.stream.streamId,type:Ae(this.options.stream.type),media:{audio:{source:Le(this.options.stream.info.audio.source),muted:this.audioMuteWanted,floor:this.options.stream.info.audio.floor},video:{source:xe(this.options.stream.info.video.source),muted:this.videoMuteWanted,floor:this.options.stream.info.video.floor}},sdp:this.options.offerSdp};return this.simulcastWanted&&this.simulcastWanted.length&&(e.media.video.simulcast=this.simulcastWanted.map((function(e){return $t($t({},e),{},{rid:Ue(e.type)})}))),e}if(this.streamKind===_e.AudioOnly)return{streamId:this.options.stream.streamId,type:Ae(this.options.stream.type),media:{audio:{source:Le(this.options.stream.info.audio.source),muted:this.audioMuteWanted,floor:this.options.stream.info.audio.floor}},sdp:this.options.offerSdp};if(this.streamKind===_e.VideoOnly){var t={streamId:this.options.stream.streamId,type:Ae(this.options.stream.type),media:{video:{source:xe(this.options.stream.info.video.source),muted:this.videoMuteWanted,floor:this.options.stream.info.video.floor}},sdp:this.options.offerSdp};return this.simulcastWanted&&this.simulcastWanted.length&&(t.media.video.simulcast=this.simulcastWanted.map((function(e){return $t($t({},e),{},{rid:Ue(e.type)})}))),t}}},{key:"doPublish",value:function(){var e=this;try{var t=this.buildPublishParams();null!==t&&(this.options.rpcClient.sendRequest({method:"publish",params:t},(function(t){if((e.state.state()!==Jt.Publishing||e.state.setState(Jt.Published))&&(e.state.state()!==Jt.Republishing||e.state.setState(Jt.Republished))){if(e.options.publishCb){var i=t.result;e.options.publishCb(fe.Success,null,{roomId:e.options.roomId,streamId:i.streamId,answer_sdp:i.sdp})}e.recoveryOperations()}}),(function(t){e.options.logger.info("publish stream failed"),e.options.publishCb&&e.options.publishCb(fe.Failed,t.error.message,{roomId:e.options.roomId,streamId:e.options.stream.streamId})}))||this.options.logger.error("Json Rpc Client send publish request error"))}catch(e){this.options.publishCb&&this.options.publishCb(fe.Failed,e,{roomId:this.options.roomId}),this.options.logger.error(e)}}},{key:"doUnpublish",value:function(e){var t=this;this.options.rpcClient.sendRequest({method:"unpublish",params:{id:this.options.stream.streamId}},(function(){t.state.setState(Jt.Unpublished)&&e&&(e(fe.Success,null,{roomId:t.options.roomId}),t.unpublishCb=null)}),(function(i){e&&(t.options.logger.info("unpublish stream failed"),e(fe.Failed,i.error.message,{roomId:t.options.roomId}),t.unpublishCb=null)}))||this.options.logger.error("Json Rpc Client send unpublish request error")}},{key:"doUpdateSimulcast",value:function(e){var t=this,i={method:"publishControl",params:{type:"simulcast",streamId:this.options.stream.streamId,simulcast:this.simulcastWanted.map((function(e){return{rid:Ue(e.type),maxWidth:e.maxWidth,maxHeight:e.maxHeight}}))}};this.options.rpcClient.sendRequest(i,(function(i){t.options.logger.info("updateSimulcast ".concat(t.options.stream.streamId," success")),t.options.stream.info.video.simulcast=t.simulcastWanted,e&&e(fe.Success,null,t.options.roomId)}),(function(i){t.options.logger.info("updateSimulcast ".concat(t.options.stream.streamId," failed")),e&&e(fe.Failed,i.error.message,null)}))||this.options.logger.error("Json Rpc Client send unsubscribe request error")}},{key:"control",value:function(e,t,i,r){var n=this,o={method:"controlCommand",params:{type:t,streamId:this.options.stream.streamId,member:e,userData:r}};this.options.logger.info("control command with",t),this.options.rpcClient.sendRequest(o,(function(e){switch(n.options.logger.info("control command with ".concat(t," success")),t){case"mute":case"unmute":n.options.stream.info.audio.muted=n.audioMuteWanted;break;case"vmute":case"unvmute":n.options.stream.info.video.muted=n.videoMuteWanted}i&&i(fe.Success,null,{roomId:n.options.roomId})}),(function(e){n.options.logger.info("control command with ".concat(t," failed")),i&&i(fe.Failed,e.error.message,{roomId:n.options.roomId})}))||this.options.logger.error("Json Rpc Client send ControlCommand request error")}}]),e}(),Zt=function(){function e(t){L(this,e),this.options=t,this.publiccation=new Qt({roomId:this.options.roomId,stream:this.options.stream,offerSdp:this.options.offerSdp,rpcClient:this.options.rpcClient,logger:this.options.logger,publishCb:this.options.publishCb,publishUpdateCb:this.options.publishUpdateCb})}return x(e,[{key:"publish",value:function(){this.publiccation.publish()}},{key:"unpublish",value:function(e){this.publiccation.unpublish(e)}},{key:"updateSimulcast",value:function(e,t){this.publiccation.updateSimulcast(e,t)}},{key:"muteAudio",value:function(e,t,i){this.publiccation.muteAudio(e,t,i)}},{key:"muteVideo",value:function(e,t,i){this.publiccation.muteVideo(e,t,i)}},{key:"unmuteAudio",value:function(e,t,i){this.publiccation.unmuteAudio(e,t,i)}},{key:"unmuteVideo",value:function(e,t,i){this.publiccation.unmuteVideo(e,t,i)}},{key:"onConnectionLost",value:function(){this.publiccation.onConnectionLost()}},{key:"onConnectionRecovery",value:function(e,t){this.options.logger.info("localStreams onConnectionRecovery"),this.publiccation.onConnectionRecovery(e,t)}}]),e}();function ei(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var ti,ii=function(){function e(t){L(this,e),this.options=t,this.permissionWanted=t.permission,this.permissionCb=null,this.localStreams=new Map}return x(e,[{key:"getUserId",value:function(){return this.options.userId}},{key:"switchPermission",value:function(e,t){this.permissionWanted=e,this.permissionCb=t;var i=this.options.rpcClient.getWsState().state;["CONNECTED","RECOVERY"].includes(i)?this.doSwitchPermission(t):this.options.logger.info("websocketState: ".concat(i,",the operation has been cached"))}},{key:"publishStream",value:function(e,t,i,r,n,o){var s={roomId:this.options.roomId,stream:{userId:this.options.userId,streamId:e,type:t,info:{}},offerSdp:r.offerSdp,rpcClient:this.options.rpcClient,logger:this.options.logger,publishCb:n,publishUpdateCb:o};i===_e.AudioVideo&&(s.stream.info={audio:r.audioInfo,video:r.videoInfo}),i===_e.AudioOnly&&(s.stream.info.audio=r.audioInfo),i===_e.VideoOnly&&(s.stream.info.video=r.videoInfo);var a=new Zt(s);a.publish(),this.localStreams.set(e,a)}},{key:"unpublishStream",value:function(e,t){var i=this;this.localStreams.has(e)&&this.localStreams.get(e).unpublish((function(r,n,o){r===fe.Success&&i.localStreams.delete(e),t&&t(r,n,o)}))}},{key:"updateSimulcast",value:function(e,t,i){this.localStreams.has(e)&&this.localStreams.get(e).updateSimulcast(t.simulcast,i)}},{key:"muteAudio",value:function(e,t,i){this.localStreams.has(e)&&this.localStreams.get(e).muteAudio(this.options.userId,t,i)}},{key:"muteVideo",value:function(e,t,i){this.localStreams.has(e)&&this.localStreams.get(e).muteVideo(this.options.userId,t,i)}},{key:"unmuteAudio",value:function(e,t,i){this.localStreams.has(e)&&this.localStreams.get(e).unmuteAudio(this.options.userId,t,i)}},{key:"unmuteVideo",value:function(e,t,i){this.localStreams.has(e)&&this.localStreams.get(e).unmuteVideo(this.options.userId,t,i)}},{key:"onConnectionLost",value:function(){}},{key:"onConnectionRecovery",value:function(e,t){if(this.options.logger.info("localUser onConnectionRecovery",e),e){var i,r=function(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return ei(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ei(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}(this.localStreams);try{for(r.s();!(i=r.n()).done;){var n=w(i.value,2),o=n[0],s=n[1];this.options.logger.info("localUser onConnectionRecovery",o,this.localStreams,t),s.onConnectionRecovery(e,null==t?void 0:t.get(o))}}catch(e){r.e(e)}finally{r.f()}JSON.stringify(this.permissionWanted)!==JSON.stringify(this.options.permission)&&this.doSwitchPermission(this.permissionCb)}}},{key:"doSwitchPermission",value:function(e){var t=this;this.options.rpcClient.sendRequest({method:"switchPermission",params:{permission:this.permissionWanted}},(function(i){t.options.logger.info("switch permission success"),t.options.permission=t.permissionWanted,e&&e(fe.Success,null,t.options.roomId)}),(function(i){t.options.logger.info("switch permission failed"),e&&e(fe.Failed,i.error.message,t.options.roomId)}))||this.options.logger.error("Json Rpc Client send switch permission request error")}}]),e}();!function(e){e[e.Create=0]="Create",e[e.Subscribing=1]="Subscribing",e[e.Subscribed=2]="Subscribed",e[e.Resubscribing=3]="Resubscribing",e[e.Resubscribed=4]="Resubscribed",e[e.Unsubscribing=5]="Unsubscribing",e[e.Unsubscribed=6]="Unsubscribed",e[e.Destroyed=7]="Destroyed",e[e.StateMax=8]="StateMax"}(ti||(ti={}));var ri=["Create","Subscribing","Subscribed","Resubscribing","Resubscribed","Unsubscribing","Unsubscribed","Destroy"],ni=function(){function e(t){L(this,e),this.currentState=ti.Create,this.stateTransformTable=new Array,this.logger=t,this.initStateTransformTable()}return x(e,[{key:"setState",value:function(e){return this.checkStateChange(this.currentState,e)?(this.logger.info("SubscriptionState : state change from "+ri[this.currentState]+" to "+ri[e]),this.currentState=e,!0):(this.logger.error("SubscriptionState : INVALID state change from"+ri[this.currentState]+" to "+ri[e]),!1)}},{key:"state",value:function(){return this.currentState}},{key:"checkStateChange",value:function(e,t){return this.stateTransformTable[e][t]}},{key:"initStateTransformTable",value:function(){for(var e=ti.Create;e<ti.StateMax;e++){this.stateTransformTable[e]=new Array;for(var t=ti.Create;t<ti.StateMax;t++)this.stateTransformTable[e][t]=!1}this.stateTransformTable[ti.Create][ti.Subscribing]=!0,this.stateTransformTable[ti.Create][ti.Destroyed]=!0,this.stateTransformTable[ti.Subscribing][ti.Subscribed]=!0,this.stateTransformTable[ti.Subscribing][ti.Unsubscribing]=!0,this.stateTransformTable[ti.Subscribing][ti.Destroyed]=!0,this.stateTransformTable[ti.Subscribing][ti.Resubscribing]=!0,this.stateTransformTable[ti.Subscribed][ti.Unsubscribing]=!0,this.stateTransformTable[ti.Subscribed][ti.Resubscribing]=!0,this.stateTransformTable[ti.Subscribed][ti.Destroyed]=!0,this.stateTransformTable[ti.Resubscribing][ti.Resubscribing]=!0,this.stateTransformTable[ti.Resubscribing][ti.Resubscribed]=!0,this.stateTransformTable[ti.Resubscribing][ti.Unsubscribing]=!0,this.stateTransformTable[ti.Resubscribing][ti.Subscribed]=!0,this.stateTransformTable[ti.Resubscribing][ti.Destroyed]=!0,this.stateTransformTable[ti.Resubscribed][ti.Resubscribing]=!0,this.stateTransformTable[ti.Resubscribed][ti.Unsubscribing]=!0,this.stateTransformTable[ti.Resubscribed][ti.Destroyed]=!0,this.stateTransformTable[ti.Unsubscribing][ti.Unsubscribed]=!0,this.stateTransformTable[ti.Unsubscribing][ti.Destroyed]=!0,this.stateTransformTable[ti.Unsubscribed][ti.Destroyed]=!0}}]),e}(),oi=function(){function e(t){L(this,e),this.options=t,this.state=new ni(t.logger),this.connectionStatus=be.ConnectionConnected,this.options.rid&&(this.ridWanted=this.options.rid),this.switchSimulcastCb=null,this.unsubscribeCb=null}return x(e,[{key:"subscribe",value:function(){if(this.options.logger.info("start subscribe: ".concat(this.options.subscriptionId)),this.state.setState(ti.Subscribing)){var e=this.options.rpcClient.getWsState().state;["CONNECTED","RECOVERY"].includes(e)&&this.doSubscribe()}}},{key:"unsubscribe",value:function(e){if(this.options.logger.info("unsubscribe: ".concat(this.options.subscriptionId)),this.state.setState(ti.Unsubscribing)){this.unsubscribeCb=e;var t=this.options.rpcClient.getWsState().state;["CONNECTED","RECOVERY"].includes(t)?this.doUnsubscribe(this.unsubscribeCb):this.options.logger.info("websocketState: ".concat(t,",unsubscribe has been cached"))}}},{key:"switchSimulcast",value:function(e,t){if(this.ridWanted!==e){this.ridWanted=e,this.switchSimulcastCb=t;var i=this.options.rpcClient.getWsState().state,r=this.state.state();["CONNECTED","RECOVERY"].includes(i)?r===ti.Resubscribed||r===ti.Subscribed?this.doSwitchSimulcast(t):this.options.logger.info("publicationState: ".concat(i,",switchSimulcast has been cached")):this.options.logger.info("websocketState: ".concat(i,",switchSimulcast has been cached"))}else this.options.logger.info("can not switch the same simulcast")}},{key:"onConnectionLost",value:function(){}},{key:"onConnectionRecovery",value:function(e,t){if(this.options.logger.info("onConnectionRecovery subscriptionId",this.options.subscriptionId,"sessionTimeout",e,"sdp",t),this.state.state()===ti.Unsubscribing)return this.doUnsubscribe(this.unsubscribeCb);e?t&&(this.options.offerSdp=t,this.resubscribe()):this.recoveryOperations()}},{key:"resubscribe",value:function(){this.options.logger.info("start resubscribe: "+this.options.subscriptionId),this.state.setState(ti.Resubscribing)&&this.doSubscribe()}},{key:"recoveryOperations",value:function(){this.ridWanted&&this.ridWanted!==this.options.rid&&this.doSwitchSimulcast(this.switchSimulcastCb)}},{key:"doSubscribe",value:function(){var e=this;try{var t={userId:this.options.userId,subscriptionId:this.options.subscriptionId,media:{audio:{has:this.options.subAudio},video:{has:this.options.subVideo}},sdp:this.options.offerSdp};this.options.subAudio&&(t.media.audio.streamId=this.options.audioStreamId),this.options.subVideo&&(t.media.video.streamId=this.options.videoStreamId,t.media.video.rid=this.ridWanted),this.options.rpcClient.sendRequest({method:"subscribe",params:t},(function(t){if((e.state.state()!==ti.Subscribing||e.state.setState(ti.Subscribed))&&(e.state.state()!==ti.Resubscribing||e.state.setState(ti.Resubscribed))){e.options.logger.info("subscribe ".concat(e.options.subscriptionId," success"));var i=t.result;e.options.subscribeCb&&e.options.subscribeCb(fe.Success,null,{roomId:e.options.roomId,subscriptionId:i.subscriptionId,answer_sdp:i.sdp}),e.recoveryOperations()}}),(function(t){e.options.logger.info("subscribe ".concat(e.options.subscriptionId," failed")),e.options.subscribeCb&&e.options.subscribeCb(fe.Failed,t.error.message,{roomId:e.options.roomId,subscriptionId:e.options.subscriptionId})}))||this.options.logger.error("Json Rpc Client send subscribe request error")}catch(e){this.options.subscribeCb&&this.options.subscribeCb(fe.Failed,e,null),this.options.logger.error(e)}}},{key:"doSwitchSimulcast",value:function(e){var t=this;this.options.rpcClient.sendRequest({method:"subscribeControl",params:{subscriptionId:this.options.subscriptionId,type:"simulcast",rid:this.ridWanted}},(function(i){t.options.logger.info("switchSimulcast ".concat(t.options.subscriptionId," success")),t.options.rid=t.ridWanted,e&&e(fe.Success,null,t.options.roomId)}),(function(i){t.options.logger.info("switchSimulcast ".concat(t.options.subscriptionId," failed")),e&&e(fe.Failed,i.error.message,null)}))||this.options.logger.error("Json Rpc Client send unsubscribe request error")}},{key:"doUnsubscribe",value:function(e){var t=this;this.options.rpcClient.sendRequest({method:"unsubscribe",params:{id:this.options.subscriptionId}},(function(i){t.state.setState(ti.Unsubscribed)&&(t.options.logger.info("unsubscribe ".concat(t.options.subscriptionId," success")),e&&(e(fe.Success,null,{roomId:t.options.roomId}),t.unsubscribeCb=null))}),(function(i){t.options.logger.info("unsubscribe ".concat(t.options.subscriptionId," failed")),e&&(e(fe.Failed,i.error.message,null),t.unsubscribeCb=null)}))||this.options.logger.error("Json Rpc Client send unsubscribe request error")}}]),e}();function si(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var ai=function(){function e(t){L(this,e),this.options=t,this.subscriptions=new Map}return x(e,[{key:"subscribe",value:function(e,t,i,r,n,o,s){var a=new oi({roomId:this.options.roomId,userId:this.options.stream.userId,subscriptionId:e,offerSdp:n,subAudio:t,audioStreamId:this.options.stream.streamId,subVideo:i,videoStreamId:this.options.stream.streamId,rid:r,rpcClient:this.options.rpcClient,logger:this.options.logger,subscribeCb:o,subscribeUpdateCb:s});a.subscribe(),this.options.logger.info("remoteStream subscribe subscriptionId",e),this.subscriptions.set(e,a)}},{key:"unsubscribe",value:function(e,t){var i=this;this.subscriptions.has(e)&&this.subscriptions.get(e).unsubscribe((function(r,n,o){r===fe.Success&&(i.options.logger.info("remoteStream unsubscribe subscriptionId",e),i.subscriptions.delete(e)),t&&t(r,n,o)}))}},{key:"updateSimulcast",value:function(e){this.options.stream.info.video.simulcast=e}},{key:"updateLiveStatus",value:function(e){e.audio&&(this.options.stream.info.audio.muted=e.audio.muted),e.video&&(this.options.stream.info.video.muted=e.video.muted)}},{key:"switchSimulcast",value:function(e,t,i){this.subscriptions.has(e)&&this.subscriptions.get(e).switchSimulcast(t,i)}},{key:"onConnectionLost",value:function(){var e,t=function(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return si(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?si(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}(this.subscriptions.values());try{for(t.s();!(e=t.n()).done;)e.value.onConnectionLost()}catch(e){t.e(e)}finally{t.f()}}},{key:"onConnectionRecovery",value:function(e,t,i){this.options.logger.info("remoteStream onConnectionRecovery subscriptionId",t),this.subscriptions.has(t)&&this.subscriptions.get(t).onConnectionRecovery(e,i)}}]),e}();function ci(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return ui(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ui(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}function ui(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var di=function(){function e(t){L(this,e),this.options=t,this.remoteStreams=new Map,this.streamIdArray=new Array,this.subPubIdMap=new Map}return x(e,[{key:"addStream",value:function(e){var t=e.streamId;this.remoteStreams.set(t,new ai({roomId:this.options.roomId,stream:e,rpcClient:this.options.rpcClient,logger:this.options.logger})),this.streamIdArray.push(t)}},{key:"deleteStream",value:function(e){if(this.remoteStreams.has(e)){this.remoteStreams.delete(e);var t=this.streamIdArray.indexOf(e);-1!=t&&this.streamIdArray.splice(t,1);var i,r=null,n=ci(this.subPubIdMap);try{for(n.s();!(i=n.n()).done;){var o=w(i.value,2);o[1]===e&&(r=o[0])}}catch(e){n.e(e)}finally{n.f()}r&&this.subPubIdMap.delete(r)}}},{key:"updateStreamSimulcast",value:function(e,t){this.remoteStreams.has(e)&&this.remoteStreams.get(e).updateSimulcast(t)}},{key:"updateStreamStatus",value:function(e,t){this.remoteStreams.has(e)&&this.remoteStreams.get(e).updateLiveStatus(t)}},{key:"subscribeStream",value:function(e,t,i,r,n,o){this.options.logger.info("remote user subscribe stream",this.remoteStreams.has(t)),this.remoteStreams.has(t)&&(this.remoteStreams.get(t).subscribe(e,r.hasAudio,r.hasVideo,Ue(null==r?void 0:r.type),r.offerSdp,n,o),this.options.logger.info("remoteUser subscribeStream subscriptionId",e),this.subPubIdMap.set(e,t))}},{key:"unsubscribeStream",value:function(e,t){var i=this,r=this.subPubIdMap.get(e);r&&this.remoteStreams.has(r)&&this.remoteStreams.get(r).unsubscribe(e,(function(r,n,o){r===fe.Success&&i.subPubIdMap.delete(e),t&&t(r,n,o)}))}},{key:"switchSimulcast",value:function(e,t,i){var r=this.subPubIdMap.get(e);r&&this.remoteStreams.has(r)&&this.remoteStreams.get(r).switchSimulcast(e,Ue(t.type),i)}},{key:"getAllStreamId",value:function(){return this.streamIdArray}},{key:"onConnectionLost",value:function(){}},{key:"onConnectionRecovery",value:function(e,t){this.options.logger.info("remoteUser onConnectionRecovery",e);var i,r=ci(this.subPubIdMap);try{for(r.s();!(i=r.n()).done;){var n=w(i.value,2),o=n[0],s=n[1];this.options.logger.info("remoteUser onConnectionRecovery subscriptionId",o,"streamId",s),this.remoteStreams.has(s)&&this.remoteStreams.get(s).onConnectionRecovery(e,o,t.get(o))}}catch(e){r.e(e)}finally{r.f()}}}]),e}(),li=function(){function e(t,i){L(this,e),this.options=t,this.userId=t.userId,this.logger=i,this.wsSocket=null,this.pendingRequests=new Array,this.successCallbacks=new Map,this.errorCallbacks=new Map,this.requestTypes=new Map,this.retryTimerId=0,this.retryCount=0,this.maxRetryCount=30,this.currentId=1,this.times=6e4,this.timer=null,this.state="DISCONNECTED",this.prevState="DISCONNECTED",this.autoReconnected=!0,this.lockReconnect=!1,this.heartCheck=this.initHeartCheck()}return x(e,[{key:"sendRequest",value:function(e,t,i){if(!e)return!1;e.jsonrpc="2.0",e.id="".concat(this.userId,"_").concat(Date.now(),"_").concat(this.currentId++);var r=this;t||(t=function(e){r.logger.debug("success: "+JSON.stringify(e))}),i||(i=function(e){r.logger.debug("error: "+JSON.stringify(e))});var n=JSON.stringify(e);return!!this.wsSocket&&(this.wsSocket.readyState<1?this.pendingRequests.push(n):(r.logger.info("send message: \n"+JSON.stringify(JSON.parse(n),null,4)),this.sendMessage(n)),this.successCallbacks.set(e.id,t),this.errorCallbacks.set(e.id,i),this.requestTypes.set(e.id,e.method),!0)}},{key:"socketReady",value:function(){return this.logger.info("wsSocket readyState",this.wsSocket&&this.wsSocket.readyState),!(null==this.wsSocket||this.wsSocket.readyState>1)}},{key:"connect",value:function(){if(!this.options.wsUrl&&0===this.options.wsUrl.length)return this.logger.error("Websocket url is empty!"),!1;if(this.retryTimerId&&window.clearTimeout(this.retryTimerId),!this.socketReady()){this.prevState=this.state,this.state="CONNECTING",this.options.onWsStateChange(this.prevState,this.state,this.retryCount);var e=this.getWsUrl();this.wsSocket=new WebSocket(e),this.wsSocket&&(this.wsSocket.onmessage=this.onWsMessage.bind(this),this.wsSocket.onclose=this.onWsClose.bind(this),this.wsSocket.onerror=this.onWsError.bind(this),this.wsSocket.onopen=this.onConnect.bind(this))}return!!this.wsSocket}},{key:"close",value:function(){this.socketReady()&&(this.autoReconnected=!1,this.pendingRequests=[],this.wsSocket.close(),this.resetWs(),this.timer&&clearTimeout(this.timer),this.retryTimerId&&clearTimeout(this.retryTimerId),this.heartCheck.reset(),this.heartCheck=null,this.logger.info("close websocket"))}},{key:"onConnect",value:function(){var e;for(this.heartCheck.start(),this.prevState=this.state,this.retryTimerId?(window.clearTimeout(this.retryTimerId),this.retryTimerId=null,this.state="RECOVERY"):this.state="CONNECTED",this.retryCount=0,this.options.onWsStateChange(this.prevState,this.state,this.retryCount);e=this.pendingRequests.shift();)this.logger.info("send message: \n"+e),this.sendMessage(e)}},{key:"onWsMessage",value:function(e){var t;if(this.heartCheck&&this.heartCheck.serverTimeoutObj&&(clearTimeout(this.heartCheck.serverTimeoutObj),this.heartCheck.serverTimeoutObj=null),this.timer&&clearTimeout(this.timer),this.timer=null,"object"!==X(t=JSON.parse(e.data))||"pong"!==t.method){if(this.logger.info("格式化消息: \n"+JSON.stringify(t,null,4)),"object"===X(t)&&"jsonrpc"in t&&"2.0"===t.jsonrpc){var i=t.id,r=this.successCallbacks.get(i),n=this.errorCallbacks.get(i);if("result"in t&&r)return r({jsonrpc:"2.0",id:t.id,result:t.result}),this.successCallbacks.delete(i),void this.errorCallbacks.delete(i);if("error"in t&&n){var o={jsonrpc:"2.0",id:t.id,error:t.error};return this.logger.error("信令返回错误: \n"+JSON.stringify(o,null,4)),n(o),this.successCallbacks.delete(i),void this.errorCallbacks.delete(i)}}if("id"in t){if("function"==typeof this.options.onRequest){var s=this.options.onRequest({jsonrpc:"2.0",id:t.id,method:t.method,params:t.params});s.jsonrpc="2.0",s.id=t.id,this.wsSocket&&this.wsSocket.send(JSON.stringify(s))}}else this.options.onNotification({jsonrpc:"2.0",method:t.method,params:t.params})}}},{key:"onWsClose",value:function(e){this.logger.info("onWsClose",e),this.heartCheck&&this.heartCheck.reset(),this.pendingRequests=[],this.prevState=this.state,this.state="DISCONNECTED",this.prevState!==this.state&&this.options.onWsStateChange(this.prevState,this.state,this.retryCount)}},{key:"onWsError",value:function(e){if(this.logger.info("onWsError",e),this.heartCheck&&this.heartCheck.reset(),this.pendingRequests=[],this.prevState=this.state,this.state="DISCONNECTED",this.prevState!==this.state&&this.options.onWsStateChange(this.prevState,this.state,this.retryCount),0===this.retryCount){this.logger.onError({c:Fe.TOP_ERROR,v:K.SIGNAL_CHANNEL_SETUP_FAILED});var t=new ne({code:K.SIGNAL_CHANNEL_SETUP_FAILED,message:"WebSocket connect failed"});"function"==typeof this.options.onError&&this.options.onError(t)}}},{key:"reconnect",value:function(){var e=this;!this.autoReconnected&&this.lockReconnect||(this.lockReconnect=!0,this.prevState=this.state,this.retryTimerId&&clearTimeout(this.retryTimerId),this.retryCount<this.maxRetryCount?this.retryTimerId=window.setTimeout((function(){e.retryCount++,e.logger.info("".concat((new Date).toLocaleString()," Try to reconnect, count: ").concat(e.retryCount)),e.state="RECONNECTING",e.options.onWsStateChange(e.prevState,e.state,e.retryCount),e.resetWs(),e.connect(),e.lockReconnect=!1}),this.getReconnectDelay(this.retryCount)):(this.logger.warn("SDK has tried reconnect signal channel for ".concat(this.maxRetryCount," times, but all failed. please check your network")),this.options.onWsReconnectFailed&&this.options.onWsReconnectFailed()))}},{key:"sendMessage",value:function(e){var t=this;this.wsSocket.send(e),this.timer||(this.timer=setTimeout((function(){t.logger.onError({c:Fe.TOP_ERROR,v:K.SERVER_TIMEOUT},"websocket connection timeout!");var e=new ne({code:K.SERVER_TIMEOUT,message:"server timeout"});"function"==typeof t.options.onError&&t.options.onError(e)}),this.times))}},{key:"getWsState",value:function(){return{state:this.state,prevState:this.prevState}}},{key:"resetWs",value:function(){this.wsSocket&&(this.wsSocket.onmessage=null,this.wsSocket.onclose=null,this.wsSocket.onerror=null,this.wsSocket.onopen=null,this.wsSocket=null)}},{key:"initHeartCheck",value:function(){var e=this;return{timeout:2e3,serverTimeout:1e4,timeoutObj:null,serverTimeoutObj:null,reset:function(){return clearInterval(this.timeoutObj),clearTimeout(this.serverTimeoutObj),this.timeoutObj=null,this.serverTimeoutObj=null,this},start:function(){var t=this;this.reset(),this.timeoutObj=setInterval((function(){e.sendMessage(JSON.stringify({jsonrpc:"2.0",id:0,method:"ping",params:{}})),t.serverTimeoutObj||(t.serverTimeoutObj=setTimeout((function(){e.logger.info((new Date).toLocaleString(),"not received pong, close the websocket"),e.onWsError()}),t.serverTimeout))}),this.timeout)}}}},{key:"getReconnectDelay",value:function(e){return Math.round(e/2)+1>6?13e3:3e3}},{key:"getWsUrl",value:function(){var e=this.options.customSignParam,t=this.options.wsUrl+"&appKey=".concat(this.options.appId);if(this.options.privateKey&&(t+="&privateKey=".concat(this.options.privateKey)),this.options.extendInfo&&this.options.extendInfo.location&&(t+="&location=".concat(this.options.extendInfo.location)),e){if("object"===X(e.getHeader)&&"{}"!==JSON.stringify(e.getHeader)){var i=e.getHeader;if("[object Object]"===Object.prototype.toString.call(i)&&"{}"!==JSON.stringify(i))for(var r=0,n=Object.entries(i);r<n.length;r++){var o=w(n[r],2),s=o[1];t+="&".concat(o[0],"=").concat(s)}}if("object"===X(e.getQuery)&&"{}"!==JSON.stringify(e.getQuery)){var a=e.getQuery;if("[object Object]"===Object.prototype.toString.call(a)&&"{}"!==JSON.stringify(a))for(var c=0,u=Object.entries(a);c<u.length;c++){var d=w(u[c],2),l=d[1];t+="&".concat(d[0],"=").concat(l)}}}else e||(t="".concat(t,"&Authorization=").concat(this.options.userSig));return t}}]),e}();function hi(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return pi(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?pi(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}function pi(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var fi=function(){function e(t){L(this,e),this.options=t;var i=t.roomCbs;this.roomCbs={connectionLostCb:i.connectionLostCb,connectionRecoveryCb:i.connectionRecoveryCb,tryToReconnectCb:i.tryToReconnectCb,notificationCb:i.notificationCb,onWsStateChange:i.onWsStateChange,onWsError:i.onWsError,onWsReconnectFailed:i.onWsReconnectFailed},this.remoteUsers=new Map,this.remoteUserIdArray=new Array,this.remoteStreams=new Map,this.remoteStreamIdArray=new Array,this.subUserIdMap=new Map,this.state=new Bt(this.options.logger),this.connectionStatus=be.New}var t,i;return x(e,[{key:"enter",value:function(e,t,i,r,n,o,s,a,c,u,d){this.state.setState(Pt.Entering)&&(this.options.logger.info("Enter Room "+this.options.roomId),this.roomCbs.enterRoomCb=d,this.initJsonRpcClient(e,t,i,r,u,c),this.initLoginAndLocalUser(t,r,n,o,s,a,c))}},{key:"exit",value:function(e){this.state.setState(Pt.Exiting)&&(this.options.logger.info("Exit Room "+this.options.roomId),this.roomCbs.exitRoomCb=e,this.login.logout())}},{key:"publishStream",value:function(e,t,i,r,n,o){if(this.state.state()==Pt.Entered)return this.localUser.publishStream(e,t,i,r,n,o);this.options.logger.info("We are not enter room, can not publish stream")}},{key:"unpublishStream",value:function(e,t){if(this.state.state()==Pt.Entered)return this.localUser.unpublishStream(e,t);this.options.logger.info("We are not enter room, can not unpublish stream")}},{key:"updateSimulcast",value:function(e,t,i){if(this.state.state()==Pt.Entered)return this.localUser.updateSimulcast(e,t,i);this.options.logger.info("We arn not enter room, can not publish updateSimulcast")}},{key:"muteLocalAudio",value:function(e,t,i){this.state.state()==Pt.Entered?this.localUser.muteAudio(e,t,i):this.options.logger.info("We are not enter room, can not muteLocalAudio")}},{key:"muteLocalVideo",value:function(e,t,i){this.state.state()==Pt.Entered?this.localUser.muteVideo(e,t,i):this.options.logger.info("We are not enter room, can not muteLocalVideo")}},{key:"unmuteLocalAudio",value:function(e,t,i){this.state.state()==Pt.Entered?this.localUser.unmuteAudio(e,t,i):this.options.logger.info("We are not enter room, can not unmuteLocalAudio")}},{key:"unmuteLocalVideo",value:function(e,t,i){this.state.state()==Pt.Entered?this.localUser.unmuteVideo(e,t,i):this.options.logger.info("We are not enter room, can not unmuteLocalVideo")}},{key:"subscribeStream",value:function(e,t,i,r,n,o,s){this.state.state()==Pt.Entered?(this.options.logger.info("room subscribe stream",this.remoteUsers.has(t)),this.remoteUsers.has(t)&&(this.remoteUsers.get(t).subscribeStream(e,i,r,n,o,s),this.subUserIdMap.set(e,t))):this.options.logger.info("We are not enter room, can not subscribe stream")}},{key:"unsubscribeStream",value:function(e,t){if(this.state.state()==Pt.Entered)if(this.options.logger.debug("subUserIdMap",this.subUserIdMap,e),this.subUserIdMap.has(e)){var i=this.subUserIdMap.get(e);this.remoteUsers.has(i)?(this.remoteUsers.get(i).unsubscribeStream(e,t),this.subUserIdMap.delete(e)):this.options.logger.info("unsubscription: "+e+"  no related user")}else this.options.logger.info("unsubscription: "+e+"  no related user");else this.options.logger.info("We are not enter room, can not unsubscribe stream")}},{key:"switchSimulcast",value:function(e,t,i){if(this.state.state()==Pt.Entered)if(this.subUserIdMap.has(e)){var r=this.subUserIdMap.get(e);this.remoteUsers.has(r)&&this.remoteUsers.get(r).switchSimulcast(e,t,i)}else this.options.logger.info("Subscription: "+e+" not exist");else this.options.logger.info("We are not enter room, can not switchSimulcast")}},{key:"switchPermission",value:function(e,t){var i=this;this.state.state()==Pt.Entered?this.localUser.switchPermission(e,(function(r,n,o){1===r&&i.login.updatePermission(e),t&&t(r,n,o)})):this.options.logger.info("We are not enter room, can not switchPermission")}},{key:"getWsState",value:function(){return this.rpcClient.getWsState()}},{key:"onLogin",value:function(e,t,i){if(e===Ft.LoginSuccess){if(this.connectionStatus=be.ConnectionConnected,!this.state.setState(Pt.Entered))return;this.options.logger.info("Enter Room ".concat(this.options.roomId," success")),this.roomCbs.enterRoomCb&&this.roomCbs.enterRoomCb(fe.Success,null,{roomId:this.options.roomId,roomUniqueId:t.room.roomUniqueId,participants:t.room.participants});var r=this.buildRemoteUserAndCollectNotification(t,!1),n=this.buildRemoteStreamsAndCollectNotification(t,!1),o=setTimeout((function(){var e,t=hi(r);try{for(t.s();!(e=t.n()).done;)(0,e.value)()}catch(e){t.e(e)}finally{t.f()}var i,s=hi(n);try{for(s.s();!(i=s.n()).done;)(0,i.value)()}catch(e){s.e(e)}finally{s.f()}o&&clearTimeout(o),o=null}),300)}else if(e===Ft.LoginTimeout){if(!this.state.setState(Pt.EnterTimeout))return;this.options.logger.info("Enter Room ".concat(this.options.roomId," timeout")),this.roomCbs.enterRoomCb&&this.roomCbs.enterRoomCb(fe.Timeout,i,{roomId:this.options.roomId})}else if(e===Ft.LoginFailed){if(!this.state.setState(Pt.EnterFailed))return;this.options.logger.info("Enter Room ".concat(this.options.roomId," failed")),this.roomCbs.enterRoomCb&&this.roomCbs.enterRoomCb(fe.Failed,i,{roomId:this.options.roomId})}else this.options.logger.error("Enter room result type is invalid!")}},{key:"onRelogin",value:(i=P(U.mark((function e(t,i,r){var n,o,s,a,c,u,d,l,h,p,f,m;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=18;break}if(this.connectionStatus=be.ConnectionRecovery,this.options.logger.info("onRelogin:",t,i,r),o=this.buildRemoteUserAndCollectNotification(r,!0),s=this.buildRemoteStreamsAndCollectNotification(r,!0),a=!1,!this.roomCbs.connectionRecoveryCb){e.next=11;break}return c=r.room.roomUniqueId,e.next=10,this.roomCbs.connectionRecoveryCb(this.options.roomId,c,i);case 10:a=e.sent;case 11:this.localUser.onConnectionRecovery(i,null===(n=a)||void 0===n?void 0:n.publishOfferSdp),u=hi(this.remoteUsers.values());try{for(u.s();!(d=u.n()).done;)d.value.onConnectionRecovery(i,null===(l=a)||void 0===l?void 0:l.subscribeOfferSdp)}catch(e){u.e(e)}finally{u.f()}h=hi(o);try{for(h.s();!(p=h.n()).done;)(0,p.value)()}catch(e){h.e(e)}finally{h.f()}f=hi(s);try{for(f.s();!(m=f.n()).done;)(0,m.value)()}catch(e){f.e(e)}finally{f.f()}case 18:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return i.apply(this,arguments)})},{key:"onLogout",value:function(e,t){if(e===jt.LogoutSuccess){if(!this.state.setState(Pt.Exited))return;this.options.logger.info("Exit Room "+this.options.roomId+" success"),this.roomCbs.exitRoomCb&&this.roomCbs.exitRoomCb(fe.Success,null,{roomId:this.options.roomId,reason:me.ActivelyLeave}),this.rpcClient&&(this.rpcClient.close(),this.rpcClient=null)}else if(e===jt.LogoutFailed){if(!this.state.setState(Pt.ExitFailed))return;this.roomCbs.exitRoomCb&&(this.options.logger.info("exit room failed"),this.roomCbs.exitRoomCb(fe.Failed,t,{roomId:this.options.roomId,reason:me.ActivelyLeave})),this.rpcClient&&(this.rpcClient.close(),this.rpcClient=null)}else if(e===jt.LogoutTimeout){if(!this.state.setState(Pt.ExitTimeout))return;this.options.logger.info("Exit Room "+this.options.roomId+" timeout"),this.roomCbs.exitRoomCb&&(this.options.logger.info("exit room timeout"),this.roomCbs.exitRoomCb(fe.Failed,t,{roomId:this.options.roomId,reason:me.ActivelyLeave})),this.rpcClient&&(this.rpcClient.close(),this.rpcClient=null)}else this.options.logger.error("Exit room result type is invalid!")}},{key:"onNotification",value:function(e){this.options.logger.info("room: "+this.options.roomId+" receive notification message");var t,i=e.method;if(i)if("participant"===i){var r=e.params.type;if("join"===r){var n=e.params,o={participant:{userId:n.userId,previousRoomId:n.previousRoomId,userData:n.userData}};this.buildRemoteUser(o.participant),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+o.participant.userId+"join notification"),this.roomCbs.notificationCb(this.options.roomId,ye.ParticipantJoin,o))}else if("leave"===r){var s=e.params,a={userId:s.userId,reason:s.reason};this.remoteUsers.has(a.userId)&&this.deleteRemoteUser(a.userId),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+a.userId+" leave notification"),this.roomCbs.notificationCb(this.options.roomId,ye.ParticipantLeave,a))}else this.options.logger.error("participant notification type error!!!")}else if("stream"===i){var c=e.params.type;if("add"===c){var u={stream:Ut(e.params)};this.remoteUsers.has(u.stream.userId)&&!this.remoteStreams.has(u.stream.streamId)&&(this.buildRemoteStream(u.stream),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+u.stream.userId+", stream "+u.stream.streamId+" add notification"),this.roomCbs.notificationCb(this.options.roomId,ye.StreamAdd,u)))}else if("remove"===c){var d=e.params,l={userId:d.userId,streamId:d.streamId};this.remoteStreams.has(l.streamId)&&(this.deleteRemoteStream(l.streamId),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+l.userId+", stream "+l.streamId+" remove notification"),this.roomCbs.notificationCb(this.options.roomId,ye.StreamRemove,l)))}else if("update"===c){var h=e.params,p={userId:h.userId,streamId:h.streamId,liveStatus:h.data.liveStatus,userData:h.userData};h.data.simulcast&&this.remoteStreams.has(p.streamId)&&(p.simulcast=xt(h.data.simulcast),this.remoteStreams.get(p.streamId).info.video.simulcast=p.simulcast,this.remoteUsers.get(p.userId).updateStreamSimulcast(p.streamId,p.simulcast)),h.data.liveStatus&&(p.liveStatus=h.data.liveStatus,this.remoteStreams.has(p.streamId)&&(p.liveStatus.audio&&(this.remoteStreams.get(p.streamId).info.audio.muted=p.liveStatus.audio.muted),p.liveStatus.video&&(this.remoteStreams.get(p.streamId).info.video.muted=p.liveStatus.video.muted),this.remoteUsers.get(p.userId).updateStreamStatus(p.streamId,p.liveStatus))),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+p.userId+", stream "+p.streamId+" update notification"),this.roomCbs.notificationCb(this.options.roomId,ye.StreamUpdate,p))}else this.options.logger.error("Stream notification type error!!!")}else if("drop"===i){var f={cause:(t=e.params.cause,"kicked"==t?ve.Kicked:"repeatlogin"==t?ve.RepeatLogin:"disbanded"==t?ve.RoomDissolved:ve.Unknown)};if(!this.state.setState(Pt.Destroyed))return;this.roomCbs.notificationCb&&(this.options.logger.info("drop notification"),this.roomCbs.notificationCb(this.options.roomId,ye.Drop,f)),this.rpcClient&&(this.rpcClient.close(),this.rpcClient=null)}else if("permission"===i){var m=e.params,g={userId:m.userId,publish:m.publish,subscribe:m.subscribe,control:m.control};this.roomCbs.notificationCb&&(this.options.logger.info("permission change notification"),this.roomCbs.notificationCb(this.options.roomId,ye.PermissionChange,g));var v=g.publish&&(g.publish.audio||g.publish.video);if(this.remoteUsers.has(g.userId)){if(!v){this.deleteRemoteUser(g.userId);var b={userId:g.userId,reason:ge.Normal};this.roomCbs.notificationCb&&(this.options.logger.info("user: "+b.userId+"leave notification"),this.roomCbs.notificationCb(this.options.roomId,ye.ParticipantLeave,b))}}else if(v){var y={userId:g.userId,previousRoomId:"",userData:{userId:g.userId,userName:""}};this.buildRemoteUser(y),this.roomCbs.notificationCb&&(this.options.logger.info("user: "+y.userId+"join notification"),this.roomCbs.notificationCb(this.options.roomId,ye.ParticipantJoin,{participant:y}))}}}},{key:"onRpcStateChange",value:function(e,t,i){this.options.logger.info("prevState: ".concat(e,",state: ").concat(t)),"DISCONNECTED"===t?this.onConnectionLost():"RECONNECTING"===t?this.onTryToReconenct():"RECOVERY"===t&&this.onConnectionRecovery(),this.roomCbs.onWsStateChange&&this.roomCbs.onWsStateChange(this.options.roomId,e,t)}},{key:"onConnectionLost",value:function(){this.options.logger.info("room: "+this.options.roomId+" connection lost!!!"),this.connectionStatus=be.ConnectionLost,this.login.onConnectionLost(),this.localUser.onConnectionLost();var e,t=hi(this.remoteUsers.values());try{for(t.s();!(e=t.n()).done;)e.value.onConnectionLost()}catch(e){t.e(e)}finally{t.f()}this.roomCbs.connectionLostCb&&this.roomCbs.connectionLostCb(this.options.roomId),this.state.state()==Pt.Entered&&this.rpcClient.reconnect()}},{key:"onTryToReconenct",value:function(){this.options.logger.info("room: "+this.options.roomId+" connection retring ......"),this.connectionStatus=be.ConnectionRetring,this.roomCbs.tryToReconnectCb&&this.roomCbs.tryToReconnectCb(this.options.roomId)}},{key:"onConnectionRecovery",value:function(){this.options.logger.info("room: ".concat(this.options.roomId," connection recovery!!!")),this.state.state()!=Pt.Entering&&this.login.onConnectionRecovery()}},{key:"onRpcReconnectFailed",value:function(){this.options.logger.info("room: "+this.options.roomId+" reconnection failed!!!"),this.roomCbs.onWsReconnectFailed&&this.roomCbs.onWsReconnectFailed(this.options.roomId)}},{key:"initJsonRpcClient",value:function(e,t,i,r,n,o){var s={customSignParam:e,appId:t,userSig:i,userId:r,privateKey:n,extendInfo:o,wsUrl:this.options.serverUrl,onRequest:null,onNotification:this.onNotification.bind(this),onError:this.roomCbs.onWsError,onConnect:null,onWsStateChange:this.onRpcStateChange.bind(this),onWsReconnectFailed:this.onRpcReconnectFailed.bind(this)};this.rpcClient=new li(s,this.options.logger),this.rpcClient.connect()}},{key:"initLoginAndLocalUser",value:(t=P(U.mark((function e(t,i,r,n,o,s,a){var c,u;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,new Promise(function(){var e=P(U.mark((function e(t){var i;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i={sdk:{type:"WebRTC",version:"4.2.1"},device:{osName:"",osVersion:"".concat(Oe,"/").concat(we),netType:le()},capabilities:{isp:"unknown",location:"unknown",trikleIce:!1,secure:!0}},e.prev=1,e.next=4,new Promise(function(){var e=P(U.mark((function e(t,i){return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!he.any()){e.next=5;break}t(he.getOsName()),e.next=9;break;case 5:return e.next=7,new Promise(function(){var e=P(U.mark((function e(t,i){var r,n,o,s,a,c,u,d;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r="-",n=navigator.appVersion,o=navigator.userAgent,s=r,a=[{s:"Chrome OS",r:/CrOS/},{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows Vista",r:/Windows NT 6.0/},{s:"Windows Server 2003",r:/Windows NT 5.2/},{s:"Windows XP",r:/(Windows NT 5.1|Windows XP)/},{s:"Windows 2000",r:/(Windows NT 5.0|Windows 2000)/},{s:"Windows ME",r:/(Win 9x 4.90|Windows ME)/},{s:"Windows 98",r:/(Windows 98|Win98)/},{s:"Windows 95",r:/(Windows 95|Win95|Windows_95)/},{s:"Windows NT 4.0",r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},{s:"Windows CE",r:/Windows CE/},{s:"Windows 3.11",r:/Win16/},{s:"Android",r:/Android/},{s:"Open BSD",r:/OpenBSD/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"QNX",r:/QNX/},{s:"UNIX",r:/UNIX/},{s:"BeOS",r:/BeOS/},{s:"OS/2",r:/OS\/2/},{s:"Search Bot",r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}],c=0;case 6:if(!(u=a[c])){e.next=13;break}if(!u.r.test(o)){e.next=10;break}return s=u.s,e.abrupt("break",13);case 10:c++,e.next=6;break;case 13:if(d=r,!/Windows/.test(s)){e.next=28;break}if(!/Windows (.*)/.test(s)){e.next=27;break}if(10!=(d=/Windows (.*)/.exec(s)[1])){e.next=27;break}return e.prev=18,e.next=21,new Promise((function(e){navigator&&navigator.userAgentData&&navigator.userAgentData.getHighEntropyValues?navigator.userAgentData.getHighEntropyValues(["platformVersion"]).then((function(t){if(navigator.userAgentData.platform&&"windows"===navigator.userAgentData.platform.toLowerCase()&&t.platformVersion){var i=parseInt(t.platformVersion.split(".")[0]);e(i>=13?11:10)}else e(10)})).catch((function(){e(10)})):e(10)}));case 21:d=e.sent,e.next=27;break;case 24:e.prev=24,e.t0=e.catch(18),d=10;case 27:s="Windows";case 28:e.t1=s,e.next="Mac OS X"===e.t1?31:"Android"===e.t1?33:"iOS"===e.t1?35:37;break;case 31:return/Mac OS X (10[/._\d]+)/.test(o)&&(d=/Mac OS X (10[\.\_\d]+)/.exec(o)[1]),e.abrupt("break",37);case 33:return/Android ([\.\_\d]+)/.test(o)&&(d=/Android ([\.\_\d]+)/.exec(o)[1]),e.abrupt("break",37);case 35:return/OS (\d+)_(\d+)_?(\d+)?/.test(o)&&(d=(d=/OS (\d+)_(\d+)_?(\d+)?/.exec(n))[1]+"."+d[2]+"."+(0|d[3])),e.abrupt("break",37);case 37:t({osName:s+d,type:"desktop"});case 38:case"end":return e.stop()}}),e,null,[[18,24]])})));return function(t,i){return e.apply(this,arguments)}}());case 7:t(e.sent);case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),i(e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,i){return e.apply(this,arguments)}}());case 4:i.device.osName=e.sent.osName,t(i),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),t(i);case 12:case"end":return e.stop()}}),e,null,[[1,9]])})));return function(t){return e.apply(this,arguments)}}());case 2:c=e.sent,a&&a.location&&(c.capabilities.location=a.location),u={appId:t,userId:i,userType:r,roomId:this.options.roomId,previousRoomId:n,userAgent:c,permission:o,userData:s,rpcClient:this.rpcClient,logger:this.options.logger,loginCb:this.onLogin.bind(this),reloginCb:this.onRelogin.bind(this),logoutCb:this.onLogout.bind(this)},this.login=new Kt(u),this.localUser=new ii({userId:i,roomId:this.options.roomId,previousRoomId:n,userAgent:u.userAgent,permission:o,userData:s,rpcClient:this.rpcClient,logger:this.options.logger}),this.login.login();case 9:case"end":return e.stop()}}),e,this)}))),function(e,i,r,n,o,s,a){return t.apply(this,arguments)})},{key:"buildRemoteUser",value:function(e){var t=e.userId,i=new di({roomId:this.options.roomId,participant:e,rpcClient:this.rpcClient,logger:this.options.logger});this.remoteUsers.set(t,i),this.remoteUserIdArray.push(t),this.options.logger.info("this.remoteUsers",JSON.stringify(this.remoteUsers,null,4))}},{key:"deleteRemoteStream",value:function(e){if(this.remoteStreams.has(e)){var t=this.remoteStreams.get(e).userId;this.remoteUsers.has(t)&&this.remoteUsers.get(t).deleteStream(e),this.remoteStreams.has(e)&&this.remoteStreams.delete(e);var i=this.remoteStreamIdArray.indexOf(e);-1!=i&&this.remoteStreamIdArray.splice(i,1)}}},{key:"deleteRemoteUser",value:function(e){if(this.remoteUsers.has(e)){var t,i=hi(this.remoteUsers.get(e).getAllStreamId());try{for(i.s();!(t=i.n()).done;)this.deleteRemoteStream(t.value)}catch(e){i.e(e)}finally{i.f()}this.remoteUsers.delete(e);var r=this.remoteUserIdArray.indexOf(e);-1!=r&&this.remoteUserIdArray.splice(r,1)}}},{key:"buildRemoteStream",value:function(e){var t=e.streamId;this.remoteUsers.get(e.userId).addStream(e),this.remoteStreams.set(t,e),this.remoteStreamIdArray.push(t)}},{key:"buildRemoteUserAndCollectNotification",value:function(e,t){var i=this,r=new Array;if(t){var n,o=hi(e.room.participants);try{var s=function(){var e=n.value,t={participant:e},o=e.userId;if(o===i.localUser.getUserId())return"continue";i.remoteUsers.has(o)||(i.buildRemoteUser(e),r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+t.participant.userId+"join notification"),i.roomCbs.notificationCb(i.options.roomId,ye.ParticipantJoin,t))})))};for(o.s();!(n=o.n()).done;)s()}catch(e){o.e(e)}finally{o.f()}var a,c=hi(JSON.parse(JSON.stringify(this.remoteUserIdArray)));try{for(c.s();!(a=c.n()).done;){var u,d=a.value,l=!0,h=hi(e.room.participants);try{for(h.s();!(u=h.n()).done;)u.value.userId===d&&(l=!1)}catch(e){h.e(e)}finally{h.f()}if(l){this.deleteRemoteUser(d);var p={userId:d,reason:ge.Normal};this.roomCbs.notificationCb&&(this.options.logger.info("user: "+p.userId+"leave notification"),this.roomCbs.notificationCb(this.options.roomId,ye.ParticipantLeave,p))}}}catch(e){c.e(e)}finally{c.f()}}else{var f,m=hi(e.room.participants);try{var g=function(){var e=f.value,t={participant:e};if(e.userId===i.localUser.getUserId())return"continue";i.buildRemoteUser(e),r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: ".concat(t.participant.userId," join notification")),i.roomCbs.notificationCb(i.options.roomId,ye.ParticipantJoin,t))}))};for(m.s();!(f=m.n()).done;)g()}catch(e){m.e(e)}finally{m.f()}}return r}},{key:"buildRemoteStreamsAndCollectNotification",value:function(e,t){var i=this,r=new Array;if(t){var n,o=hi(JSON.parse(JSON.stringify(this.remoteStreamIdArray)));try{for(o.s();!(n=o.n()).done;){var s,a=n.value,c=!0,u=hi(e.room.streams);try{for(u.s();!(s=u.n()).done;)s.value.streamId===a&&(c=!1)}catch(e){u.e(e)}finally{u.f()}if(c&&this.remoteStreams.has(a)){var d=this.remoteStreams.get(a).userId;this.deleteRemoteStream(a);var l={userId:d,streamId:a};this.roomCbs.notificationCb&&(this.options.logger.info("user: "+l.userId+", stream "+l.streamId+" remove notification"),this.roomCbs.notificationCb(this.options.roomId,ye.StreamRemove,l))}}}catch(e){o.e(e)}finally{o.f()}var h,p=hi(e.room.streams);try{var f=function(){var e=h.value,t={stream:Mt(e)},n=e.userId,o=e.streamId;if(n===i.localUser.getUserId())return"continue";if(i.remoteStreams.has(o)){if(e.info.audio&&e.info.audio.muted!=i.remoteStreams.get(o).info.audio.muted){var s={audio:{muted:e.info.audio.muted,floor:e.info.audio.floor}};i.remoteStreams.get(o).info.audio.muted=e.info.audio.muted,i.remoteStreams.get(o).info.audio.floor=e.info.audio.floor,i.remoteUsers.get(n).updateStreamStatus(o,s);var a={userId:n,streamId:o,liveStatus:s};r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+n+", stream "+o+" update notification"),i.roomCbs.notificationCb(i.options.roomId,ye.StreamUpdate,a))}))}if(e.info.video&&e.info.video.muted!=i.remoteStreams.get(o).info.video.muted){var c={video:{muted:e.info.video.muted,floor:e.info.video.floor}};i.remoteStreams.get(o).info.video.muted=e.info.video.muted,i.remoteStreams.get(o).info.video.floor=e.info.video.floor,i.remoteUsers.get(n).updateStreamStatus(o,c);var u={userId:n,streamId:o,liveStatus:c};r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+n+", stream "+o+" update notification"),i.roomCbs.notificationCb(i.options.roomId,ye.StreamUpdate,u))}))}if(e.info.video&&e.info.video.simulcast&&e.info.video.simulcast.length!=i.remoteStreams.get(o).info.video.simulcast.length){var d=xt(e.info.video.simulcast);i.remoteStreams.get(o).info.video.simulcast=d,i.remoteUsers.get(n).updateStreamSimulcast(o,d);var l={userId:n,streamId:o,simulcast:d};r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+n+", stream "+o+" update notification"),i.roomCbs.notificationCb(i.options.roomId,ye.StreamUpdate,l))}))}}else{if(!i.remoteUsers.has(n))return i.options.logger.error("Stream "+o+" no related user!"),{v:r};i.buildRemoteStream(t.stream),r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+n+", stream "+o+" add notification"),i.roomCbs.notificationCb(i.options.roomId,ye.StreamAdd,t))}))}};for(p.s();!(h=p.n()).done;){var m=f();if("continue"!==m&&"object"===X(m))return m.v}}catch(e){p.e(e)}finally{p.f()}}else{var g,v=hi(e.room.streams);try{var b=function(){var e=g.value,t={stream:Mt(e)},n=e.userId,o=e.streamId;return n===i.localUser.getUserId()?"continue":i.remoteUsers.has(n)?(i.buildRemoteStream(t.stream),void r.push((function(){i.roomCbs.notificationCb&&(i.options.logger.info("user: "+n+", stream "+o+" add notification"),i.roomCbs.notificationCb(i.options.roomId,ye.StreamAdd,t))}))):(i.options.logger.error("Stream "+o+" no related user!"),{v:r})};for(v.s();!(g=v.n()).done;){var y=b();if("continue"!==y&&"object"===X(y))return y.v}}catch(e){v.e(e)}finally{v.f()}}return r}}]),e}();function mi(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}var gi,vi=function(){function e(t,i){L(this,e),this.rooms=new Map,this.logger=t,this.roomCbs=i}return x(e,[{key:"enterRoom",value:function(e,t){var i=t.customSignParam,r=t.appId,n=t.userSig,o=t.userId,s=t.userType,a=t.previousRoomId,c=t.permission,u=t.userData,d=t.extendInfo,l=t.serverUrl,h=t.privateKey,p=t.enterRoomCb;if(this.logger.info("XsigoStackClient enterRoom: "+e),!this.rooms.has(e)){var f=new fi({roomId:e,serverUrl:l,logger:this.logger,roomCbs:this.roomCbs});this.rooms.set(e,f)}this.rooms.get(e).enter(i,r,n,o,s,a,c,u,d,h,p)}},{key:"exitRoom",value:function(e,t){if(this.logger.info("XsigoStackClient exitRoom: "+e),this.rooms.has(e))return this.rooms.get(e).exit(t),void this.rooms.delete(e);this.logger.error("XsigoStackClient exitRoom: "+e+"error, room not exist")}},{key:"publishStream",value:function(e,t){var i=t.streamType,r=t.streamKind,n=t.params,o=t.cb,s=t.updateCb,a=mi();if(this.logger.info("XsigoStackClient publishStream :  "+a+" in room"+e,this.rooms.has(e)),this.rooms.has(e))return this.rooms.get(e).publishStream(a,i,r,n,o,s),a}},{key:"unpublishStream",value:function(e,t,i){this.logger.info("XsigoStackClient unpublishStream :  "+t+" in room"+e),this.rooms.has(e)&&this.rooms.get(e).unpublishStream(t,i)}},{key:"updateSimulcast",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).updateSimulcast(t,i,r)}},{key:"muteAudio",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).muteLocalAudio(t,i,r)}},{key:"muteVideo",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).muteLocalVideo(t,i,r)}},{key:"unmuteAudio",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).unmuteLocalAudio(t,i,r)}},{key:"unmuteVideo",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).unmuteLocalVideo(t,i,r)}},{key:"subscribeStream",value:function(e,t){var i=t.publisherUserId,r=t.streamId,n=t.streamKind,o=t.params,s=t.cb,a=t.updateCb;this.logger.info("XsigoStackClient subscribeStream :  "+r+" in room"+e);var c=mi();return this.rooms.has(e)?(this.rooms.get(e).subscribeStream(c,i,r,n,o,s,a),c):""}},{key:"unsubscribeStream",value:function(e,t,i){this.logger.info("XsigoStackClient unsubscribe :  "+t+" in room"+e),this.rooms.has(e)&&this.rooms.get(e).unsubscribeStream(t,i)}},{key:"switchSimulcast",value:function(e,t,i,r){this.rooms.has(e)&&this.rooms.get(e).switchSimulcast(t,i,r)}},{key:"switchPermission",value:function(e,t,i){this.rooms.has(e)&&this.rooms.get(e).switchPermission(t,i)}},{key:"getWsState",value:function(e){if(this.rooms.has(e))return this.rooms.get(e).getWsState()}},{key:"isDisconnected",value:function(e){var t=this.getWsState(e).state;return["CONNECTED","RECOVERY"].includes(t)||this.logger.warn("cannot operate during network disconnection"),!["CONNECTED","RECOVERY"].includes(t)}}]),e}();function bi(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}!function(e){e[e.AuthTypeHeader=0]="AuthTypeHeader",e[e.AuthTypeQuery=1]="AuthTypeQuery"}(gi||(gi={}));var yi={appKey:null,authorization:"",timeout:1e4,extendInfo:{},ssl:!0,customSignParam:null,path:"",privateKey:"",timeoutObj:{},init:function(e){var t=e.wsUrl,i=e.sdkAppId,r=e.userSig,n=e.customSignParam,o=e.extendInfo;this.ssl=e.ssl,this.path=this.baseUrl(t),this.appKey=i,this.authorization=r,this.customSignParam=n,this.extendInfo=o},timeoutPromise:function(e,t,i){var r=this;return new Promise((function(n,o){r.timeoutObj[i]=setTimeout((function(){n(new Response("timeout",{status:408,statusText:"request timeout"})),t.abort()}),e)}))},getHeader:function(){if(this.customSignParam&&"{}"!==JSON.stringify(this.customSignParam.getHeader)){if("object"===X(this.customSignParam.getHeader)){var e=this.customSignParam.getHeader;if("[object Object]"===Object.prototype.toString.call(e)){if("{}"!==JSON.stringify(e))return new Headers(function(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?bi(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):bi(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}({"Content-Type":"application/json",appKey:this.appKey},e));throw new Error("customSignParam.getHeader result is an empty object")}throw new Error("customSignParam.getHeader result is not an object")}throw new Error("customSignParam.getHeader is not a object")}if(!this.customSignParam)return new Headers({"Content-Type":"application/json",appKey:this.appKey,Authorization:this.authorization})},getQuerys:function(){if(this.customSignParam&&"{}"!==JSON.stringify(this.customSignParam.getQuery)){if("object"===X(this.customSignParam.getQuery)){var e=this.customSignParam.getQuery;if("[object Object]"===Object.prototype.toString.call(e)){if("{}"!==JSON.stringify(e)){for(var t="",i=0,r=Object.entries(e);i<r.length;i++){var n=w(r[i],2),o=n[1];t+="".concat(n[0],"=").concat(o,"&")}return t}throw new Error("customSignParam.getQuery result is an empty object")}throw new Error("customSignParam.getQuery result is not an object")}throw new Error("customSignParam.getQuery is not a object")}return""},baseUrl:function(e){return e.includes("//")?e:"".concat(this.ssl?"https://":"http://").concat(e)},getAppConfig:function(e){var t=this,i=new AbortController,r="".concat(this.path,"/api/v1/app/config/get?").concat(this.getQuerys(),"appId=").concat(this.appKey);return this.extendInfo&&this.extendInfo.location&&(r+="&location=".concat(this.extendInfo.location)),e&&(this.privateKey=e,r+="&privateKey=".concat(e)),Promise.race([this.timeoutPromise(this.timeout,i,"getAppConfig"),fetch(r,{method:"GET",headers:t.getHeader(),signal:i.signal})]).then((function(e){if(e.ok)return e.json();throw new ne(401===e.status?{code:K.AUTHORIZATION_FAILED,message:"Authorization failed: /api/v1/app/config/get?appId"}:404===e.status?{code:K.GET_SERVER_NODE_FAILED,message:"404: /api/v1/app/config/get?appId"}:408===e.status?{code:K.REQUEST_TIMEOUT,message:"".concat(e.statusText,": /api/v1/app/config/get?appId")}:{code:K.SERVER_UNKNOWN_ERROR,message:"Server unknown error: /api/v1/app/config/get?appId"})})).catch((function(e){return Promise.reject(e)})).finally((function(){t.timeoutObj.getAppConfig&&clearTimeout(t.timeoutObj.getAppConfig),t.timeoutObj.getAppConfig=null}))},getWsUrl:function(e,t){var i=this,r="".concat(this.path,"/api/v1/dispatch/get-can-use?").concat(this.getQuerys(),"mucNum=").concat(e);this.extendInfo&&this.extendInfo.location&&(r+="&location=".concat(this.extendInfo.location)),t&&(r+="&privateKey=".concat(t));var n=new AbortController;return Promise.race([this.timeoutPromise(this.timeout,n,"getWsUrl"),fetch(r,{method:"GET",headers:i.getHeader(),signal:n.signal})]).then((function(e){if(e.ok)return e.json();throw new ne(401===e.status?{code:K.AUTHORIZATION_FAILED,message:"Authorization failed: /api/v1/dispatch/get-can-use?mucNum"}:404===e.status?{code:K.GET_SERVER_NODE_FAILED,message:"404: /api/v1/dispatch/get-can-use?mucNum"}:408===e.status?{code:K.REQUEST_TIMEOUT,message:"".concat(e.statusText,": /api/v1/dispatch/get-can-use?mucNum")}:{code:K.SERVER_UNKNOWN_ERROR,message:"Server unknown error: /api/v1/dispatch/get-can-use?mucNum"})})).catch((function(e){return Promise.reject(e)})).finally((function(){i.timeoutObj.getWsUrl&&clearTimeout(i.timeoutObj.getWsUrl),i.timeoutObj.getWsUrl=null}))},upload:function(e){var t=this.getQuerys(),i="".concat(this.path,"/api/v1/logging/collect/list").concat(t&&"?".concat(t));return this.extendInfo&&this.extendInfo.location&&(i+="?location=".concat(this.extendInfo.location)),this.privateKey&&(i+="?privateKey=".concat(this.privateKey)),fetch(i,{method:"POST",body:JSON.stringify(e),headers:this.getHeader()}).then((function(e){if(e.ok)return e.json();throw new ne({code:e.status,message:e.statusText})})).catch((function(e){return Promise.reject(e)}))}};function Si(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function Ei(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?Si(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Si(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function _i(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=function(e,t){if(e){if("string"==typeof e)return Ci(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Ci(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}function Ci(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var Ii,Ti,Ri=function(){function e(t,i){L(this,e),this.logger=i,this.ssl=!!t.wsUrl.includes("https")||!t.wsUrl.includes("http")&&!1!==t.ssl,this.wsUrl=t.wsUrl,this.userId=t.userId,this.userName=t.userName,this.mode=t.mode,this.sdkAppId=t.sdkAppId,this.userSig=t.userSig,this.customSignParam=t.customSignParam,this.extendInfo=t.extendInfo,this.appConfig=null,this.init()}var t,i,r,n,o,s,a,c,u,d,l,h,p,f;return x(e,[{key:"init",value:function(){var e=this;this.publications=new Map,this.subscriptions=new Map,this.roomId=null,this.roomUniqueId=null,this.role="anchor",this.remoteStreams=new Map,this.state=B.New,this.localStreams=[],this.enablemicVolume=!1,this.soundMeter=null,this.timer=null,this.micStream=null,this.isEnableSmallStream=!1,this._emitter=new N(this.logger),this.subscribeManager=new gt(this.logger),this._interval=-1,this._remoteMutedStateMap=new Map,this.audioVolumeInterval=null,this.isWaterMark=!1,this.waterMarkoptions=null,this.waterMarkImage=null,this.smallStreamConfig={width:160,height:120,bitrate:100,framerate:15},this.logger.setUserId(this.userId),this.logger.setServerUrl(this.wsUrl),yi.init({ssl:this.ssl,wsUrl:this.wsUrl,sdkAppId:this.sdkAppId,userSig:this.userSig,customSignParam:this.customSignParam,extendInfo:this.extendInfo}),this.xsigoClient=new vi(this.logger,{notificationCb:this.notificationCb.bind(this),connectionLostCb:this.connectionLostCb.bind(this),tryToReconnectCb:this.tryToReconnectCb.bind(this),connectionRecoveryCb:this.connectionRecoveryCb.bind(this),onWsStateChange:this.onWsStateChange.bind(this),onWsError:this.onError.bind(this),onWsReconnectFailed:this.onWsReconnectFailed.bind(this)}),this.ssl&&navigator.mediaDevices&&Ge().then((function(t){e._preDiviceList=t,e.logger.info("mediaDevices",JSON.stringify(e._preDiviceList,null,4))})).catch((function(){e._preDiviceList=[]})),this.senderStats=new Map,this.receiverStats=new Map,this.senderLocalStats=new Map,this.deviceChange=this.onDeviceChange.bind(this),this.visibilitychange=this.onVisibilitychange.bind(this),this.logger.info("userAgent:",navigator.userAgent)}},{key:"setAppConfig",value:function(e){var t=e.serverTs,i=e.logPeriod,r=e.enableLog,n=e.eventPeriod,o=e.enableEvent,s=e.metricCollectPeriod;this.appConfig={serverTs:t,timeDiff:t?Date.now()-t:0,logPeriod:i,enableLog:!1!==r,eventPeriod:n,enableEvent:!1!==o,metricCollectPeriod:s},this.logger.setApppConfig(this.appConfig),this.logger.info("get app config",JSON.stringify(this.appConfig,null,4))}},{key:"reset",value:function(){this.publications=new Map,this.subscriptions=new Map,this.localStreams=[],this.remoteStreams.clear(),this.subscribeManager.reset(),this._remoteMutedStateMap.clear(),this.appConfig=null,this._interval&&window.clearInterval(this._interval),this._interval=-1,this.audioVolumeInterval&&window.clearInterval(this.audioVolumeInterval),this.audioVolumeInterval=null,this.removeEventListenser("devicechange"),this.removeEventListenser("visibilitychange"),this.logger.setServerUrl(null),this.logger.setApppConfig(null),this.closeWaterMark()}},{key:"notificationCb",value:function(e,t,i){if(t===ye.ParticipantJoin){var r=i.participant,n=r.userId,o=r.previousRoomId,s=r.userData;this.logger.info("======notification: participant join======"),this.updateRemoteMutedState(n),this.logger.buriedLog({c:Fe.ON_PEER_JOIN,v:"uid:".concat(n)}),this._emitter.emit("peer-join",{userId:n,previousRoomId:o,userData:s})}if(t===ye.ParticipantLeave){var a=i.userId;this.logger.info("======notification: participant leave======"),this.onParticipantLeave(a)}if(t===ye.StreamAdd&&(this.logger.info("======notification: stream add======"),this.onStreamAdd(i.stream)),t===ye.StreamRemove&&(this.logger.info("======notification: stream remove======"),this.onStreamChange(i.userId,i.streamId)),t===ye.Drop){var c=i.cause;this.logger.info("======notification: participant drop======"),this.onClientBanned(c)}if(t===ye.StreamUpdate&&(this.logger.info("======notification: stream update======"),this.onStreamUpdate(i.userId,i.streamId,i.liveStatus,i.simulcast)),t===ye.PermissionChange){this.logger.info("======notification: role change======",i.userId,i.publish)}}},{key:"connectionLostCb",value:function(e){this.logger&&(this.logger.info("room: ".concat(e," connection lost")),this.logger.buriedLog({c:Fe.CONNECTIONLOST_CB}))}},{key:"tryToReconnectCb",value:function(e){this.logger.info("room: ".concat(e," connection retring ......"))}},{key:"connectionRecoveryCb",value:function(e,t,i){var r=this;return new Promise(function(){var n=P(U.mark((function n(o,s){var a,c,u,d,l,h,p,f,m,g,v,b,y;return U.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r.logger.info("room: ".concat(e," connection recovery")),r.logger.buriedLog({c:Fe.CONNECTION_RECOVERY_CB}),!i){n.next=47;break}r.roomUniqueId=t,r.logger.setRoomUniqueId(t),a=new Map,c=new Map,u=new Map(r.publications),d=_i(u.entries()),n.prev=9,h=U.mark((function e(){var t,i,n,o,s,c;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=w(l.value,2),i=t[0],n=t[1],(o=r.localStreams.find((function(e){return[e.audioStreamId,e.videoStreamId].includes(i)})))&&(s=o.screen?"share_".concat(o.getUserId()):o.getUserId(),r.senderStats.delete(s)),e.next=5,n.republish();case 5:(c=e.sent)&&a.set(i,c.sdp);case 7:case"end":return e.stop()}}),e)})),d.s();case 12:if((l=d.n()).done){n.next=16;break}return n.delegateYield(h(),"t0",14);case 14:n.next=12;break;case 16:n.next=21;break;case 18:n.prev=18,n.t1=n.catch(9),d.e(n.t1);case 21:return n.prev=21,d.f(),n.finish(21);case 24:p=new Map(r.subscriptions),f=_i(p.entries()),n.prev=26,f.s();case 28:if((m=f.n()).done){n.next=38;break}return g=w(m.value,2),b=g[1],r.logger.info("resubscribe stream",v=g[0]),n.next=33,b.subscriber.resubscribe();case 33:y=n.sent,r.receiverStats.delete(b.stream.getUserSeq()),y&&c.set(v,y.sdp);case 36:n.next=28;break;case 38:n.next=43;break;case 40:n.prev=40,n.t2=n.catch(26),f.e(n.t2);case 43:return n.prev=43,f.f(),n.finish(43);case 46:o({publishOfferSdp:a,subscribeOfferSdp:c});case 47:case"end":return n.stop()}}),n,null,[[9,18,21,24],[26,40,43,46]])})));return function(e,t){return n.apply(this,arguments)}}())}},{key:"join",value:function(e){var t=this;if(this.logger.info("join room with options",JSON.stringify(e)),this.logger.buriedLog({c:Fe.JOIN}),this.logger.buriedLog({c:Fe.JOIN_FIRST}),[B.New,B.Leaved].includes(this.state))return new Promise(function(){var i=P(U.mark((function i(r,n){var o,s,a,c,u,d,l,h,p,f,m,g,v,b,y,S,E,_,C,I,T;return U.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,t.isJoinRoomSupported();case 2:if(s=(o=i.sent).code,a=o.message,o.isSupported){i.next=11;break}return t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),c="join room failed,".concat(a),t.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},c,!0),i.abrupt("return",n(new ne({code:s,message:a})));case 11:if(d=e.role,l="",e.privateKey&&(l=e.privateKey),h=/^[A-Za-z0-9_-]+$/g,!(u=e.roomId)||h.test(u)){i.next=20;break}return p="join room failed,roomId:".concat(u,' is invalid，roomId can only be numbers, letters and "-" '),t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),t.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},p,!0),i.abrupt("return",n(new ne({code:K.INVALID_OPERATION,message:p})));case 20:if(!(t.wsUrl&&t.sdkAppId&&(t.userSig||t.customSignParam)&&u&&t.userId)){i.next=50;break}return i.prev=21,t.roomId=u,t.role="live"===t.mode&&d||t.role,t.logger.setRoomId(t.roomId),i.next=27,yi.getAppConfig(l);case 27:return(f=i.sent)&&t.setAppConfig(f.data),i.next=31,yi.getWsUrl(t.roomId,l);case 31:g=(m=i.sent.data||{}).host,v=m.metadata,b=m.port,y="",S=t.wsUrl,(t.wsUrl.includes("https://")||t.wsUrl.includes("http://"))&&(S=t.wsUrl.split("//")[1]),S.split(":").length-1>=2?(_=v.hostV6,(E=v.sslHostV6).includes(":")&&(E=v.sslHostV6.includes("[")?v.sslHostV6:"[".concat(E),E=v.sslHostV6.includes("]")?v.sslHostV6:"".concat(E,"]")),_.includes(":")&&(_=v.hostV6.includes("[")?v.hostV6:"[".concat(_),_=v.hostV6.includes("]")?v.hostV6:"".concat(_,"]")),y=t.ssl?"wss://".concat(E,":").concat(v.sslPort,"/xsigo?roomNum=").concat(t.roomId,"&userId=").concat(t.userId):"ws://".concat(_,":").concat(b,"/xsigo?roomNum=").concat(t.roomId,"&userId=").concat(t.userId)):y=t.ssl?"wss://".concat(v.sslHost,":").concat(v.sslPort,"/xsigo?roomNum=").concat(t.roomId,"&userId=").concat(t.userId):"ws://".concat(g,":").concat(b,"/xsigo?roomNum=").concat(t.roomId,"&userId=").concat(t.userId),t.state=B.Joining,C={customSignParam:t.customSignParam,appId:t.sdkAppId,userSig:t.userSig,userId:t.userId,userType:Ce.Normal,previousRoomId:"",permission:V.get(t.role),userData:{userId:t.userId,userName:t.userName,extra:e.extra},extendInfo:t.extendInfo,serverUrl:y,privateKey:l,enterRoomCb:function(e,i,o){if(1===e){t.state=B.Joined;var s=o.participants,a=o.roomUniqueId;t.getNetworkQuality(),t.addEventListenser("devicechange"),t.addEventListenser("visibilitychange"),t.roomUniqueId=a,t.logger.setRoomUniqueId(a),t.logger.buriedLog({c:Fe.JOIN_SUCCESS}),t._emitter.emit("members",s),t.logger.info("join room ".concat(t.roomId," success")),r(!0)}else if(2===e){t.state=B.New;var c="".concat(K.JOIN_ROOM_FAILED," join room timeout");t.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},c,!0),t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),n(new ne({code:K.JOIN_ROOM_FAILED,message:"join room timeout"}))}else{t.state=B.New;var u="join room failed:, ".concat(i);t.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},u,!0),t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),n(new ne({code:K.JOIN_ROOM_FAILED,message:i}))}}},t.xsigoClient.enterRoom(t.roomId,C),i.next=48;break;case 42:i.prev=42,i.t0=i.catch(21),I="join room failed:,".concat(i.t0),t.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},I,!0),t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),n(new ne({code:K.JOIN_ROOM_FAILED,message:i.t0}));case 48:i.next=54;break;case 50:T="join room failed, options is invalid,wsUrl:".concat(t.wsUrl,",sdkAppId:").concat(t.sdkAppId,",userSig:").concat(t.userSig,",customSignParam:").concat(JSON.stringify(t.customSignParam),",roomId:").concat(e.roomId,",userId:").concat(t.userId),t.logger.error(T),t.logger.buriedLog({c:Fe.JOIN_FAILED},!0),n(new ne({code:K.INVALID_OPERATION,message:T}));case 54:case"end":return i.stop()}}),i,null,[[21,42]])})));return function(e,t){return i.apply(this,arguments)}}());this.logger.buriedLog({c:Fe.JOIN_FAILED});var i="join room failed,client state is error ,state:".concat(this.state);this.logger.onError({c:Fe.TOP_ERROR,v:K.JOIN_ROOM_FAILED},i,!0)}},{key:"leave",value:(f=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(![B.Leaving,B.Leaved].includes(this.state)){e.next=2;break}return e.abrupt("return");case 2:return this.logger.buriedLog({c:Fe.LEAVE}),e.abrupt("return",new Promise((function(e,i){var r,n=_i(t.publications.entries());try{var o=function(){var e=w(r.value,2),i=e[0],n=e[1],o=t.localStreams.find((function(e){return e.streamId===i}));o&&o.close(),n.close()};for(n.s();!(r=n.n()).done;)o()}catch(e){n.e(e)}finally{n.f()}var s,a=_i(t.subscriptions.values());try{for(a.s();!(s=a.n()).done;){var c=s.value;c.stream.close(),c.subscriber.close()}}catch(e){a.e(e)}finally{a.f()}t.state=B.Leaving,setTimeout((function(){window.initStream&&(window.initStream.getTracks().forEach((function(e){e.stop()})),window.initStream=null)}),2e3),t.xsigoClient.exitRoom(t.roomId,(function(i,r,n){if(1===i)t.logger.info("leave room success");else{var o="leave room failed:, ".concat(r);t.logger.onError({c:Fe.TOP_ERROR,v:K.LEAVE_ROOM_FAILED},o,!0)}t.logger.buriedLog({c:Fe.LEAVE_SUCCESS},!0),t.state=B.Leaved,t.reset(),e(!0)}))})));case 4:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"publish",value:(p=P(U.mark((function e(t){var i,r,n=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.logger.info("publish stream"),t){e.next=5;break}throw this.logger.error("stream is undefined or null"),new ne({code:K.INVALID_OPERATION,message:"stream is undefined or null"});case 5:if(!(i=[F.Publishing,F.Published]).includes(t.getPubState("audio"))||!i.includes(t.getPubState("video"))){e.next=8;break}throw new ne({code:K.INVALID_OPERATION,message:"duplicate publishing, please unpublish and then re-publish"});case 8:if("live"!==this.mode||"audience"!==this.role){e.next=10;break}throw new ne({code:K.INVALID_OPERATION,message:'no permission to publish() under live/audience, please call swithRole("anchor") firstly before publish()'});case 10:if(t.mediaStream){e.next=12;break}throw new ne({code:K.INVALID_OPERATION,message:"stream not initialized!"});case 12:return this.logger.buriedLog({c:t.screen?Fe.PUBLISH_STREAM_SCREEN:Fe.PUBLISH_STREAM}),r=t.mediaStream.getTracks().map((function(e){return new Promise((function(r,o){var s=new MediaStream;s.addTrack(e);var a="audio"===e.kind,c=a&&!e.enabled,u="video"===e.kind,d=u&&!e.enabled;if(i.includes(t.getPubState(e.kind)))n.logger.warn("".concat(a?"audio":"video"," is publishing or published"));else{t.setPubState(e.kind,F.Publishing);var l={localStream:t,mediaStream:s,screen:t.screen,hasAudio:a,audioMuted:c,hasVideo:u,videoMuted:d,bitrate:t.getBitrate()};n.doPublish(l,(function(i,s,c){if(n.logger.info("xsigo client publish stream success",c&&c.streamId),window.initStream&&(window.initStream=null),t.getPubState(e.kind)!==F.Unpublished)if(1===i)t.published||(t.published=!0,t.roomId=c.roomId,t.streamId=c.streamId,t.xsigoClient=n.xsigoClient,-1===n.localStreams.findIndex((function(e){return e.streamId===c.streamId}))&&(n.localStreams.push(t),t.onTrackAdd(n.onAddTrack.bind(n)),t.onTrackRemove(n.onRemoveTrack.bind(n)),t.onSwitchDevice(n.onReplaceTrack.bind(n)),t.onReplaceTrack(n.onReplaceTrack.bind(n)))),a&&(t.setHasAudio(!!a),t.setAudioStreamId(c.streamId)),u&&(t.setHasVideo(!!u),t.setVideoStreamId(c.streamId)),n.logger.buriedLog({c:t.screen?Fe.PUBLISH_STREAM_SCREEN_SUCCESS:Fe.PUBLISH_STREAM_SUCCESS,v:a?"audio":"video"}),t.setPubState(e.kind,F.Published),r(t);else if(n.logger.buriedLog({c:t.screen?Fe.PUBLISH_STREAM_SCREEN_FAILED:Fe.PUBLISH_STREAM_FAILED,v:a?"audio":"video"}),c&&n.publications.delete(c.streamId),t.setPubState(e.kind,F.Create),"H264 not supported"===s){n.logger.onError({c:Fe.TOP_ERROR,v:K.H264_NOT_SUPPORTED});var d=new ne({code:K.H264_NOT_SUPPORTED,message:"publish stream failed h264 not supported"});n._emitter.emit(G,d),o(d)}else n.logger.onError({c:Fe.TOP_ERROR,v:K.PUBLISH_STREAM_FAILED},s),o(new ne({code:K.PUBLISH_STREAM_FAILED,message:s}))}))}}))})),e.abrupt("return",Promise.all(r));case 16:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"onAddTrack",value:function(e){var t=this,i=e.track,r=e.streamId,n=new MediaStream;n.addTrack(i);var o="audio"===i.kind,s=o&&!i.enabled,a="video"===i.kind,c=a&&!i.enabled,u=this.localStreams.find((function(e){return e.streamId===r}));if(this.logger.info("PublishState",u&&u.getPubState(i.kind)),u&&![F.Publishing,F.Published].includes(u.getPubState(i.kind))){var d={localStream:u,screen:!1,hasAudio:o,audioMuted:s,hasVideo:a,videoMuted:c,mediaStream:n,bitrate:u.getBitrate()};u.setPubState(i.kind,F.Publishing),this.doPublish(d,(function(e,r,n){u.getPubState(i.kind)!==F.Unpublished&&(1===e?(u.mediaStream.addTrack(i),u.published=!0,window.initStream&&(window.initStream=null),o&&(u.setHasAudio(o),u.setAudioStreamId(n.streamId),u.setAudioTrack(i)),a&&(u.setHasVideo(a),u.setVideoStreamId(n.streamId),u.setVideoTrack(i)),u.setPubState(i.kind,F.Published)):(n&&t.publications.delete(n.streamId),u.setPubState(i.kind,F.Create)),t.logger.buriedLog({c:1===e?Fe.PUBLISH_STREAM_SUCCESS:Fe.PUBLISH_STREAM_FAILED,v:o?"addAudioTrack":"addVideoTrack"}),u._emitter.emit("stream-track-update-result",{code:e,message:r}))}))}else i.stop(),this.logger.warn(u?"same track is publishing or published":"stream is not published")}},{key:"doPublish",value:(h=P(U.mark((function e(t,i){var r,n,o,s,a,c;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,s=(r=t||{}).localStream,a=new ht({roomId:this.roomId,userId:this.userId,mediaStream:r.mediaStream,screen:r.screen,bitrate:r.bitrate,isEnableSmallStream:this.isEnableSmallStream,smallStreamConfig:this.smallStreamConfig,hasAudio:n=r.hasAudio,audioMuted:r.audioMuted,hasVideo:o=r.hasVideo,videoMuted:r.videoMuted,logger:this.logger,xsigoClient:this.xsigoClient,onPublish:i}),e.next=5,a.publish();case 5:"string"!=typeof(c=e.sent)||this.publications.has(c)||(a.onPublishPeerConnectionFailed(this.onPublishPeerConnectionFailed.bind(this)),this.publications.set(c,a),s.streamId=c,n&&s.setAudioStreamId(c),o&&s.setVideoStreamId(c)),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),i&&i(0,e.t0);case 12:case"end":return e.stop()}}),e,this,[[0,9]])}))),function(e,t){return h.apply(this,arguments)})},{key:"onPublishPeerConnectionFailed",value:function(e){var t=this,i=e.state,r=e.streamId,n=this.xsigoClient.getWsState(this.roomId);if(this.publications.has(r)){var o=this.localStreams.find((function(e){return[e.audioStreamId,e.videoStreamId].includes(r)}));o&&("failed"===i&&n&&["CONNECTED","RECOVERY"].includes(n.state)?(this.logger.warn("publish peerConnection failed try to republish streamId:".concat(r)),o.updatePeerConnectionFailed(i),this.doUnpublish(o,r).then((function(){var e=new MediaStream,i=o.audioStreamId===r,n=o.videoStreamId===r,s=i?o.getAudioTrack():o.getVideoTrack(),a=i&&!s.enabled,c=n&&!s.enabled;t.logger.info("publish ".concat(i?"audio":"video",",trackId:").concat(s&&s.id)),e.addTrack(s);var u={localStream:o,screen:o.screen,hasAudio:i,audioMuted:a,hasVideo:n,videoMuted:c,mediaStream:e,bitrate:o.getBitrate()};t.doPublish(u,(function(e,r,s){1===e?(o.published=!0,i&&(o.setHasAudio(i),o.setAudioStreamId(s.streamId)),n&&(o.setHasVideo(n),o.setVideoStreamId(s.streamId))):s&&t.publications.delete(s.streamId)}))}))):"connected"===i&&o.updatePeerConnectionFailed(i))}}},{key:"onRemoveTrack",value:function(e){var t=this,i=e.track,r=e.streamId,n=i.kind,o=this.localStreams.find((function(e){return e.streamId===r}));if(o&&![F.Create,F.Unpublished].includes(o.getPubState(i.kind))){var s="audio"===n?o.audioStreamId:o.videoStreamId;o.mediaStream.removeTrack(i),"audio"===n&&o.setHasAudio(!1),"video"===n&&o.setHasVideo(!1),this.doUnpublish(o,s,(function(e,i){1===e&&t.logger.info("remove track success"),o._emitter.emit("stream-track-update-result",{code:e,message:i})}))}else this.logger.warn("stream is not published")}},{key:"onReplaceTrack",value:function(e){var t=e.streamId,i=e.type,r=e.track,n=this.localStreams.find((function(e){return e.streamId===t}));if(n){var o="audio"===i?n.audioStreamId:n.videoStreamId;o&&this.publications.has(o)&&this.publications.get(o).replaceMediaStreamTrack(r)}}},{key:"unpublish",value:function(e){var t=this;if(this.logger.info("unpublish stream"),!e)throw this.logger.error("stream is undefined or null"),new ne({code:K.INVALID_OPERATION,message:"stream is undefined or null"});this.logger.buriedLog({c:e.screen?Fe.UNPUBLISH_STREAM_SCREEN:Fe.UNPUBLISH_STREAM});var i,r=[],n=_i(this.publications.keys());try{for(n.s();!(i=n.n()).done;){var o=i.value;[e.audioStreamId,e.videoStreamId].includes(o)&&r.push(this.doUnpublish(e,o))}}catch(e){n.e(e)}finally{n.f()}return Promise.all(r).then((function(){var i=t.localStreams.findIndex((function(t){return t.getId()===e.getId()}));-1!==i&&t.localStreams.splice(i,1)}))}},{key:"doUnpublish",value:function(e,t,i){var r=this;return new Promise((function(n,o){var s=r.publications.get(t);if(s){r.publications.delete(t),e.audioStreamId===t&&e.setHasAudio(!1),e.videoStreamId===t&&e.setHasVideo(!1);var a=e.screen?"share_".concat(e.getUserId()):e.getUserId();if(e.hasAudio()||e.hasVideo()||(e.published=!1),e.setPubState(e.audioStreamId===t?"audio":"video",F.Unpublished),r.senderStats.has(a)){var c=Ei({},r.senderStats.get(a));e.hasAudio()||(c.audio={bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0}),e.hasVideo()||(c.video={bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0},c.smallVideo={bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0}),r.senderStats.set(a,c)}n(!0),s.unpublish((function(n,o,s){1===n?(r.logger.info("unpublish stream success",t),i&&i(n,o),r.logger.buriedLog({c:e.screen?Fe.UNPUBLISH_STREAM_SCREEN_SUCCESS:Fe.UNPUBLISH_STREAM_SUCCESS,v:e.audioStreamId===t?"audio":"video"})):(r.logger.buriedLog({c:e.screen?Fe.UNPUBLISH_STREAM_SCREEN_FAILED:Fe.UNPUBLISH_STREAM_FAILED,v:e.audioStreamId===t?"audio":"video"}),i&&i(n,o),r.logger.onError({c:Fe.TOP_ERROR,v:K.UNPUBLISH_STREAM_FAILED},"unpublish stream with response:, ".concat(n,",").concat(o)))}))}else r.logger.warn("stream is not published",t),n(!0)}))}},{key:"subscribe",value:(l=P(U.mark((function e(t,i){var r,n,o;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}throw this.logger.error("stream is undefined or null"),new ne({code:K.INVALID_OPERATION,message:"stream is undefined or null"});case 3:if(t.isRemote){e.next=6;break}throw this.logger.error("try to subscribe a local stream"),new ne({code:K.INVALID_OPERATION,message:"try to subscribe a local stream"});case 6:if(!(r=[j.Subscribing,j.Subscribed]).includes(t.getSubState("audio"))||!r.includes(t.getSubState("video"))){e.next=10;break}throw this.logger.error("Stream already subscribing or subscribed"),new ne({code:K.INVALID_OPERATION,message:"Stream already subscribing or subscribed"});case 10:return!i&&(i={audio:!0,video:!0,small:!1}),n=t.getUserSeq(),t=this.remoteStreams.get(n),this.logger.info("subscribe with options:",JSON.stringify(i,null,4),t),this.subscribeManager.setSubscriptionOpts(n,i),o=[],t&&t.audioStreamId&&!r.includes(t.getSubState("audio"))?i.audio&&o.push(this.doSubscribe(t,{audio:!0,video:!1,small:!!i.small&&i.small})):r.includes(t.getSubState("audio"))&&this.logger.warn("audio is subscribing or subscribed"),t&&t.videoStreamId&&!r.includes(t.getSubState("video"))?i.video&&o.push(this.doSubscribe(t,{audio:!1,video:!0,small:!!i.small&&i.small})):r.includes(t.getSubState("video"))&&this.logger.warn("video is subscribing or subscribed"),e.abrupt("return",Promise.all(o));case 19:case"end":return e.stop()}}),e,this)}))),function(e,t){return l.apply(this,arguments)})},{key:"doSubscribe",value:(d=P(U.mark((function e(t,i,r){var n=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(){var e=P(U.mark((function e(o,s){var a,c,u,d,l,h,p,f,m,g;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n.logger.info("doSubscribe options",JSON.stringify(i,null,4)),a=t.getUserSeq(),c=t.getSimulcasts(),l=!!i.small&&i.small,n.logger.info("---do subscribe options",u=i.audio?t.audio:i.audio,d=i.video?t.video:i.video),n.logger.buriedLog({c:t.getType()===H?Fe.SUBSCRIBE_STREAM_SCREEN:Fe.SUBSCRIBE_STREAM,v:"uid:".concat(t.getUserId(),",").concat(u?"audio":"video")}),t.setSubState(u?"audio":"video",j.Subscribing),u&&n.subscribeManager.updateSubscriptedState(a,{audio:u,small:l}),d&&n.subscribeManager.updateSubscriptedState(a,{video:d,small:l}),h=0,p=0,f=new Lt({userId:t.userId,publisherUserId:t.getUserId(),hasAudio:u,hasVideo:d,simulcast:c,audioStreamId:u?t.audioStreamId:null,videoStreamId:d?t.videoStreamId:null,logger:n.logger,xsigoClient:n.xsigoClient,roomId:n.roomId,small:l,onRemoteStream:function(e,i,s,c){if(!(u&&t.getSubState("audio")===j.Unsubscribed||d&&t.getSubState("video")===j.Unsubscribed)){var l=n.remoteStreams.get(a);if("audio"===i.kind&&h++,"video"===i.kind&&p++,l){l.setIsAlphaChannels(c),l.mediaStream||l.setMediaStream(e),"audio"===i.kind&&(h>1||r||!l.getAudioTrack())&&(l.updateTrack("audio",i),r||h>1?l.setAudioTrack(i):l.restartAudio()),"video"===i.kind&&(p>1||r||!l.getVideoTrack())&&(l.updateTrack("video",i),r||p>1?l.setVideoTrack(i):l.restartVideo()),n.subscribeManager.addSubscriptionRecord(a,l);var f=t.getType()===H?"share_".concat(t.getUserId()):t.getUserId();if(l.subscribed){n._emitter.emit("stream-updated",{stream:l});var m="";if(s===_e.AudioOnly){var g=n.remoteStreams.values().next().value.audioOutputDeviceId||"default";l.setAudioOutput(g),m=l.audioMuted?"mute-audio":"unmute-audio"}else s===_e.VideoOnly&&(m=l.videoMuted?"mute-video":"unmute-video");n._emitter.emit(m,{userId:f})}else if(l.subscribed=!0,n.logger.buriedLog({c:t.getType()===H?Fe.ON_STREAM_SUBSCRIBED_SCREEN:Fe.ON_STREAM_SUBSCRIBED,v:"uid:".concat(t.getUserId())}),n._emitter.emit("stream-subscribed",{stream:l}),l.getType()===H&&n.isWaterMark&&l.startWaterMark(n.waterMarkoptions,n.waterMarkImage),s===_e.AudioOnly){var v=n.remoteStreams.values().next().value.audioOutputDeviceId||"default";l.setAudioOutput(v),n._emitter.emit(l.audioMuted?"mute-audio":"unmute-audio",{userId:f})}else s===_e.VideoOnly&&n._emitter.emit(l.videoMuted?"mute-video":"unmute-video",{userId:f});t.setSubState(u?"audio":"video",j.Subscribed),o(!0)}}},onSubscribe:function(e,i,r){var o=n.subscribeManager.getSubscriptedState(a);if(!(u&&t.getSubState("audio")===j.Unsubscribed||d&&t.getSubState("video")===j.Unsubscribed))if(1===e)u&&(o.audio=!0),d&&(o.video=!0),n.subscribeManager.updateSubscriptedState(a,o),c.length&&(l?c.find((function(e){return e.type===Re.SmallStream}))&&t.setSimulcastType(Re.SmallStream):t.setSimulcastType(c[0].type)),n.logger.info("subscribe stream success"),n.logger.buriedLog({c:t.getType()===H?Fe.SUBSCRIBE_STREAM_SCREEN_SUCCESS:Fe.SUBSCRIBE_STREAM_SUCCESS,v:"uid:".concat(t.getUserId(),",").concat(u?"audio":"video")});else{n.logger.onError({c:Fe.TOP_ERROR,v:K.SUBSCRIBE_FAILED},"on subscribe stream with response:, ".concat(e,", ").concat(i)),n.logger.buriedLog({c:t.getType()===H?Fe.SUBSCRIBE_STREAM_SCREEN_FAILED:Fe.SUBSCRIBE_STREAM_FAILED,v:"uid:".concat(t.getUserId(),",").concat(u?"audio":"video")}),n.subscriptions.delete(r.subscriptionId),t.setSubState(u?"audio":"video",j.Create);var h=n.subscribeManager.getSubscriptedState(a),p=n.subscribeManager.needSubscribeKind(a);p===_e.AudioVideo&&h.audio&&h.video&&(u&&n.subscribeManager.updateSubscriptedState(a,{audio:!1}),d&&n.subscribeManager.updateSubscriptedState(a,{video:!1})),p===_e.VideoOnly&&d&&n.subscribeManager.updateSubscriptedState(a,{video:!1}),p===_e.AudioVideo&&u&&n.subscribeManager.updateSubscriptedState(a,{audio:!1}),s(new ne({code:K.SUBSCRIBE_FAILED,message:i}))}}}),e.next=17,f.subscribe();case 17:if(m=e.sent,n.logger.info("time Date.now doSubscribe",n.remoteStreams.has(a),t.audioStreamId,t.videoStreamId),!u||t.audioStreamId){e.next=21;break}return e.abrupt("return",f.close());case 21:if(!d||t.videoStreamId){e.next=23;break}return e.abrupt("return",f.close());case 23:n.subscriptions.has(m)||(f.onSubscribePeerConnectionFailed(n.onSubscribePeerConnectionFailed.bind(n)),n.subscriptions.set(m,{subscriber:f,stream:t})),u&&t.setAudioSubscriptionId(m),d&&t.setVideoSubscriptionId(m),e.next=31;break;case 28:e.prev=28,e.t0=e.catch(0),"H264 not supported"===e.t0?(n.logger.onError({c:Fe.TOP_ERROR,v:K.H264_NOT_SUPPORTED},"subscribe stream failed h264 not supported"),g=new ne({code:K.H264_NOT_SUPPORTED,message:"subscribe stream failed h264 not supported"}),n._emitter.emit(G,g)):(n.logger.onError({c:Fe.TOP_ERROR,v:K.SUBSCRIBE_FAILED},"subscribe stream error:, ".concat(e.t0,"}")),s(new ne({code:K.SUBSCRIBE_FAILED,message:e.t0})));case 31:case"end":return e.stop()}}),e,null,[[0,28]])})));return function(t,i){return e.apply(this,arguments)}}()));case 1:case"end":return e.stop()}}),e)}))),function(e,t,i){return d.apply(this,arguments)})},{key:"onSubscribePeerConnectionFailed",value:function(e){var t=this,i=e.state,r=e.subscriptionId,n=this.xsigoClient.getWsState(this.roomId);if(this.subscriptions.has(r)){var o=this.subscribeManager.getSubscriptionOpts(this.userId),s=this.subscriptions.get(r).stream;if(s)if("failed"===i&&n&&["CONNECTED","RECOVERY"].includes(n.state)){this.logger.warn("subscribe peerConnection failed try to resubscribe subscriptionId:".concat(r)),s.updatePeerConnectionFailed(i);var a=s.audioSubscriptionId,c=s.videoSubscriptionId;this.doUnsubscribe(s,r).then((function(){a===r&&o.audio&&t.doSubscribe(s,{audio:!0,video:!1,small:!!o.small&&o.small},!0),c===r&&o.video&&t.doSubscribe(s,{audio:!1,video:!0,small:!!o.small&&o.small},!0)}))}else"connected"===i&&s.updatePeerConnectionFailed(i)}}},{key:"unsubscribe",value:function(e){if(!e)throw this.logger.error("stream is undefined or null"),new ne({code:K.INVALID_OPERATION,message:"stream is undefined or null"});var t=[],i=this.subscribeManager.getSubscriptionOpts(e.getUserSeq());return e.setSubState("audio",j.Unsubscribed),e.setSubState("video",j.Unsubscribed),i.audio&&e.audioSubscriptionId&&(t.push(this.doUnsubscribe(e,e.audioSubscriptionId)),e.setEnableTrackFlag("audio",!0)),i.video&&e.videoSubscriptionId&&(t.push(this.doUnsubscribe(e,e.videoSubscriptionId)),e.setEnableTrackFlag("video",!0)),Promise.all(t)}},{key:"doUnsubscribe",value:function(e,t){var i=this,r=e.audioSubscriptionId&&e.audioSubscriptionId===t,n=e.videoSubscriptionId&&e.videoSubscriptionId===t;this.logger.buriedLog({c:e.getType()===H?Fe.UNSUBSCRIBE_STREAM_SCREEN:Fe.UNSUBSCRIBE_STREAM,v:"uid:".concat(e.getUserId(),",").concat(r?"audio":"video")});var o=e.getUserSeq(),s=this.remoteStreams.get(o),a=this.subscribeManager.getSubscriptedState(o);return s&&r&&(s.getAudioTrack()&&s.mediaStream.removeTrack(s.getAudioTrack()),s.setSubState("audio",j.Unsubscribed),e.setAudioSubscriptionId(null),this.subscribeManager.updateSubscriptedState(o,Ei(Ei({},a),{},{audio:!1,small:!1}))),s&&n&&(s.getVideoTrack()&&s.mediaStream.removeTrack(s.getVideoTrack()),s.setSubState("video",j.Unsubscribed),e.setVideoSubscriptionId(null),this.subscribeManager.updateSubscriptedState(o,Ei(Ei({},a),{},{video:!1,small:!1}))),!s||e.getAudioTrack()||e.getVideoTrack()||(s.subscribed=!1,s.mediaStream=null),this.receiverStats.has(o)&&this.receiverStats.delete(o),new Promise((function(n,o){if(i.subscriptions.has(t)){var s=i.subscriptions.get(t);i.subscriptions.delete(t),n(!0),s.subscriber.unsubscribe((function(t,n,o){1===t?(i.logger.info("unsubscribe stream success"),i.logger.buriedLog({c:e.getType()===H?Fe.UNSUBSCRIBE_STREAM_SCREEN_SUCCESS:Fe.UNSUBSCRIBE_STREAM_SUCCESS,v:"uid:".concat(e.getUserId(),",").concat(r?"audio":"video")})):(i.logger.buriedLog({c:e.getType()===H?Fe.UNSUBSCRIBE_STREAM_SCREEN_FAILED:Fe.UNSUBSCRIBE_STREAM_FAILED,v:"uid:".concat(e.getUserId(),",").concat(r?"audio":"video")}),i.logger.onError({c:Fe.TOP_ERROR,v:K.UNSUBSCRIBE_FAILED},"unsubscribe stream with response:,".concat(t,", ").concat(n)))}))}else i.logger.warn("stream is not subscribed",e.getUserId()),i.logger.buriedLog({c:e.getType()===H?Fe.UNSUBSCRIBE_STREAM_SCREEN_SUCCESS:Fe.UNSUBSCRIBE_STREAM_SUCCESS,v:"uid:".concat(e.getUserId(),",").concat(r?"audio":"video")}),n(!0)}))}},{key:"updateSimulcast",value:function(e,t){var i=this;return new Promise((function(r,n){var o=i.publications.get(e.videoStreamId);if(!o&&e.screen)throw new ne({code:K.INVALID_OPERATION,message:"stream is invalid"});var s=t.map((function(e){return{type:Ne(e.rid),maxWidth:e.maxWidth,maxHeight:e.maxHeight}})),a=e.getSimulcasts();if(JSON.stringify(s)===JSON.stringify(a))return i.logger.warn("simulcast  ".concat(t," is same"));i.logger.info("Update Simulcast ".concat(t)),o.updateSimulcast(s,(function(e,o,s){if(1===e)r(!0),i.logger.info("Update Simulcast ".concat(t," Success"));else{i.logger.onError({c:Fe.TOP_ERROR,v:K.LOCAL_SWITCH_SIMULCAST});var a=new ne({code:K.LOCAL_SWITCH_SIMULCAST,message:o});n(a)}}))}))}},{key:"setRemoteVideoStreamType",value:function(e,t){var i=this;if(!e||!t)throw this.logger.error("stream or status is undefined or null"),new ne({code:K.INVALID_OPERATION,message:"stream or status is undefined or null"});return new Promise((function(r,n){var o={big:"h",small:"l"}[t];if(!o)throw new ne({code:K.INVALID_OPERATION,message:"status: ".concat(t," is invalid")});var s=i.getRemoteMutedState().filter((function(t){return t.userId===e.getUserId()}))[0];if("small"===t&&s&&!s.hasSmall)throw new ne({code:K.INVALID_OPERATION,message:"does not publish small stream"});var a=i.subscriptions.get(e.videoSubscriptionId);if(!a)throw new ne({code:K.INVALID_OPERATION,message:"remoteStream is invalid"});var c=Ne(o);if(e.getSimulcastType()===c)return i.logger.warn("status ".concat(t," is same"));i.logger.info("Set Remote Video Stream Type ".concat(t)),i.logger.buriedLog({c:c===Re.SmallStream?Fe.SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL:Fe.SET_REMOTE_VIDEO_STREAM_TYPE_BIG}),a.subscriber.switchSimulcast(c,(function(o,s,a){if(1===o)e.setSimulcastType(c),i.logger.info("Set Remote Video Stream Type ".concat(t," Success")),i.logger.buriedLog({c:c===Re.SmallStream?Fe.SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL_SUCCESS:Fe.SET_REMOTE_VIDEO_STREAM_TYPE_BIG_SUCCESSE}),r(!0);else{var u=new ne({code:K.REMOTE_SWITCH_SIMULCAST,message:s});i.logger.onError({c:Fe.TOP_ERROR,v:K.LOCAL_SWITCH_SIMULCAST},s),i.logger.buriedLog({c:c===Re.SmallStream?Fe.SET_REMOTE_VIDEO_STREAM_TYPE_SAMLL_FAILED:Fe.SET_REMOTE_VIDEO_STREAM_TYPE_BIG_FAILED}),n(u)}}))}))}},{key:"switchRole",value:(u=P(U.mark((function e(t){var i,r,n,o=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new ne({code:K.INVALID_OPERATION,message:"role is undefined or null"});case 2:if("rtc"!==this.mode){e.next=4;break}throw new ne({code:K.INVALID_OPERATION,message:"role is only valid in live mode"});case 4:if("anchor"===t||"audience"===t){e.next=7;break}throw this.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_PARAMETER}),new ne({code:K.INVALID_PARAMETER,message:"role could only be set to a value as anchor or audience"});case 7:if(t!==this.role){e.next=10;break}return this.logger.warn("can not switch the same role"),e.abrupt("return",Promise.resolve(!0));case 10:if(this.logger.buriedLog({c:"anchor"===t?Fe.SWITCH_ROLE_ANCHOR:Fe.SWITCH_ROLE_AUDIENCE}),"audience"===t){i=_i(this.publications.keys());try{for(n=function(){var e=r.value,t=o.localStreams.find((function(t){return[t.audioStreamId,t.videoStreamId].includes(e)}));t&&o.doUnpublish(t,e)},i.s();!(r=i.n()).done;)n()}catch(e){i.e(e)}finally{i.f()}}return e.abrupt("return",new Promise(function(){var e=P(U.mark((function e(i,r){return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o.xsigoClient.switchPermission(o.roomId,V.get(t),(function(e,n,s){if(1===e)o.logger.info("switch role from ".concat(o.role," to ").concat(t)),o.role=t,o.localStreams=[],o.logger.buriedLog({c:"anchor"===t?Fe.SWITCH_ROLE_ANCHOR_SUCCESS:Fe.SWITCH_ROLE_AUDIENCE_SUCCESS}),i(!0);else{o.logger.buriedLog({c:"anchor"===t?Fe.SWITCH_ROLE_ANCHOR_FAILED:Fe.SWITCH_ROLE_AUDIENCE_FAILED}),o.logger.onError({c:Fe.TOP_ERROR,v:K.SWITCH_ROLE_ERROR});var a=new ne({code:K.SWITCH_ROLE_ERROR,message:n});r(a)}}));case 1:case"end":return e.stop()}}),e)})));return function(t,i){return e.apply(this,arguments)}}()));case 13:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"on",value:function(e,t){this._emitter.on(e,t)}},{key:"off",value:function(e,t){this.logger&&this.logger.buriedLog({c:Fe["OFF_".concat(e.replace("-","_").toUpperCase())]}),this._emitter.off(e,t)}},{key:"getRemoteMutedState",value:function(){this.logger.buriedLog({c:Fe.GET_REMOTE_MUTED_STATE});var e,t=[],i=_i(this._remoteMutedStateMap);try{for(i.s();!(e=i.n()).done;){var r=w(e.value,2);t.push(Ei({userId:r[0]},r[1]))}}catch(e){i.e(e)}finally{i.f()}return t.filter((function(e){return!e.userId.includes("share_")}))}},{key:"updateRemoteMutedState",value:function(e,t){if(![e,"share_".concat(e)].includes(this.userId)){var i={hasAudio:!1,hasVideo:!1,audioMuted:!0,videoMuted:!0,hasSmall:!1};t=t||i;var r=this._remoteMutedStateMap.get(e)||i;this._remoteMutedStateMap.set(e,Ei(Ei({},r),t))}}},{key:"getTransportStats",value:(c=P(U.mark((function e(){var t,i,r,n=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.publications.size){e.next=2;break}throw new ne({code:K.INVALID_OPERATION,message:"local stream is not published"});case 2:t=null,i=_i(this.publications.values()),e.prev=4,i.s();case 6:if((r=i.n()).done){e.next=12;break}return t=r.value,e.abrupt("break",12);case 10:e.next=6;break;case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(4),i.e(e.t0);case 17:return e.prev=17,i.f(),e.finish(17);case 20:return e.abrupt("return",new Promise((function(e,i){t.getTransportStats().then((function(i){var r=t.userId,o=0;if(n.senderStats.has(r)){var s=n.senderStats.get(r);s.video.timestamp?o=s.video.packetLossRate:s.audio.timestamp?o=s.audio.packetLossRate:s.smallVideo.timestamp&&(o=s.smallVideo.packetLossRate)}i.packetLossRate=o,e(i)})).catch((function(e){n.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_TRANSPORT_STATA});var t=new ne({code:K.INVALID_TRANSPORT_STATA,message:e});i(t)}))})));case 21:case"end":return e.stop()}}),e,this,[[4,14,17,20]])}))),function(){return c.apply(this,arguments)})},{key:"getRemoteTransportStats",value:(a=P(U.mark((function e(){var t=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){try{var i,r=[],n=[],o=[],s=function(e,t,i){e.getTransportStats().then((function(e){-1===o.findIndex((function(e){return e.userId===t}))&&o.push({userId:t,packetLossRate:i,rtt:e})})).catch((function(){-1===o.findIndex((function(e){return e.userId===t}))&&o.push({userId:t,packetLossRate:-1,rtt:-1})}))},a=_i(t.subscriptions.values());try{for(a.s();!(i=a.n()).done;){var c=i.value,u=c.subscriber.userId,d=0;if(t.receiverStats.has(u)){var l=t.receiverStats.get(u);l.video.timestamp?s(c.subscriber,u,d=l.video.packetLossRate):l.audio.timestamp&&s(c.subscriber,u,d=l.audio.packetLossRate)}n.push(d),r.push(c.subscriber.getTransportStats())}}catch(e){a.e(e)}finally{a.f()}Promise.all(r).then((function(i){k(t._remoteMutedStateMap.keys()).forEach((function(e){-1===o.findIndex((function(t){return t.userId===e}))&&o.push({userId:e,packetLossRate:-1,rtt:-1})}));var r=i.length>0?i.reduce((function(e,t){return e+t}))/i.length:-1,s=n.length>0?n.reduce((function(e,t){return e+t}))/n.length:-1;e({packetLossRate:s,rtt:r,list:o})}))}catch(i){var h=k(t._remoteMutedStateMap.keys()).map((function(e){return{userId:e,packetLossRate:-1,rtt:-1}}));e({packetLossRate:-1,rtt:-1,list:h})}})));case 1:case"end":return e.stop()}}),e)}))),function(){return a.apply(this,arguments)})},{key:"getNetworkQuality",value:function(){var e=this;if(-1===this._interval){var t={uplinkNetworkQuality:0,downlinkNetworkQuality:0,downlinkNetworkQualityList:[]};this._interval=window.setInterval((function(){if(e.xsigoClient){var i,r=e.xsigoClient.getWsState(e.roomId),n=r||{},o=n.state;r&&("DISCONNECTED"===o||"RECONNECTING"===o||"CONNECTING"===o&&"RECONNECTING"===n.prevState)?(t.uplinkNetworkQuality=6,t.downlinkNetworkQuality=6,t.downlinkNetworkQualityList=k(e._remoteMutedStateMap.keys()).map((function(e){return{userId:e,downlinkNetworkQuality:6}}))):(e.getTransportStats().then((function(i){t.uplinkNetworkQuality=e.networkLevel(i.packetLossRate,i.rtt)})).catch((function(){t.uplinkNetworkQuality=0})),e.getRemoteTransportStats().then((function(i){t.downlinkNetworkQuality=e.networkLevel(i.packetLossRate,i.rtt),i.list&&(t.downlinkNetworkQualityList=i.list.map((function(t){return{userId:t.userId,downlinkNetworkQuality:e.networkLevel(t.packetLossRate,t.rtt)}})))})).catch((function(e){t.downlinkNetworkQuality=0,t.downlinkNetworkQualityList=e.list.map((function(e){return{userId:e.userId,downlinkNetworkQuality:0}}))}))),(t.uplinkNetworkQuality>=3||t.downlinkNetworkQuality>=3)&&e.logger.warn("network-quality",JSON.stringify(t,null,4)),e._emitter.emit("network-quality",t),null!==(i=e.appConfig)&&void 0!==i&&i.enableEvent&&(e.getSenderStats(),e.getReceiverStats())}}),2e3)}else this.logger.warn("network quality calculating is already started")}},{key:"getSenderStats",value:(s=P(U.mark((function e(){var t,i,r,n,o,s,a,c,u,d,l,h,p,f,m,g,v,b,y,S,E,_,C,I,T,R,k,O,A,P,L,D,x,M,N,V,B,F,j,W,H,G,z,J,K;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=_i(this.publications.entries()),e.prev=1,t.s();case 3:if((i=t.n()).done){e.next=27;break}r=w(i.value,2),n=r[0],o=r[1],e.t0=U.keys(this.localStreams);case 6:if((e.t1=e.t0()).done){e.next=25;break}if(a=(s=this.localStreams[e.t1.value]).screen?"share_".concat(s.getUserId()):s.getUserId(),this.senderStats.has(a)||this.senderStats.set(a,{audio:{bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0},video:{bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0},smallVideo:{bytesSent:0,timestamp:0,retransmittedPacketsSent:0,packetsSent:0,packetLossRate:0}}),!s.audioStreamId||s.audioStreamId!==n||s.screen){e.next=18;break}return c=this.senderStats.get(a),d=(u=c.audio).bytesSent,l=u.timestamp,h=u.retransmittedPacketsSent,p=u.packetsSent,e.next=16,o.getLocalStats("audio");case 16:(f=e.sent)&&(g=(f.timestamp-l)/1e3,b=(v=(m=f.audio).bytesSent-d)<=0?0:Number((8*v/g/1024).toFixed(2)),S=(y=m.retransmittedPacketsSent-h)<=0?0:parseFloat((y/(m.packetsSent-p)).toFixed(6)),this.senderStats.set(a,Ei(Ei({},c),{},{audio:{bytesSent:m.bytesSent,timestamp:f.timestamp,packetsSent:m.packetsSent,retransmittedPacketsSent:m.retransmittedPacketsSent,packetLossRate:S}})),this.logger.mediaLog({med_type:"mic",pub:!0,ruid:this.roomUniqueId,uid:this.userId,streams:[{rate:b,lost:S,rtt:f.rtt}]}),E=this.xsigoClient.getWsState(this.roomId),["CONNECTED","RECOVERY"].includes((E||{}).state)&&0===m.bytesSent&&o.updateBytesSentIs0Count("audio"));case 18:if(!s.videoStreamId||s.videoStreamId!==n){e.next=23;break}return e.next=21,o.getLocalStats("video");case 21:(_=e.sent)&&(C={med_type:s.screen?"screen":"camera",pub:!0,ruid:this.roomUniqueId,uid:this.userId,sess_id:n,streams:[]},I=this.senderStats.get(a),k=(T=I.video).retransmittedPacketsSent,O=T.packetsSent,P=(_.timestamp-T.timestamp)/1e3,D=(L=(A=_.video).bytesSent-(R=T.bytesSent))<=0?0:(8*L/P/1024).toFixed(2),this.logger.debug("video vStats.bytesSent:".concat(A.bytesSent,",bytesSent:").concat(R,",bitrate:").concat(D)),M=(x=A.retransmittedPacketsSent-k)<=0?0:parseFloat((x/(A.packetsSent-O)).toFixed(6)),C.streams.push({rate:D,lost:M,rtt:_.rtt,fps:A.framesPerSecond,rid:A.rid,width:A.frameWidth,height:A.frameHeight}),N=I.smallVideo,!s.screen&&this.isEnableSmallStream&&(B=(V=N).retransmittedPacketsSent,F=V.packetsSent,W=(_.timestamp-V.timestamp)/1e3,G=(H=(j=_.smallVideo).bytesSent-V.bytesSent)<=0?0:(8*H/W/1024).toFixed(2),J=(z=j.retransmittedPacketsSent-B)<=0?0:parseFloat((z/(j.packetsSent-F)).toFixed(6)),N={bytesSent:j.bytesSent,timestamp:_.timestamp,packetsSent:j.packetsSent,retransmittedPacketsSent:j.retransmittedPacketsSent,packetLossRate:J},C.streams.push({rate:G,lost:J,rtt:_.rtt,fps:j.framesPerSecond,rid:j.rid,width:j.frameWidth,height:j.frameHeight})),this.senderStats.set(a,Ei(Ei({},I),{},{video:{bytesSent:A.bytesSent,timestamp:_.timestamp,packetsSent:A.packetsSent,retransmittedPacketsSent:A.retransmittedPacketsSent,packetLossRate:M},smallVideo:N})),this.logger.mediaLog(C),K=this.xsigoClient.getWsState(this.roomId),["CONNECTED","RECOVERY"].includes((K||{}).state)&&0===A.bytesSent&&o.updateBytesSentIs0Count("video"));case 23:e.next=6;break;case 25:e.next=3;break;case 27:e.next=32;break;case 29:e.prev=29,e.t2=e.catch(1),t.e(e.t2);case 32:return e.prev=32,t.f(),e.finish(32);case 35:case"end":return e.stop()}}),e,this,[[1,29,32,35]])}))),function(){return s.apply(this,arguments)})},{key:"getReceiverStats",value:(o=P(U.mark((function e(){var t,i,r,n,o,s,a,c,u,d,l,h,p,f,m,g,v,b,y,S,E,_,C,I,T,R,k,O,A,P,L,D,x,M,N,V,B,F,j,W,G;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=_i(this.subscriptions.entries()),e.prev=1,t.s();case 3:if((i=t.n()).done){e.next=25;break}if(r=w(i.value,2),n=r[0],s=(o=r[1]).stream.getUserSeq(),a=o.stream.getUserId(),this.receiverStats.has(s)||this.receiverStats.set(s,{audio:{bytesReceived:0,timestamp:0,packetsReceived:0,packetsLost:0,nackCount:0,packetLossRate:0},video:{bytesReceived:0,timestamp:0,packetsReceived:0,packetsLost:0,nackCount:0,packetLossRate:0}}),c=o.stream.getType(),n!==o.stream.audioSubscriptionId||c===H){e.next=16;break}return u=this.receiverStats.get(s),l=(d=u.audio).bytesReceived,h=d.timestamp,p=d.packetsLost,f=d.packetsReceived,m=d.nackCount,e.next=14,o.subscriber.getRemoteAudioOrVideoStats("audio");case 14:(g=e.sent)&&(b=(g.timestamp-h)/1e3,S=(y=(v=g.audio).bytesReceived-l)<=0?0:Number((8*y/b/1024).toFixed(2)),E=v.packetsLost-p,_=v.packetsReceived-f,0,I=(C=v.nackCount-m)<=0||_<=0?0:C>_?100:E<0?parseFloat((C/_).toFixed(6)):parseFloat(((E+C)/(E+_)).toFixed(6)),this.receiverStats.set(s,Ei(Ei({},u),{},{audio:{bytesReceived:v.bytesReceived,timestamp:g.timestamp,packetsReceived:v.packetsReceived,packetsLost:v.packetsLost,nackCount:v.nackCount,packetLossRate:I}})),this.logger.mediaLog({med_type:"mic",pub:!1,ruid:this.roomUniqueId,uid:a,streams:[{rate:S,lost:I,rtt:g.rtt}]}));case 16:if(n!==o.stream.videoSubscriptionId){e.next=23;break}return T=this.receiverStats.get(s),k=(R=T.video).bytesReceived,O=R.timestamp,A=R.packetsLost,P=R.packetsReceived,L=R.nackCount,e.next=21,o.subscriber.getRemoteAudioOrVideoStats("video");case 21:(D=e.sent)&&(M=(D.timestamp-O)/1e3,V=(N=(x=D.video).bytesReceived-k)<=0?0:Number((8*N/M/1024).toFixed(2)),B=x.packetsLost-A,F=x.packetsReceived-P,0,W=(j=x.nackCount-L)<=0||F<=0?0:j>F?100:B<0?parseFloat((j/F).toFixed(6)):parseFloat(((B+j)/(B+F)).toFixed(6)),this.receiverStats.set(s,Ei(Ei({},T),{},{video:{bytesReceived:x.bytesReceived,timestamp:D.timestamp,packetsReceived:x.packetsReceived,packetsLost:x.packetsLost,nackCount:x.nackCount,packetLossRate:W}})),G=o.stream.getSimulcastType(),this.logger.mediaLog({med_type:c!==H?"camera":"screen",pub:!1,ruid:this.roomUniqueId,uid:a,streams:[{rate:V,lost:W,rtt:D.rtt,fps:x.framesPerSecond,rid:G===Re.SmallStream?"l":"h",width:x.frameWidth,height:x.frameHeight}]}));case 23:e.next=3;break;case 25:e.next=30;break;case 27:e.prev=27,e.t0=e.catch(1),t.e(e.t0);case 30:return e.prev=30,t.f(),e.finish(30);case 33:case"end":return e.stop()}}),e,this,[[1,27,30,33]])}))),function(){return o.apply(this,arguments)})},{key:"networkLevel",value:function(e,t){return e>50||t>500?5:e>30||t>350?4:e>20||t>200?3:e>10||t>100?2:e>=0||t>=0?1:0}},{key:"getLocalAudioStats",value:function(){return this.getLocalStatsMap("audio")}},{key:"getLocalVideoStats",value:function(){return this.getLocalStatsMap("video")}},{key:"getLocalStatsMap",value:(n=P(U.mark((function e(t){var i,r,n,o,s,a,c,u,d,l,h,p,f,m,g,v,b,y;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.localStreams.some((function(e){return e.published&&("audio"===t?e.audioStreamId:e.videoStreamId)}))){e.next=3;break}throw new ne({code:K.INVALID_OPERATION,message:"local stream is not published"});case 3:i=new Map,e.prev=4,r=_i(this.publications.entries()),e.prev=6,r.s();case 8:if((n=r.n()).done){e.next=25;break}o=w(n.value,2),s=o[0],a=o[1],e.t0=U.keys(this.localStreams);case 11:if((e.t1=e.t0()).done){e.next=23;break}if(c=this.localStreams[e.t1.value],!("audio"===t?c.audioStreamId===s:c.videoStreamId===s)){e.next=21;break}return u=c.screen?"share_".concat(c.getUserId()):c.getUserId(),e.next=18,a.getLocalStats(t);case 18:d=e.sent,"audio"===t&&d&&(this.senderLocalStats.has(u)||this.senderLocalStats.set(u,{audio:{bytesSent:0,timestamp:0,packetsSent:0}}),l=this.senderLocalStats.get(u),f=(h=d[t]).packetsSent,m=(d.timestamp-l.audio.timestamp)/1e3,v=(g=(p=h.bytesSent)-l.audio.bytesSent)<=0?0:Number((8*g/m/1024).toFixed()),i.set(u,{bytesSent:p,packetsSent:f,bitrate:v}),this.senderLocalStats.set(c.getUserId(),Ei(Ei({},l),{},{audio:{bytesSent:p,timestamp:d.timestamp,packetsSent:f}}))),"video"===t&&d&&i.set(u,{bytesSent:(b=d[t]).bytesSent,packetsSent:b.packetsSent,framesEncoded:b.framesEncoded,frameWidth:b.frameWidth,frameHeight:b.frameHeight,framesSent:b.framesSent});case 21:e.next=11;break;case 23:e.next=8;break;case 25:e.next=30;break;case 27:e.prev=27,e.t2=e.catch(6),r.e(e.t2);case 30:return e.prev=30,r.f(),e.finish(30);case 33:return e.abrupt("return",Promise.resolve(i));case 36:return e.prev=36,e.t3=e.catch(4),this.logger.info("Get local ".concat(t," stats failed"),e.t3),this.logger.onError({c:Fe.TOP_ERROR,v:"audio"===t?K.LOCAL_AUDIO_STATA_ERROR:K.LOCAL_VIDEO_STATA_ERROR}),y=new ne({code:"audio"===t?K.LOCAL_AUDIO_STATA_ERROR:K.LOCAL_VIDEO_STATA_ERROR,message:e.t3.message}),e.abrupt("return",Promise.reject(y));case 42:case"end":return e.stop()}}),e,this,[[4,36],[6,27,30,33]])}))),function(e){return n.apply(this,arguments)})},{key:"getRemoteAudioStats",value:function(){var e=this;return new Promise(function(){var t=P(U.mark((function t(i,r){var n,o,s,a,c,u,d,l,h;return U.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=new Map,t.prev=1,e.logger.info("get remote audio Stats",e.subscriptions),o=_i(e.subscriptions.entries()),t.prev=4,o.s();case 6:if((s=o.n()).done){t.next=16;break}if(a=w(s.value,2),c=a[0],d=(u=a[1]).stream.getUserSeq(),c!==u.stream.audioSubscriptionId){t.next=14;break}return t.next=12,u.subscriber.getRemoteAudioOrVideoStats("audio");case 12:(l=t.sent)&&n.set(d,l.audio);case 14:t.next=6;break;case 16:t.next=21;break;case 18:t.prev=18,t.t0=t.catch(4),o.e(t.t0);case 21:return t.prev=21,o.f(),t.finish(21);case 24:i(n),t.next=32;break;case 27:t.prev=27,t.t1=t.catch(1),e.logger.onError({c:Fe.TOP_ERROR,v:K.REMOTE_AUDIO_STATA_ERROR},"Get Remote Audio Stats Failed, ".concat(t.t1)),h=new ne({code:K.REMOTE_AUDIO_STATA_ERROR,message:t.t1.message}),r(h);case 32:case"end":return t.stop()}}),t,null,[[1,27],[4,18,21,24]])})));return function(e,i){return t.apply(this,arguments)}}())}},{key:"getRemoteVideoStats",value:function(){var e=this;return new Promise(function(){var t=P(U.mark((function t(i,r){var n,o,s,a,c,u,d,l,h,p;return U.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=new Map,t.prev=1,e.logger.info("get remote video Stats",e.subscriptions),o=_i(e.subscriptions.entries()),t.prev=4,o.s();case 6:if((s=o.n()).done){t.next=16;break}if(a=w(s.value,2),c=a[0],d=(u=a[1]).stream.getUserSeq(),c!==u.stream.videoSubscriptionId){t.next=14;break}return t.next=12,u.subscriber.getRemoteAudioOrVideoStats("video");case 12:(l=t.sent)&&n.set(d,{bytesReceived:(h=l.video).bytesReceived,packetsReceived:h.packetsReceived,packetsLost:h.packetsLost,framesDecoded:h.framesDecoded,frameWidth:h.frameWidth,frameHeight:h.frameHeight});case 14:t.next=6;break;case 16:t.next=21;break;case 18:t.prev=18,t.t0=t.catch(4),o.e(t.t0);case 21:return t.prev=21,o.f(),t.finish(21);case 24:i(n),t.next=32;break;case 27:t.prev=27,t.t1=t.catch(1),e.logger.onError({c:Fe.TOP_ERROR,v:K.REMOTE_VIDEO_STATA_ERROR},"Get Remote Video Stats Failed, ".concat(t.t1)),p=new ne({code:K.REMOTE_VIDEO_STATA_ERROR,message:t.t1.message}),r(p);case 32:case"end":return t.stop()}}),t,null,[[1,27],[4,18,21,24]])})));return function(e,i){return t.apply(this,arguments)}}())}},{key:"onWsStateChange",value:function(e,t,i){this.logger.info("Ws state from ".concat(t," to ").concat(i)),this._emitter.emit("connection-state-changed",{state:i,prevState:t})}},{key:"onError",value:function(e){this.logger.buriedLog({c:Fe.ON_ERROR,v:"".concat(e.message)}),this._emitter.emit(G,e)}},{key:"onWsReconnectFailed",value:function(e){this.logger.warn("room: ".concat(e," reconnection failed")),this.logger.onError({c:Fe.TOP_ERROR,v:K.SIGNAL_CHANNEL_RECONNECTION_FAILED});var t=new ne({code:K.SIGNAL_CHANNEL_RECONNECTION_FAILED,message:"signal channel reconnection failed, please check your network"});this._emitter.emit(G,t),this.leave()}},{key:"onParticipantLeave",value:function(e){var t=this;this.logger.info("======notification: ".concat(e," leave======"));try{var i="share_".concat(e),r=this.remoteStreams.get(e),n=this.remoteStreams.get(i),o=function(i,r){t.logger.buriedLog({c:i.type===H?Fe.ON_STREAM_REMOVED_SCREEN:Fe.ON_STREAM_REMOVED,v:"uid:".concat(e)}),t._emitter.emit("stream-removed",{stream:i}),i.close(),t.remoteStreams.delete(r),t.subscribeManager.subscriptedState.delete(r),t.receiverStats.has(r)&&t.receiverStats.delete(r);var n,o=_i(t.subscriptions.entries());try{for(o.s();!(n=o.n()).done;){var s=w(n.value,2),a=s[0],c=s[1];[i.audioSubscriptionId,i.videoSubscriptionId].includes(a)&&(c.subscriber.close(),t.subscriptions.delete(a))}}catch(e){o.e(e)}finally{o.f()}};r&&o(r,e),n&&o(n,i),this._remoteMutedStateMap.has(e)&&this._remoteMutedStateMap.delete(e)}catch(e){this.logger.info(e)}this.logger.buriedLog({c:Fe.ON_PEER_LEVAE,v:"uid:".concat(e)}),this._emitter.emit("peer-leave",{userId:e})}},{key:"onStreamAdd",value:function(e){var t=e||{},i=t.userId,r=t.streamId,n=t.info,o=t.mixedInfo,s=n||{},a=s.audio,c=s.video;if(this.logger.info("time  Date.now stream-add",Date.now()),i!==this.userId){var u=(a||c||{}).source;u===Te.ScreenShare&&(i="share_".concat(i));var d=this.remoteStreams.get(i);if(this.logger.info("userId: "+i,"remote stream",d),this.logger.info("userId: "+i,JSON.stringify(this.subscribeManager.getSubscriptedState(i),null,4)),d){var l=this.subscribeManager.needSubscribeKind(i),h={audio:!1,video:!1,small:this.subscribeManager.getSubscriptionOpts(i).small};if(l===_e.AudioOnly&&(h.audio=!0),l===_e.VideoOnly&&(h.video=!0),l===_e.AudioVideo&&(h.audio=!0,h.video=!0),a&&(d.setAudio(!!a),d.setHasAudio(!!a),d.setAudioStreamId(r),d.setMutedState("audio",a.muted),this.updateRemoteMutedState(i,{hasAudio:!0,audioMuted:a.muted})),c){d.setVideo(!!c),d.setHasVideo(!!c),d.setVideoStreamId(r),d.setInfo(n);var p=(c.simulcast||[]).find((function(e){return e.type===Re.SmallStream}));d.setMutedState("video",c.muted),this.updateRemoteMutedState(i,{hasVideo:!0,videoMuted:c.muted,hasSmall:!!p})}this._emitter.emit("stream-updated",{stream:d}),this.logger.info("Auto subscribe options",JSON.stringify(h)),(h.audio||h.video)&&this.doSubscribe(d,h)}else{if(d=new ut({userId:i,type:u===Te.ScreenShare?H:"main",info:n,mixedInfo:o},this.logger),u===Te.ScreenShare&&d.setLocalUserId(this.userId),d.streamId=r,this.remoteStreams.set(i,d),a&&(d.setAudio(!!a),d.setHasAudio(!!a),d.setAudioStreamId(r),d.setMutedState("audio",a.muted),this.updateRemoteMutedState(i,{hasAudio:!0,audioMuted:a.muted})),c){d.setVideo(!!c),d.setHasVideo(!!c),d.setVideoStreamId(r);var f=(c.simulcast||[]).find((function(e){return e.type===Re.SmallStream}));d.setMutedState("video",c.muted),this.updateRemoteMutedState(i,{hasVideo:!0,videoMuted:c.muted,hasSmall:!!f})}this.logger.buriedLog({c:d.type===H?Fe.ON_STREAM_ADDED_SCREEN:Fe.ON_STREAM_ADDED,v:"uid:".concat(d.getUserId())}),this._emitter.emit("stream-added",{stream:d})}}}},{key:"onStreamChange",value:function(e,t){var i=e;this.getType(t)===H&&(i="share_".concat(i),this.remoteStreams.get(i).closeWaterMark()),this.logger.info("time  Date.now stream-remove",Date.now());var r=this.remoteStreams.get(i);if(i!==this.userId&&i!=="share_".concat(i)&&r){var n=this.subscribeManager.getSubscriptedState(i);if(r&&r.getStreamKind(t)===_e.AudioOnly){if(!r.hasVideo())return void this.doStreamRemove(i);if(r.hasAudio()){var o=r.getAudioTrack();o&&r.mediaStream.removeTrack(o),n.audio=!1,r.setHasAudio(!1),r.setAudioStreamId(null),this.subscriptions.has(r.audioSubscriptionId)&&(this.subscriptions.get(r.audioSubscriptionId).subscriber.close(),this.subscriptions.delete(r.audioSubscriptionId),r.setAudioSubscriptionId(null)),r.setMutedState("audio",!0),this.updateRemoteMutedState(i,{hasAudio:!1,audioMuted:!0}),this.receiverStats.has(i)&&(this.receiverStats.get(i).audio={bytesReceived:0,timestamp:0,packetsReceived:0,packetsLost:0,nackCount:0,packetLossRate:0}),this._emitter.emit("mute-audio",{userId:i})}}if(r&&r.getStreamKind(t)===_e.VideoOnly){if(!r.hasAudio())return void this.doStreamRemove(i);if(r.hasVideo()){var s=r.getVideoTrack();s&&r.mediaStream.removeTrack(s),n.video=!1,r.setHasVideo(!1),r.setVideoStreamId(null),this.subscriptions.has(r.videoSubscriptionId)&&(this.subscriptions.get(r.videoSubscriptionId).subscriber.close(),this.subscriptions.delete(r.videoSubscriptionId),r.setVideoSubscriptionId(null)),r.setSimulcasts([]),r.setMutedState("video",!0),this.updateRemoteMutedState(i,{hasVideo:!1,videoMuted:!0,hasSmall:!1}),this.receiverStats.has(i)&&(this.receiverStats.get(i).video={bytesReceived:0,timestamp:0,packetsReceived:0,packetsLost:0,nackCount:0,packetLossRate:0}),this._emitter.emit("mute-video",{userId:i})}}this.subscribeManager.updateSubscriptedState(i,n),this._emitter.emit("stream-updated",{stream:r})}}},{key:"doStreamRemove",value:function(e){var t=this,i=this.remoteStreams.get(e),r=this.subscribeManager.getSubscriptedState(e);if(r.audio=!1,r.video=!1,this.subscribeManager.updateSubscriptedState(e,r),i){i.getType()===H&&i.closeWaterMark(),i.setAudioStreamId(null),i.setVideoStreamId(null),i.setMutedState("audio",!0),i.setMutedState("video",!0),this.remoteStreams.delete(e),this.logger.info("time  Date.now delete remoteStreams",Date.now(),i.audioSubscriptionId,i.videoSubscriptionId),this.receiverStats.has(e)&&this.receiverStats.delete(e);var n=[];i.audioSubscriptionId&&n.push(i.audioSubscriptionId),i.videoSubscriptionId&&n.push(i.videoSubscriptionId),n.forEach((function(e){t.subscriptions.has(e)&&(t.subscriptions.get(e).subscriber.close(),t.subscriptions.delete(e),e===i.audioSubscriptionId&&i.setAudioSubscriptionId(null),e===i.videoSubscriptionId&&i.setVideoSubscriptionId(null))})),this.logger.info("do stream remove with ",this.subscriptions),this.logger.buriedLog({c:i.type===H?Fe.ON_STREAM_REMOVED_SCREEN:Fe.ON_STREAM_REMOVED,v:"uid:".concat(i.getUserId())}),this.updateRemoteMutedState(e),this._emitter.emit("stream-removed",{stream:i})}}},{key:"onClientBanned",value:function(e){var t=this;this.logger.buriedLog({c:Fe.ON_CLIENT_BANNED,v:"cause:".concat(e)},!0);var i,r=_i(this.publications.entries());try{var n=function(){var e=w(i.value,2),r=e[0],n=e[1],o=t.localStreams.find((function(e){return e.streamId===r}));o&&o.close(),n.close()};for(r.s();!(i=r.n()).done;)n()}catch(e){r.e(e)}finally{r.f()}var o,s=_i(this.subscriptions.values());try{for(s.s();!(o=s.n()).done;){var a=o.value;a.stream.close(),a.subscriber.close()}}catch(e){s.e(e)}finally{s.f()}this.state=B.Leaved,this.reset(),this._emitter.emit("client-banned",{cause:e})}},{key:"onStreamUpdate",value:function(e,t,i,r){var n=e;if(this.remoteStreams.has("share_".concat(e))){var o=this.remoteStreams.get("share_".concat(e));[o.audioStreamId,o.videoStreamId].includes(t)&&(n="share_".concat(e))}var s=this.localStreams.find((function(e){return[e.audioStreamId,e.videoStreamId].includes(t)}));if(s||(s=this.remoteStreams.get(n)),null!=i&&i.audio&&(e!==this.userId&&this.logger.buriedLog({c:i.audio.muted?Fe.ON_MUTE_AUDIO:Fe.ON_UNMUTE_AUDIO,v:"uid:".concat(e)}),this._emitter.emit(i.audio.muted?"mute-audio":"unmute-audio",{userId:n}),s.setMutedState("audio",i.audio.muted),this.updateRemoteMutedState(e,{audioMuted:i.audio.muted})),null!=i&&i.video&&(e!==this.userId&&this.logger.buriedLog({c:i.video.muted?Fe.ON_MUTE_VIDEO:Fe.ON_UNMUTE_VIDEO,v:"uid:".concat(e)}),this._emitter.emit(i.video.muted?"mute-video":"unmute-video",{userId:n}),s.setMutedState("video",i.video.muted),this.updateRemoteMutedState(e,{videoMuted:i.video.muted})),r&&r.length){var a=(r||[]).find((function(e){return e.type===Re.SmallStream}));if(this.remoteStreams.has(e)){var c=this.remoteStreams.get(e);c.setSimulcasts(r),this.logger.buriedLog({c:c.getType()===H?Fe.ON_STREAM_UPDATED_SCREEN:Fe.ON_STREAM_UPDATED,v:"uid:".concat(c.getUserId())}),this._emitter.emit("stream-updated",{stream:c}),a||c.getSimulcastType()===Re.SmallStream&&(this.logger.info("auto setRemoteVideoStreamType big"),this.setRemoteVideoStreamType(c,"big"))}this.updateRemoteMutedState(e,{hasVideo:!0,hasSmall:!!a})}}},{key:"getType",value:function(e){var t,i,r=_i(this.remoteStreams);try{for(r.s();!(i=r.n()).done;){var n=w(i.value,2)[1];if(n.audioStreamId===e||n.videoStreamId===e){t=n.getType();break}}}catch(e){r.e(e)}finally{r.f()}return t}},{key:"hasPublishedStream",value:function(){return this.localStreams.some((function(e){return e.published}))}},{key:"getClientState",value:function(){return this.state}},{key:"onDeviceChange",value:(r=P(U.mark((function e(){var t,i,r,n,o,s,a,c,u,d,l,h,p,f,m,g,v,b,y,S,E,_,C,I=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Ge();case 3:if(t=e.sent,i=JSON.parse(JSON.stringify(this._preDiviceList)),this._preDiviceList=t,(r=t.filter((function(e){return-1===i.findIndex((function(t){return t.deviceId===e.deviceId}))}))).length&&this.logger.info("onDeviceChange addedDevices",JSON.stringify(r,null,4)),(n=i.filter((function(e){return-1===t.findIndex((function(t){return t.deviceId===e.deviceId}))}))).length&&this.logger.info("onDeviceChange removedDevices",JSON.stringify(n,null,4)),o=this.localStreams.find((function(e){return!e.screen})),s=r.filter((function(e){return"audiooutput"===e.kind})),a=t.filter((function(e){return"default"===e.deviceId})),!o){e.next=64;break}if(c=t.filter((function(e){return"audioinput"===e.kind&&"default"===e.deviceId})),!r||!r.length){e.next=43;break}if(u=i.find((function(e){return"audioinput"===e.kind})),d=i.find((function(e){return"videoinput"===e.kind})),l=r.filter((function(e){return"audioinput"===e.kind})),h=r.filter((function(e){return"videoinput"===e.kind})),p=!u&&l.length>0&&o.hasAudio(),f=!d&&h.length>0&&o.hasVideo(),!p||!f){e.next=30;break}return this.logger.warn("new microphone and camera detected, but there was no device before."),e.next=26,o.updateStream({audio:!0,video:!0,cameraId:h[0].deviceId,microphoneId:c.length?c[0].deviceId:l[0].deviceId});case 26:this._emitter.emit("auto-switch-device",{type:"audio",deviceId:c.length?c[0].deviceId:l[0].deviceId}),this._emitter.emit("auto-switch-device",{type:"video",deviceId:h[0].deviceId}),e.next=42;break;case 30:if(!p){e.next=37;break}return this.logger.warn("new microphone  detected, but there was no device before."),e.next=34,o.updateStream({audio:!0,video:!1,microphoneId:l[0].deviceId});case 34:this._emitter.emit("auto-switch-device",{type:"audio",deviceId:l[0].deviceId}),e.next=42;break;case 37:if(!f){e.next=42;break}return this.logger.warn("new camera  detected, but there was no device before."),e.next=41,o.updateStream({audio:!1,video:!0,cameraId:h[0].deviceId});case 41:this._emitter.emit("auto-switch-device",{type:"video",deviceId:h[0].deviceId});case 42:s.length&&a.length&&o.setAudioOutput("default");case 43:if(!n||!n.length){e.next=64;break}if(m=o.getDevicesInfoInUse(),g=m.microphone,v=m.camera,this.logger.warn("Devices in use microphone:".concat(JSON.stringify(g,null,4),",camera:").concat(JSON.stringify(v,null,4))),b=n.find((function(e){return e.groupId&&g.groupId?e.deviceId===g.deviceId&&e.groupId===g.groupId:e.deviceId===g.deviceId})),y=n.find((function(e){return e.groupId&&v.groupId?e.deviceId===v.deviceId&&e.groupId===v.groupId:e.deviceId===v.deviceId})),S=t.find((function(e){return"audioinput"===e.kind})),E=t.find((function(e){return"videoinput"===e.kind})),_=b&&o.hasAudio(),C=y&&o.hasVideo(),!_){e.next=58;break}if(this.logger.warn("current microphone in use is lost, deviceId: ".concat(g.deviceId)),!S){e.next=58;break}return e.next=57,o.updateStream({audio:!0,video:!1});case 57:this._emitter.emit("auto-switch-device",{type:"audio"});case 58:if(!C){e.next=64;break}if(this.logger.warn("current camera in use is lost, deviceId: ".concat(v.deviceId)),!E){e.next=64;break}return e.next=63,o.updateStream({audio:!1,video:!0});case 63:this._emitter.emit("auto-switch-device",{type:"video"});case 64:s.length&&a.length&&this.remoteStreams.forEach((function(e){e.setAudioOutput("default")})),r.forEach((function(e){switch(e.kind){case"audioinput":I.logger.info("The new microphone device be detected is",e.label),I._emitter.emit("recording-device-changed",{deviceId:e.deviceId,state:"ADD"});break;case"videoinput":I.logger.info("The new camera device be detected is",e.label),I._emitter.emit("camera-changed",{deviceId:e.deviceId,state:"ADD"});break;case"audiooutput":I.logger.info("The new speaker device be detected is",e.label),I._emitter.emit("playback-device-changed",{deviceId:e.deviceId,state:"ADD"})}})),n.forEach((function(e){switch(e.kind){case"audioinput":I.logger.info("The microphone device is detected to be removed: ",e.label),I._emitter.emit("recording-device-changed",{deviceId:e.deviceId,state:"REMOVE"});break;case"videoinput":I.logger.info("The camera device is detected to be removed: ",e.label),I._emitter.emit("camera-changed",{deviceId:e.deviceId,state:"REMOVE"});break;case"audiooutput":I.logger.info("The speaker device is detected to be removed: ",e.label),I._emitter.emit("playback-device-changed",{deviceId:e.deviceId,state:"REMOVE"})}})),e.next=72;break;case 69:e.prev=69,e.t0=e.catch(0),this.logger.onError({c:Fe.TOP_ERROR,v:K.SWITCH_DEVICE_FAILED},"on device change error, ".concat(e.t0));case 72:case"end":return e.stop()}}),e,this,[[0,69]])}))),function(){return r.apply(this,arguments)})},{key:"enableAudioVolumeEvaluation",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3;if(this.logger.info("enableAudioVolumeEvaluation with interval: "+t),this.logger.buriedLog({c:Fe.ENABLE_AUDIO_VOLUME_EVALUATION,v:"time:".concat(t)}),"number"!=typeof t)throw this.logger.onError({c:Fe.TOP_ERROR,v:K.INVALID_PARAMETER}),new ne({code:K.INVALID_PARAMETER,message:"parameter must be numeric type"});t<=0?(window.clearInterval(this.audioVolumeInterval),this.audioVolumeInterval=null):(this.audioVolumeInterval&&(window.clearInterval(this.audioVolumeInterval),this.audioVolumeInterval=null),this.audioVolumeInterval=window.setInterval((function(){var t=[];e.localStreams.forEach((function(e){if(!e.screen&&e.published){var i=Math.floor(100*e.getAudioLevel());t.push({userId:e.getUserId(),audioVolume:i,stream:e})}}));var i,r=_i(e.remoteStreams);try{for(r.s();!(i=r.n()).done;){var n=w(i.value,2)[1];if("main"===n.getType()&&n.subscribed){var o=Math.floor(100*n.getAudioLevel());t.push({userId:n.getUserId(),audioVolume:o,stream:n})}}}catch(e){r.e(e)}finally{r.f()}e._emitter.emit("audio-volume",{result:t})}),Math.floor(Math.max(t,16))))}},{key:"addEventListenser",value:function(e){this.ssl&&navigator.mediaDevices&&"devicechange"===e&&navigator.mediaDevices.addEventListener(e,this.deviceChange),"visibilitychange"===e&&document.addEventListener(e,this.visibilitychange)}},{key:"removeEventListenser",value:function(e){this.ssl&&navigator.mediaDevices&&"devicechange"===e&&navigator.mediaDevices.removeEventListener(e,this.deviceChange),"visibilitychange"===e&&document.removeEventListener(e,this.visibilitychange)}},{key:"startWaterMark",value:(i=P(U.mark((function e(t){var i,r,n,o,s=this;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isWaterMark){e.next=2;break}return e.abrupt("return");case 2:return this.isWaterMark=!0,i={fontColor:"rgba(200,200,200,0.6)",fontSize:"12",fontType:"Microsoft Yahei"},r=t.fontColor,n=t.fontSize,o=t.fontType,t&&(r&&(i.fontColor=r),n&&(i.fontSize=n),o&&(i.fontType=o)),e.next=8,ot(i,this.userId);case 8:this.waterMarkImage=e.sent,this.waterMarkoptions=i,this.remoteStreams.size&&this.remoteStreams.forEach(function(){var e=P(U.mark((function e(t){return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.getType()!==H){e.next=3;break}return e.next=3,t.startWaterMark(i,s.waterMarkImage);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 11:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"closeWaterMark",value:function(){this.isWaterMark&&(this.isWaterMark=!1,this.waterMarkImage=null,this.remoteStreams.size&&this.remoteStreams.forEach((function(e){e.getType()===H&&e.closeWaterMark()})))}},{key:"enableSmallStream",value:function(){var e=this.localStreams.find((function(e){return e.videoStreamId&&!e.screen}));if(e&&e.published)throw new ne({code:K.INVALID_OPERATION,message:"Cannot enable small stream after localStream published."});if(!Ye())throw new ne({code:K.INVALID_OPERATION,message:"Your browser does not support opening small stream"});return this.setIsEnableSmallStream(!0),this.logger.info("SmallStream successfully enabled"),this.logger.buriedLog({c:Fe.ENABLE_SMALL_STREAM}),Promise.resolve(!0)}},{key:"disableSmallStream",value:function(){var e=this.localStreams.find((function(e){return e.videoStreamId&&!e.screen}));if(e&&e.published)throw new ne({code:K.INVALID_OPERATION,message:"Cannot enable small stream after having published."});return this.setIsEnableSmallStream(!1),this.logger.info("SmallStream successfully disabled"),this.logger.buriedLog({c:Fe.DISABLE_SMALL_STREAM}),Promise.resolve(!0)}},{key:"setSmallStreamProfile",value:function(e){var t=e.width,i=e.height,r=e.bitrate,n=e.framerate;if(this.logger.info("setSmallStreamProfile:width=".concat(t,",height=").concat(i,",bitrate=").concat(r,",framerate=").concat(n)),t<0||i<0||r<0||n<0)throw new ne({code:K.INVALID_OPERATION,message:"Small stream profile is invalid."});this.logger.buriedLog({c:Fe.SET_SMALL_STREAM_PROFILE,v:JSON.stringify(e)}),this.smallStreamConfig={width:t,height:i,bitrate:r,framerate:n}}},{key:"setIsEnableSmallStream",value:function(e){this.isEnableSmallStream=e}},{key:"onVisibilitychange",value:function(){"visible"===document.visibilityState?(this.logger.warn("User enter the page"),this._emitter.emit("page-visibility-state",{state:"visible"})):"hidden"===document.visibilityState&&(this.logger.warn("User leave the pag"),this._emitter.emit("page-visibility-state",{state:"hidden"}))}},{key:"isJoinRoomSupported",value:(t=P(U.mark((function e(){var t,i,r,n,o,s,a,c,u;return U.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!he.specil()){e.next=4;break}return e.abrupt("return",Promise.resolve({isSupported:!0,code:0,message:""}));case 4:if(!he.any()){e.next=6;break}return e.abrupt("return",Promise.resolve({isSupported:!1,code:K.OS_NOT_SUPPORTED,message:"".concat(he.getOsName().osName," is not supported!")}));case 6:return e.next=8,He();case 8:if(i=(t=e.sent.detail).isBrowserSupported,n=t.isH264Supported,(r=t.isWebRTCSupported)&&n&&i){e.next=15;break}return o=pe(),s=o.browser,a=o.version,c=r?n?K.BROWSER_NOT_SUPPORTED:K.H264_NOT_SUPPORTED:K.WEBRTC_NOT_SUPPORTED,u=r?n?"".concat(s).concat(a," browser is not supported"):"your device does not support H.264 encoding.":"your browser does NOT support WebRTC!",e.abrupt("return",Promise.resolve({isSupported:!1,code:c,message:u}));case 15:return e.abrupt("return",Promise.resolve({isSupported:!0,code:0,message:""}));case 16:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})},{key:"enableMicVolume",value:function(e){var t=this;if(t.enablemicVolume=!t.enablemicVolume,t.enablemicVolume){if("number"!=typeof e)throw new ne({code:K.INVALID_PARAMETER,message:"parameter must be numeric type"});if(e<=0)clearInterval(t.timer),t.timer=null;else{var i=function(){t.logger.error("Please enable microphone permission")},r=function(i){t.logger.info("microphone permission is ok"),t.micStream=i,t.soundMeter=new oe,t.soundMeter.connectToSource(t.micStream.getAudioTracks()[0]),t.timer=setInterval((function(){t._emitter.emit("mic-volume",{volumes:Math.round(100*t.soundMeter.getVolume())})}),Math.floor(Math.max(e,100)))};this.localStreams.length>0?this.localStreams.forEach((function(e){if(!e.screen&&e.published){var t=e.getDevicesInfoInUse();navigator.mediaDevices.getUserMedia({audio:{deviceId:t.microphone.deviceId}}).then(r).catch(i)}else navigator.mediaDevices.getUserMedia({audio:!0}).then(r).catch(i)})):navigator.mediaDevices.getUserMedia({audio:!0}).then(r).catch(i)}}else clearInterval(t.timer),t.timer=null,t.micStream&&t.micStream.getAudioTracks()[0].stop()}}]),e}();function ki(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function Oi(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ki(Object(i),!0).forEach((function(t){O(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ki(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}!function(e){e[e.TRACE=0]="TRACE",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.NONE=5]="NONE"}(Ii||(Ii={})),function(e){e.NORMAL="normal",e.POINT="point",e.MEDIA="media"}(Ti||(Ti={}));var wi=function(){function e(){L(this,e),this.LogLevel={TRACE:Ii.TRACE,DEBUG:Ii.DEBUG,INFO:Ii.INFO,WARN:Ii.WARN,ERROR:Ii.ERROR,NONE:Ii.NONE},this.level=Ii.INFO,this.myConsole=window.console,this.uploadLog=!1,this.logList=[],this.buriedLogList=[],this.mediaLogList=[],this.maxNumber=10,this.timeout=1e4,this._interval=0,this._intervalBuried=0,this.roomId=null,this.serverUrl="",this.appConfig=null,this.roomUniqueId=null,this.reUploadMaxCount=30,this.logReUploadCount=0,this.buriedlogReUploadCount=0,this.enableUploadLog(),window.addEventListener("beforeunload",this.beforeUnload.bind(this),{once:!0})}return x(e,[{key:"setLogLevel",value:function(e){this.level=e,this.buriedLog({c:Fe.SET_LOG_LEVEL,v:["TRACE","DEBUG","INFO","WARN","ERROR","NONE"][e]})}},{key:"getLogLevel",value:function(){return this.level}},{key:"setUserId",value:function(e){this.userId=e}},{key:"setRoomId",value:function(e){this.roomId=e}},{key:"setRoomUniqueId",value:function(e){this.roomUniqueId=e}},{key:"setServerUrl",value:function(e){e||(this.uploadLogList=this.logList.splice(0,this.logList.length),this.upload(this.uploadLogList)),this.serverUrl=e}},{key:"setApppConfig",value:function(e){var t,i;this.appConfig=e,null!==(t=this.appConfig)&&void 0!==t&&t.enableLog?this.enableUploadLog():this.disableUploadLog(),null!==(i=this.appConfig)&&void 0!==i&&i.enableEvent?this.enableUploadBuriedLogs():this.disableUploadBuriedLogs()}},{key:"debug",value:function(e){var t;if(!(this.level===Ii.NONE||Ii.DEBUG<this.level)){for(var i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];(t=this.myConsole).debug.apply(t,["XRTC <Debug> ".concat(this.userId?"[".concat(this.userId,"]"):""),e].concat(r)),this.collect(Ii.DEBUG,e,r)}}},{key:"info",value:function(e){for(var t,i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];this.collect(Ii.INFO,e,r),this.level===Ii.NONE||Ii.INFO<this.level||(t=this.myConsole).info.apply(t,["XRTC <Info> ".concat(this.userId?"[".concat(this.userId,"]"):""),e].concat(r))}},{key:"warn",value:function(e){for(var t,i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];this.collect(Ii.WARN,e,r),this.level===Ii.NONE||Ii.WARN<this.level||(t=this.myConsole).warn.apply(t,["XRTC <Warn> ".concat(this.userId?"[".concat(this.userId,"]"):""),e].concat(r))}},{key:"error",value:function(e){for(var t,i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];this.collect(Ii.ERROR,e,r),this.level===Ii.NONE||Ii.ERROR<this.level||(t=this.myConsole).error.apply(t,["XRTC <Error> ".concat(this.userId?"[".concat(this.userId,"]"):""),e].concat(r))}},{key:"enableUploadLog",value:function(){var e=this;this._interval||(this.uploadLog=!0,this._interval=window.setInterval((function(){e.logList.length>0&&(e.uploadLogList=e.logList.splice(0,e.logList.length),e.upload(e.uploadLogList))}),this.timeout),this.buriedLog({c:Fe.ENABLE_UPLOAD_LOG}))}},{key:"disableUploadLog",value:function(){this.uploadLog=!1,window.clearInterval(this._interval),this._interval=0,this.buriedLog({c:Fe.DISABLE_UPLOAD_LOG})}},{key:"collect",value:function(e,t,i){this.uploadLog&&(this.logList.push({t:Date.now(),lv:e,mdu:"XRTC",msg:"XRTC ".concat(this.userId?"[".concat(this.userId,"]"):""," ").concat(t," ").concat((i||[]).join(" "),"\r\n")}),this.logList.length>=this.maxNumber&&(this.uploadLogList=this.logList.splice(0,this.logList.length),this.upload(this.uploadLogList)))}},{key:"upload",value:function(e){var t=this;this.serverUrl&&yi.upload({type:Ti.NORMAL,app:yi.appKey,rid:this.roomId,uid:this.userId,pf:"web",list:e}).then((function(){})).catch((function(){t.logReUploadCount=t.logReUploadCount+1,t.logReUploadCount>t.reUploadMaxCount?(t.logList=[],t.logReUploadCount=0,t.info("SDK has tried reupload log ".concat(t.reUploadMaxCount," times, but all failed, and old log cleared"))):t.logList=[].concat(k(e),k(t.logList))}))}},{key:"buriedLog",value:function(e,t){var i,r=this;if((!this.appConfig||this.appConfig.enableEvent)&&(this.buriedLogList.push(Oi(Oi({},e),{},{t:this.adjustServerTime(Date.now())})),e.c===Fe.JOIN_SUCCESS&&this.buriedLogList.forEach((function(e){e.c!==Fe.JOIN_SUCCESS&&(e.t=r.adjustServerTime(e.t))})),null!==(i=this.appConfig)&&void 0!==i&&i.enableEvent&&this.roomUniqueId&&(t||this.buriedLogList.length>=this.maxNumber))){var n=this.buriedLogList.splice(0,this.buriedLogList.length);this.uploadBuriedLogs(n,Ti.POINT)}}},{key:"mediaLog",value:function(e,t){var i;if((!this.appConfig||this.appConfig.enableEvent)&&(this.mediaLogList.push(Oi(Oi({},e),{},{t:this.adjustServerTime(Date.now())})),null!==(i=this.appConfig)&&void 0!==i&&i.enableEvent&&this.roomUniqueId&&(t||this.mediaLogList.length>=this.maxNumber))){var r=this.mediaLogList.splice(0,this.mediaLogList.length);this.uploadBuriedLogs(r,Ti.MEDIA)}}},{key:"uploadBuriedLogs",value:function(e,t){var i=this;!this.serverUrl||this.appConfig&&!this.appConfig.enableEvent||yi.upload({type:t,app:yi.appKey,ruid:this.roomUniqueId,uid:this.userId,pf:"web",list:e}).then((function(){})).catch((function(r){i.buriedlogReUploadCount=i.buriedlogReUploadCount+1,i.buriedlogReUploadCount>i.reUploadMaxCount?(t===Ti.POINT&&(i.buriedLogList=[]),t===Ti.MEDIA&&(i.mediaLogList=[]),i.buriedlogReUploadCount=0,i.info("SDK has tried reupload buried log ".concat(i.reUploadMaxCount," times, but all failed, and old log cleared"))):t===Ti.POINT?i.buriedLogList=[].concat(k(e),k(i.buriedLogList)):t===Ti.MEDIA&&(i.mediaLogList=[].concat(k(e),k(i.mediaLogList)))}))}},{key:"enableUploadBuriedLogs",value:function(){var e=this;this._intervalBuried||(this._intervalBuried=window.setInterval((function(){if(e.buriedLogList.length>0){var t=e.buriedLogList.splice(0,e.buriedLogList.length);e.uploadBuriedLogs(t,Ti.POINT)}if(e.mediaLogList.length>0){var i=e.mediaLogList.splice(0,e.mediaLogList.length);e.uploadBuriedLogs(i,Ti.MEDIA)}}),this.timeout))}},{key:"disableUploadBuriedLogs",value:function(){window.clearInterval(this._intervalBuried),this._intervalBuried=0}},{key:"beforeUnload",value:function(){if(this.logList.length>0){var e=this.logList.splice(0,this.logList.length);this.upload(e)}if(this.buriedLogList.length>0){var t=this.buriedLogList.splice(0,this.buriedLogList.length);this.uploadBuriedLogs(t,Ti.POINT)}if(this.mediaLogList.length>0){var i=this.mediaLogList.splice(0,this.mediaLogList.length);this.uploadBuriedLogs(i,Ti.MEDIA)}this.disableUploadLog(),this.disableUploadBuriedLogs()}},{key:"adjustServerTime",value:function(e){var t;return null!==(t=this.appConfig)&&void 0!==t&&t.timeDiff?e-this.appConfig.timeDiff:e}},{key:"onError",value:function(e,t,i){i?this.buriedLog(e,i):this.buriedLog(e),t&&this.error(t)}}]),e}();let Ai=!0,Pi=!0;function Li(e,t,i){const r=e.match(t);return r&&r.length>=i&&parseInt(r[i],10)}function Di(e,t,i){if(!e.RTCPeerConnection)return;const r=e.RTCPeerConnection.prototype,n=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return n.apply(this,arguments);const o=e=>{const t=i(e);t&&(r.handleEvent?r.handleEvent(t):r(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(r,o),n.apply(this,[e,o])};const o=r.removeEventListener;r.removeEventListener=function(e,i){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(i))return o.apply(this,arguments);const r=this._eventMap[t].get(i);return this._eventMap[t].delete(i),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function xi(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Ai=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function Mi(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Pi=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Ui(){if("object"==typeof window){if(Ai)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function Ni(e,t){Pi&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Vi(e){return"[object Object]"===Object.prototype.toString.call(e)}function Bi(e){return Vi(e)?Object.keys(e).reduce((function(t,i){const r=Vi(e[i]),n=r?Bi(e[i]):e[i],o=r&&!Object.keys(n).length;return void 0===n||o?t:Object.assign(t,{[i]:n})}),{}):e}function Fi(e,t,i){const r=i?"outbound-rtp":"inbound-rtp",n=new Map;if(null===t)return n;const o=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)}),o.forEach(t=>{e.forEach(i=>{i.type===r&&i.trackId===t.id&&function e(t,i,r){i&&!r.has(i.id)&&(r.set(i.id,i),Object.keys(i).forEach(n=>{n.endsWith("Id")?e(t,t.get(i[n]),r):n.endsWith("Ids")&&i[n].forEach(i=>{e(t,t.get(i),r)})}))}(e,i,n)})}),n}const ji=Ui;function Wi(e,t){const i=e&&e.navigator;if(!i.mediaDevices)return;const r=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(i=>{if("require"===i||"advanced"===i||"mediaSource"===i)return;const r="object"==typeof e[i]?e[i]:{ideal:e[i]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);const n=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[n("min",i)]=r.ideal,t.optional.push(e),e={},e[n("max",i)]=r.ideal,t.optional.push(e)):(e[n("",i)]=r.ideal,t.optional.push(e))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[n("",i)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[n(e,i)]=r[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},n=function(e,n){if(t.version>=61)return n(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const s=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!i.mediaDevices.getSupportedConstraints||!i.mediaDevices.getSupportedConstraints().facingMode||s)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return i.mediaDevices.enumerateDevices().then(i=>{let s=(i=i.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return!s&&i.length&&t.includes("back")&&(s=i[i.length-1]),s&&(e.video.deviceId=o.exact?{exact:s.deviceId}:{ideal:s.deviceId}),e.video=r(e.video),ji("chrome: "+JSON.stringify(e)),n(e)})}e.video=r(e.video)}return ji("chrome: "+JSON.stringify(e)),n(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(i.getUserMedia=function(e,t,r){n(e,e=>{i.webkitGetUserMedia(e,t,e=>{r&&r(o(e))})})}.bind(i),i.mediaDevices.getUserMedia){const e=i.mediaDevices.getUserMedia.bind(i.mediaDevices);i.mediaDevices.getUserMedia=function(t){return n(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}function Hi(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Gi(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",i=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===i.track.id):{track:i.track};const n=new Event("track");n.track=i.track,n.receiver=r,n.transceiver={receiver:r},n.streams=[t.stream],this.dispatchEvent(n)}),t.stream.getTracks().forEach(i=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===i.id):{track:i};const n=new Event("track");n.track=i,n.receiver=r,n.transceiver={receiver:r},n.streams=[t.stream],this.dispatchEvent(n)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else Di(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function zi(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&(this._dtmf="audio"===t.kind?e.createDTMFSender(t):null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const i=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){let n=i.apply(this,arguments);return n||(n=t(this,e),this._senders.push(n)),n};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],i.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{const t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&(this._dtmf="audio"===this.track.kind?this._pc.createDTMFSender(this.track):null),this._dtmf}})}}function Ji(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,r]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const n=function(e){const t={};return e.result().forEach(e=>{const i={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{i[t]=e.stat(t)}),t[i.id]=i}),t},o=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};return arguments.length>=2?t.apply(this,[function(e){i(o(n(e)))},e]):new Promise((e,i)=>{t.apply(this,[function(t){e(o(n(t)))},i])}).then(i,r)}}function Ki(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Fi(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),Di(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Fi(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,i,r;return this.getSenders().forEach(i=>{i.track===e&&(t?r=!0:t=i)}),this.getReceivers().forEach(t=>(t.track===e&&(i?r=!0:i=t),t.track===e)),r||t&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function Yi(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){if(!i)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const r=t.apply(this,arguments);return this._shimmedLocalStreams[i.id]?-1===this._shimmedLocalStreams[i.id].indexOf(r)&&this._shimmedLocalStreams[i.id].push(r):this._shimmedLocalStreams[i.id]=[i,r],r};const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();i.apply(this,arguments);const r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{const i=this._shimmedLocalStreams[t].indexOf(e);-1!==i&&this._shimmedLocalStreams[t].splice(i,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),n.apply(this,arguments)}}function qi(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return Yi(e);const i=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=i.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){const i=new e.MediaStream(t.getTracks());this._streams[t.id]=i,this._reverseStreams[i.id]=t,t=i}r.apply(this,[t])};const n=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let i=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t];i=i.replace(new RegExp(e._streams[r.id].id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:i})}function s(e,t){let i=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],n=e._streams[r.id];i=i.replace(new RegExp(r.id,"g"),n.id)}),new RTCSessionDescription({type:t.type,sdp:i})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},n.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,i){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const r=[].slice.call(arguments,1);if(1!==r.length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const n=this.getSenders().find(e=>e.track===t);if(n)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[i.id];if(o)o.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const r=new e.MediaStream([t]);this._streams[i.id]=r,this._reverseStreams[r.id]=i,this.addStream(r)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?i.apply(this,[t=>{const i=o(this,t);e[0].apply(null,[i])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):i.apply(this,arguments).then(e=>o(this,e))}}[t]}));const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=s(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(i=>{this._streams[i].getTracks().find(t=>e.track===t)&&(t=this._streams[i])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Xi(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}}[t]}))}function $i(e,t){Di(e,"negotiationneeded",e=>{const i=e.target;if(!(t.version<72||i.getConfiguration&&"plan-b"===i.getConfiguration().sdpSemantics)||"stable"===i.signalingState)return e})}var Qi=Object.freeze({__proto__:null,shimMediaStream:Hi,shimOnTrack:Gi,shimGetSendersWithDtmf:zi,shimGetStats:Ji,shimSenderReceiverGetStats:Ki,shimAddTrackRemoveTrackWithNative:Yi,shimAddTrackRemoveTrack:qi,shimPeerConnection:Xi,fixNegotiationNeeded:$i,shimGetUserMedia:Wi,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(i){return t(i).then(t=>{const r=i.video&&i.video.width,n=i.video&&i.video.height;return i.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:i.video&&i.video.frameRate||3}},r&&(i.video.mandatory.maxWidth=r),n&&(i.video.mandatory.maxHeight=n),e.navigator.mediaDevices.getUserMedia(i)})}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});function Zi(e,t){const i=e&&e.navigator,r=e&&e.MediaStreamTrack;if(i.getUserMedia=function(e,t,r){Ni("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(e).then(t,r)},!(t.version>55&&"autoGainControl"in i.mediaDevices.getSupportedConstraints())){const e=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])},t=i.mediaDevices.getUserMedia.bind(i.mediaDevices);if(i.mediaDevices.getUserMedia=function(i){return"object"==typeof i&&"object"==typeof i.audio&&(i=JSON.parse(JSON.stringify(i)),e(i.audio,"autoGainControl","mozAutoGainControl"),e(i.audio,"noiseSuppression","mozNoiseSuppression")),t(i)},r&&r.prototype.getSettings){const t=r.prototype.getSettings;r.prototype.getSettings=function(){const i=t.apply(this,arguments);return e(i,"mozAutoGainControl","autoGainControl"),e(i,"mozNoiseSuppression","noiseSuppression"),i}}if(r&&r.prototype.applyConstraints){const t=r.prototype.applyConstraints;r.prototype.applyConstraints=function(i){return"audio"===this.kind&&"object"==typeof i&&(i=JSON.parse(JSON.stringify(i)),e(i,"autoGainControl","mozAutoGainControl"),e(i,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[i])}}}}function er(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function tr(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}}[t]}));const i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,o]=arguments;return r.apply(this,[e||null]).then(e=>{if(t.version<53&&!n)try{e.forEach(e=>{e.type=i[e.type]||e.type})}catch(t){if("TypeError"!==t.name)throw t;e.forEach((t,r)=>{e.set(r,Object.assign({},t,{type:i[t.type]||t.type}))})}return e}).then(n,o)}}function ir(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function rr(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),Di(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function nr(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){Ni("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function or(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function sr(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];const e=arguments[1],i=e&&"sendEncodings"in e;i&&e.sendEncodings.forEach(e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=t.apply(this,arguments);if(i){const{sender:t}=r,i=t.getParameters();(!("encodings"in i)||1===i.encodings.length&&0===Object.keys(i.encodings[0]).length)&&(i.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(i).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return r})}function ar(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function cr(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function ur(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}var dr=Object.freeze({__proto__:null,shimOnTrack:er,shimPeerConnection:tr,shimSenderGetStats:ir,shimReceiverGetStats:rr,shimRemoveStream:nr,shimRTCDataChannel:or,shimAddTransceiver:sr,shimGetParameters:ar,shimCreateOffer:cr,shimCreateAnswer:ur,shimGetUserMedia:Zi,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(i){if(!i||!i.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===i.video?i.video={mediaSource:t}:i.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(i)})}});function lr(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(i=>t.call(this,i,e)),e.getVideoTracks().forEach(i=>t.call(this,i,e))},e.RTCPeerConnection.prototype.addTrack=function(e,...i){return i&&i.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const i=e.getTracks();this.getSenders().forEach(e=>{i.includes(e.track)&&this.removeTrack(e)})})}}function hr(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const i=new Event("addstream");i.stream=t,e.dispatchEvent(i)})}),t.apply(e,arguments)}}}function pr(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,i=t.createOffer,r=t.createAnswer,n=t.setLocalDescription,o=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],n=i.apply(this,[r]);return t?(n.then(e,t),Promise.resolve()):n},t.createAnswer=function(e,t){const i=arguments.length>=2?arguments[2]:arguments[0],n=r.apply(this,[i]);return t?(n.then(e,t),Promise.resolve()):n};let a=function(e,t,i){const r=n.apply(this,[e]);return i?(r.then(t,i),Promise.resolve()):r};t.setLocalDescription=a,a=function(e,t,i){const r=o.apply(this,[e]);return i?(r.then(t,i),Promise.resolve()):r},t.setRemoteDescription=a,a=function(e,t,i){const r=s.apply(this,[e]);return i?(r.then(t,i),Promise.resolve()):r},t.addIceCandidate=a}function fr(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,i=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>i(mr(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,i,r){t.mediaDevices.getUserMedia(e).then(i,r)}.bind(t))}function mr(e){return e&&void 0!==e.video?Object.assign({},e,{video:Bi(e.video)}):e}function gr(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,i){if(e&&e.iceServers){const t=[];for(let i=0;i<e.iceServers.length;i++){let r=e.iceServers[i];!r.hasOwnProperty("urls")&&r.hasOwnProperty("url")?(Ni("RTCIceServer.url","RTCIceServer.urls"),r=JSON.parse(JSON.stringify(r)),r.urls=r.url,delete r.url,t.push(r)):t.push(e.iceServers[i])}e.iceServers=t}return new t(e,i)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function vr(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function br(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const i=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&i?"sendrecv"===i.direction?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":"recvonly"===i.direction&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):!0!==e.offerToReceiveVideo||i||this.addTransceiver("video")}return t.apply(this,arguments)}}function yr(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var Sr=Object.freeze({__proto__:null,shimLocalStreamsAPI:lr,shimRemoteStreamsAPI:hr,shimCallbacksAPI:pr,shimGetUserMedia:fr,shimConstraints:mr,shimRTCIceServerUrls:gr,shimTrackEventTransceiver:vr,shimCreateOfferLegacy:br,shimAudioContext:yr}),Er=M((function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map(e=>e.trim())},t.splitSections=function(e){return e.split("\nm=").map((e,t)=>(t>0?"m="+e:e).trim()+"\r\n")},t.getDescription=function(e){const i=t.splitSections(e);return i&&i[0]},t.getMediaSections=function(e){const i=t.splitSections(e);return i.shift(),i},t.matchPrefix=function(e,i){return t.splitLines(e).filter(e=>0===e.indexOf(i))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const i={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":i.relatedAddress=t[e+1];break;case"rport":i.relatedPort=parseInt(t[e+1],10);break;case"tcptype":i.tcpType=t[e+1];break;case"ufrag":i.ufrag=t[e+1],i.usernameFragment=t[e+1];break;default:void 0===i[t[e]]&&(i[t[e]]=t[e+1])}return i},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const i=e.component;t.push("rtp"===i?1:"rtcp"===i?2:i),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){let t=e.substr(9).split(" ");const i={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),i.name=t[0],i.clockRate=parseInt(t[1],10),i.channels=3===t.length?parseInt(t[2],10):1,i.numChannels=i.channels,i},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const i=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==i?"/"+i:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){const t={};let i;const r=e.substr(e.indexOf(" ")+1).split(";");for(let e=0;e<r.length;e++)i=r[e].trim().split("="),t[i[0].trim()]=i[1];return t},t.writeFmtp=function(e){let t="",i=e.payloadType;if(void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const r=[];Object.keys(e.parameters).forEach(t=>{r.push(void 0!==e.parameters[t]?t+"="+e.parameters[t]:t)}),t+="a=fmtp:"+i+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",i=e.payloadType;return void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(e=>{t+="a=rtcp-fb:"+i+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),i={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(i.attribute=e.substr(t+1,r-t-1),i.value=e.substr(r+1)):i.attribute=e.substr(t+1),i},t.parseSsrcGroup=function(e){const t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(e=>parseInt(e,10))}},t.getMid=function(e){const i=t.matchPrefix(e,"a=mid:")[0];if(i)return i.substr(6)},t.parseFingerprint=function(e){const t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,i){return{role:"auto",fingerprints:t.matchPrefix(e+i,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let i="a=setup:"+t+"\r\n";return e.fingerprints.forEach(e=>{i+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),i},t.parseCryptoLine=function(e){const t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,i){return t.matchPrefix(e+i,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,i){const r=t.matchPrefix(e+i,"a=ice-ufrag:")[0],n=t.matchPrefix(e+i,"a=ice-pwd:")[0];return r&&n?{usernameFragment:r.substr(12),password:n.substr(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const i={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" ");for(let n=3;n<r.length;n++){const o=r[n],s=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(s){const r=t.parseRtpMap(s),n=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(r.parameters=n.length?t.parseFmtp(n[0]):{},r.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),i.codecs.push(r),r.name.toUpperCase()){case"RED":case"ULPFEC":i.fecMechanisms.push(r.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach(e=>{i.headerExtensions.push(t.parseExtmap(e))}),i},t.writeRtpDescription=function(e,i){let r="";r+="m="+e+" ",r+=i.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=i.codecs.map(e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",i.codecs.forEach(e=>{r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)});let n=0;return i.codecs.forEach(e=>{e.maxptime>n&&(n=e.maxptime)}),n>0&&(r+="a=maxptime:"+n+"\r\n"),i.headerExtensions&&i.headerExtensions.forEach(e=>{r+=t.writeExtmap(e)}),r},t.parseRtpEncodingParameters=function(e){const i=[],r=t.parseRtpParameters(e),n=-1!==r.fecMechanisms.indexOf("RED"),o=-1!==r.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute),a=s.length>0&&s[0].ssrc;let c;const u=t.matchPrefix(e,"a=ssrc-group:FID").map(e=>e.substr(17).split(" ").map(e=>parseInt(e,10)));u.length>0&&u[0].length>1&&u[0][0]===a&&(c=u[0][1]),r.codecs.forEach(e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:a,codecPayloadType:parseInt(e.parameters.apt,10)};a&&c&&(t.rtx={ssrc:c}),i.push(t),n&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:a,mechanism:o?"red+ulpfec":"red"},i.push(t))}}),0===i.length&&a&&i.push({ssrc:a});let d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substr(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substr(5),10)*.95-16e3:void 0,i.forEach(e=>{e.maxBitrate=d})),i},t.parseRtcpParameters=function(e){const i={},r=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute)[0];r&&(i.cname=r.value,i.ssrc=r.ssrc);const n=t.matchPrefix(e,"a=rtcp-rsize");i.reducedSize=n.length>0,i.compound=0===n.length;const o=t.matchPrefix(e,"a=rtcp-mux");return i.mux=o.length>0,i},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let i;const r=t.matchPrefix(e,"a=msid:");if(1===r.length)return i=r[0].substr(7).split(" "),{stream:i[0],track:i[1]};const n=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"msid"===e.attribute);return n.length>0?(i=n[0].value.split(" "),{stream:i[0],track:i[1]}):void 0},t.parseSctpDescription=function(e){const i=t.parseMLine(e),r=t.matchPrefix(e,"a=max-message-size:");let n;r.length>0&&(n=parseInt(r[0].substr(19),10)),isNaN(n)&&(n=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substr(12),10),protocol:i.fmt,maxMessageSize:n};const s=t.matchPrefix(e,"a=sctpmap:");if(s.length>0){const e=s[0].substr(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:n}}},t.writeSctpDescription=function(e,t){let i=[];return i="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&i.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),i.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,i,r){let n;const o=void 0!==i?i:2;return n=e||t.generateSessionId(),"v=0\r\no="+(r||"thisisadapterortc")+" "+n+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,i){const r=t.splitLines(e);for(let e=0;e<r.length;e++)switch(r[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[e].substr(2)}return i?t.getDirection(i):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const i=t.splitLines(e)[0].substr(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},t.parseOLine=function(e){const i=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const i=t.splitLines(e);for(let e=0;e<i.length;e++)if(i[e].length<2||"="!==i[e].charAt(1))return!1;return!0},e.exports=t})),_r=Object.freeze(Object.assign(Object.create(null),Er,{default:Er}));function Cr(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const i=new t(e),r=Er.parseCandidate(e.candidate),n=Object.assign(i,r);return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,Di(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function Ir(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const i=function(e){if(!e||!e.sdp)return!1;const t=Er.splitSections(e.sdp);return t.shift(),t.some(e=>{const t=Er.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},r=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const i=parseInt(t[1],10);return i!=i?-1:i},n=function(e){let i=65536;return"firefox"===t.browser&&(i=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),i},o=function(e,i){let r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);const n=Er.matchPrefix(e.sdp,"a=max-message-size:");return n.length>0?r=parseInt(n[0].substr(19),10):"firefox"===t.browser&&-1!==i&&(r=2147483637),r},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(i(arguments[0])){const e=r(arguments[0]),t=n(e),i=o(arguments[0],e);let s;s=0===t&&0===i?Number.POSITIVE_INFINITY:0===t||0===i?Math.max(t,i):Math.min(t,i);const a={};Object.defineProperty(a,"maxMessageSize",{get:()=>s}),this._sctp=a}return s.apply(this,arguments)}}function Tr(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const i=e.send;e.send=function(){const r=arguments[0],n=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&n>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return i.apply(e,arguments)}}const i=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=i.apply(this,arguments);return t(e,this),e},Di(e,"datachannel",e=>(t(e.channel,e.target),e))}function Rr(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const i=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const i=new Event("connectionstatechange",e);t.dispatchEvent(i)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),i.apply(this,arguments)}})}function kr(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const i=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const i=t.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:i}):t.sdp=i}return i.apply(this,arguments)}}function Or(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const i=e.RTCPeerConnection.prototype.addIceCandidate;i&&0!==i.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():i.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function wr(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const i=e.RTCPeerConnection.prototype.setLocalDescription;i&&0!==i.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return i.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return i.apply(this,[e]);const t="offer"===e.type?this.createOffer:this.createAnswer;return t.apply(this).then(e=>i.apply(this,[e]))})}var Ar=Object.freeze({__proto__:null,shimRTCIceCandidate:Cr,shimMaxMessageSize:Ir,shimSendThrowTypeError:Tr,shimConnectionState:Rr,removeExtmapAllowMixed:kr,shimAddIceCandidateNullOrEmpty:Or,shimParameterlessSetLocalDescription:wr});const Pr=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const i=Ui,r=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:i}=e;if(i.mozGetUserMedia)t.browser="firefox",t.version=Li(i.userAgent,/Firefox\/(\d+)\./,1);else if(i.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=Li(i.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!i.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=Li(i.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),n={browserDetails:r,commonShim:Ar,extractVersion:Li,disableLog:xi,disableWarnings:Mi,sdp:_r};switch(r.browser){case"chrome":if(!Qi||!Xi||!t.shimChrome)return i("Chrome shim is not included in this adapter release."),n;if(null===r.version)return i("Chrome shim can not determine version, not shimming."),n;i("adapter.js shimming chrome."),n.browserShim=Qi,Or(e,r),wr(e),Wi(e,r),Hi(e),Xi(e,r),Gi(e),qi(e,r),zi(e),Ji(e),Ki(e),$i(e,r),Cr(e),Rr(e),Ir(e,r),Tr(e),kr(e,r);break;case"firefox":if(!dr||!tr||!t.shimFirefox)return i("Firefox shim is not included in this adapter release."),n;i("adapter.js shimming firefox."),n.browserShim=dr,Or(e,r),wr(e),Zi(e,r),tr(e,r),er(e),nr(e),ir(e),rr(e),or(e),sr(e),ar(e),cr(e),ur(e),Cr(e),Rr(e),Ir(e,r),Tr(e);break;case"safari":if(!Sr||!t.shimSafari)return i("Safari shim is not included in this adapter release."),n;i("adapter.js shimming safari."),n.browserShim=Sr,Or(e,r),wr(e),gr(e),br(e),pr(e),lr(e),hr(e),vr(e),fr(e),yr(e),Cr(e),Ir(e,r),Tr(e),kr(e,r);break;default:i("Unsupported browser!")}return n}({window:"undefined"==typeof window?void 0:window});var Lr;(Lr=(Lr=window)||self).RTCBeautyPlugin=function(e){function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e;var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(e,t){return e(t={exports:{}},t.exports),t.exports}var s,a,c=function(e){return e&&e.Math==Math&&e},u=c("object"==("undefined"==typeof globalThis?"undefined":X(globalThis))&&globalThis)||c("object"==("undefined"==typeof window?"undefined":X(window))&&window)||c("object"==("undefined"==typeof self?"undefined":X(self))&&self)||c("object"==X(n)&&n)||function(){return this}()||Function("return this")(),d=function(e){try{return!!e()}catch(e){return!0}},l=!d((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),h={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,f={f:p&&!h.call({1:2},1)?function(e){var t=p(this,e);return!!t&&t.enumerable}:h},m=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},g={}.toString,v=function(e){return g.call(e).slice(8,-1)},b="".split,y=d((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==v(e)?b.call(e,""):Object(e)}:Object,S=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},E=function(e){return y(S(e))},_=function(e){return"object"==X(e)?null!==e:"function"==typeof e},C=function(e){return"function"==typeof e?e:void 0},I=function(e,t){return arguments.length<2?C(u[e]):u[e]&&u[e][t]},T=I("navigator","userAgent")||"",R=u.process,k=u.Deno,O=R&&R.versions||k&&k.version,w=O&&O.v8;w?a=(s=w.split("."))[0]<4?1:s[0]+s[1]:T&&(!(s=T.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=T.match(/Chrome\/(\d+)/))&&(a=s[1]);var A=a&&+a,P=!!Object.getOwnPropertySymbols&&!d((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&A&&A<41})),L=P&&!Symbol.sham&&"symbol"==X(Symbol.iterator),D=L?function(e){return"symbol"==X(e)}:function(e){var t=I("Symbol");return"function"==typeof t&&Object(e)instanceof t},x=function(e,t){try{Object.defineProperty(u,e,{value:t,configurable:!0,writable:!0})}catch(i){u[e]=t}return t},M=u["__core-js_shared__"]||x("__core-js_shared__",{}),U=o((function(e){(e.exports=function(e,t){return M[e]||(M[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.16.0",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),N=function(e){return Object(S(e))},V={}.hasOwnProperty,B=Object.hasOwn||function(e,t){return V.call(N(e),t)},F=0,j=Math.random(),W=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++F+j).toString(36)},H=U("wks"),G=u.Symbol,z=L?G:G&&G.withoutSetter||W,J=function(e){return B(H,e)&&(P||"string"==typeof H[e])||(H[e]=P&&B(G,e)?G[e]:z("Symbol."+e)),H[e]},K=J("toPrimitive"),Y=function(e){var t=function(e,t){if(!_(e)||D(e))return e;var i,r=e[K];if(void 0!==r){if(void 0===t&&(t="default"),i=r.call(e,t),!_(i)||D(i))return i;throw TypeError("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var i,r;if("string"===t&&"function"==typeof(i=e.toString)&&!_(r=i.call(e)))return r;if("function"==typeof(i=e.valueOf)&&!_(r=i.call(e)))return r;if("string"!==t&&"function"==typeof(i=e.toString)&&!_(r=i.call(e)))return r;throw TypeError("Can't convert object to primitive value")}(e,t)}(e,"string");return D(t)?t:String(t)},q=u.document,$=_(q)&&_(q.createElement),Q=function(e){return $?q.createElement(e):{}},Z=!l&&!d((function(){return 7!=Object.defineProperty(Q("div"),"a",{get:function(){return 7}}).a})),ee=Object.getOwnPropertyDescriptor,te={f:l?ee:function(e,t){if(e=E(e),t=Y(t),Z)try{return ee(e,t)}catch(e){}if(B(e,t))return m(!f.f.call(e,t),e[t])}},ie=function(e){if(!_(e))throw TypeError(String(e)+" is not an object");return e},re=Object.defineProperty,ne={f:l?re:function(e,t,i){if(ie(e),t=Y(t),ie(i),Z)try{return re(e,t,i)}catch(e){}if("get"in i||"set"in i)throw TypeError("Accessors not supported");return"value"in i&&(e[t]=i.value),e}},oe=l?function(e,t,i){return ne.f(e,t,m(1,i))}:function(e,t,i){return e[t]=i,e},se=Function.toString;"function"!=typeof M.inspectSource&&(M.inspectSource=function(e){return se.call(e)});var ae,ce,ue,de=M.inspectSource,le=u.WeakMap,he="function"==typeof le&&/native code/.test(de(le)),pe=U("keys"),fe=function(e){return pe[e]||(pe[e]=W(e))},me={};if(he||M.state){var ge=M.state||(M.state=new(0,u.WeakMap)),ve=ge.get,be=ge.has,ye=ge.set;ae=function(e,t){if(be.call(ge,e))throw new TypeError("Object already initialized");return t.facade=e,ye.call(ge,e,t),t},ce=function(e){return ve.call(ge,e)||{}},ue=function(e){return be.call(ge,e)}}else{var Se=fe("state");me[Se]=!0,ae=function(e,t){if(B(e,Se))throw new TypeError("Object already initialized");return t.facade=e,oe(e,Se,t),t},ce=function(e){return B(e,Se)?e[Se]:{}},ue=function(e){return B(e,Se)}}var Ee={set:ae,get:ce,has:ue,enforce:function(e){return ue(e)?ce(e):ae(e,{})},getterFor:function(e){return function(t){var i;if(!_(t)||(i=ce(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return i}}},_e=o((function(e){var t=Ee.get,i=Ee.enforce,r=String(String).split("String");(e.exports=function(e,t,n,o){var s,a=!!o&&!!o.unsafe,c=!!o&&!!o.enumerable,d=!!o&&!!o.noTargetGet;"function"==typeof n&&("string"!=typeof t||B(n,"name")||oe(n,"name",t),(s=i(n)).source||(s.source=r.join("string"==typeof t?t:""))),e!==u?(a?!d&&e[t]&&(c=!0):delete e[t],c?e[t]=n:oe(e,t,n)):c?e[t]=n:x(t,n)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||de(this)}))})),Ce=Math.ceil,Ie=Math.floor,Te=function(e){return isNaN(e=+e)?0:(e>0?Ie:Ce)(e)},Re=Math.min,ke=function(e){return e>0?Re(Te(e),9007199254740991):0},Oe=Math.max,we=Math.min,Ae=function(e,t){var i=Te(e);return i<0?Oe(i+t,0):we(i,t)},Pe=function(e){return function(t,i,r){var n,o=E(t),s=ke(o.length),a=Ae(r,s);if(e&&i!=i){for(;s>a;)if((n=o[a++])!=n)return!0}else for(;s>a;a++)if((e||a in o)&&o[a]===i)return e||a||0;return!e&&-1}},Le={includes:Pe(!0),indexOf:Pe(!1)},De=Le.indexOf,xe=function(e,t){var i,r=E(e),n=0,o=[];for(i in r)!B(me,i)&&B(r,i)&&o.push(i);for(;t.length>n;)B(r,i=t[n++])&&(~De(o,i)||o.push(i));return o},Me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ue=Me.concat("length","prototype"),Ne={f:Object.getOwnPropertyNames||function(e){return xe(e,Ue)}},Ve={f:Object.getOwnPropertySymbols},Be=I("Reflect","ownKeys")||function(e){var t=Ne.f(ie(e)),i=Ve.f;return i?t.concat(i(e)):t},Fe=function(e,t){for(var i=Be(t),r=ne.f,n=te.f,o=0;o<i.length;o++){var s=i[o];B(e,s)||r(e,s,n(t,s))}},je=/#|\.prototype\./,We=function(e,t){var i=Ge[He(e)];return i==Je||i!=ze&&("function"==typeof t?d(t):!!t)},He=We.normalize=function(e){return String(e).replace(je,".").toLowerCase()},Ge=We.data={},ze=We.NATIVE="N",Je=We.POLYFILL="P",Ke=We,Ye=te.f,qe=function(e,t){var i,r,n,o,s,a=e.target,c=e.global,d=e.stat;if(i=c?u:d?u[a]||x(a,{}):(u[a]||{}).prototype)for(r in t){if(o=t[r],n=e.noTargetGet?(s=Ye(i,r))&&s.value:i[r],!Ke(c?r:a+(d?".":"#")+r,e.forced)&&void 0!==n){if(X(o)==X(n))continue;Fe(o,n)}(e.sham||n&&n.sham)&&oe(o,"sham",!0),_e(i,r,o,e)}},Xe=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},$e=function(e,t,i){if(Xe(e),void 0===t)return e;switch(i){case 0:return function(){return e.call(t)};case 1:return function(i){return e.call(t,i)};case 2:return function(i,r){return e.call(t,i,r)};case 3:return function(i,r,n){return e.call(t,i,r,n)}}return function(){return e.apply(t,arguments)}},Qe=Array.isArray||function(e){return"Array"==v(e)},Ze=J("species"),et=function(e,t){return new(function(e){var t;return Qe(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!Qe(t.prototype)?_(t)&&null===(t=t[Ze])&&(t=void 0):t=void 0),void 0===t?Array:t}(e))(0===t?0:t)},tt=[].push,it=function(e){var t=1==e,i=2==e,r=3==e,n=4==e,o=6==e,s=7==e,a=5==e||o;return function(c,u,d,l){for(var h,p,f=N(c),m=y(f),g=$e(u,d,3),v=ke(m.length),b=0,S=l||et,E=t?S(c,v):i||s?S(c,0):void 0;v>b;b++)if((a||b in m)&&(p=g(h=m[b],b,f),e))if(t)E[b]=p;else if(p)switch(e){case 3:return!0;case 5:return h;case 6:return b;case 2:tt.call(E,h)}else switch(e){case 4:return!1;case 7:tt.call(E,h)}return o?-1:r||n?n:E}},rt={forEach:it(0),map:it(1),filter:it(2),some:it(3),every:it(4),find:it(5),findIndex:it(6),filterReject:it(7)},nt=J("species"),ot=rt.filter,st=A>=51||!d((function(){var e=[];return(e.constructor={})[nt]=function(){return{foo:1}},1!==e.filter(Boolean).foo}));qe({target:"Array",proto:!0,forced:!st},{filter:function(e){return ot(this,e,arguments.length>1?arguments[1]:void 0)}});var at=Date.prototype,ct=at.toString,ut=at.getTime;"Invalid Date"!=String(new Date(NaN))&&_e(at,"toString",(function(){var e=ut.call(this);return e==e?ct.call(this):"Invalid Date"}));var dt=[].slice,lt={},ht=function(e,t,i){if(!(t in lt)){for(var r=[],n=0;n<t;n++)r[n]="a["+n+"]";lt[t]=Function("C,a","return new C("+r.join(",")+")")}return lt[t](e,i)};qe({target:"Function",proto:!0},{bind:Function.bind||function(e){var t=Xe(this),i=dt.call(arguments,1),r=function r(){var n=i.concat(dt.call(arguments));return this instanceof r?ht(t,n.length,n):t.apply(e,n)};return _(t.prototype)&&(r.prototype=t.prototype),r}});var pt=[].slice,ft=/MSIE .\./.test(T),mt=function(e){return function(t,i){var r=arguments.length>2,n=r?pt.call(arguments,2):void 0;return e(r?function(){("function"==typeof t?t:Function(t)).apply(this,n)}:t,i)}};qe({global:!0,bind:!0,forced:ft},{setTimeout:mt(u.setTimeout),setInterval:mt(u.setInterval)});var gt,vt=Object.keys||function(e){return xe(e,Me)},bt=l?Object.defineProperties:function(e,t){ie(e);for(var i,r=vt(t),n=r.length,o=0;n>o;)ne.f(e,i=r[o++],t[i]);return e},yt=I("document","documentElement"),St=fe("IE_PROTO"),Et=function(){},_t=function(e){return"<script>"+e+"<\/script>"},Ct=function(e){e.write(_t("")),e.close();var t=e.parentWindow.Object;return e=null,t},It=function(){try{gt=new ActiveXObject("htmlfile")}catch(e){}It=document.domain&&gt?Ct(gt):function(){var e,t=Q("iframe");if(t.style)return t.style.display="none",yt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(_t("document.F=Object")),e.close(),e.F}()||Ct(gt);for(var e=Me.length;e--;)delete It.prototype[Me[e]];return It()};me[St]=!0;var Tt=Object.create||function(e,t){var i;return null!==e?(Et.prototype=ie(e),i=new Et,Et.prototype=null,i[St]=e):i=It(),void 0===t?i:bt(i,t)},Rt=J("unscopables"),kt=Array.prototype;null==kt[Rt]&&ne.f(kt,Rt,{configurable:!0,value:Tt(null)});var Ot,wt,At,Pt=function(e){kt[Rt][e]=!0},Lt={},Dt=!d((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),xt=fe("IE_PROTO"),Mt=Object.prototype,Ut=Dt?Object.getPrototypeOf:function(e){return e=N(e),B(e,xt)?e[xt]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Mt:null},Nt=J("iterator"),Vt=!1;[].keys&&("next"in(At=[].keys())?(wt=Ut(Ut(At)))!==Object.prototype&&(Ot=wt):Vt=!0),(null==Ot||d((function(){var e={};return Ot[Nt].call(e)!==e})))&&(Ot={}),B(Ot,Nt)||oe(Ot,Nt,(function(){return this}));var Bt={IteratorPrototype:Ot,BUGGY_SAFARI_ITERATORS:Vt},Ft=ne.f,jt=J("toStringTag"),Wt=function(e,t,i){e&&!B(e=i?e:e.prototype,jt)&&Ft(e,jt,{configurable:!0,value:t})},Ht=Bt.IteratorPrototype,Gt=function(){return this},zt=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,i={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(i,[]),t=i instanceof Array}catch(e){}return function(i,r){return ie(i),function(e){if(!_(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(r),t?e.call(i,r):i.__proto__=r,i}}():void 0),Jt=Bt.IteratorPrototype,Kt=Bt.BUGGY_SAFARI_ITERATORS,Yt=J("iterator"),qt=function(){return this},Xt=function(e,t,i,r,n,o,s){!function(e,t,i){var r=t+" Iterator";e.prototype=Tt(Ht,{next:m(1,i)}),Wt(e,r,!1),Lt[r]=Gt}(i,t,r);var a,c,u,d=function(e){if(e===n&&g)return g;if(!Kt&&e in p)return p[e];switch(e){case"keys":case"values":case"entries":return function(){return new i(this,e)}}return function(){return new i(this)}},l=t+" Iterator",h=!1,p=e.prototype,f=p[Yt]||p["@@iterator"]||n&&p[n],g=!Kt&&f||d(n),v="Array"==t&&p.entries||f;if(v&&(a=Ut(v.call(new e)),Jt!==Object.prototype&&a.next&&(Ut(a)!==Jt&&(zt?zt(a,Jt):"function"!=typeof a[Yt]&&oe(a,Yt,qt)),Wt(a,l,!0))),"values"==n&&f&&"values"!==f.name&&(h=!0,g=function(){return f.call(this)}),p[Yt]!==g&&oe(p,Yt,g),Lt[t]=g,n)if(c={values:d("values"),keys:o?g:d("keys"),entries:d("entries")},s)for(u in c)(Kt||h||!(u in p))&&_e(p,u,c[u]);else qe({target:t,proto:!0,forced:Kt||h},c);return c},$t=Ee.set,Qt=Ee.getterFor("Array Iterator"),Zt=Xt(Array,"Array",(function(e,t){$t(this,{type:"Array Iterator",target:E(e),index:0,kind:t})}),(function(){var e=Qt(this),t=e.target,i=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:r,done:!1}:"values"==i?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");Lt.Arguments=Lt.Array,Pt("keys"),Pt("values"),Pt("entries");var ei=Ne.f,ti={}.toString,ii="object"==("undefined"==typeof window?"undefined":X(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],ri={f:function(e){return ii&&"[object Window]"==ti.call(e)?function(e){try{return ei(e)}catch(e){return ii.slice()}}(e):ei(E(e))}},ni=!d((function(){return Object.isExtensible(Object.preventExtensions({}))})),oi=o((function(e){var t=ne.f,i=!1,r=W("meta"),n=0,o=Object.isExtensible||function(){return!0},s=function(e){t(e,r,{value:{objectID:"O"+n++,weakData:{}}})},a=e.exports={enable:function(){a.enable=function(){},i=!0;var e=Ne.f,t=[].splice,n={};n[r]=1,e(n).length&&(Ne.f=function(i){for(var n=e(i),o=0,s=n.length;o<s;o++)if(n[o]===r){t.call(n,o,1);break}return n},qe({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:ri.f}))},fastKey:function(e,t){if(!_(e))return"symbol"==X(e)?e:("string"==typeof e?"S":"P")+e;if(!B(e,r)){if(!o(e))return"F";if(!t)return"E";s(e)}return e[r].objectID},getWeakData:function(e,t){if(!B(e,r)){if(!o(e))return!0;if(!t)return!1;s(e)}return e[r].weakData},onFreeze:function(e){return ni&&i&&o(e)&&!B(e,r)&&s(e),e}};me[r]=!0})),si=J("iterator"),ai=Array.prototype,ci=function(e){return void 0!==e&&(Lt.Array===e||ai[si]===e)},ui={};ui[J("toStringTag")]="z";var di="[object z]"===String(ui),li=J("toStringTag"),hi="Arguments"==v(function(){return arguments}()),pi=di?v:function(e){var t,i,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(i=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),li))?i:hi?v(t):"Object"==(r=v(t))&&"function"==typeof t.callee?"Arguments":r},fi=J("iterator"),mi=function(e){if(null!=e)return e[fi]||e["@@iterator"]||Lt[pi(e)]},gi=function(e){var t=e.return;if(void 0!==t)return ie(t.call(e)).value},vi=function(e,t){this.stopped=e,this.result=t},bi=function(e,t,i){var r,n,o,s,a,c,u,d=!(!i||!i.AS_ENTRIES),l=!(!i||!i.IS_ITERATOR),h=!(!i||!i.INTERRUPTED),p=$e(t,i&&i.that,1+d+h),f=function(e){return r&&gi(r),new vi(!0,e)},m=function(e){return d?(ie(e),h?p(e[0],e[1],f):p(e[0],e[1])):h?p(e,f):p(e)};if(l)r=e;else{if("function"!=typeof(n=mi(e)))throw TypeError("Target is not iterable");if(ci(n)){for(o=0,s=ke(e.length);s>o;o++)if((a=m(e[o]))&&a instanceof vi)return a;return new vi(!1)}r=n.call(e)}for(c=r.next;!(u=c.call(r)).done;){try{a=m(u.value)}catch(e){throw gi(r),e}if("object"==X(a)&&a&&a instanceof vi)return a}return new vi(!1)},yi=function(e,t,i){if(!(e instanceof t))throw TypeError("Incorrect "+(i?i+" ":"")+"invocation");return e},Si=J("iterator"),Ei=!1;try{var _i=0,Ci={next:function(){return{done:!!_i++}},return:function(){Ei=!0}};Ci[Si]=function(){return this},Array.from(Ci,(function(){throw 2}))}catch(e){}var Ii=function(e,t){if(!t&&!Ei)return!1;var i=!1;try{var r={};r[Si]=function(){return{next:function(){return{done:i=!0}}}},e(r)}catch(e){}return i},Ti=function(e,t,i){var r,n;return zt&&"function"==typeof(r=t.constructor)&&r!==i&&_(n=r.prototype)&&n!==i.prototype&&zt(e,n),e},Ri=function(e,t,i){for(var r in t)_e(e,r,t[r],i);return e},ki=J("species"),Oi=function(e){var t=I(e);l&&t&&!t[ki]&&(0,ne.f)(t,ki,{configurable:!0,get:function(){return this}})},wi=ne.f,Ai=oi.fastKey,Pi=Ee.set,Li=Ee.getterFor,Di=(function(e,t,i){var r=-1!==e.indexOf("Map"),n=-1!==e.indexOf("Weak"),o=r?"set":"add",s=u.Map,a=s&&s.prototype,c=s,l={},h=function(e){var t=a[e];_e(a,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(n&&!_(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return n&&!_(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(n&&!_(e))&&t.call(this,0===e?0:e)}:function(e,i){return t.call(this,0===e?0:e,i),this})};if(Ke(e,"function"!=typeof s||!(n||a.forEach&&!d((function(){(new s).entries().next()})))))c=i.getConstructor(t,e,r,o),oi.enable();else if(Ke(e,!0)){var p=new c,f=p[o](n?{}:-0,1)!=p,m=d((function(){p.has(1)})),g=Ii((function(e){new s(e)})),v=!n&&d((function(){for(var e=new s,t=5;t--;)e[o](t,t);return!e.has(-0)}));g||((c=t((function(t,i){yi(t,c,e);var n=Ti(new s,t,c);return null!=i&&bi(i,n[o],{that:n,AS_ENTRIES:r}),n}))).prototype=a,a.constructor=c),(m||v)&&(h("delete"),h("has"),r&&h("get")),(v||f)&&h(o),n&&a.clear&&delete a.clear}l.Map=c,qe({global:!0,forced:c!=s},l),Wt(c,e),n||i.setStrong(c,e,r)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,i,r){var n=e((function(e,o){yi(e,n,t),Pi(e,{type:t,index:Tt(null),first:void 0,last:void 0,size:0}),l||(e.size=0),null!=o&&bi(o,e[r],{that:e,AS_ENTRIES:i})})),o=Li(t),s=function(e,t,i){var r,n,s=o(e),c=a(e,t);return c?c.value=i:(s.last=c={index:n=Ai(t,!0),key:t,value:i,previous:r=s.last,next:void 0,removed:!1},s.first||(s.first=c),r&&(r.next=c),l?s.size++:e.size++,"F"!==n&&(s.index[n]=c)),e},a=function(e,t){var i,r=o(e),n=Ai(t);if("F"!==n)return r.index[n];for(i=r.first;i;i=i.next)if(i.key==t)return i};return Ri(n.prototype,{clear:function(){for(var e=o(this),t=e.index,i=e.first;i;)i.removed=!0,i.previous&&(i.previous=i.previous.next=void 0),delete t[i.index],i=i.next;e.first=e.last=void 0,l?e.size=0:this.size=0},delete:function(e){var t=o(this),i=a(this,e);if(i){var r=i.next,n=i.previous;delete t.index[i.index],i.removed=!0,n&&(n.next=r),r&&(r.previous=n),t.first==i&&(t.first=r),t.last==i&&(t.last=n),l?t.size--:this.size--}return!!i},forEach:function(e){for(var t,i=o(this),r=$e(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:i.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!a(this,e)}}),Ri(n.prototype,i?{get:function(e){var t=a(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),l&&wi(n.prototype,"size",{get:function(){return o(this).size}}),n},setStrong:function(e,t,i){var r=t+" Iterator",n=Li(t),o=Li(r);Xt(e,t,(function(e,t){Pi(this,{type:r,target:e,state:n(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,i=e.last;i&&i.removed;)i=i.previous;return e.target&&(e.last=i=i?i.next:e.state.first)?"keys"==t?{value:i.key,done:!1}:"values"==t?{value:i.value,done:!1}:{value:[i.key,i.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),i?"entries":"values",!i,!0),Oi(t)}}),di?{}.toString:function(){return"[object "+pi(this)+"]"});di||_e(Object.prototype,"toString",Di,{unsafe:!0});var xi=function(e){if(D(e))throw TypeError("Cannot convert a Symbol value to a string");return String(e)},Mi=function(e){return function(t,i){var r,n,o=xi(S(t)),s=Te(i),a=o.length;return s<0||s>=a?e?"":void 0:(r=o.charCodeAt(s))<55296||r>56319||s+1===a||(n=o.charCodeAt(s+1))<56320||n>57343?e?o.charAt(s):r:e?o.slice(s,s+2):n-56320+(r-55296<<10)+65536}},Ui={codeAt:Mi(!1),charAt:Mi(!0)},Ni=Ui.charAt,Vi=Ee.set,Bi=Ee.getterFor("String Iterator");Xt(String,"String",(function(e){Vi(this,{type:"String Iterator",string:xi(e),index:0})}),(function(){var e,t=Bi(this),i=t.string,r=t.index;return r>=i.length?{value:void 0,done:!0}:(e=Ni(i,r),t.index+=e.length,{value:e,done:!1})}));var Fi={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},ji=J("iterator"),Wi=J("toStringTag"),Hi=Zt.values;for(var Gi in Fi){var zi=u[Gi],Ji=zi&&zi.prototype;if(Ji){if(Ji[ji]!==Hi)try{oe(Ji,ji,Hi)}catch(e){Ji[ji]=Hi}if(Ji[Wi]||oe(Ji,Wi,Gi),Fi[Gi])for(var Ki in Zt)if(Ji[Ki]!==Zt[Ki])try{oe(Ji,Ki,Zt[Ki])}catch(e){Ji[Ki]=Zt[Ki]}}}var Yi="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,qi=function(e){if(void 0===e)return 0;var t=Te(e),i=ke(t);if(t!==i)throw RangeError("Wrong length or index");return i},Xi=Math.abs,$i=Math.pow,Qi=Math.floor,Zi=Math.log,er=Math.LN2,tr=function(e){for(var t=N(this),i=ke(t.length),r=arguments.length,n=Ae(r>1?arguments[1]:void 0,i),o=r>2?arguments[2]:void 0,s=void 0===o?i:Ae(o,i);s>n;)t[n++]=e;return t},ir=Ne.f,rr=ne.f,nr=Ee.get,or=Ee.set,sr=u.ArrayBuffer,ar=sr,cr=u.DataView,ur=cr&&cr.prototype,dr=Object.prototype,lr=u.RangeError,hr=function(e,t,i){var r,n,o,s=new Array(i),a=8*i-t-1,c=(1<<a)-1,u=c>>1,d=23===t?$i(2,-24)-$i(2,-77):0,l=e<0||0===e&&1/e<0?1:0,h=0;for((e=Xi(e))!=e||1/0===e?(n=e!=e?1:0,r=c):(r=Qi(Zi(e)/er),e*(o=$i(2,-r))<1&&(r--,o*=2),(e+=r+u>=1?d/o:d*$i(2,1-u))*o>=2&&(r++,o/=2),r+u>=c?(n=0,r=c):r+u>=1?(n=(e*o-1)*$i(2,t),r+=u):(n=e*$i(2,u-1)*$i(2,t),r=0));t>=8;s[h++]=255&n,n/=256,t-=8);for(r=r<<t|n,a+=t;a>0;s[h++]=255&r,r/=256,a-=8);return s[--h]|=128*l,s},pr=function(e,t){var i,r=e.length,n=8*r-t-1,o=(1<<n)-1,s=o>>1,a=n-7,c=r-1,u=e[c--],d=127&u;for(u>>=7;a>0;d=256*d+e[c],c--,a-=8);for(i=d&(1<<-a)-1,d>>=-a,a+=t;a>0;i=256*i+e[c],c--,a-=8);if(0===d)d=1-s;else{if(d===o)return i?NaN:u?-1/0:1/0;i+=$i(2,t),d-=s}return(u?-1:1)*i*$i(2,d-t)},fr=function(e){return[255&e]},mr=function(e){return[255&e,e>>8&255]},gr=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},vr=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},br=function(e){return hr(e,23,4)},yr=function(e){return hr(e,52,8)},Sr=function(e,t){rr(e.prototype,t,{get:function(){return nr(this)[t]}})},Er=function(e,t,i,r){var n=qi(i),o=nr(e);if(n+t>o.byteLength)throw lr("Wrong index");var s=nr(o.buffer).bytes,a=n+o.byteOffset,c=s.slice(a,a+t);return r?c:c.reverse()},_r=function(e,t,i,r,n,o){var s=qi(i),a=nr(e);if(s+t>a.byteLength)throw lr("Wrong index");for(var c=nr(a.buffer).bytes,u=s+a.byteOffset,d=r(+n),l=0;l<t;l++)c[u+l]=d[o?l:t-l-1]};if(Yi){if(!d((function(){sr(1)}))||!d((function(){new sr(-1)}))||d((function(){return new sr,new sr(1.5),new sr(NaN),"ArrayBuffer"!=sr.name}))){for(var Cr,Ir=(ar=function(e){return yi(this,ar),new sr(qi(e))}).prototype=sr.prototype,Tr=ir(sr),Rr=0;Tr.length>Rr;)(Cr=Tr[Rr++])in ar||oe(ar,Cr,sr[Cr]);Ir.constructor=ar}zt&&Ut(ur)!==dr&&zt(ur,dr);var kr=new cr(new ar(2)),Or=ur.setInt8;kr.setInt8(0,2147483648),kr.setInt8(1,2147483649),!kr.getInt8(0)&&kr.getInt8(1)||Ri(ur,{setInt8:function(e,t){Or.call(this,e,t<<24>>24)},setUint8:function(e,t){Or.call(this,e,t<<24>>24)}},{unsafe:!0})}else ar=function(e){yi(this,ar,"ArrayBuffer");var t=qi(e);or(this,{bytes:tr.call(new Array(t),0),byteLength:t}),l||(this.byteLength=t)},cr=function(e,t,i){yi(this,cr,"DataView"),yi(e,ar,"DataView");var r=nr(e).byteLength,n=Te(t);if(n<0||n>r)throw lr("Wrong offset");if(n+(i=void 0===i?r-n:ke(i))>r)throw lr("Wrong length");or(this,{buffer:e,byteLength:i,byteOffset:n}),l||(this.buffer=e,this.byteLength=i,this.byteOffset=n)},l&&(Sr(ar,"byteLength"),Sr(cr,"buffer"),Sr(cr,"byteLength"),Sr(cr,"byteOffset")),Ri(cr.prototype,{getInt8:function(e){return Er(this,1,e)[0]<<24>>24},getUint8:function(e){return Er(this,1,e)[0]},getInt16:function(e){var t=Er(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=Er(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return vr(Er(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return vr(Er(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return pr(Er(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return pr(Er(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){_r(this,1,e,fr,t)},setUint8:function(e,t){_r(this,1,e,fr,t)},setInt16:function(e,t){_r(this,2,e,mr,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){_r(this,2,e,mr,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){_r(this,4,e,gr,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){_r(this,4,e,gr,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){_r(this,4,e,br,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){_r(this,8,e,yr,t,arguments.length>2?arguments[2]:void 0)}});Wt(ar,"ArrayBuffer"),Wt(cr,"DataView");var wr={ArrayBuffer:ar,DataView:cr},Ar=J("species"),Pr=function(e,t){var i,r=ie(e).constructor;return void 0===r||null==(i=ie(r)[Ar])?t:Xe(i)},Lr=wr.ArrayBuffer,Dr=wr.DataView,xr=Lr.prototype.slice,Mr=d((function(){return!new Lr(2).slice(1,void 0).byteLength}));qe({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:Mr},{slice:function(e,t){if(void 0!==xr&&void 0===t)return xr.call(ie(this),e);for(var i=ie(this).byteLength,r=Ae(e,i),n=Ae(void 0===t?i:t,i),o=new(Pr(this,Lr))(ke(n-r)),s=new Dr(this),a=new Dr(o),c=0;r<n;)a.setUint8(c++,s.getUint8(r++));return o}});var Ur,Nr,Vr,Br=ne.f,Fr=u.Int8Array,jr=Fr&&Fr.prototype,Wr=u.Uint8ClampedArray,Hr=Wr&&Wr.prototype,Gr=Fr&&Ut(Fr),zr=jr&&Ut(jr),Jr=Object.prototype,Kr=Jr.isPrototypeOf,Yr=J("toStringTag"),qr=W("TYPED_ARRAY_TAG"),Xr=W("TYPED_ARRAY_CONSTRUCTOR"),$r=Yi&&!!zt&&"Opera"!==pi(u.opera),Qr=!1,Zr={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},en={BigInt64Array:8,BigUint64Array:8},tn=function(e){if(!_(e))return!1;var t=pi(e);return B(Zr,t)||B(en,t)};for(Ur in Zr)(Vr=(Nr=u[Ur])&&Nr.prototype)?oe(Vr,Xr,Nr):$r=!1;for(Ur in en)(Vr=(Nr=u[Ur])&&Nr.prototype)&&oe(Vr,Xr,Nr);if((!$r||"function"!=typeof Gr||Gr===Function.prototype)&&(Gr=function(){throw TypeError("Incorrect invocation")},$r))for(Ur in Zr)u[Ur]&&zt(u[Ur],Gr);if((!$r||!zr||zr===Jr)&&(zr=Gr.prototype,$r))for(Ur in Zr)u[Ur]&&zt(u[Ur].prototype,zr);if($r&&Ut(Hr)!==zr&&zt(Hr,zr),l&&!B(zr,Yr))for(Ur in Qr=!0,Br(zr,Yr,{get:function(){return _(this)?this[qr]:void 0}}),Zr)u[Ur]&&oe(u[Ur],qr,Ur);var rn={NATIVE_ARRAY_BUFFER_VIEWS:$r,TYPED_ARRAY_CONSTRUCTOR:Xr,TYPED_ARRAY_TAG:Qr&&qr,aTypedArray:function(e){if(tn(e))return e;throw TypeError("Target is not a typed array")},aTypedArrayConstructor:function(e){if(zt&&!Kr.call(Gr,e))throw TypeError("Target is not a typed array constructor");return e},exportTypedArrayMethod:function(e,t,i){if(l){if(i)for(var r in Zr){var n=u[r];if(n&&B(n.prototype,e))try{delete n.prototype[e]}catch(e){}}zr[e]&&!i||_e(zr,e,i?t:$r&&jr[e]||t)}},exportTypedArrayStaticMethod:function(e,t,i){var r,n;if(l){if(zt){if(i)for(r in Zr)if((n=u[r])&&B(n,e))try{delete n[e]}catch(e){}if(Gr[e]&&!i)return;try{return _e(Gr,e,i?t:$r&&Gr[e]||t)}catch(e){}}for(r in Zr)!(n=u[r])||n[e]&&!i||_e(n,e,t)}},isView:function(e){if(!_(e))return!1;var t=pi(e);return"DataView"===t||B(Zr,t)||B(en,t)},isTypedArray:tn,TypedArray:Gr,TypedArrayPrototype:zr},nn=u.ArrayBuffer,on=u.Int8Array,sn=!rn.NATIVE_ARRAY_BUFFER_VIEWS||!d((function(){on(1)}))||!d((function(){new on(-1)}))||!Ii((function(e){new on,new on(null),new on(1.5),new on(e)}),!0)||d((function(){return 1!==new on(new nn(2),1,void 0).length})),an=Math.floor,cn=function(e,t){var i=function(e){var t=Te(e);if(t<0)throw RangeError("The argument can't be less than 0");return t}(e);if(i%t)throw RangeError("Wrong offset");return i},un=rn.aTypedArrayConstructor,dn=function(e){var t,i,r,n,o,s,a=N(e),c=arguments.length,u=c>1?arguments[1]:void 0,d=void 0!==u,l=mi(a);if(null!=l&&!ci(l))for(s=(o=l.call(a)).next,a=[];!(n=s.call(o)).done;)a.push(n.value);for(d&&c>2&&(u=$e(u,arguments[2],2)),i=ke(a.length),r=new(un(this))(i),t=0;i>t;t++)r[t]=d?u(a[t],t):a[t];return r};o((function(e){var t=Ne.f,i=rt.forEach,r=Ee.get,n=Ee.set,o=ne.f,s=te.f,a=Math.round,c=u.RangeError,d=wr.ArrayBuffer,h=wr.DataView,p=rn.NATIVE_ARRAY_BUFFER_VIEWS,f=rn.TYPED_ARRAY_CONSTRUCTOR,g=rn.TYPED_ARRAY_TAG,v=rn.TypedArray,b=rn.TypedArrayPrototype,y=rn.aTypedArrayConstructor,S=rn.isTypedArray,E=function(e,t){for(var i=0,r=t.length,n=new(y(e))(r);r>i;)n[i]=t[i++];return n},C=function(e,t){o(e,t,{get:function(){return r(this)[t]}})},I=function(e){var t;return e instanceof d||"ArrayBuffer"==(t=pi(e))||"SharedArrayBuffer"==t},T=function(e,t){return S(e)&&!D(t)&&t in e&&!_(i=+t)&&isFinite(i)&&an(i)===i&&t>=0;var i},R=function(e,t){return t=Y(t),T(e,t)?m(2,e[t]):s(e,t)},k=function(e,t,i){return t=Y(t),!(T(e,t)&&_(i)&&B(i,"value"))||B(i,"get")||B(i,"set")||i.configurable||B(i,"writable")&&!i.writable||B(i,"enumerable")&&!i.enumerable?o(e,t,i):(e[t]=i.value,e)};l?(p||(te.f=R,ne.f=k,C(b,"buffer"),C(b,"byteOffset"),C(b,"byteLength"),C(b,"length")),qe({target:"Object",stat:!0,forced:!p},{getOwnPropertyDescriptor:R,defineProperty:k}),e.exports=function(e,s,l){var m=e.match(/\d+$/)[0]/8,y=e+(l?"Clamped":"")+"Array",C="get"+e,T="set"+e,R=u[y],k=R,O=k&&k.prototype,w={},A=function(e,t){o(e,t,{get:function(){return function(e,t){var i=r(e);return i.view[C](t*m+i.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,i){var n=r(e);l&&(i=(i=a(i))<0?0:i>255?255:255&i),n.view[T](t*m+n.byteOffset,i,!0)}(this,t,e)},enumerable:!0})};p?sn&&(k=s((function(e,t,i,r){return yi(e,k,y),Ti(_(t)?I(t)?void 0!==r?new R(t,cn(i,m),r):void 0!==i?new R(t,cn(i,m)):new R(t):S(t)?E(k,t):dn.call(k,t):new R(qi(t)),e,k)})),zt&&zt(k,v),i(t(R),(function(e){e in k||oe(k,e,R[e])})),k.prototype=O):(k=s((function(e,t,i,r){yi(e,k,y);var o,s,a,u=0,l=0;if(_(t)){if(!I(t))return S(t)?E(k,t):dn.call(k,t);o=t,l=cn(i,m);var p=t.byteLength;if(void 0===r){if(p%m)throw c("Wrong length");if((s=p-l)<0)throw c("Wrong length")}else if((s=ke(r)*m)+l>p)throw c("Wrong length");a=s/m}else a=qi(t),o=new d(s=a*m);for(n(e,{buffer:o,byteOffset:l,byteLength:s,length:a,view:new h(o)});u<a;)A(e,u++)})),zt&&zt(k,v),O=k.prototype=Tt(b)),O.constructor!==k&&oe(O,"constructor",k),oe(O,f,k),g&&oe(O,g,y),w[y]=k,qe({global:!0,forced:k!=R,sham:!p},w),"BYTES_PER_ELEMENT"in k||oe(k,"BYTES_PER_ELEMENT",m),"BYTES_PER_ELEMENT"in O||oe(O,"BYTES_PER_ELEMENT",m),Oi(y)}):e.exports=function(){}}))("Float32",(function(e){return function(t,i,r){return e(this,t,i,r)}}));var ln=Math.min,hn=[].copyWithin||function(e,t){var i=N(this),r=ke(i.length),n=Ae(e,r),o=Ae(t,r),s=arguments.length>2?arguments[2]:void 0,a=ln((void 0===s?r:Ae(s,r))-o,r-n),c=1;for(o<n&&n<o+a&&(c=-1,o+=a-1,n+=a-1);a-- >0;)o in i?i[n]=i[o]:delete i[n],n+=c,o+=c;return i},pn=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("copyWithin",(function(e,t){return hn.call(pn(this),e,t,arguments.length>2?arguments[2]:void 0)}));var fn=rt.every,mn=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("every",(function(e){return fn(mn(this),e,arguments.length>1?arguments[1]:void 0)}));var gn=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("fill",(function(e){return tr.apply(gn(this),arguments)}));var vn=rn.TYPED_ARRAY_CONSTRUCTOR,bn=rn.aTypedArrayConstructor,yn=function(e){return bn(Pr(e,e[vn]))},Sn=function(e,t){return function(e,t){for(var i=0,r=t.length,n=new e(r);r>i;)n[i]=t[i++];return n}(yn(e),t)},En=rt.filter,_n=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("filter",(function(e){var t=En(_n(this),e,arguments.length>1?arguments[1]:void 0);return Sn(this,t)}));var Cn=rt.find,In=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("find",(function(e){return Cn(In(this),e,arguments.length>1?arguments[1]:void 0)}));var Tn=rt.findIndex,Rn=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("findIndex",(function(e){return Tn(Rn(this),e,arguments.length>1?arguments[1]:void 0)}));var kn=rt.forEach,On=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("forEach",(function(e){kn(On(this),e,arguments.length>1?arguments[1]:void 0)}));var wn=Le.includes,An=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("includes",(function(e){return wn(An(this),e,arguments.length>1?arguments[1]:void 0)}));var Pn=Le.indexOf,Ln=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("indexOf",(function(e){return Pn(Ln(this),e,arguments.length>1?arguments[1]:void 0)}));var Dn=J("iterator"),xn=u.Uint8Array,Mn=Zt.values,Un=Zt.keys,Nn=Zt.entries,Vn=rn.aTypedArray,Bn=rn.exportTypedArrayMethod,Fn=xn&&xn.prototype[Dn],jn=!!Fn&&("values"==Fn.name||null==Fn.name),Wn=function(){return Mn.call(Vn(this))};Bn("entries",(function(){return Nn.call(Vn(this))})),Bn("keys",(function(){return Un.call(Vn(this))})),Bn("values",Wn,!jn),Bn(Dn,Wn,!jn);var Hn=rn.aTypedArray,Gn=[].join;(0,rn.exportTypedArrayMethod)("join",(function(e){return Gn.apply(Hn(this),arguments)}));var zn=function(e,t){var i=[][e];return!!i&&d((function(){i.call(null,t||function(){throw 1},1)}))},Jn=Math.min,Kn=[].lastIndexOf,Yn=!!Kn&&1/[1].lastIndexOf(1,-0)<0,qn=zn("lastIndexOf"),Xn=Yn||!qn?function(e){if(Yn)return Kn.apply(this,arguments)||0;var t=E(this),i=ke(t.length),r=i-1;for(arguments.length>1&&(r=Jn(r,Te(arguments[1]))),r<0&&(r=i+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1}:Kn,$n=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("lastIndexOf",(function(e){return Xn.apply($n(this),arguments)}));var Qn=rt.map,Zn=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("map",(function(e){return Qn(Zn(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(yn(e))(t)}))}));var eo=function(e){return function(t,i,r,n){Xe(i);var o=N(t),s=y(o),a=ke(o.length),c=e?a-1:0,u=e?-1:1;if(r<2)for(;;){if(c in s){n=s[c],c+=u;break}if(c+=u,e?c<0:a<=c)throw TypeError("Reduce of empty array with no initial value")}for(;e?c>=0:a>c;c+=u)c in s&&(n=i(n,s[c],c,o));return n}},to={left:eo(!1),right:eo(!0)},io=to.left,ro=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("reduce",(function(e){return io(ro(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var no=to.right,oo=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("reduceRight",(function(e){return no(oo(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var so=rn.aTypedArray,ao=Math.floor;(0,rn.exportTypedArrayMethod)("reverse",(function(){for(var e,t=so(this).length,i=ao(t/2),r=0;r<i;)e=this[r],this[r++]=this[--t],this[t]=e;return this}));var co=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("set",(function(e){co(this);var t=cn(arguments.length>1?arguments[1]:void 0,1),i=this.length,r=N(e),n=ke(r.length),o=0;if(n+t>i)throw RangeError("Wrong length");for(;o<n;)this[t+o]=r[o++]}),d((function(){new Int8Array(1).set({})})));var uo=rn.aTypedArray,lo=[].slice;(0,rn.exportTypedArrayMethod)("slice",(function(e,t){for(var i=lo.call(uo(this),e,t),r=yn(this),n=0,o=i.length,s=new r(o);o>n;)s[n]=i[n++];return s}),d((function(){new Int8Array(1).slice()})));var ho=rt.some,po=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("some",(function(e){return ho(po(this),e,arguments.length>1?arguments[1]:void 0)}));var fo=Math.floor,mo=function(e,t){for(var i,r,n=e.length,o=1;o<n;){for(r=o,i=e[o];r&&t(e[r-1],i)>0;)e[r]=e[--r];r!==o++&&(e[r]=i)}return e},go=function(e,t,i){for(var r=e.length,n=t.length,o=0,s=0,a=[];o<r||s<n;)a.push(o<r&&s<n?i(e[o],t[s])<=0?e[o++]:t[s++]:o<r?e[o++]:t[s++]);return a},vo=function e(t,i){var r=t.length,n=fo(r/2);return r<8?mo(t,i):go(e(t.slice(0,n),i),e(t.slice(n),i),i)},bo=T.match(/firefox\/(\d+)/i),yo=!!bo&&+bo[1],So=/MSIE|Trident/.test(T),Eo=T.match(/AppleWebKit\/(\d+)\./),_o=!!Eo&&+Eo[1],Co=rn.aTypedArray,Io=rn.exportTypedArrayMethod,To=u.Uint16Array,Ro=To&&To.prototype.sort,ko=!!Ro&&!d((function(){var e=new To(2);e.sort(null),e.sort({})})),Oo=!!Ro&&!d((function(){if(A)return A<74;if(yo)return yo<67;if(So)return!0;if(_o)return _o<602;var e,t,i=new To(516),r=Array(516);for(e=0;e<516;e++)t=e%4,i[e]=515-e,r[e]=e-2*t+3;for(i.sort((function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(i[e]!==r[e])return!0}));Io("sort",(function(e){if(void 0!==e&&Xe(e),Oo)return Ro.call(this,e);Co(this);var t,i=ke(this.length),r=Array(i);for(t=0;t<i;t++)r[t]=this[t];for(r=vo(this,function(e){return function(t,i){return void 0!==e?+e(t,i)||0:i!=i?-1:t!=t?1:0===t&&0===i?1/t>0&&1/i<0?1:-1:t>i}}(e)),t=0;t<i;t++)this[t]=r[t];return this}),!Oo||ko);var wo=rn.aTypedArray;(0,rn.exportTypedArrayMethod)("subarray",(function(e,t){var i=wo(this),r=i.length,n=Ae(e,r);return new(yn(i))(i.buffer,i.byteOffset+n*i.BYTES_PER_ELEMENT,ke((void 0===t?r:Ae(t,r))-n))}));var Ao=u.Int8Array,Po=rn.aTypedArray,Lo=rn.exportTypedArrayMethod,Do=[].toLocaleString,xo=[].slice,Mo=!!Ao&&d((function(){Do.call(new Ao(1))}));Lo("toLocaleString",(function(){return Do.apply(Mo?xo.call(Po(this)):Po(this),arguments)}),d((function(){return[1,2].toLocaleString()!=new Ao([1,2]).toLocaleString()}))||!d((function(){Ao.prototype.toLocaleString.call([1,2])})));var Uo=rn.exportTypedArrayMethod,No=u.Uint8Array,Vo=No&&No.prototype||{},Bo=[].toString,Fo=[].join;d((function(){Bo.call({})}))&&(Bo=function(){return Fo.call(this)}),Uo("toString",Bo,Vo.toString!=Bo);var jo=function(){var e=ie(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t},Wo=function(e,t){return RegExp(e,t)},Ho={UNSUPPORTED_Y:d((function(){var e=Wo("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:d((function(){var e=Wo("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},Go=d((function(){var e=RegExp(".","string".charAt(0));return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),zo=d((function(){var e=RegExp("(?<a>b)","string".charAt(5));return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),Jo=Ee.get,Ko=RegExp.prototype.exec,Yo=U("native-string-replace",String.prototype.replace),qo=Ko,Xo=function(){var e=/a/,t=/b*/g;return Ko.call(e,"a"),Ko.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),$o=Ho.UNSUPPORTED_Y||Ho.BROKEN_CARET,Qo=void 0!==/()??/.exec("")[1];(Xo||Qo||$o||Go||zo)&&(qo=function(e){var t,i,r,n,o,s,a,c=this,u=Jo(c),d=xi(e),l=u.raw;if(l)return l.lastIndex=c.lastIndex,t=qo.call(l,d),c.lastIndex=l.lastIndex,t;var h=u.groups,p=$o&&c.sticky,f=jo.call(c),m=c.source,g=0,v=d;if(p&&(-1===(f=f.replace("y","")).indexOf("g")&&(f+="g"),v=d.slice(c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==d.charAt(c.lastIndex-1))&&(m="(?: "+m+")",v=" "+v,g++),i=new RegExp("^(?:"+m+")",f)),Qo&&(i=new RegExp("^"+m+"$(?!\\s)",f)),Xo&&(r=c.lastIndex),n=Ko.call(p?i:c,v),p?n?(n.input=n.input.slice(g),n[0]=n[0].slice(g),n.index=c.lastIndex,c.lastIndex+=n[0].length):c.lastIndex=0:Xo&&n&&(c.lastIndex=c.global?n.index+n[0].length:r),Qo&&n&&n.length>1&&Yo.call(n[0],i,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(n[o]=void 0)})),n&&h)for(n.groups=s=Tt(null),o=0;o<h.length;o++)s[(a=h[o])[0]]=n[a[1]];return n});var Zo=qo;qe({target:"RegExp",proto:!0,forced:/./.exec!==Zo},{exec:Zo});J("species");var es=RegExp.prototype,ts=Ui.charAt,is=function(e,t,i){return t+(i?ts(e,t).length:1)},rs=Math.floor,ns="".replace,os=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,ss=/\$([$&'`]|\d{1,2})/g,as=function(e,t,i,r,n,o){var s=i+e.length,a=r.length,c=ss;return void 0!==n&&(n=N(n),c=os),ns.call(o,c,(function(o,c){var u;switch(c.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,i);case"'":return t.slice(s);case"<":u=n[c.slice(1,-1)];break;default:var d=+c;if(0===d)return o;if(d>a){var l=rs(d/10);return 0===l?o:l<=a?void 0===r[l-1]?c.charAt(1):r[l-1]+c.charAt(1):o}u=r[d-1]}return void 0===u?"":u}))},cs=function(e,t){var i=e.exec;if("function"==typeof i){var r=i.call(e,t);if("object"!=X(r))throw TypeError("RegExp exec method returned something other than an Object or null");return r}if("RegExp"!==v(e))throw TypeError("RegExp#exec called on incompatible receiver");return Zo.call(e,t)},us=J("replace"),ds=Math.max,ls=Math.min,hs="$0"==="a".replace(/./,"$0"),ps=!!/./[us]&&""===/./[us]("a","$0");!function(e,t,i,r){var n=J(e),o=!d((function(){var t={};return t[n]=function(){return 7},7!=""[e](t)})),s=o&&!d((function(){var e=!1,t=/a/;return t.exec=function(){return e=!0,null},t[n](""),!e}));if(!o||!s||i){var a=/./[n],c=function(e,t,i){var r=ps?"$":"$0";return[function(e,i){var r=S(this),n=null==e?void 0:e[us];return void 0!==n?n.call(e,r,i):t.call(xi(r),e,i)},function(e,n){var o=ie(this),s=xi(e);if("string"==typeof n&&-1===n.indexOf(r)&&-1===n.indexOf("$<")){var a=i(t,o,s,n);if(a.done)return a.value}var c="function"==typeof n;c||(n=xi(n));var u=o.global;if(u){var d=o.unicode;o.lastIndex=0}for(var l=[];;){var h=cs(o,s);if(null===h)break;if(l.push(h),!u)break;""===xi(h[0])&&(o.lastIndex=is(s,ke(o.lastIndex),d))}for(var p,f="",m=0,g=0;g<l.length;g++){for(var v=xi((h=l[g])[0]),b=ds(ls(Te(h.index),s.length),0),y=[],S=1;S<h.length;S++)y.push(void 0===(p=h[S])?p:String(p));var E=h.groups;if(c){var _=[v].concat(y,b,s);void 0!==E&&_.push(E);var C=xi(n.apply(void 0,_))}else C=as(v,s,b,y,E,n);b>=m&&(f+=s.slice(m,b)+C,m=b+v.length)}return f+s.slice(m)}]}(0,""[e],(function(e,t,i,r,n){var s=t.exec;return s===Zo||s===es.exec?o&&!n?{done:!0,value:a.call(t,i,r)}:{done:!0,value:e.call(i,t,r)}:{done:!1}}));_e(String.prototype,e,c[0]),_e(es,n,c[1])}}("replace",0,!!d((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!hs||ps);var fs=RegExp.prototype,ms=fs.toString;(d((function(){return"/a/b"!=ms.call({source:"a",flags:"b"})}))||"toString"!=ms.name)&&_e(RegExp.prototype,"toString",(function(){var e=ie(this),t=xi(e.source),i=e.flags;return"/"+t+"/"+xi(void 0===i&&e instanceof RegExp&&!("flags"in fs)?jo.call(e):i)}),{unsafe:!0});var gs=J("match"),vs=ne.f,bs=Ne.f,ys=Ee.enforce,Ss=J("match"),Es=u.RegExp,_s=Es.prototype,Cs=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Is=/a/g,Ts=/a/g,Rs=new Es(Is)!==Is,ks=Ho.UNSUPPORTED_Y,Os=l&&(!Rs||ks||Go||zo||d((function(){return Ts[Ss]=!1,Es(Is)!=Is||Es(Ts)==Ts||"/a/i"!=Es(Is,"i")})));if(Ke("RegExp",Os)){for(var ws=function e(t,i){var r,n,o,s,a,c,u,d,l=this instanceof e,h=_(r=t)&&(void 0!==(n=r[gs])?!!n:"RegExp"==v(r)),p=void 0===i,f=[],m=t;if(!l&&h&&p&&t.constructor===e)return t;if((h||t instanceof e)&&(t=t.source,p&&(i="flags"in m?m.flags:jo.call(m))),t=void 0===t?"":xi(t),i=void 0===i?"":xi(i),m=t,Go&&"dotAll"in Is&&(s=!!i&&i.indexOf("s")>-1)&&(i=i.replace(/s/g,"")),o=i,ks&&"sticky"in Is&&(a=!!i&&i.indexOf("y")>-1)&&(i=i.replace(/y/g,"")),zo&&(t=(c=function(e){for(var t,i=e.length,r=0,n="",o=[],s={},a=!1,c=!1,u=0,d="";r<=i;r++){if("\\"===(t=e.charAt(r)))t+=e.charAt(++r);else if("]"===t)a=!1;else if(!a)switch(!0){case"["===t:a=!0;break;case"("===t:Cs.test(e.slice(r+1))&&(r+=2,c=!0),n+=t,u++;continue;case">"===t&&c:if(""===d||B(s,d))throw new SyntaxError("Invalid capture group name");s[d]=!0,o.push([d,u]),c=!1,d="";continue}c?d+=t:n+=t}return[n,o]}(t))[0],f=c[1]),u=Ti(Es(t,i),l?this:_s,e),(s||a||f.length)&&(d=ys(u),s&&(d.dotAll=!0,d.raw=e(function(e){for(var t,i=e.length,r=0,n="",o=!1;r<=i;r++)"\\"!==(t=e.charAt(r))?o||"."!==t?("["===t?o=!0:"]"===t&&(o=!1),n+=t):n+="[\\s\\S]":n+=t+e.charAt(++r);return n}(t),o)),a&&(d.sticky=!0),f.length&&(d.groups=f)),t!==m)try{oe(u,"source",""===m?"(?:)":m)}catch(e){}return u},As=function(e){e in ws||vs(ws,e,{configurable:!0,get:function(){return Es[e]},set:function(t){Es[e]=t}})},Ps=bs(Es),Ls=0;Ps.length>Ls;)As(Ps[Ls++]);_s.constructor=ws,ws.prototype=_s,_e(u,"RegExp",ws)}Oi("RegExp");var Ds=[].join,xs=y!=Object,Ms=zn("join",",");function Us(e,t,i){var r=function(e,t,i){var r=new RegExp("\\b".concat(t," \\w+ (\\w+)"),"ig");e.replace(r,(function(e,t){return i[t]=0,e}))},n=function(e,t,i){var r=e.createShader(i);return e.shaderSource(r,t),e.compileShader(r),e.getShaderParameter(r,e.COMPILE_STATUS)?r:(console.log(e.getShaderInfoLog(r)),null)};this.uniform={},this.attribute={};var o=n(e,t,e.VERTEX_SHADER),s=n(e,i,e.FRAGMENT_SHADER);for(var a in this.id=e.createProgram(),e.attachShader(this.id,o),e.attachShader(this.id,s),e.linkProgram(this.id),e.getProgramParameter(this.id,e.LINK_STATUS)||console.log(e.getProgramInfoLog(this.id)),e.useProgram(this.id),r(t,"attribute",this.attribute),this.attribute)this.attribute[a]=e.getAttribLocation(this.id,a);for(var c in r(t,"uniform",this.uniform),r(i,"uniform",this.uniform),this.uniform)this.uniform[c]=e.getUniformLocation(this.id,c)}qe({target:"Array",proto:!0,forced:xs||!Ms},{join:function(e){return Ds.call(E(this),void 0===e?",":e)}});var Ns=function(){function e(i){t(this,e),this.canvas=i.canvas,this.width=i.width||640,this.height=i.height||480,this.gl=this.createGL(i.canvas),this.sourceTexture=this.gl.createTexture(),this.vertexBuffer=null,this.currentProgram=null,this.applied=!1,this.beautyParams={beauty:.5,brightness:.5,ruddy:.5}}return r(e,[{key:"setRect",value:function(e,t){this.width=e,this.height=t}},{key:"apply",value:function(e){if(!this.vertexBuffer){var t=new Float32Array([-1,-1,0,1,1,-1,1,1,-1,1,0,0,-1,1,0,0,1,-1,1,1,1,1,1,0]);this.vertexBuffer=this.gl.createBuffer(),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.vertexBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,t,this.gl.STATIC_DRAW),this.gl.pixelStorei(this.gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0)}this.gl.viewport(0,0,this.width,this.height),this.gl.bindTexture(this.gl.TEXTURE_2D,this.sourceTexture),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_S,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_T,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MIN_FILTER,this.gl.NEAREST),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MAG_FILTER,this.gl.NEAREST),this.applied?this.gl.texSubImage2D(this.gl.TEXTURE_2D,0,0,0,this.gl.RGB,this.gl.UNSIGNED_BYTE,e):(this.gl.texImage2D(this.gl.TEXTURE_2D,0,this.gl.RGB,this.gl.RGB,this.gl.UNSIGNED_BYTE,e),this.applied=!0),this.beauty()}},{key:"beauty",value:function(){var e=this.beautyParams,t=e.beauty,i=e.brightness,r=e.ruddy,n=2/this.width,o=2/this.height,s=this.compileBeautyShader();this.gl.uniform2f(s.uniform.singleStepOffset,n,o);var a=new Float32Array([1-.8*t,1-.6*t,.1+.45*r,.1+.45*r]);this.gl.uniform4fv(s.uniform.params,a),this.gl.uniform1f(s.uniform.brightness,.12*i),this.draw()}},{key:"draw",value:function(){this.gl.bindTexture(this.gl.TEXTURE_2D,this.sourceTexture),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,null),this.gl.uniform1f(this.currentProgram.uniform.flipY,1),this.gl.drawArrays(this.gl.TRIANGLES,0,6)}},{key:"compileBeautyShader",value:function(){if(this.currentProgram)return this.currentProgram;this.currentProgram=new Us(this.gl,["precision highp float;","attribute vec2 pos;","attribute vec2 uv;","varying vec2 vUv;","uniform float flipY;","void main(void) {","vUv = uv;","gl_Position = vec4(pos.x, pos.y*flipY, 0.0, 1.);","}"].join("\n"),["precision highp float;","uniform vec2 singleStepOffset;","uniform sampler2D texture;","uniform vec4 params;","uniform float brightness;","varying vec2 vUv;","const highp vec3 W = vec3(0.299,0.587,0.114);","const mat3 saturateMatrix = mat3(1.1102,-0.0598,-0.061,-0.0774,1.0826,-0.1186,-0.0228,-0.0228,1.1772);","vec2 blurCoordinates[24];","float hardLight(float color){","if(color <= 0.5){","color = color * color * 2.0;","} else {","color = 1.0 - ((1.0 - color)*(1.0 - color) * 2.0);","}","return color;","}","void main(){","vec3 centralColor = texture2D(texture, vUv).rgb;","blurCoordinates[0] = vUv.xy + singleStepOffset * vec2(0.0, -10.0);","blurCoordinates[1] = vUv.xy + singleStepOffset * vec2(0.0, 10.0);","blurCoordinates[2] = vUv.xy + singleStepOffset * vec2(-10.0, 0.0);","blurCoordinates[3] = vUv.xy + singleStepOffset * vec2(10.0, 0.0);","blurCoordinates[4] = vUv.xy + singleStepOffset * vec2(5.0, -8.0);","blurCoordinates[5] = vUv.xy + singleStepOffset * vec2(5.0, 8.0);","blurCoordinates[6] = vUv.xy + singleStepOffset * vec2(-5.0, 8.0);","blurCoordinates[7] = vUv.xy + singleStepOffset * vec2(-5.0, -8.0);","blurCoordinates[8] = vUv.xy + singleStepOffset * vec2(8.0, -5.0);","blurCoordinates[9] = vUv.xy + singleStepOffset * vec2(8.0, 5.0);","blurCoordinates[10] = vUv.xy + singleStepOffset * vec2(-8.0, 5.0);","blurCoordinates[11] = vUv.xy + singleStepOffset * vec2(-8.0, -5.0);","blurCoordinates[12] = vUv.xy + singleStepOffset * vec2(0.0, -6.0);","blurCoordinates[13] = vUv.xy + singleStepOffset * vec2(0.0, 6.0);","blurCoordinates[14] = vUv.xy + singleStepOffset * vec2(6.0, 0.0);","blurCoordinates[15] = vUv.xy + singleStepOffset * vec2(-6.0, 0.0);","blurCoordinates[16] = vUv.xy + singleStepOffset * vec2(-4.0, -4.0);","blurCoordinates[17] = vUv.xy + singleStepOffset * vec2(-4.0, 4.0);","blurCoordinates[18] = vUv.xy + singleStepOffset * vec2(4.0, -4.0);","blurCoordinates[19] = vUv.xy + singleStepOffset * vec2(4.0, 4.0);","blurCoordinates[20] = vUv.xy + singleStepOffset * vec2(-2.0, -2.0);","blurCoordinates[21] = vUv.xy + singleStepOffset * vec2(-2.0, 2.0);","blurCoordinates[22] = vUv.xy + singleStepOffset * vec2(2.0, -2.0);","blurCoordinates[23] = vUv.xy + singleStepOffset * vec2(2.0, 2.0);","float sampleColor = centralColor.g * 22.0;","sampleColor += texture2D(texture, blurCoordinates[0]).g;","sampleColor += texture2D(texture, blurCoordinates[1]).g;","sampleColor += texture2D(texture, blurCoordinates[2]).g;","sampleColor += texture2D(texture, blurCoordinates[3]).g;","sampleColor += texture2D(texture, blurCoordinates[4]).g;","sampleColor += texture2D(texture, blurCoordinates[5]).g;","sampleColor += texture2D(texture, blurCoordinates[6]).g;","sampleColor += texture2D(texture, blurCoordinates[7]).g;","sampleColor += texture2D(texture, blurCoordinates[8]).g;","sampleColor += texture2D(texture, blurCoordinates[9]).g;","sampleColor += texture2D(texture, blurCoordinates[10]).g;","sampleColor += texture2D(texture, blurCoordinates[11]).g;","sampleColor += texture2D(texture, blurCoordinates[12]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[13]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[14]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[15]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[16]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[17]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[18]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[19]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[20]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[21]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[22]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[23]).g * 3.0;","sampleColor = sampleColor / 62.0;","float highPass = centralColor.g - sampleColor + 0.5;","for(int i = 0; i < 5;i++){","highPass = hardLight(highPass);","}","float luminance = dot(centralColor, W);","float alpha = pow(luminance, params.r);","vec3 smoothColor = centralColor + (centralColor-vec3(highPass))*alpha*0.1;","smoothColor.r = clamp(pow(smoothColor.r, params.g),0.0,1.0);","smoothColor.g = clamp(pow(smoothColor.g, params.g),0.0,1.0);","smoothColor.b = clamp(pow(smoothColor.b, params.g),0.0,1.0);","vec3 screen = vec3(1.0) - (vec3(1.0)-smoothColor) * (vec3(1.0)-centralColor);","vec3 lighten = max(smoothColor, centralColor);","vec3 softLight = 2.0 * centralColor*smoothColor + centralColor*centralColor - 2.0 * centralColor*centralColor * smoothColor;","gl_FragColor = vec4(mix(centralColor, screen, alpha), 1.0);","gl_FragColor.rgb = mix(gl_FragColor.rgb, lighten, alpha);","gl_FragColor.rgb = mix(gl_FragColor.rgb, softLight, params.b);","vec3 satColor = gl_FragColor.rgb * saturateMatrix;","gl_FragColor.rgb = mix(gl_FragColor.rgb, satColor, params.a);","gl_FragColor.rgb = vec3(gl_FragColor.rgb + vec3(brightness));","}"].join("\n"));var e=Float32Array.BYTES_PER_ELEMENT,t=4*e;return this.gl.enableVertexAttribArray(this.currentProgram.attribute.pos),this.gl.vertexAttribPointer(this.currentProgram.attribute.pos,2,this.gl.FLOAT,!1,t,0),this.gl.enableVertexAttribArray(this.currentProgram.attribute.uv),this.gl.vertexAttribPointer(this.currentProgram.attribute.uv,2,this.gl.FLOAT,!1,t,2*e),this.currentProgram}},{key:"createGL",value:function(e){var t=e.getContext("webgl");if(t||e.getContext("experimental-webgl",{preserveDrawingBuffer:!0}),!t)throw"Couldn't get WebGL context";return t}},{key:"setBeautyParams",value:function(e){this.beautyParams=e}},{key:"reset",value:function(){this.applied=!1}}]),e}(),Vs=function(e){return"number"==typeof e},Bs=function(){function i(){t(this,i),this.video=document.createElement("video"),this.video.loop=!0,this.video.autoplay=!0,this.canvas=document.createElement("canvas"),this.filter=new Ns({canvas:this.canvas}),this.beautyParams={beauty:.5,brightness:.5,ruddy:.5},this.timeoutId=null,this.rafId=null,this.startTime=null,this.originTrack=null,this.beautyTrack=null,this.localStream=null,this.frameRate=null,this.disableStatus=!1}return r(i,[{key:"generateBeautyStream",value:function(e){var t=e.getVideoTrack();if(!t)throw new Error("Your localStream does not contain video track.");var i=this.generateBeautyTrack(t);return e.replaceTrack(i),this.localStream=e,e.setBeautyStatus&&e.setBeautyStatus(!0),e}},{key:"generateBeautyTrack",value:function(e){var t=this;this.reset();var i=e.getSettings();this.frameRate=i.frameRate,this.filter.setRect(i.width,i.height),this.setRect(i.width,i.height);var r=new MediaStream;r.addTrack(e),this.video.srcObject=r,this.video.play();var n=this.generateVideoTrackFromCanvasCapture(i.frameRate||15);return this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=requestAnimationFrame((function(){t.startTime=(new Date).getTime(),t.render()})),this.installEvents(),this.setBeautyTrack({originTrack:e,beautyTrack:n}),this.originTrack=e,this.beautyTrack=n,n}},{key:"draw",value:function(){this.video&&this.video.readyState===this.video.HAVE_ENOUGH_DATA&&this.filter.apply(this.video)}},{key:"render",value:function(){var e=this,t=(new Date).getTime();t-this.startTime>1e3/this.frameRate&&(this.draw(),this.startTime=t),document.hidden?(clearTimeout(this.timeoutId),this.timeoutId=setTimeout((function(){e.render()}),1e3/this.frameRate)):(this.timeoutId&&clearTimeout(this.timeoutId),this.rafId&&cancelAnimationFrame(this.rafId),requestAnimationFrame(this.render.bind(this)))}},{key:"setBeautyParam",value:function(e){var t=e.beauty,i=e.brightness,r=e.ruddy;Vs(t)&&(this.beautyParams.beauty=t),Vs(i)&&(this.beautyParams.brightness=i),Vs(r)&&(this.beautyParams.ruddy=r),this.filter.setBeautyParams(this.beautyParams),this.getClose()&&!this.disableStatus&&this.disable(),!this.getClose()&&this.disableStatus&&this.enable()}},{key:"setRect",value:function(e,t){var i=e||640,r=t||480;this.video.height=r,this.video.width=i,this.canvas.height=r,this.canvas.width=i}},{key:"reset",value:function(){cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.video.pause(),this.filter.reset(),this.beautyTrack&&this.beautyTrack.stop(),this.originTrack&&this.originTrack.stop()}},{key:"destroy",value:function(){cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.canvas&&(this.canvas.width=0,this.canvas.height=0,this.canvas.remove(),delete this.canvas),this.video&&(this.video.pause(),this.video.removeAttribute("srcObject"),this.video.removeAttribute("src"),this.video.load(),this.video.width=0,this.video.height=0,this.video.remove(),delete this.video),this.beautyTrack&&this.beautyTrack.stop(),this.originTrack&&this.originTrack.stop(),this.uninstallEvents()}},{key:"generateVideoTrackFromCanvasCapture",value:function(e){return this.canvas.captureStream(e).getVideoTracks()[0]}},{key:"setBeautyTrack",value:function(t){var i=t.originTrack,r=t.beautyTrack;e&&(e.beautyTrackMap||(e.beautyTrackMap=new Map),e.beautyTrackMap.set(r.id,{originTrack:i,beautyTrack:r,param:this.beautyParams,pluginInstance:this}))}},{key:"disable",value:function(){this.localStream&&this.originTrack&&(this.localStream.replaceTrack(this.originTrack),cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.disableStatus=!0)}},{key:"enable",value:function(){this.localStream&&this.beautyTrack&&(this.localStream.replaceTrack(this.beautyTrack),this.render(),this.disableStatus=!1)}},{key:"installEvents",value:function(){document.addEventListener("visibilitychange",this.render.bind(this))}},{key:"uninstallEvents",value:function(){document.removeEventListener("visibilitychange",this.render.bind(this))}},{key:"getClose",value:function(){return 0===this.beautyParams.beauty&&0===this.beautyParams.brightness&&0===this.beautyParams.ruddy}}]),i}();return e&&(e.getRTCBeautyPlugin=function(){return new Bs}),Bs}(Lr.XRTC);var Dr=RTCBeautyPlugin,xr=(function(){function e(t){L(this,e),this.logger=t,this.beautyParams={beauty:.5,brightness:.5,ruddy:.5},this.isBeautyStreamSupported=qe(),this.isBeautyStreamSupported&&(this.rtcBeautyPlugin=new Dr)}x(e,[{key:"generateBeautyStream",value:function(e){return this.logger.info("generate beauty stream ,streamId ".concat(e.streamId)),this.isBeautyStreamSupported?this.rtcBeautyPlugin.generateBeautyStream(e):e}},{key:"setBeautyParam",value:function(e){var t,i;if(!this.isBeautyStreamSupported)return this.logger.warn("The current browser does not support beauty");var r=null===(t=this.rtcBeautyPlugin)||void 0===t||null===(i=t.localStream)||void 0===i?void 0:i.getVideoTrack();if(null==r||!r.enabled)return this.logger.warn("cannot set beauty param when video track is muted");var n,o=e.beauty,s=e.brightness,a=e.ruddy;return o>=0&&o<=1&&s>=0&&s<=1&&a>=0&&a<=1?(this.beautyParams=e,this.logger.info("set beauty param beauty:".concat(o,",brightness:").concat(s,",ruddy:").concat(a)),o>.5&&(this.beautyParams.beauty=Number((.6*(o-.5)+.5).toFixed(2))),null===(n=this.rtcBeautyPlugin)||void 0===n?void 0:n.setBeautyParam(this.beautyParams)):void 0}},{key:"destroy",value:function(){var e=this,t=setTimeout((function(){clearTimeout(t),e.rtcBeautyPlugin=null,e.logger=null,e.beautyParams=null}),100);return this.logger.info("destroy beauty"),this.rtcBeautyPlugin&&this.rtcBeautyPlugin.destroy()}},{key:"updateBeautyStream",value:function(e){if(!this.isBeautyStreamSupported)return this.logger.warn("The current browser does not support beauty");this.logger.info("update beauty stream"),this.rtcBeautyPlugin.reset();var t=e.getVideoTrack();t.enabled=!0,t&&(this.rtcBeautyPlugin.generateBeautyTrack(t),this.rtcBeautyPlugin.enable())}}])}(),new wi);xr.info("browserDetails.browser",Pr.browserDetails),window.Logger=xr;var Mr=function(e){return xr.info("create client with config",JSON.stringify(e)),new Ri(e,xr)};let Ur,Nr=null,Vr=0,Br=0,Fr=null,jr=null,Wr=null,Hr=[],Gr="",zr=null,Jr="",Kr="",Yr="",qr="",Xr="AEQWEERWQRW214325453",$r="vms.cn-huadong-1.xf-yun.com",Qr="",Zr=!1,en={appId:"",apiKey:"",apiSecret:"",width:1920,height:1080,avatarId:"",streamDomId:"",isSsl:!0,transparent:0},tn={tts:{vcn:"",speed:50,pitch:50,volume:50,rhy:1}},rn={tts:{vcn:"",speed:50,pitch:50,volume:50,rhy:1},vms_dispatch:{realtime_status:{tts_status:0,vmr_status:0,vmr_action_status:0},interactive_mode:1}},nn={header:{app_id:"",uid:Xr,session:""},parameter:{vms_dispatch:{realtime_status:{tts_status:0,vmr_status:0,vmr_action_status:0},interactive_mode:1}},payload:{audio:{audio:[],encoding:"raw",sample_rate:16e3}}},on=[];const sn=e=>{let t="",i=new Uint8Array(e),r=i.byteLength;for(let e=0;e<r;e++)t+=String.fromCharCode(i[e]);return window.btoa(t)},an=(e,t,i,r,n)=>{let s=t,a=(new Date).toGMTString(),c=o.HmacSHA256(`host: ${e}\ndate: ${a}\n${n} ${t} HTTP/1.1`,r),u=o.enc.Base64.stringify(c);return s=`${s}?authorization=${window.btoa(`api_key="${i}", algorithm="hmac-sha256", headers="host date request-line", signature="${u}"`)}&date=${a}&host=${e}`,s},cn=e=>{let t=(Zr?"wss://":"ws://")+$r+an($r,e&&e.isNit?"/v1/private/vms2d_audio_ctrl_nit":"/v1/private/vms2d_audio_ctrl",Kr,Yr,"GET");(!e||e&&!e.isNit)&&Fr&&(clearInterval(zr),Fr.close()),(Fr&&1!==Fr.readyState||!Fr)&&(Br=0);let i=Object.assign({},{frameSize:1280,sendInterval:40},e);return new Promise((e,r)=>{if("WebSocket"in window)Fr=new WebSocket(t);else{if(!("MozWebSocket"in window))return console.error("浏览器不支持WebSocket"),void r({code:29007,message:"浏览器不支持WebSocket"});{let e=window.MozWebSocket;Fr=new e(t)}}Fr.onopen=e=>{setTimeout(()=>{(e=>{if(1!==Fr.readyState)return;let t=on.splice(0,e.frameSize),i={header:{...nn.header,status:0},payload:{audio:{audio:sn(t),encoding:nn.payload.audio.encoding,sample_rate:nn.payload.audio.sample_rate,status:0,seq:Br},ctrl_t:nn.payload.ctrl_t?{...nn.payload.ctrl_t,status:0,seq:Br}:void 0,ctrl_postproc:nn.payload.ctrl_postproc?{...nn.payload.ctrl_postproc,status:0,seq:Br}:void 0}};Fr.send(JSON.stringify(i)),Br++,zr=setInterval(()=>1!==Fr.readyState?(on=[],void clearInterval(zr)):0===on.length?(Fr.send(JSON.stringify({header:{...nn.header,status:2},payload:{audio:{audio:sn(t),encoding:nn.payload.audio.encoding,sample_rate:nn.payload.audio.sample_rate,status:2,seq:Br},ctrl_t:nn.payload.ctrl_t?{...nn.payload.ctrl_t,status:2,seq:Br}:void 0,ctrl_postproc:nn.payload.ctrl_postproc?{...nn.payload.ctrl_postproc,status:2,seq:Br}:void 0}})),on=[],clearInterval(zr),!1):(t=on.splice(0,e.frameSize),Fr.send(JSON.stringify({header:{...nn.header,status:1},payload:{audio:{audio:sn(t),encoding:nn.payload.audio.encoding,sample_rate:nn.payload.audio.sample_rate,status:1,seq:Br},ctrl_t:nn.payload.ctrl_t?{...nn.payload.ctrl_t,status:1,seq:Br}:void 0,ctrl_postproc:nn.payload.ctrl_postproc?{...nn.payload.ctrl_postproc,status:1,seq:Br}:void 0}})),void Br++),e.sendInterval)})(i)},600)},Fr.onmessage=t=>{let i=JSON.parse(t.data);0===i.header.code&&2===i.header.status&&Fr.close(),0===i.header.code&&0===i.header.status&&e({code:0,message:"success"}),0!==i.header.code&&(Fr.close(),r(i.header))},Fr.onerror=e=>{console.error("WebSocket error:",e),r({code:29008,message:"WebSocket error"})},Fr.onclose=e=>{Fr=null}})},un=e=>{let t="/vmss"+an($r,"/v1/private/vms2d_ping",Kr,Yr,"POST");fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({header:{app_id:Jr,uid:Xr,session:qr}})}).then(t=>{t.json().then(t=>{0!==t.header.code?(console.log("failed to ping vms:",t.header),Nr&&clearInterval(Nr),console.log("ping保活异常，停止ping心跳保活"),document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",e&&e(t.header)):qr=t.header.session}).catch(t=>{Nr&&clearInterval(Nr),document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),console.log("ping保活异常，停止ping心跳保活"),Gr="",e&&e(t)})}).catch(t=>{Nr&&clearInterval(Nr),console.log("ping保活异常，停止ping心跳保活"),e&&e(t)})},dn=(e,t,i)=>{let r="/vmss"+an($r,"/v1/private/vms2d_start",Kr,Yr,"POST"),n=e.resId?{app_id:e.appId,uid:Xr,res_id:e.resId}:{app_id:e.appId,uid:Xr};return new Promise((o,s)=>{fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({header:n,parameter:{vmr:{stream:{protocol:"xrtc"},avatar_id:e.avatarId,width:e.width,height:e.height,template_id:e.templateId||void 0,interactive_scene:e.interactiveScene,move_h:e.moveH,move_v:e.moveV,scale:e.scale,mask_region:e.maskRegion,transparent:e.transparent||0}}})}).then(e=>{e.json().then(e=>{0===e.header.code?(qr=e.header.session,Qr=I(e.payload.stream_url.text),console.log("启动虚拟人服务成功，启动一次ping心跳保活"),un(t),((e,t)=>new Promise((i,r)=>{const n=Mr({userId:"4365",mode:"live",sdkAppId:"1000000001",userSig:"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aW1lIjoiMTY1MDAxMjU0OSIsImlzcyI6IjEwMDAwMDAwMDEifQ.0ung32D-D_izQgmy9N1XG1p_Cxkqj4ARLq-3j0uyAqM",wsUrl:Qr.split("xrtc://")[1].split("/")[0],ssl:Zr});n.join({roomId:Qr.split("xrtc://")[1].split("/")[1],role:"anchor"}).then(()=>{}).catch(e=>{console.error("Join room failed: "+e),r({code:29001,message:"Join room failed: "+e})}),n.on("stream-subscribed",o=>{console.log("订阅流成功，开始ping心跳保活（10s）"),Nr&&clearInterval(Nr),Nr=setInterval(()=>{un()},1e4),Wr=o.stream,Hr.push(Wr),Wr.on("error",e=>{Ur=e.getCode(),console.log("subscribed中监听到了视频流的错误，错误码code：",Ur),16451===Ur?r({message:"PLAY_NOT_ALLOWED ERROR: 请进行页面操作并调用resume方法回复音视频播放",code:Ur,stream:Wr,streamsAll:Hr,client:n}):Ur?(document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr=""):(document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",r({code:Ur,message:"stream error: "+Ur,client:n}))}),console.log("stream.hasVideo：",Wr.hasVideo()),console.log("stream.hasAudio：",Wr.hasAudio()),Wr.play(Gr).then(()=>{console.log("进入了play成功的回调了"),16451===Ur?(e&&e({message:"PLAY_NOT_ALLOWED ERROR: 请进行页面操作并调用resume方法回复音视频播放",code:Ur,stream:Wr,streamsAll:Hr,client:n}),console.log("play成功中监听到了视频流的错误，错误码code：",Ur),r({message:"PLAY_NOT_ALLOWED ERROR: 请进行页面操作并调用resume方法回复音视频播放",code:Ur,stream:Wr,streamsAll:Hr,client:n})):Ur?(document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr=""):(t&&t(),i({code:0,message:"paly stream successed",stream:Wr,streamsAll:Hr,client:n}))}).catch(t=>{document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",console.error("failed to play remoteStream:",t),console.log("进入了play失败的回调了，错误码code：",Ur),e&&e({code:Ur,message:"paly stream failed: "+t,client:n,stream:Wr,streamsAll:Hr}),r({code:29002,message:"paly stream failed: "+t,client:n,stream:Wr,streamsAll:Hr})})}),n.on("stream-added",t=>{n.subscribe(t.stream,{audio:!0,video:!0}).catch(t=>{console.error("failed to subscribe remoteStream"),e&&e(),document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",r({code:29003,message:"failed to subscribe remoteStream"})}).then(()=>{})}),n.on("stream-updated",e=>{const t=e.stream,i=t.getId(),r=[...Hr],n=r.findIndex(e=>e.getId()===i);r[n]=t,Hr=r})}))(t,i).then(e=>{o(e)}).catch(e=>{console.error("初始化xrtc客户端失败：",e),s(e)})):(console.error(e.header),Wr=null,Gr="",s(e.header))}).catch(e=>{console.error("failed to start vms:",e),Wr=null,Gr="",s({code:29004,message:"failed to start vms"})})}).catch(e=>{Wr=null,Gr="",console.error("failed to start vms:",e),s({code:29004,message:"failed to start vms"})})})},ln=(e,t)=>{let i=((e,t,i,r,n,s)=>{let a=(new Date).toGMTString(),c="SHA256="+o.enc.Base64.stringify(o.SHA256(s)),u=o.HmacSHA256(`host: ${e}\ndate: ${a}\n${n} ${t} HTTP/1.1\ndigest: ${c}`,r);return{"X-Date":a,Authorization:`api_key="${i}", algorithm="hmac-sha256", headers="host date request-line digest", signature="${o.enc.Base64.stringify(u)}"`,Digest:c}})("evo-hu.xf-yun.com","/individuation/sgen/reg",e.apiKey,e.apiSecret,"POST",JSON.stringify({business:{res_id:t||void 0,res_desc:{res_type:"background_data"}},common:{app_id:e.appId},data:e.resourceBase64Str}));return new Promise((r,n)=>{fetch("/individuation/sgen/reg",{method:"POST",headers:i,body:JSON.stringify({business:{res_id:t||void 0,res_desc:{res_type:"background_data"}},common:{app_id:e.appId},data:e.resourceBase64Str})}).then(e=>{e.json().then(e=>{0===e.code?r({code:e.code,data:e.data?e.data.res_id:""}):(console.error("上传个性化资源失败：",e),n(e))})}).catch(e=>{console.error("上传个性化资源失败：",e||{message:"failed to upload data",code:29005}),n(e||{message:"failed to upload data",code:29005})})})},hn=(e,t,i)=>{let r=Object.assign({},en,e);return Kr=r.apiKey,Yr=r.apiSecret,Jr=r.appId,Gr=r.streamDomId,Zr=r.isSsl,dn(r,t,i)};let pn={uploadResourceData:function(e,t){return ln(e,t)},textDriver:function(e){return(e=>new Promise((t,i)=>{if(!e.payload.text.text)return console.error("文本不可为空"),void i({code:29010,message:"文本不可为空"});e.header?(e.header.app_id=Jr,e.header.status=3,e.header.session=qr,e.header.uid=Xr):e.header={app_id:Jr,session:qr,status:3,uid:Xr},e.parameter.tts&&(e.parameter.tts=Object.assign({},tn.tts,e.parameter.tts)),e.payload.text&&(e.payload.text=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.text)),e.payload.ctrl_w&&(e.payload.ctrl_w=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.ctrl_w)),e.payload.ctrl_postproc&&(e.payload.ctrl_postproc=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.ctrl_postproc));let r="/vmss"+an($r,"/v1/private/vms2d_ctrl",Kr,Yr,"POST");fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}).then(e=>{e.json().then(e=>{0!==e.header.code?i(e.header):t(e.header)}).catch(e=>{console.error("failed to start text driver:",e),i({code:29011,message:"failed to start text driver"})})})}))(e)},textDriverNitInit:function(e){return(e=>new Promise((t,i)=>{let r=(Zr?"wss://":"ws://")+$r+an($r,"/v1/private/vms2d_text_ctrl_nit",Kr,Yr,"GET");if("WebSocket"in window)jr=new WebSocket(r);else{if(!("MozWebSocket"in window))return void i({code:29007,message:"浏览器不支持WebSocket"});{let e=window.MozWebSocket;jr=new e(r)}}jr.onopen=e=>{Vr=0,t({code:0,message:"流式文本驱动初始化成功"})},jr.onmessage=t=>{let r=JSON.parse(t.data);e&&e(r),0!==r.header.code&&(jr.close(),console.error(r.header),i(r.header))},jr.onerror=e=>{console.error("WebSocket error:",e),Vr=0,jr.close(),i({code:29008,message:"WebSocket error"})},jr.onclose=e=>{Vr=0,jr=null,console.log("非打断文本驱动ws已关闭")}}))(e)},sendTextDriverData:function(e){return(e=>new Promise((t,i)=>{e.header?(e.header.app_id=Jr,e.header.status=Vr,e.header.session=qr,e.header.uid=Xr):e.header={app_id:Jr,session:qr,status:Vr,uid:Xr},e.parameter.tts&&(e.parameter.tts=Object.assign({},rn.tts,e.parameter.tts)),e.parameter.vms_dispatch&&(e.parameter.vms_dispatch=Object.assign({},rn.vms_dispatch,e.parameter.vms_dispatch)),e.payload.text&&(e.payload.text=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.text),e.payload.text.status=Vr),e.payload.ctrl_w&&(e.payload.ctrl_w=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.ctrl_w),e.payload.ctrl_w.status=Vr),e.payload.ctrl_postproc&&(e.payload.ctrl_postproc=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",status:3,text:""},e.payload.ctrl_postproc),e.payload.ctrl_postproc.status=Vr),jr&&1===jr.readyState?(jr.send(JSON.stringify(e)),Vr=1,t({code:0,message:"文本驱动数据发送成功"})):i({code:29008,message:"WebSocket error:WebSocket已关闭或者异常"})}))(e)},pauseTextDriverNit:function(){return new Promise((e,t)=>{let i="/vmss"+an($r,"/v1/private/vms2d_reset",Kr,Yr,"POST");fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({header:{app_id:Jr,uid:Xr,session:qr}})}).then(i=>{i.json().then(i=>{0!==i.header.code?(console.error("流式文本驱动暂停失败：",i.header),t(i.header)):(e(i.header),console.log("流式文本驱动暂停成功"))}).catch(e=>{t({code:29012,message:"流式文本驱动暂停失败",data:e}),console.error("流式文本驱动暂停失败：",e)})}).catch(e=>{console.error("流式文本驱动暂停失败：",e),t({code:29012,message:"流式文本驱动暂停失败",data:e})})})},stopTextDriverNit:function(){return new Promise((e,t)=>{!jr||1!==jr.readyState&&0!==jr.readyState?t({code:29008,message:"文本驱动ws断开失败，当前已是关闭或者正在关闭状态"}):(jr.close(),e({code:0,message:"文本驱动ws断开成功"}))})},audioDriverInit:function(e){return cn(e)},audioDriverSendData:function(e){return(e=>{nn=e,e.header?(nn.header.app_id=Jr,nn.header.session=qr,nn.header.uid=Xr):e.header={app_id:Jr,session:qr,uid:"77607d78ccc5424986cdbe4039879b14"},e.parameter.vms_dispatch&&(nn.parameter.vms_dispatch=Object.assign({},nn.parameter.vms_dispatch,e.parameter.vms_dispatch)),e.payload.audio&&(nn.payload.audio=Object.assign({},nn.payload.audio,e.payload.audio),on.push(...nn.payload.audio.audio)),e.payload.ctrl_t&&(nn.payload.ctrl_t=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",text:""},e.payload.ctrl_t)),e.payload.ctrl_postproc&&(nn.payload.ctrl_postproc=Object.assign({},{encoding:"utf8",compress:"raw",format:"json",text:""},e.payload.ctrl_postproc))})(e)},stop:function(){return(()=>{let e="/vmss"+an($r,"/v1/private/vms2d_stop",Kr,Yr,"POST");return new Promise((t,i)=>{fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({header:{app_id:Jr,uid:Xr,session:qr}})}).then(e=>{Wr=null,console.log("虚拟人服务已停止，停止ping心跳保活"),Nr&&clearInterval(Nr),document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",e.json().then(e=>{Wr=null,Hr=[],0===e.header.code?t(e.header):(console.error("failed to stop vms:",e.header),i(e.header))}).catch(e=>{console.error("failed to stop vms:",e),Wr=null})}).catch(e=>{console.error("failed to stop vms:",e),Wr=null,document.getElementById(Gr)&&(document.getElementById(Gr).innerHTML=""),Gr="",i({code:29006,message:"failed to stop vms"})})})})()},start:function(e,t,i){return Gr||Wr?(console.log("当前虚拟人服务处于激活状态，请避免连续点击，建议先结束之后，再重新启动"),new Promise((e,t)=>{t({code:29004,message:"当前虚拟人服务处于激活状态，请避免连续点击，建议先结束之后，再重新启动"})})):hn(e,t,i)},VERSION:"vms-2d-web-2.0.0"};export default pn;
