//公共/全局函数定义
export const DEVELOP = 'development'; // 开发环境
export const PRODUCTION = 'production'; // 正式环境

//  基于前端的/static下文件下载地址前缀 配置来源manifest.json 中的h5 : router : base
export const h5RouterBase = '/shancai_xiaozhi';



const WECHAT_ENV = process.env.NODE_ENV;
const origin = (function() {
	const config = {
		[DEVELOP]: 'http://localhost:8080',
		// [PRODUCTION]: 'https://www.aicaipap.com',
		// [PRODUCTION]: 'https://www.papaicai.com',
		[PRODUCTION]: 'https://aicai.sdufe.edu.cn',
	};
	return config[WECHAT_ENV];
}())
const prefix = (function() {
	const config = {
		[DEVELOP]: '', // TODO
		// [PRODUCTION]: '/prod-api',
		[PRODUCTION]: '/stage-api',
	};
	return config[WECHAT_ENV];
}())

/* 生产  外部*/ 
const configProExternal = {
	appId: "ww3cf8a35b063ef0d2", // 临时测试 企业
	agentId: "1000025", // 临时测试 企业 agentId
	url: "https://www.papaicai.com/shancai_xiaozhi/",
}
/** 自建 内部应用*/
const configProInternal = {
    appId: "ww9eaf864bf860f930", // 山东银瑞 企业
    agentId: "1000002", // 山东银瑞 企业 agentId
    url: "https://www.aicaipap.com/shancai_xiaozhi/",
}
/** 自建 山东财经大学内部应用*/
const shanCaiConfigProInternal = {
	appId: "wx355a6295c2812f08", // 山东财经大学内部应用 企业
	agentId: "1000064", //  山东财经大学内部应用 agentId
	url: "https://aicai.sdufe.edu.cn/shancai_xiaozhi/",
}

/* 请求失败弹出层 */
const errorToast = (msg, success = () => {}) => {
	uni.showToast({
		title: msg,
		icon: 'none',
		type: "error",
		mask: true,
		duration: 2500,
		success: () => {
			setTimeout(() => {
				success()
			}, 2500)
		}
	});
}


/* 请求成功弹出层 */
const successToast = (msg, success = () => {}) => {
	uni.showToast({
		title: msg,
		icon: 'success',
		type: 'success',
		mask: true,
		duration: 2500,
		success: () => {
			setTimeout(() => {
				success()
			}, 2500)
		}
	});
}
//输出
module.exports = {
	origin,
	prefix,
	baseUrl: origin + prefix,
	h5RouterBase: h5RouterBase,
	errorToast,
	successToast,
    configProExternal: configProExternal,
    configProInternal: configProInternal,
	shanCaiConfigProInternal: shanCaiConfigProInternal,
}