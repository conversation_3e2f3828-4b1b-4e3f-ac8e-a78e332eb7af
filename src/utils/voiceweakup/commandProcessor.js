import store from '@/store';

/**
 * 语音命令处理器
 * 负责解析语音识别结果并触发相应的命令
 */
class CommandProcessor {
  constructor() {
    // 命令注册表
    this.commandRegistry = {
      global: {} // 全局命令
    };
    
    // 当前上下文
    this.activeContext = 'global';
    
    // 处理中标志，防止命令重复执行
    this.isProcessing = false;
    
    // 命令历史，用于避免短时间内重复执行相同命令
    this.commandHistory = [];
    
    // 历史记录最大长度
    this.historyMaxLength = 10;
  }
  
  /**
   * 注册命令
   * @param {string} context 上下文名称
   * @param {Object} commands 命令映射表 {命令文本: 处理函数}
   */
  registerCommands(context, commands) {
    if (!this.commandRegistry[context]) {
      this.commandRegistry[context] = {};
    }
    
    // 合并命令
    this.commandRegistry[context] = {
      ...this.commandRegistry[context],
      ...commands
    };
    
    console.log(`[CommandProcessor] 已为上下文 ${context} 注册 ${Object.keys(commands).length} 个命令`);
    return this;
  }
  
  /**
   * 设置当前活跃上下文
   * @param {string} context 上下文名称
   */
  setActiveContext(context) {
    this.activeContext = context;
    console.log(`[CommandProcessor] 当前上下文: ${context}`);
    return this;
  }
  
  /**
   * 处理识别结果
   * @param {string} transcript 识别文本
   * @returns {boolean} 是否匹配并执行了命令
   */
  processTranscript(transcript) {
    // 如果没有识别结果或正在处理中，则忽略
    if (!transcript || this.isProcessing) {
      return false;
    }
    
    // 防止重复处理
    this.isProcessing = true;
    
    try {
      // 获取当前上下文的命令和全局命令
      const contextCommands = this.commandRegistry[this.activeContext] || {};
      const globalCommands = this.commandRegistry.global || {};
      
      // 合并命令集，当前上下文命令优先级高于全局命令
      const allCommands = { ...globalCommands, ...contextCommands };
      
      // 检查是否匹配任何命令
      for (const [commandText, handler] of Object.entries(allCommands)) {
        if (transcript.includes(commandText)) {
          // 检查是否是最近执行过的命令
          const isDuplicate = this.checkDuplicate(commandText);
          if (isDuplicate) {
            console.log(`[CommandProcessor] 忽略重复命令: ${commandText}`);
            continue;
          }
          
          console.log(`[CommandProcessor] 匹配到命令: ${commandText}`);
          
          // 添加到历史记录
          this.addToHistory(commandText);
          
          // 执行命令
          if (typeof handler === 'function') {
            handler(transcript);
            
            // 清空识别结果
            store.dispatch('speechRecognition/clearTranscript');
            
            // 标记为已处理
            this.isProcessing = false;
            return true;
          }
        }
      }
      
      // 没有找到匹配的命令
      // console.log(`[CommandProcessor] 未找到匹配的命令: ${transcript}`);
      this.isProcessing = false;
      return false;
      
    } catch (error) {
      console.error('[CommandProcessor] 处理命令时出错:', error);
      this.isProcessing = false;
      return false;
    }
  }
  
  /**
   * 检查命令是否在短时间内重复执行
   * @param {string} command 命令文本
   * @returns {boolean} 是否是重复命令
   */
  checkDuplicate(command) {
    const now = Date.now();
    
    // 查找历史记录中最近的相同命令
    const recentCommand = this.commandHistory.find(item => 
      item.command === command && now - item.timestamp < 3000
    );
    
    return !!recentCommand;
  }
  
  /**
   * 添加命令到历史记录
   * @param {string} command 命令文本
   */
  addToHistory(command) {
    // 添加新记录
    this.commandHistory.push({
      command,
      timestamp: Date.now()
    });
    
    // 如果历史记录超过最大长度，删除最旧的记录
    if (this.commandHistory.length > this.historyMaxLength) {
      this.commandHistory.shift();
    }
  }
  
  /**
   * 重置命令处理器
   */
  reset() {
    this.isProcessing = false;
    this.commandHistory = [];
  }
}

// 创建单例实例
const commandProcessor = new CommandProcessor();

// 导出单例
export default commandProcessor;
