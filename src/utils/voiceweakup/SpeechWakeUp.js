/**
 * 语音识别服务
 * 封装了 Web Speech API 的 SpeechWakeUp 接口
 */
class SpeechWakeUp {
  // 单例实例
  static instance = null;
  
  /**
   * 获取单例实例
   * @param {Object} options 配置选项
   * @returns {SpeechWakeUp} 单例实例
   */
  static getInstance(options = {}) {
    if (!SpeechWakeUp.instance) {
      SpeechWakeUp.instance = new SpeechWakeUp(options);
    } else if (options && Object.keys(options).length > 0) {
      // 如果已有实例但提供了新选项，则更新配置
      SpeechWakeUp.instance.updateConfig(options);
    }
    return SpeechWakeUp.instance;
  }

  constructor(options = {}) {
    // 如果已经存在实例，直接返回该实例
    if (SpeechWakeUp.instance) {
      return SpeechWakeUp.instance;
    }
    
    // 默认配置
    this.config = {
      lang: options.lang || 'zh-CN',
      continuous: options.continuous !== undefined ? options.continuous : true,
      interimResults: options.interimResults !== undefined ? options.interimResults : true,
      autoRestart: options.autoRestart !== undefined ? options.autoRestart : true
    };

    // 状态
    this.isListening = false;
    this.isUnsupported = false;
    this.manualStopped = false;
    this.transcript = '';
    this.interimTranscript = '';
    
    // 回调函数
    this.onStart = options.onStart || (() => {});
    this.onEnd = options.onEnd || (() => {});
    this.onResult = options.onResult || (() => {});
    this.onError = options.onError || (() => {});
    
    // 上下文管理
    this.activeContext = 'global';
    this.commandRegistry = {};
    
    // 初始化
    this.init();
    
    // 保存实例
    SpeechWakeUp.instance = this;
  }

  /**
   * 初始化语音识别
   */
  init() {
    // 检查浏览器支持情况
    let SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    // 检测浏览器类型
    const isEdge = /Edge/.test(navigator.userAgent);
    const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);

    if (isEdge) {
      SpeechRecognition = window.SpeechRecognition;
      // this.recognition = new SpeechRecognition();
    } else if (isChrome) {
      SpeechRecognition = window.webkitSpeechRecognition;
      console.log('当前浏览器为Chrome');
      // this.recognition = new webkitSpeechRecognition();
    }
    
    if (!SpeechRecognition) {
      this.isUnsupported = true;
      console.error('当前浏览器不支持语音识别功能');
      return;
    }
    
    // 创建语音识别实例
    this.recognition = new SpeechRecognition();
    this.recognition.continuous = this.config.continuous;
    this.recognition.interimResults = this.config.interimResults;
    this.recognition.lang = this.config.lang;
    
    // 设置事件处理
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 开始事件
    this.recognition.onstart = () => {
      this.isListening = true;
      this.onStart();
    };
    
    // 结束事件
    this.recognition.onend = () => {
      this.isListening = false;
      this.onEnd();
      
      // 如果不是手动停止且配置了自动重启，则自动重新开始
      if (this.config.autoRestart && !this.manualStopped) {
        setTimeout(() => {
          this.start();
        }, 500);
      }
    };
    
    // 结果事件
    this.recognition.onresult = (event) => {
      this.interimTranscript = '';
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        
        if (event.results[i].isFinal) {
          this.transcript += transcript + ' ';
        } else {
          this.interimTranscript += transcript;
        }
      }
      
      // 调用结果回调
      this.onResult({
        final: this.transcript,
        interim: this.interimTranscript
      });
    };
    
    // 错误事件
    this.recognition.onerror = (event) => {
      // console.error('语音识别错误:', event.error);
      this.onError(event.error);
    };
  }

  /**
   * 开始语音识别
   */
  start() {
    if (this.isUnsupported || this.isListening) return;
    
    // 重置临时结果
    this.interimTranscript = '';
    
    // 重置手动停止标志
    this.manualStopped = false;
    
    // 开始识别
    try {
      this.recognition.start();
    } catch (error) {
      console.error('启动语音识别失败:', error);
    }
  }

  /**
   * 停止语音识别
   */
  stop() {
    if (!this.isListening) return;
    
    this.manualStopped = true;
    this.recognition.stop();
  }

  /**
   * 切换语音识别状态
   */
  toggle() {
    if (this.isListening) {
      this.stop();
    } else {
      this.start();
    }
  }

  /**
   * 清除识别结果
   */
  clearTranscript() {
    this.transcript = '';
    this.interimTranscript = '';
  }
  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    const wasListening = this.isListening;
    
    // 如果正在监听，先停止
    if (wasListening) {
      this.stop();
    }
    
    // 更新配置
    this.config = { ...this.config, ...newConfig };
    
    // 应用新配置
    if (this.recognition) {
      this.recognition.continuous = this.config.continuous;
      this.recognition.interimResults = this.config.interimResults;
      this.recognition.lang = this.config.lang;
    }
    
    // 如果之前在监听，重新开始
    if (wasListening) {
      setTimeout(() => {
        this.start();
      }, 300);
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isListening: this.isListening,
      isUnsupported: this.isUnsupported,
      transcript: this.transcript,
      interimTranscript: this.interimTranscript
    };
  }

  /**
   * 设置回调函数
   * @param {Object} callbacks 回调函数对象
   */
  setCallbacks(callbacks = {}) {
    if (callbacks.onStart) this.onStart = callbacks.onStart;
    if (callbacks.onEnd) this.onEnd = callbacks.onEnd;
    if (callbacks.onResult) this.onResult = callbacks.onResult;
    if (callbacks.onError) this.onError = callbacks.onError;
  }
  
  /**
   * 设置当前活跃上下文
   * @param {string} context 上下文名称
   */
  setActiveContext(context) {
    this.activeContext = context;
    console.log('语音识别上下文已切换到:', context);
  }
  
  /**
   * 注册特定上下文的语音命令
   * @param {string} context 上下文名称
   * @param {Object} commands 命令映射表
   */
  registerCommands(context, commands) {
    this.commandRegistry[context] = commands;
    console.log(`已为上下文 ${context} 注册 ${Object.keys(commands).length} 个命令`);
  }
  
  /**
   * 处理识别结果，检查是否匹配命令
   * @param {string} transcript 识别文本
   * @returns {boolean} 是否匹配并执行了命令
   */
  processTranscript(transcript) {
    if (!transcript) return false;
    
    // 先检查全局命令
    const globalCommands = this.commandRegistry['global'] || {};
    
    // 再检查当前上下文的命令
    const contextCommands = this.commandRegistry[this.activeContext] || {};
    
    // 合并命令集
    const commands = { ...globalCommands, ...contextCommands };
    
    // 检查是否匹配任何命令
    for (const [commandText, handler] of Object.entries(commands)) {
      if (transcript.includes(commandText)) {
        console.log(`匹配到命令: ${commandText}`);
        
        // 执行命令处理函数
        if (typeof handler === 'function') {
          handler(transcript);
        }
        
        // 清空识别结果，避免重复处理
        this.clearTranscript();
        return true;
      }
    }
    
    return false;
  }
}

// 导出单例获取方法
export default SpeechWakeUp.getInstance; 