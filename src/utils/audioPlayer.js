class AudioPlayer {
  constructor() {
    this.audioCache = {};
    this.preloadAudios();
  }

  // 预加载常用音频
  preloadAudios() {
    this.loadAudio('wake', require('@/assets/audio/001.mp3'));
  }

  // 加载音频到缓存
  loadAudio(name, path) {
    const audio = new Audio(path);
    audio.load();
    this.audioCache[name] = audio;
  }

  // 播放指定音频
  play(name) {
    if (this.audioCache[name]) {
      // 如果正在播放，先重置
      this.audioCache[name].pause();
      this.audioCache[name].currentTime = 0;
      
      // 播放音频
      this.audioCache[name].play().catch(e => {
        console.error('音频播放失败:', e);
      });
    } else {
      console.warn(`音频 ${name} 未找到`);
    }
  }
}

// 导出单例
export default new AudioPlayer();
