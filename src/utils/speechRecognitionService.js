// services/speechRecognitionService.js
// import jsRecorder from 'js-recorder';
import { jsRecorder } from "@/utils/record.js"; //引入录音文件
import { eventBus } from './eventBus';

class SpeechRecognitionService {
  constructor() {
    this.text = '';
    this.isRecording = false;
    this.websocket = null;
    this.recStart = null;
    this.recStop = null;
  }

  initRecorder(callback) {
    console.log('initRecorder');
    const recorder = jsRecorder(callback);
    this.recStart = recorder.recStart;
    this.recStop = recorder.recStop;
  }

  startSpeechRecognition(callback, options) {
    console.log('startSpeechRecognition');
    console.log(options);
    navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
      this.isRecording = true;
      this.recStart();
      this.asrWebSocket(options);
    }).catch(error => {
      callback(error);
    });
  }

  asrWebSocket(options) {
    console.log("建立连接");
    
    // 确保之前的连接已关闭
    if (this.websocket) {
      try {
        this.websocket.close();
      } catch (e) {
        console.log("关闭旧连接出错", e);
      }
      this.websocket = null;
    }
    
    const url = 'wss://vop.baidu.com/realtime_asr?sn=a-zA-Z0-9';
    this.websocket = new WebSocket(url);
    
    // 设置连接超时
    const connectionTimeout = setTimeout(() => {
      if (this.websocket && this.websocket.readyState !== WebSocket.OPEN) {
        console.error("WebSocket 连接超时");
        this.websocket.close();
        this.isRecording = false;
        eventBus.$emit(options.name, { type: "ERROR", text: "连接超时" });
      }
    }, 5000);
    
    this.websocket.onopen = () => {
      console.log("WebSocket 连接已打开");
      clearTimeout(connectionTimeout);
      this.sendStart(options);
    };
    
    this.websocket.onmessage = (evt) => {
      try {
        if (evt.data) {
          const obj = JSON.parse(evt.data);
          console.log("收到消息:", obj);
          
          // 检查错误
          if (obj.error !== 0 && obj.error !== undefined) {
            console.error("语音识别错误:", obj);
            eventBus.$emit(options.name, { type: "ERROR", text: "识别错误: " + obj.error_msg });
            return;
          }
          
          this.text = obj.result || "";
          const result = {
            text: this.text,
            type: obj.type
          };
          
          if (obj.type === 'MID_TEXT') {
            eventBus.$emit(options.name, result);
          }
          if (obj.type === 'FIN_TEXT') {
            eventBus.$emit(options.name, result);
          }
        }
      } catch (error) {
        console.error("处理消息时出错:", error, evt.data);
      }
    };
    
    this.websocket.onerror = (evt) => {
      console.error('websocket-asr错误：', evt);
      clearTimeout(connectionTimeout);
      eventBus.$emit(options.name, { type: "ERROR", text: "连接错误" });
    };
    
    this.websocket.onclose = (evt) => {
      console.log('websocket-asr关闭：', evt);
      clearTimeout(connectionTimeout);
      // 如果是非正常关闭，通知用户
      if (evt.code !== 1000 && this.isRecording) {
        eventBus.$emit(options.name, { type: "ERROR", text: "连接已关闭: " + evt.code });
      }
    };
  }

  sendStart(options) {
    try {
      console.log("发送START命令，参数:", options);
      
      // 确保参数类型正确
      const appId = parseInt(options.appId, 10) || 0;
      const appKey = String(options.appKey || "");
      const dev_pid = parseInt(options.dev_pid, 10) || 1537;
      
      const startData = {
        type: "START",
        data: {
          appid: appId,
          appkey: appKey,
          dev_pid: dev_pid,
          cuid: "cuid-" + new Date().getTime(), // 使用时间戳确保唯一性
          format: "pcm",
          sample: 16000
        }
      };
      
      console.log("发送数据:", JSON.stringify(startData));
      this.websocket.send(JSON.stringify(startData));
    } catch (error) {
      console.error("发送START命令时出错:", error);
    }
  }

  closeWebsocket() {
    try {
      console.log("关闭WebSocket连接");
      this.recStop();
      this.isRecording = false;
      
      // 只有在连接已打开的状态下才发送 FINISH 消息
      if (this.websocket) {
        console.log("WebSocket状态:", this.websocket.readyState);
        
        if (this.websocket.readyState === WebSocket.OPEN) {
          console.log("发送FINISH消息");
          this.websocket.send(JSON.stringify({ type: "FINISH" }));
          
          // 设置一个超时，确保消息发送后关闭连接
          setTimeout(() => {
            if (this.websocket) {
              console.log("关闭WebSocket");
              this.websocket.close();
              this.websocket = null;
            }
          }, 300);
        } else {
          // 如果连接不是OPEN状态，直接关闭
          console.log('WebSocket 未完全连接或已关闭，直接关闭');
          if (this.websocket.readyState !== WebSocket.CLOSED) {
            this.websocket.close();
          }
          this.websocket = null;
        }
      }
    } catch (error) {
      console.error('关闭 WebSocket 时出错:', error);
      // 确保重置状态
      this.websocket = null;
    }
  }
}

const speechRecognitionService = new SpeechRecognitionService();

export default speechRecognitionService;
