import Cookies from 'js-cookie'
import {getInfo} from "@/api/login";
import {getToken} from "@/utils/auth";

// 回显数据字典
export function selectDictLabelByVal(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].value === ('' + value)) {
			actions.push(datas[key].label);
			return true;
		}
	})
	return actions.join('');
}


// 删除所有cookie
export function removeAllCooke() {
	// 获取当前所有的 cookies
	const cookies = document.cookie.split(';');

	// 遍历 cookies，并逐个删除
	cookies.forEach(cookie => {
		const cookieName = cookie.split('=')[0].trim();
		Cookies.remove(cookieName); // 删除指定名称的 cookie
	});
}

// 获取登录用户角色
export function getRoles() {
	const token = getToken();
	const rawRoles = Cookies.get('roles');

	if (token && !rawRoles) {
		getInfo().then(res => {
			const expiresIn = new Date();
			expiresIn.setMinutes(expiresIn.getMinutes() + 10);
			Cookies.set('roles', res.roles.join(','), { expires: expiresIn });
			return res.roles;
		});
	}

	return (rawRoles || '').split(','); // 安全拆分，即使是 undefined/null/'' 也不报错
}

export const CreateNotificationType = {
  WARNING: 'warning',
  ERROR: 'error',
  INFO: 'info',
  SUCCESS: 'success'
};
/**
 * 创建自定义通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('warning', 'error', 'info', 'success')
 * @param {number} duration 显示时间（毫秒），0表示不自动关闭
 * @param {Object} options 额外选项
 * @param {string} options.containerId 自定义容器ID
 * @param {number} options.zIndex 自定义z-index
 * @param {Object} options.position 自定义位置 {top, right, bottom, left}
 * @returns {Object} 带有close方法的通知控制对象
 */
export function createNotification(message, type = 'warning', duration = 3000, options = {}) {
	const {
		containerId = 'global-notification-container',
		zIndex = 9999,
		position = { top: '40px', right: '20px' }
	} = options;

	// 创建通知容器（如果不存在）
	let notificationContainer = document.getElementById(containerId);

	if (!notificationContainer) {
		notificationContainer = document.createElement('div');
		notificationContainer.id = containerId;

		// 构建位置样式
		const positionStyle = Object.entries(position)
			.map(([key, value]) => `${key}: ${value};`)
			.join(' ');

		notificationContainer.style.cssText = `
			position: fixed;
			${positionStyle}
			z-index: ${zIndex};
			display: flex;
			flex-direction: column;
			align-items: flex-end;
		`;
		document.body.appendChild(notificationContainer);
	}

	// 创建通知元素
	const notification = document.createElement('div');
	notification.style.cssText = `
		padding: 12px 20px;
		margin-bottom: 10px;
		border-radius: 4px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		min-width: 260px;
		max-width: 360px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
		font-size: 14px;
		opacity: 0;
		transform: translateX(20px);
		transition: opacity 0.3s, transform 0.3s;
	`;

	// 根据类型设置样式
	let backgroundColor, textColor, iconContent;
	switch(type) {
		case 'error':
			backgroundColor = '#FEF0F0';
			textColor = '#F56C6C';
			iconContent = '❌';
			break;
		case 'success':
			backgroundColor = '#F0F9EB';
			textColor = '#67C23A';
			iconContent = '✅';
			break;
		case 'info':
			backgroundColor = '#EDF2FC';
			textColor = '#409EFF';
			iconContent = 'ℹ️';
			break;
		case 'warning':
		default:
			backgroundColor = '#FDF6EC';
			textColor = '#E6A23C';
			iconContent = '⚠️';
	}

	notification.style.backgroundColor = backgroundColor;
	notification.style.color = textColor;

	// 创建图标
	const icon = document.createElement('span');
	icon.style.marginRight = '8px';
	icon.textContent = iconContent;

	// 创建消息文本
	const text = document.createElement('span');
	text.style.flex = '1';
	text.textContent = message;

	// 创建关闭按钮
	const closeButton = document.createElement('span');
	closeButton.style.cssText = `
		margin-left: 10px;
		cursor: pointer;
		font-size: 16px;
		opacity: 0.7;
	`;
	closeButton.textContent = '×';
	closeButton.onclick = function() {
		removeNotification();
	};

	// 组合元素
	notification.appendChild(icon);
	notification.appendChild(text);
	notification.appendChild(closeButton);

	// 添加到容器
	notificationContainer.appendChild(notification);

	// 触发动画显示
	setTimeout(() => {
		notification.style.opacity = '1';
		notification.style.transform = 'translateX(0)';
	}, 10);

	// 移除通知的函数
	const removeNotification = () => {
		notification.style.opacity = '0';
		notification.style.transform = 'translateX(20px)';

		setTimeout(() => {
			if (notification.parentNode) {
				notification.parentNode.removeChild(notification);
			}

			// 检查容器是否为空，如果为空则移除容器
			if (notificationContainer.children.length === 0) {
				document.body.removeChild(notificationContainer);
			}
		}, 300);
	};

	// 设置自动移除定时器
	if (duration > 0) {
		setTimeout(removeNotification, duration);
	}

	return {
		close: removeNotification,
		element: notification
	};
}

/**
 * 创建左上角通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('warning', 'error', 'info', 'success')
 * @param {number} duration 显示时间（毫秒），0表示不自动关闭
 * @param {Object} options 额外选项
 * @returns {Object} 带有close方法的通知控制对象
 */
export function createNotificationTopLeft(message, type = 'warning', duration = 3000, options = {}) {
	const position = { top: '20px', left: '20px' };
	const mergedOptions = {
		...options,
		position,
		containerId: options.containerId || 'notification-top-left-container'
	};
	return createNotification(message, type, duration, mergedOptions);
}

/**
 * 创建右上角通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('warning', 'error', 'info', 'success')
 * @param {number} duration 显示时间（毫秒），0表示不自动关闭
 * @param {Object} options 额外选项
 * @returns {Object} 带有close方法的通知控制对象
 */
export function createNotificationTopRight(message, type = 'warning', duration = 3000, options = {}) {
	const position = { top: '20px', right: '20px' };
	const mergedOptions = {
		...options,
		position,
		containerId: options.containerId || 'notification-top-right-container'
	};
	return createNotification(message, type, duration, mergedOptions);
}

/**
 * 创建右下角通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('warning', 'error', 'info', 'success')
 * @param {number} duration 显示时间（毫秒），0表示不自动关闭
 * @param {Object} options 额外选项
 * @returns {Object} 带有close方法的通知控制对象
 */
export function createNotificationBottomRight(message, type = 'warning', duration = 3000, options = {}) {
	const position = { bottom: '20px', right: '20px' };
	const mergedOptions = {
		...options,
		position,
		containerId: options.containerId || 'notification-bottom-right-container'
	};
	return createNotification(message, type, duration, mergedOptions);
}

/**
 * 创建左下角通知
 * @param {string} message 通知消息
 * @param {string} type 通知类型 ('warning', 'error', 'info', 'success')
 * @param {number} duration 显示时间（毫秒），0表示不自动关闭
 * @param {Object} options 额外选项
 * @returns {Object} 带有close方法的通知控制对象
 */
export function createNotificationBottomLeft(message, type = 'warning', duration = 3000, options = {}) {
	const position = { bottom: '20px', left: '20px' };
	const mergedOptions = {
		...options,
		position,
		containerId: options.containerId || 'notification-bottom-left-container'
	};
	return createNotification(message, type, duration, mergedOptions);
}



