/**
 * Monaco Editor 配置工具
 */

// 设置Monaco Editor的工作环境
export function setupMonacoEnvironment() {
  // 配置Monaco Editor的worker路径
  if (typeof window !== 'undefined') {
    window.MonacoEnvironment = {
      getWorkerUrl: function (moduleId, label) {
        if (label === 'json') {
          return '/static/js/json.worker.js'
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
          return '/static/js/css.worker.js'
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
          return '/static/js/html.worker.js'
        }
        if (label === 'typescript' || label === 'javascript') {
          return '/static/js/ts.worker.js'
        }
        return '/static/js/editor.worker.js'
      }
    }
  }
}

// 默认编辑器配置
export const defaultEditorOptions = {
  fontSize: 14,
  minimap: { enabled: true },
  scrollBeyondLastLine: false,
  automaticLayout: true,
  tabSize: 4,
  insertSpaces: true,
  wordWrap: 'on',
  lineNumbers: 'on',
  glyphMargin: true,
  folding: true,
  lineDecorationsWidth: 10,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'all',
  selectOnLineNumbers: true,
  roundedSelection: false,
  readOnly: false,
  cursorStyle: 'line',
  automaticLayout: true,
  theme: 'vs'
}

// Python语言特定配置
export const pythonEditorOptions = {
  ...defaultEditorOptions,
  language: 'python'
}
