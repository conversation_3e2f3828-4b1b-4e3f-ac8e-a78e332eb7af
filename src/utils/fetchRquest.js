
import { getToken } from '@/utils/auth';

// 基础URL，比如 process.env.VUE_APP_BASE_API
const baseURL = process.env.VUE_APP_BASE_API;

const fetchRequest = (options) => {
	const {
		url,
		method = 'get',
		data = null,
		headers = {},
		params = {} // 用于 GET 请求参数
	} = options;

	// 拼接完整 URL（包含 base API）
	let fullUrl = baseURL + url;

	// 处理 GET 请求参数
	if ((method.toLowerCase() === 'get' || method.toLowerCase() === 'delete') && params) {
		const queryString = new URLSearchParams(params).toString();
		if (queryString) {
			fullUrl += '?' + queryString;
		}
	}

	// 构建请求配置
	const config = {
		method: method.toUpperCase(),
		headers: {
			'Content-Type': 'application/json',
			Authorization: getToken(),
			...headers,
		},
	};

	// POST/PUT/PATCH 等需要 body
	if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
		config.body = JSON.stringify(data);
	}

	return new Promise(async (resolve, reject) => {
		try {
			const response = await fetch(fullUrl, config);
			resolve(response);
		} catch (error) {
			reject(error);
		}
	});
};

export default fetchRequest;
