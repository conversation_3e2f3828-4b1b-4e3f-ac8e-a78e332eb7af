<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />

    <div class="right-menu">
      <template v-if="device !== 'mobile'">

        <el-dropdown size="mini" @command="(command) => handleCommand(command)"
          class="right-menu-item hover-effect language-select" trigger="click">
          <span class="el-dropdown-link">
            {{ voiceType }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown" class="language-select-menu">
            <el-dropdown-item v-for="dict in dict.type.voice_type" :key="dict.value"
              :command="dict.value">{{dict.label}}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <search id="header-search" class="right-menu-item" />

        <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar" />
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/authentication">
            <el-dropdown-item>信息认证</el-dropdown-item>
          </router-link>
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout" v-if="this.isCasLogin==='false'">
            <span>退出登录</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="handleCasLogout" v-if="this.isCasLogin==='true'">
            <span>CAS退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import { chooseLanguage, getVoiceType } from "@/api/system/layout.js";
import {casLogout} from "@/api/login";
import Cookies from "js-cookie";
import store from "@/store";
import axios from 'axios'
export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
  },
  dicts: ["voice_type"],
  data() {
    return {
      voiceType: '中文',
      isCasLogin: 'false',
    }
  },
  created() {
    this.getVoiceType()
    this.getLogOutType()

  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    getLogOutType() {
      let isCasLogin = Cookies.get("isCasLogin")
      if (isCasLogin) {
        this.isCasLogin = isCasLogin;
      }
      console.log("退出类型 this.isCasLogin", this.isCasLogin)
    },
    // 处理CAS退出
    async handleCasLogout() {
      // 退出CAS的系统
      // 跳转到CAS认证
      console.log("进行CAS退出")

      casLogout().then((res) => {
        console.log("完成CAS退出")
        console.log("res.data", res.data)
        // 清空缓存
        setTimeout(() => {
          console.log("清除本系统相关缓存")
          Cookies.remove("Admin-Token");
          Cookies.remove("Admin-Expires-In");
          Cookies.remove("isCasLogin");
          Cookies.remove("shanCaiCasLogout");
          Cookies.remove("aiCaiLoginUrl");
          Cookies.remove("shanCaiLoginRedirectUrl");
          Cookies.remove("loginRedirectUrl");
          Cookies.remove("casLogout");
          Cookies.remove("userId");
          Cookies.remove("userName");
        }, 100);
        window.location.href = res.data;
      });

    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          store.dispatch('LogOut').then(() => {
            location.href = '/login';
          });
        })
        .catch(() => { });
    },
    handleCommand(command) {
      chooseLanguage({ 'voiceType': command }).then((res) => {
        if (res.code == 200) {
          Cookies.remove('voiceType');
          Cookies.set("voiceType", command);
          this.voiceType = this.selectDictLabelByVal(this.dict.type.voice_type, command);
        }
      }).catch(() => { });
    },
    getVoiceType() {
      getVoiceType().then((res) => {
        if (res.code == 200) {
          Cookies.remove('voiceType');
          Cookies.set("voiceType", res.data.voiceType);
          this.voiceType = this.selectDictLabelByVal(this.dict.type.voice_type, res.data.voiceType);
        }
      }).catch(() => { });
    }
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
    .language-select {
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
.language-select-menu {
  top: 35px !important;
  li {
    padding: 0 15px !important;
    font-size: 14px !important;
    text-align: center !important;
  }
}
</style>
