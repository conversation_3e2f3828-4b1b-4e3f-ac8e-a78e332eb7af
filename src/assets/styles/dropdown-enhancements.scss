/**
 * 下拉列表增强样式
 * 用于解决动态背景下的可读性问题和美化下拉列表
 * {{ AURA-X: Create - 全局下拉列表增强样式文件. Approval: 寸止(ID:dropdown-enhancements-file). }}
 */

// 自定义下拉列表样式 - 适配动态背景
.custom-select-dropdown {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  padding: 8px 0 !important;
  z-index: 3000 !important;
  
  // 确保下拉列表在动态背景上的可见性
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    z-index: -1;
  }

  .el-select-dropdown__item {
    padding: 12px 16px !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: transparent !important;
    color: #303133 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    position: relative !important;
    
    // 确保文字在各种背景下都可见
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    
    &:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
      color: #667eea !important;
      transform: translateX(4px) !important;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
    }

    &.selected {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%) !important;
      color: #667eea !important;
      font-weight: 600 !important;
      
      &::after {
        content: '✓';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }

  // 选项内容样式增强
  .option-content {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;

    i {
      font-size: 14px !important;
      color: rgba(102, 126, 234, 0.8) !important;
      opacity: 0.9 !important;
      transition: all 0.3s ease !important;
      flex-shrink: 0 !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    }

    .option-text {
      flex: 1 !important;
      font-weight: 500 !important;
      color: #303133 !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    }

    .option-count {
      font-size: 12px !important;
      color: #606266 !important;
      background: rgba(102, 126, 234, 0.08) !important;
      padding: 3px 8px !important;
      border-radius: 6px !important;
      font-weight: 500 !important;
      transition: all 0.3s ease !important;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
      border: 1px solid rgba(102, 126, 234, 0.1) !important;
    }
  }

  .el-select-dropdown__item:hover .option-content {
    i {
      opacity: 1 !important;
      transform: scale(1.1) !important;
      color: #667eea !important;
    }

    .option-text {
      color: #667eea !important;
    }

    .option-count {
      background: rgba(102, 126, 234, 0.15) !important;
      color: #667eea !important;
      border-color: rgba(102, 126, 234, 0.2) !important;
    }
  }

  // 空状态样式
  .el-select-dropdown__empty {
    padding: 20px !important;
    text-align: center !important;
    color: #909399 !important;
    font-size: 14px !important;
    background: rgba(248, 249, 250, 0.8) !important;
    margin: 8px !important;
    border-radius: 8px !important;
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px !important;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05) !important;
    border-radius: 3px !important;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3) !important;
    border-radius: 3px !important;

    &:hover {
      background: rgba(102, 126, 234, 0.5) !important;
    }
  }
}

// 增强型选择器输入框样式
.enhanced-select-input {
  .el-input__inner {
    border-radius: 8px !important;
    border: 2px solid rgba(228, 231, 237, 0.8) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    color: #303133 !important;
    
    // 确保文字在动态背景下可见
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;

    &:hover {
      border-color: rgba(102, 126, 234, 0.6) !important;
      background: rgba(255, 255, 255, 0.98) !important;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
      transform: translateY(-1px) !important;
    }

    &:focus {
      border-color: #667eea !important;
      background: rgba(255, 255, 255, 1) !important;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 6px 16px rgba(102, 126, 234, 0.2) !important;
      transform: translateY(-2px) !important;
    }
    
    &::placeholder {
      color: rgba(192, 196, 204, 0.8) !important;
      text-shadow: none !important;
    }
  }

  // 优化下拉箭头样式
  .el-input__suffix {
    .el-input__suffix-inner {
      .el-select__caret {
        color: rgba(144, 147, 153, 0.8) !important;
        transition: all 0.3s ease !important;
        
        &.is-reverse {
          color: #667eea !important;
          transform: rotateZ(180deg) scale(1.1) !important;
        }
      }
    }
  }

  &:hover .el-input__suffix .el-input__suffix-inner .el-select__caret {
    color: #667eea !important;
    transform: scale(1.1) !important;
  }
}

// 增强型搜索框样式
.enhanced-search-input {
  .el-input__inner {
    border-radius: 12px !important;
    border: 2px solid rgba(228, 231, 237, 0.8) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    padding: 12px 16px 12px 40px !important;
    font-size: 14px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    color: #303133 !important;
    
    // 确保文字在动态背景下可见
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;

    &:hover {
      border-color: rgba(102, 126, 234, 0.6) !important;
      background: rgba(255, 255, 255, 0.98) !important;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
      transform: translateY(-1px) !important;
    }

    &:focus {
      border-color: #667eea !important;
      background: rgba(255, 255, 255, 1) !important;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 6px 16px rgba(102, 126, 234, 0.2) !important;
      transform: translateY(-2px) !important;
    }

    &::placeholder {
      color: rgba(192, 196, 204, 0.8) !important;
      font-style: italic !important;
      text-shadow: none !important;
    }
  }

  .el-input__prefix {
    left: 12px !important;
    color: rgba(144, 147, 153, 0.8) !important;
    transition: all 0.3s ease !important;
  }

  &:hover .el-input__prefix {
    color: #667eea !important;
    transform: scale(1.1) !important;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .custom-select-dropdown {
    border-radius: 8px !important;
    
    .el-select-dropdown__item {
      padding: 10px 12px !important;
      margin: 1px 6px !important;
      font-size: 13px !important;
    }
  }
  
  .enhanced-select-input,
  .enhanced-search-input {
    .el-input__inner {
      font-size: 14px !important;
      padding: 10px 12px !important;
    }
  }
  
  .enhanced-search-input {
    .el-input__inner {
      padding-left: 36px !important;
    }
  }
}
