export function splitTextByPunctuation(text,maxLength) {
        const punctuation = /[，。！？；]/g;
        let result = []
        let currentSegment = ""
        while(text.length > 0){
            // 正则表达式匹配字符
            let match = punctuation.exec(text);
            // 如果匹配到字符
            if(match){
                let segment = text.slice(0,match.index+1);
                // 移出匹配之前的字符
                text = text.slice(match.index+1);
                // 如果当前片段加上匹配字符的长度小于最大长度，则将匹配字符添加到当前片段中
                if(currentSegment.length + segment.length <= maxLength){
                    currentSegment += segment
                }else{
                    // 如果当前片段的长度大于0，则将当前片段添加到结果数组中
                    if(currentSegment.length > 0){
                        // 将当前片段添加到结果数组中并去掉空格
                        result.push(currentSegment.trim())
                    }
                    // 将未匹配的字符赋值给当前片段 
                    currentSegment = segment;
                }
            }else{
                // 如果没有匹配到字符，则将剩余字符添加到当前片段中（当前文字段没有字符且长度小于最大长度）
                if(currentSegment.length + text.length <= maxLength){
                    currentSegment += text;
                    text = "";
                }else{
                    // 当前字符串没有标点每次添加最打的
                    if(currentSegment.length > 0){
                        result.push(currentSegment.trim())
                    }
                    // 截取最大字符串
                    currentSegment = text.slice(0,maxLength)
                    // 移出截取字符串
                    text = text.slice(maxLength)
                }
            }
        }
        // 最后剩一个片段，将当前片段添加到结果数组中
        if(currentSegment.length > 0){
            result.push(currentSegment.trim())
        }
        return result
    }