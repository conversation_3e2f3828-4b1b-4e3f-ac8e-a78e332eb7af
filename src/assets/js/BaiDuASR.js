import { baiduSpeechToText } from "../../api/explorationCenter/experience.js";
import { HZRecorder} from './recorder.js';
import Cookies from "js-cookie";
export const AudioRecorder = {
    audio_context: '',
    countdown: 60, // 倒计时初始值
    intervalId: null, // 倒计时的 intervalId
    translationFlag: false, // 录音标志
    mediaRecorder: null, // 录音对象
    baiduToken: '', // 百度 API token
    analyser: null,
    pid: '',
    isRecorder: true, // 是否正在录音
    resultData: {
        content: '',
        flag: '',
    },
    content: '',
    cuid: `${Math.floor(Math.random() * 1000000)}`, // 设备唯一标识

    
    // 初始化音频录制
    InitAudio() {
        var that = this
          //获取录音对象
          try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            navigator.mediaDevices.getUserMedia =  navigator.mediaDevices.getUserMedia ||
                                    navigator.webkitGetUserMedia ||
                                navigator.mozGetUserMedia ||
                             navigator.msGetUserMedia
            window.URL = window.URL || window.webkitURL;
            that.audio_context = new AudioContext();
            console.log(
              "navigator.getUserMedia " +
                (navigator.getUserMedia ? "available." : "not present!")
            );
          } catch (e) {
            alert("No web audio support in this browser!");
          }
    
          navigator.getUserMedia(
            { audio: true },
            function (stream) {
                that.mediaRecorder = new HZRecorder(stream);
                // console.log("初始化完成");
                // 创建 AnalyserNode 用于监控音量
                that.analyser = that.audio_context.createAnalyser();
                const source = that.audio_context.createMediaStreamSource(stream);
                source.connect(that.analyser);
                that.mediaRecorder && that.mediaRecorder.start();
                setTimeout(() => {
                    that.monitorVolume();
                }, 1500);
            },
            function (e) {
              console.log("No live audio input: " + e);
            }
          );
      },
  
    // 开始录音并倒计时
    startRecording() {
        this.InitAudio();
        this.isRecorder = true;
        return new Promise((resolve, reject) => {
        // 开始倒计时
        this.countdown = 60;
        // 60 秒后还在录音，自动停止
        this.intervalId = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--;
          }else{
            if(this.isRecorder){
                this.stopRecording().then(result => {
                    // this.content = result;
                    // this.resultData.content = this.content;
                    // this.resultData.flag = this.isRecorder;
                    resolve(result);
                })
            }
          }
        }, 1000);
        
        });

    },
    // 停止录音
    stopRecording() {
        this.mediaRecorder.stop();
        var mp3Blob = this.mediaRecorder.upload()
        this.isRecorder = false;

        // 是否存在数据
        if(mp3Blob.size == 0){
            return Promise.reject('未检测到音频');
        }
        return new Promise((resolve, reject) => {
            let blobToDataURL=(blob, callback)=> {
                var a = new FileReader();
                a.onload = function (e) { callback(e.target.result.split('data:audio/wav;base64,')[1]); }
                a.readAsDataURL(blob);
            }
            blobToDataURL(mp3Blob,(base_64)=>{
                const type = Cookies.get("voiceType")
                if(type === 'CN'){
                    this.pid = 1537
                }else if(type === 'EN'){
                    this.pid = 1737
                }else{
                    this.pid = 1537
                }
                // console.log(base_64);
                const option = {
                    speech: base_64,//本地语音文件的的二进制语音数据 ，需要进行base64 编码。与len参数连一起使用。
                    len: mp3Blob.size,		//字节数
                    dev_pid: this.pid,//普通话识别代码
                    cuid: `${Math.floor(Math.random() * 1000000)}`,
                };
                baiduSpeechToText(option).then(res => {
                    if(res.msg != null && res.msg != ''){
                        this.content = res.msg
                    //   console.log(res.msg);
                        this.resultData.content = this.content;
                        this.resultData.flag = this.isRecorder;
                        resolve(this.resultData);
                    }else{
                      console.log('识别失败')
                      this.resultData.content = this.content;
                        this.resultData.flag = this.isRecorder;
                      resolve(this.resultData);
                    }
                    this.mediaRecorder.clear();
                  }).catch(error => {
                    reject(error);
                  });
                // this.mediaRecorder.clear();
            }
        )      
    });
},
  
    // 监控音量
    monitorVolume() {
        if (!this.analyser) {
            console.error('AnalyserNode 未初始化');
            return;
          }
        const bufferLength = this.analyser.fftSize;
        const dataArray = new Uint8Array(bufferLength);
      
        let silenceStart = 0; // 记录静音开始的时间
        const silenceThreshold = 1000; // 1 秒静音时间
        const checkInterval = 100; // 每 100 毫秒检查一次音量
      
        return new Promise((resolve, reject) => {
            const checkVolume = () => {
                if(!this.isRecorder){
                    return;
                }
    
              // 获取频谱数据
              this.analyser.getByteFrequencyData(dataArray);
              const averageVolume = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
          
              if (averageVolume < 10) {
                // 如果音量很低（接近静音），记录静音的时间
                if (silenceStart === 0) silenceStart = Date.now();
          
                // 如果静音超过 2 秒，停止录音
                if (Date.now() - silenceStart > silenceThreshold) {
                  console.log("没有音频输入，自动停止录音");
                  this.stopRecording().then(result => {
                    console.log(result);
                    resolve(result);
                });
                  return; // 停止检测
                }
              } else {
                // 如果检测到有音频输入，重置静音时间
                silenceStart = 0;
              }
          
              // 每隔 100 毫秒继续检测
              setTimeout(checkVolume, checkInterval);
            };
          
            // 开始第一次检测
            checkVolume();
        });
        
      },
      
  };

  