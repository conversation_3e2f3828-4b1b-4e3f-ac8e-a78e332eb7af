
const APPID = 'eb53539a' //在科大讯飞控制台中获取的服务接口认证信息

import Worker from './transcode.worker.js'
import { getAuthUrl } from '@/api/explorationCenter/experience.js'
const transWorker = new Worker()
// console.log(transWorker)

function getWebSocketUrl() {
  return new Promise((resolve, reject) => {
    var token = '220E178653D011EFB2260242AC110002'
    var sid = '0003'
    var url = 'wss://47.105.127.226:11311/knowengine/socket/qa'
    //var url = 'wss://papaicai.com/knowengine/socket/qa'
    url = `${url}?token=${token}&sid=${sid}`
    console.log(url);
    resolve(url)

  })
}
const Knowengine = class {
  constructor({} = {}) {
    let self = this
    this.status = 'null'
    // 记录听写结果
    this.resultText = ''
    // wpgs下的听写结果需要中间状态辅助记录
    this.resultTextTemp = ''
    // 开启流式结果返回功能
    this.dwa = 'wpgs'
    //每一小段文本
    this.step = ''
    //结束标识 yes代表结束
    this.finish = ''
  }

  // 修改录音听写状态
  setStatus(status) {
    this.onWillStatusChange && this.status !== status && this.onWillStatusChange(this.status, status)
    this.status = status
  }
  setResultText({ resultText, resultTextTemp,step,finish } = {}) {
    //console.log(finish);
    this.onTextChange && this.onTextChange(resultTextTemp || resultText || step || finish || '')
    resultText !== undefined && (this.resultText = resultText)
    resultTextTemp !== undefined && (this.resultTextTemp = resultTextTemp)
    step !== undefined && (this.step = step)
    finish !== undefined && (this.finish = finish)
  }
  // 连接websocket
  connectWebSocket() {
    return getWebSocketUrl().then(url => {
      let iatWS
      if ('WebSocket' in window) {
        iatWS = new WebSocket(url)
      } else if ('MozWebSocket' in window) {
        iatWS = new MozWebSocket(url)
      } else {
        alert('浏览器不支持WebSocket')
        return
      }
      this.webSocket = iatWS
      this.setStatus('init')
      iatWS.onopen = e => {
        this.setStatus('ing')
        // 重新开始录音
        // setTimeout(() => {
        //   this.webSocketSend()
        // }, 500)
      }
      iatWS.onmessage = e => {
        //console.log(e.data);
        let jsonData = JSON.parse(e.data)
        if(jsonData.type == "success") {
          console.log(jsonData.text);
        } else if(jsonData.type == "reply") {
          this.result(e.data);
        }

      }
      iatWS.onerror = e => {
        this.recorderStop()
      }
      iatWS.onclose = e => {
        console.log("断开连接")
        // console.log("持续时间", endTime - startTime)
        this.recorderStop()
      }
    })
  }
  // 初始化浏览器录音
  recorderInit() {
    navigator.getUserMedia =
      navigator.getUserMedia ||
      navigator.webkitGetUserMedia ||
      navigator.mozGetUserMedia ||
      navigator.msGetUserMedia

    // 创建音频环境
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
      this.audioContext.resume()
      if (!this.audioContext) {
        alert('浏览器不支持webAudioApi相关接口')
        return
      }
    } catch (e) {
      if (!this.audioContext) {
        alert('浏览器不支持webAudioApi相关接口')
        return
      }
    }

    // 获取浏览器录音权限
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices
        .getUserMedia({
          audio: true,
          video: false,
        })
        .then(stream => {
          getMediaSuccess(stream)
        })
        .catch(e => {
          getMediaFail(e)
        })
    } else if (navigator.getUserMedia) {
      navigator.getUserMedia(
        {
          audio: true,
          video: false,
        },
        stream => {
          getMediaSuccess(stream)
        },
        function (e) {
          getMediaFail(e)
        }
      )
    } else {
      if (navigator.userAgent.toLowerCase().match(/chrome/) && location.origin.indexOf('https://') < 0) {
        alert('chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限')
      } else {
        alert('无法获取浏览器录音功能，请升级浏览器或使用chrome')
      }
      this.audioContext && this.audioContext.close()
      return
    }
    // 获取浏览器录音权限成功的回调
    let getMediaSuccess = stream => {
      // 创建一个用于通过JavaScript直接处理音频
      this.scriptProcessor = this.audioContext.createScriptProcessor(0, 1, 1)
      this.scriptProcessor.onaudioprocess = e => {
        // 去处理音频数据
        if (this.status === 'ing') {
          transWorker.postMessage(e.inputBuffer.getChannelData(0))
          //  this.audioData.push(e.inputBuffer.getChannelData(0))
        }
      }
      // 创建一个新的MediaStreamAudioSourceNode 对象，使来自MediaStream的音频可以被播放和操作
      this.mediaSource = this.audioContext.createMediaStreamSource(stream)
      // 连接
      this.mediaSource.connect(this.scriptProcessor)
      this.scriptProcessor.connect(this.audioContext.destination)
      this.connectWebSocket()
    }

    let getMediaFail = (e) => {
      this.audioContext && this.audioContext.close()
      this.audioContext = undefined
      // 关闭websocket
      if (this.webSocket && this.webSocket.readyState === 1) {
        this.webSocket.close()
      }
    }
  }
  recorderStart() {
    if (!this.audioContext) {
      this.recorderInit()
    } else {
      this.audioContext.resume()
      this.connectWebSocket()
    }
  }
  // 暂停录音
  recorderStop() {
    // safari下suspend后再次resume录音内容将是空白，设置safari下不做suspend
    if (!(/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgen))) {
      this.audioContext && this.audioContext.suspend()
    }
    this.setStatus('end')
  }
  // 处理音频数据
  // transAudioData(audioData) {
  //   audioData = transAudioData.transaction(audioData)
  //   this.audioData.push(...audioData)
  // }
  // 对处理后的音频数据进行base64编码，
  toBase64(buffer) {
    var binary = ''
    var bytes = new Uint8Array(buffer)
    var len = bytes.byteLength
    for (var i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }
  // 向webSocket发送数据
  webSocketSend(question) {
    if (this.webSocket.readyState !== 1) {
      return
    }

    this.setResultText({
      resultTextTemp: "",
      resultText: "",
      step: ""
    })

    var params = {
      requestId: "1cd95155-53cc-11ef-b226-0242ac110002",
      type: "question",
      text: question
    }
    this.webSocket.send(JSON.stringify(params))
  }
  result(resultData) {
    // 识别结束
    let jsonData = JSON.parse(resultData)
    if (jsonData && jsonData.type != "error") {
      let str = jsonData.text


      //结束时，将resultTextTemp同步给resultText
      if (!jsonData.final) {
        // 将结果存储在resultTextTemp中
        this.setResultText({
          resultTextTemp: this.resultTextTemp + str,
          step: str,
          finish: 'no'
        })

      } else {
        this.setResultText({
          resultTextTemp: this.resultTextTemp + str,
          step: str,
          finish: 'yes'
        })
        console.log("结束")
        this.setResultText({
          resultText: this.resultTextTemp
        })
      }


    }
    // if (jsonData.final) {
    //   this.webSocket.close()
    // }
  }
  close() {
    this.webSocket.close()
  }
  start() {
    this.connectWebSocket()
    this.setResultText({ resultText: '', resultTextTemp: '' })
  }
  stop() {
    this.recorderStop()
  }

}

export default Knowengine
