<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>a

<script>
import ThemePicker from "@/components/ThemePicker";
import index from "./views/intelligencePromotion/analyticalPrediction/index.vue";
// import { getToken } from "@/utils/auth";
// import { mapActions } from "vuex";

export default {
  name: "App",
  components: { ThemePicker },
  // data() {
  //   return {
  //     userInfo: {
  //       studentId: ""
  //     }
  //   };
  // },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
  }
  // mounted() {
  //   this.fetchAndCommitUserInfo(); // 获取用户信息（studentId）
  // //  this.getyucefenxi(this.userInfo.studentId);
  // },
  // methods: {
  //   ...mapActions(["GetInfo"]),
  //   //登陆后预先，生成预测分析长文本，存入数据库，并返回响应“1” 。

  //   async fetchAndCommitUserInfo() {
  //     try {
  //       const result = await this.GetInfo();
  //       console.log("用户信息获取成功:", result);
  //       if (result && result.user) {
  //         // 检查 result 是否存在以及是否有 sysUser 属性
  //         const { user } = result;
  //         this.userInfo.studentId = user.studentId;
  //         console.log("token中获取到学生id:", this.userInfo.studentId);
  //         this.getyucefenxi(this.userInfo.studentId);
  //       } else {
  //         console.error("返回结果中缺少 userInfo 或 user");
  //       }
  //     } catch (error) {
  //       console.error("获取用户信息失败:", error);
  //       throw error; // 抛出错误以便外部捕获
  //     }
  //   },
  //     async getyucefenxi(stuid) {
  //       console.log("getyucefenxi_main.vue预先请求新增开始执行");
  //     try {
  //           const url =
  //           process.env.VUE_APP_BASE_API + `/test/mySelfPortrait/yucefenxi/${ this.userInfo.studentId}`;
  //           console.log("新增", stuid,"的预测分析结果");
  //           console.log("等待中...");
  //           const response = await fetch(url, {
  //             method: "GET",
  //             headers: {
  //               Authorization: "Bearer " + getToken(),
  //               "Content-Type": "application/json",
  //             },
  //           });
  //           if (!response.ok) {
  //             throw new Error(`HTTP error! Status: ${response.status}`);
  //           }
  //           const res = await response.json();
  //           console.log("main.vue向后端请求新增文本成功：", res);

  //     } catch (error) {
  //       console.error("没找到学生信息", error);
  //       throw error; // 抛出错误以便外部捕获

  //     }
  //   },
  // }
  
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
