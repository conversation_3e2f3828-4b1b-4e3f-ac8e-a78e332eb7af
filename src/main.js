import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

// 引入调试保护工具
import debugProtection from '@/utils/debugProtection'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
import { selectDictLabelByVal } from '@/utils/common'
import * as echarts from "echarts";

// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
//引入首页滚动播放组件
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/dist/css/swiper.css'
// 抽屉详情组件
import RightDrawer from '@/components/RightDrawer'

import dataV from '@jiaminghi/data-view'

// 导入语音识别模块
import '@/utils/voiceweakup/commandProcessor'
import audioPlayer from '@/utils/audioPlayer';

import { DraggablePlugin, DraggableDirective ,Draggable} from '@braks/revue-draggable';
Vue.use(DraggablePlugin)
Vue.directive('draggable', DraggableDirective)
Vue.component('Draggable', Draggable)

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.selectDictLabelByVal = selectDictLabelByVal // 通过value值返回label


// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('RightDrawer', RightDrawer)

Vue.use(dataV)
Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
Vue.use(VueAwesomeSwiper)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

// 初始化语音识别服务
store.dispatch('speechRecognition/initSpeechService');

// 启动语音识别
store.dispatch('speechRecognition/startListening');

// 注册全局命令
store.dispatch('speechRecognition/registerCommands', {
  context: 'global',
  commands: {
    '你好，小智': () => {
      // 播放唤醒音效
      audioPlayer.play('wake');
      Vue.prototype.$message({
        message: '您好，我在为您服务',
        type: 'success'
      });
    },
    '回到首页': () => {
      router.push({ path: '/' });
    },
  }
});

// 监听路由变化，更新语音识别上下文
router.afterEach((to) => {
  // 使用路由名称作为上下文名称
  const context = to.name || 'default';
  store.dispatch('speechRecognition/setActiveContext', context);
});

// 处理页面关闭事件
window.addEventListener('beforeunload', () => {
  // 页面关闭时停止语音识别
  store.dispatch('speechRecognition/stopListening');
});

// 全局错误处理
window.addEventListener('error', () => {
  // 出错时尝试重新启动语音识别
  store.dispatch('speechRecognition/stopListening');
  setTimeout(() => {
    store.dispatch('speechRecognition/startListening');
  }, 3000);
});

// 初始化调试保护
debugProtection.init();

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
