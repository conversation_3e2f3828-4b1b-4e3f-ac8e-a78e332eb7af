import router from './router'
import store from './store'
import {Message} from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import {getToken} from '@/utils/auth'
import {isRelogin} from '@/utils/request'
import { checkForCasRedirect } from '@/utils/casRedirect'

NProgress.configure({showSpinner: false})

const whiteList = ['/login', '/register', '/externalCertification', '/modify', '/Experience', '/VideoPlayer', '/ssoLogin', '/cas', '/createPPT', '/pptV1', '/cas/pptV1']

router.beforeEach(async (to, from, next) => {
	NProgress.start()

  // 优先检查是否需要CAS重定向
  try {
    const shouldRedirect = await checkForCasRedirect(to)
    if (shouldRedirect) {
      // 如果需要重定向，停止当前导航
      NProgress.done()
      return // 不调用next()，防止Vue Router完成导航
    }
  } catch (error) {
    console.error('CAS重定向检查失败:', error)
    // 出错时继续正常导航流程
  }

  // 原有的权限验证逻辑
	if (getToken()) {
		to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
		/* has token*/
		if (to.path === '/login') {
			next({path: '/'})
			NProgress.done()
		} else if (whiteList.indexOf(to.path) !== -1) {
			next()
		} else {
			if (store.getters.roles.length === 0) {
				isRelogin.show = true
				// 判断当前用户是否已拉取完user_info信息
				store.dispatch('GetInfo').then(() => {
					isRelogin.show = false
					store.dispatch('GenerateRoutes').then(accessRoutes => {
						// 根据roles权限生成可访问的路由表
						router.addRoutes(accessRoutes) // 动态添加可访问路由表
						next({...to, replace: true}) // hack方法 确保addRoutes已完成
					})
				}).catch(err => {
					store.dispatch('LogOut').then(() => {
						Message.error(err)
						next({path: '/'})
					})
				})
			} else {
				next()
			}
		}
	} else {
		// 没有token
		if (whiteList.indexOf(to.path) !== -1) {
			// 在免登录白名单，直接进入
			next()
		} else {
			next(`/login?redirect=${encodeURIComponent(to.fullPath)}`) // 否则全部重定向到登录页
			NProgress.done()
		}
	}
})

router.afterEach(() => {
	NProgress.done()
})
