<template>
	<div class="markdown-renderer">
		<div ref="previewElement" class="vditor-reset"></div>
	</div>
</template>

<script>
import 'vditor/dist/index.css';
import Vditor from 'vditor';
import Cookies from 'js-cookie'
export default {
	name: 'MarkdownRenderer',
	props: {
		content: {
			type: String,
			default: ''
		},
		theme: {
			type: String,
			default: 'dark'
		},
		codeTheme: {
			type: String,
			default: 'tokyonight-moon'
		},
	},
	watch: {
		content: {
			handler(newVal) {
				this.renderMarkdown(newVal);
			},
			immediate: true
		},
		theme(newTheme) {
			this.updateTheme(newTheme);
		}
	},
	mounted() {
		this.renderMarkdown(this.content);
	},
	methods: {
		renderMarkdown(markdown) {
			if (!markdown) return;

			const element = this.$refs.previewElement;
			if (!element) return;

			Vditor.preview(element, markdown, {
				hljs: {
					style: this.codeTheme,
					lineNumber: true
				},
				lang: 'zh_CN',
				theme: {
					current: this.theme
				},
				cache: {
					enable: true
				},
				math: {
					engine: 'MathJax',
					inlineDigit:true,
					macros:{
						bf: '{\\\\boldsymbol f}',
						bu: '{\\\\boldsymbol u}',
						bv: '{\\\\boldsymbol v}',
						bw: '{\\\\boldsymbol w}'
					},
					mathjaxOptions: {
						tex: {
							inlineMath: [['$', '$'], ['\\(', '\\)']], // 支持 $...$ 和 \(...\)
							displayMath: [['$$', '$$']],              // 仅支持 $$...$$
							processEscapes: false                     // 禁用 \$ 转义
						},
						output: 'svg'                               // 使用 SVG 渲染
					}
				},
				markdown: {
					toc: false,
					mark: true,
					footnotes: true,
					autoSpace: true
				},
				delay: 3000,
				after: () => {
					this.processImages();
					this.processLinks();
					this.removeCodeBlockLimit();
					this.scrollToBottom();
					this.$emit('rendered');
				}
			});
		},

		updateTheme(theme) {
			Vditor.setContentTheme(theme, 'https://cdn.jsdelivr.net/npm/vditor@3.6.2/dist/css/content-theme/');
			Vditor.setCodeTheme(this.codeTheme);
		},

		removeCodeBlockLimit() {
			console.log("index this.codeBlockLimit", Cookies.get('codeBlockLimit'));
			if (Cookies.get('codeBlockLimit')) {
				const codeBlocks = this.$refs.previewElement.querySelectorAll('.language-html');
				console.log("aa", codeBlocks);
				const lastFive = Array.from(codeBlocks).slice(-1);

				lastFive.forEach(block => {
					block.style.maxHeight = 'none';
					block.style.overflow = 'auto';
				});
			}
		},

		scrollToBottom() {
			this.$nextTick(() => {
				const el = this.$refs.previewElement;
				el.scrollTop = el.scrollHeight;

				// 确保最后一个 code 区域也能展示到底部
				const lastCode = el.querySelector('.vditor-linenumber:last-of-type');
				if (lastCode) {
					lastCode.scrollTop = lastCode.scrollHeight;
				}
			});
		},

		processImages() {
			let element = this.$refs.previewElement;
			if (element) {
				const images = element.querySelectorAll('img');
				images.forEach(img => {
					img.addEventListener('click', () => {
						this.$emit('image-click', img.src);
					});
				});
			}
		},

		processLinks() {
			let element = this.$refs.previewElement;
			if (element){
				const links = element.querySelectorAll('a');
				links.forEach(link => {
					link.setAttribute('target', '_blank');
					link.setAttribute('rel', 'noopener');
				});
			}
		}
	}
};
</script>

<style scoped>
.markdown-renderer {
	width: 100%;
	height: 100%;
	overflow: auto;
	/* 继承父组件的字体大小设置 */
	font-size: inherit;
	line-height: inherit;
}

.markdown-renderer .vditor-reset {
	letter-spacing: normal !important;
	word-spacing: normal !important;
	font-family: inherit !important;
	white-space: normal !important;
	height: 100%;
	/* 确保继承字体大小 */
	font-size: inherit !important;
	line-height: inherit !important;
}
</style>

<!-- 全局样式，用于支持字体大小缩放功能 -->
<style>
/* 当父组件应用缩放时，确保所有 vditor 内容都正确继承字体大小 */
.scaled-content .vditor-reset {
	/* 确保缩放时文本仍然清晰 */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	/* 继承父组件设置的字体大小 */
	font-size: inherit !important;
	line-height: inherit !important;
}

/* 确保所有文本元素都继承字体大小 */
.scaled-content .vditor-reset h1,
.scaled-content .vditor-reset h2,
.scaled-content .vditor-reset h3,
.scaled-content .vditor-reset h4,
.scaled-content .vditor-reset h5,
.scaled-content .vditor-reset h6 {
	/* 标题按比例缩放 */
	font-size: inherit;
	/* 保持标题的相对大小关系 */
}

.scaled-content .vditor-reset h1 { font-size: 2em; }
.scaled-content .vditor-reset h2 { font-size: 1.5em; }
.scaled-content .vditor-reset h3 { font-size: 1.17em; }
.scaled-content .vditor-reset h4 { font-size: 1em; }
.scaled-content .vditor-reset h5 { font-size: 0.83em; }
.scaled-content .vditor-reset h6 { font-size: 0.67em; }

/* 段落和其他文本元素 */
.scaled-content .vditor-reset p,
.scaled-content .vditor-reset div,
.scaled-content .vditor-reset span,
.scaled-content .vditor-reset li {
	font-size: inherit;
	line-height: inherit;
}

/* 代码块字体大小 */
.scaled-content .vditor-reset pre,
.scaled-content .vditor-reset code {
	font-size: 0.9em; /* 相对于基础字体稍小 */
	line-height: inherit;
}

/* 表格字体大小 */
.scaled-content .vditor-reset table,
.scaled-content .vditor-reset td,
.scaled-content .vditor-reset th {
	font-size: inherit;
	line-height: inherit;
}

/* 确保图片等元素不受字体大小影响 */
.scaled-content .vditor-reset img {
	max-width: 100%;
	height: auto;
}
</style>
