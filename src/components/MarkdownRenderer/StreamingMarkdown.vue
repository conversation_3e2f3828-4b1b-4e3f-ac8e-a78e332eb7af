<template>
  <div class="streaming-markdown" :class="{ 'scaled-content': scale !== 1 }" :style="contentStyle">
    <markdown-renderer
        :content="content"
        :theme="theme"
        :code-theme="codeTheme"
        @rendered="onRendered"
        @image-click="onImageClick"
    ></markdown-renderer>

    <span v-show="finalShowCursor" class="typing-cursor">
      <el-skeleton :rows="3" animated :throttle="100"/>
    </span>
  </div>
</template>

<script>
import MarkdownRenderer from './index.vue'

export default {
  name: 'StreamingMarkdown',
  components: {
    MarkdownRenderer
  },
  props: {
    content: {
      type: String,
      default: ''
    },
    showCursor: {
      type: Boolean,
      default: true
    },
    theme: {
      type: String,
      default: 'Ant Design'
    },
    codeTheme: {
      type: String,
      default: 'github'
    },
    // 新增缩放属性，支持传入缩放比例
    scale: {
      type: Number,
      default: 1,
      validator: (value) => value > 0 && value <= 2 // 限制缩放范围 0-200%
    }
  },
  data() {
    return {
      lastRenderedContent: '',
      cursorTimeout: null, // 用于存储定时器
      internalShowCursor: true // 内部控制光标显示状态，避免直接修改prop
    }
  },
  computed: {
    // 计算内容样式 - 通过字体大小控制整体大小
    contentStyle() {
      if (this.scale === 1) return {}

      // 基础字体大小 16px，根据缩放比例调整
      const baseFontSize = 16
      const scaledFontSize = baseFontSize * this.scale

      return {
        fontSize: `${scaledFontSize}px`,
        // 同时调整行高保持比例
        lineHeight: 1.6
      }
    },
    // 计算最终的光标显示状态：结合prop和内部状态
    finalShowCursor() {
      return this.showCursor && this.internalShowCursor
    }
  },
  mounted() {
    // 初始化时根据prop设置内部状态
    this.internalShowCursor = this.showCursor
  },
  beforeDestroy() {
    // 组件销毁前清理定时器，防止内存泄漏
    if (this.cursorTimeout) {
      clearTimeout(this.cursorTimeout)
      this.cursorTimeout = null
    }
  },

  watch: {
    content: {
      handler(newVal) {
        // 当内容发生变化时，可以在这里执行一些额外的处理
        this.handleStreamingContent(newVal)
      },
      immediate: true
    },
    // 监听prop变化，同步到内部状态
    showCursor: {
      handler(newVal) {
        this.internalShowCursor = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleStreamingContent(content) {
      // 如果内容发生变化，重置定时器
      if (this.lastRenderedContent !== content) {
        this.lastRenderedContent = content
        this.resetCursorTimeout() // 重置光标定时器
        this.internalShowCursor = true // 修复：使用内部状态而不是直接修改prop
      }
    },
    resetCursorTimeout() {
      // 如果已经有定时器在运行，清除它
      if (this.cursorTimeout) {
        clearTimeout(this.cursorTimeout)
      }
      // 设置新的定时器
      this.cursorTimeout = setTimeout(() => {
        this.internalShowCursor = false // 修复：使用内部状态而不是直接修改prop
        console.log('Cursor hidden due to inactivity')
      }, 3000)
    },
    onRendered() {
      // Markdown渲染完成后的回调
      this.$emit('rendered')
    },
    onImageClick(src) {
      // 图片点击事件，可以实现预览功能
      this.$emit('image-click', src)
    }
  }
}
</script>

<style scoped>
.streaming-markdown {
  position: relative;
  width: 100%;
}

/* 缩放内容的样式优化 - 通过字体大小实现真正的内容缩放 */
.scaled-content {
  /* 确保所有子元素都继承缩放后的字体大小 */
  font-size: inherit;
  line-height: inherit;
}

/* 流式指示器样式调整 */
.streaming-indicator {
  margin-top: 10px;
}
</style>
