<template>
  <div class="right-drawer" :class="{'isShow':showDrawer}">
    <el-button class="toggle-drawer-btn el-icon-d-arrow-right" @click="toggleDrawer" v-show='show'></el-button>
    <slot name="drawer-content"></slot>
  </div>
</template>
<script>
export default {
  name: "RightDrawer",
  props: {
    showDrawer: {
      type: Boolean,
      default: true,
    },
    show: {
      type: Boolean,
      default: true,
    }
  },
  methods: {
    // 按钮显示隐藏
    toggleDrawer() {
      this.$emit("update:showDrawer", !this.showDrawer);
    },
  },
};
</script>
<style lang='scss' scoped>
.right-drawer {
  width: 0%;
  position: absolute;
  right: 0px;
  bottom: 0;
  top: 0;
  box-shadow: -2px 0 2px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in;
  z-index: 99;
  background: #fff;
  overflow: hidden;
  &.isShow {
    width: 70%;
  }
}
.toggle-drawer-btn {
  background: #eeeeee;
  border: 1px solid #dddddd;
  width: 18px;
  font-size: 12px;
  height: 50px;
  top: 0;
  bottom: 0;
  z-index: 999;
  left: 0;
  margin: auto;
  cursor: pointer;
  position: absolute;
  padding: 0;
  border-radius: 0;
}
</style>

