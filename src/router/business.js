import Layout from '@/layout'
// 业务模块
const businessRouter = [
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addDataSet',
        component: (resolve) => require(['@/views/dataSet/dataSetManagement/addDataSet'], resolve),
        name: 'AddDataSet',
        meta: { title: '创建数据集', activeMenu: '/dataSet/dataSetManagement' }
      },
      {
        path: 'reviewDataSet',
        component: (resolve) => require(['@/views/dataSet/dataSetManagement/reviewDataSet'], resolve),
        name: 'ReviewDataSet',
        meta: { title: '查看数据集', activeMenu: '/dataSet/dataSetManagement' }
      },
      {
        path: 'pictureDetails',
        component: (resolve) => require(['@/views/dataSet/dataSetManagement/pictureDetails'], resolve),
        name: 'ReviewDataSet',
        meta: { title: '查看数据集图片', activeMenu: '/dataSet/pictureDetails' }
      }
    ]
  },
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addDataProcessing',
        component: (resolve) => require(['@/views/dataSet/dataProcessing/addDataProcessing'], resolve),
        name: 'AddDataProcessing',
        meta: { title: '创建任务', activeMenu: '/dataSet/dataProcessing' }
      },
      {
        path: 'reviewDataProcessing',
        component: (resolve) => require(['@/views/dataSet/dataProcessing/reviewDataProcessing'], resolve),
        name: 'ReviewDataProcessing',
        meta: { title: '查看任务详情', activeMenu: '/dataSet/dataProcessing' }
      }
    ]
  },
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addGenerateDataSet',
        component: (resolve) => require(['@/views/dataSet/generateDataSet/addDataProcessing'], resolve),
        name: 'AddGenerateDataSet',
        meta: { title: '创建临时数据集', activeMenu: '/dataSet/generateDataSet' }
      },
      {
        path: 'reviewGenerateDataSet',
        component: (resolve) => require(['@/views/dataSet/generateDataSet/reviewDataProcessing'], resolve),
        name: 'ReviewGenerateDataSet',
        meta: { title: '查看临时数据集详情', activeMenu: '/dataSet/generateDataSet' }
      }
    ]
  },
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addTrainingMission',
        component: (resolve) => require(['@/views/modelFineTuning/trainingMission/addTrainingMission'], resolve),
        name: 'AddTrainingMission',
        meta: { title: '创建训练任务', activeMenu: '/modelFineTuning/trainingMission' }
      },
      {
        path: 'reviewTrainingMission',
        component: (resolve) => require(['@/views/modelFineTuning/trainingMission/reviewTrainingMission'], resolve),
        name: 'ReviewTrainingMission',
        meta: { title: '训练任务详情', activeMenu: '/modelFineTuning/trainingMission' }
      }
    ]
  },
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'schoolFile',
        component: (resolve) => require(['@/views/dataSet/schoolRules/fileList'], resolve),
        name: 'SchoolFile',
        meta: { title: '知识库文件', activeMenu: '/schoolRules/fileList' }
      },
    ]
  },


  {
    path: '/explorationCenter9/governance/:dynamicPart',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addDataSet',
        component: (resolve) => require(['@/views/dataSet/dataSetManagement/addDataSet'], resolve),
        name: 'AddDataSet',
        meta: { title: '创建数据集', activeMenu: '/explorationCenter9/governance/:dynamicPart/dataSetManagement' }
      },
      {
        path: 'reviewDataSet',
        component: (resolve) => require(['@/views/dataSet/dataSetManagement/reviewDataSet'], resolve),
        name: 'ReviewDataSet',
        meta: { title: '查看数据集', activeMenu: '/explorationCenter9/governance/:dynamicPart/dataSetManagement' }
      }
    ]
  },
  {
    path: '/explorationCenter9/governance/:dynamicPart',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addDataProcessing',
        component: (resolve) => require(['@/views/dataSet/dataProcessing/addDataProcessing'], resolve),
        name: 'AddDataProcessing',
        meta: { title: '创建任务', activeMenu: '/explorationCenter9/governance/:dynamicPart/dataProcessing' }
      },
      {
        path: 'reviewDataProcessing',
        component: (resolve) => require(['@/views/dataSet/dataProcessing/reviewDataProcessing'], resolve),
        name: 'ReviewDataProcessing',
        meta: { title: '查看任务详情', activeMenu: '/explorationCenter9/governance/:dynamicPart/dataProcessing' }
      }
    ]
  },
  // {
  //   path: '/explorationCenter9/:dynamicPart',
  //   component: Layout,
  //   hidden: true,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: 'addGenerateDataSet',
  //       component: (resolve) => require(['@/views/dataSet/generateDataSet/addDataProcessing'], resolve),
  //       name: 'AddGenerateDataSet',
  //       meta: { title: '创建临时数据集', activeMenu: '/explorationCenter9/:dynamicPart/generateDataSet' }
  //     },
  //     {
  //       path: 'reviewGenerateDataSet',
  //       component: (resolve) => require(['@/views/dataSet/generateDataSet/reviewDataProcessing'], resolve),
  //       name: 'ReviewGenerateDataSet',
  //       meta: { title: '查看临时数据集详情', activeMenu: '/explorationCenter9/:dynamicPart/generateDataSet' }
  //     }
  //   ]
  // },
  {
    path: '/explorationCenter9/governance/:dynamicPart',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addTrainingMission',
        component: (resolve) => require(['@/views/modelFineTuning/trainingMission/addTrainingMission'], resolve),
        name: 'AddTrainingMission',
        meta: { title: '创建训练任务', activeMenu: '/explorationCenter9/governance/:dynamicPart/trainingMission' }
      },
      {
        path: 'reviewTrainingMission',
        component: (resolve) => require(['@/views/modelFineTuning/trainingMission/reviewTrainingMission'], resolve),
        name: 'ReviewTrainingMission',
        meta: { title: '训练任务详情', activeMenu: '/explorationCenter9/governance/:dynamicPart/trainingMission' }
      }
    ]
  },
  {
    path: '/jobCorrectionCreation',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addJob',
        component: (resolve) => require(['@/views/jobCorrectionCreation/addJob.vue'], resolve),
        name: 'AddJob',
        meta: { title: '创建作业', activeMenu: '/explorationCenter6/jobCorrectionCreation' }
      },
      {
        path: 'updateJob',
        component: (resolve) => require(['@/views/jobCorrectionCreation/updateJob.vue'], resolve),
        name: 'UpdateJob',
        meta: { title: '修改作业', activeMenu: '/explorationCenter6/jobCorrectionCreation' }
      },
      {
        path: 'correctJob',
        component: (resolve) => require(['@/views/jobCorrectionCreation/correctJob.vue'], resolve),
        name: 'CorrectJob',
        meta: { title: '作业批改', activeMenu: '/explorationCenter6/jobCorrectionCreation' }
      },
      {
        path: 'lookJob',
        component: (resolve) => require(['@/views/jobCorrectionCreation/lookJob.vue'], resolve),
        name: 'LookJob',
        meta: { title: '作业详情', activeMenu: '/explorationCenter6/jobCorrectionCreation' }
      },
    ]
  },
  {
    path: '/homework',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'doHomework',
        component: (resolve) => require(['@/views/homework/doHomework.vue'], resolve),
        name: 'DoHomework',
        meta: { title: '写作业', activeMenu: '/explorationCenter7/homework' }
      },
    ]
  },
  {
    path: '/digitalHumanClass',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'courseList',
        component: (resolve) => require(['@/views/digitalHumanClass/courseList.vue'], resolve),
        name: 'courseList',
        meta: { title: '课堂', activeMenu: '/explorationCenter7/digitalHumanClass' }
      },
    ]
  },
  {
    path: '/knowledgeBase',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'discipline',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/disciplineList.vue'], resolve),
        name: 'Discipline',
        meta: { title: '专业课程', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'addKnowledgeBase',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/addKnowledgeBase.vue'], resolve),
        name: 'AddKnowledgeBase',
        meta: { title: '添加教材', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'reviewKnowledgeBase',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/reviewKnowledgeBase.vue'], resolve),
        name: 'ReviewKnowledgeBase',
        meta: { title: '课程教材', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'researchGroup',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/researchGroupList.vue'], resolve),
        name: 'ResearchGroup',
        meta: { title: '课题组', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'analysisResult',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/analysisResult.vue'], resolve),
        name: 'AnalysisResult',
        meta: { title: '解析结果', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'knowledgeDomain',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/knowledgeDomain.vue'], resolve),
        name: 'KnowledgeDomain',
        meta: { title: '知识图谱', activeMenu: '/dataSet/knowledgeBase' }
      },
      {
        path: 'knowledgeData',
        component: (resolve) => require(['@/views/dataSet/knowledgeBase/knowledgeData.vue'], resolve),
        name: 'KnowledgeData',
        meta: { title: '知识数据', activeMenu: '/dataSet/knowledgeBase' }
      },
    ]
  },
  {
    path: '/dataSet',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addDataMake',
        component: (resolve) => require(['@/views/dataSet/dataSetMake/addDataMake'], resolve),
        name: 'AddDataMake',
        meta: { title: '生成数据集', activeMenu: '/dataSet/dataMake' }
      },
      {
        path: 'reviewDataMake',
        component: (resolve) => require(['@/views/dataSet/dataSetMake/reviewDataMake'], resolve),
        name: 'ReviewDataMake',
        meta: { title: '查看详情', activeMenu: '/dataSet/dataMake' }
      },
      {
        path: 'editDataMake',
        component: (resolve) => require(['@/views/dataSet/dataSetMake/editDataMake'], resolve),
        name: 'EditDataMake',
        meta: { title: '修改数据集', activeMenu: '/dataSet/dataMake' }
      },
      // {
      //   path: 'editDataMake',
      //   component: () => import('@/views/dataSet/dataSetMake/editDataMake.vue'),
      //   name: 'EditDataMake',
      //   meta: { title: '修改数据集', activeMenu: '/dataSet/dataMake' }
      // },
      {
        path: 'makeDataSet',
        component: (resolve) => require(['@/views/dataSet/dataSetMake/makeDataSet'], resolve),
        name: 'MakeDataSet',
        meta: { title: '去制作数据集', activeMenu: '/dataSet/dataMake' }
      },
      {
        path: 'scenesEmulate',
        component: (resolve) => require(['@/views/dataSet/scenesEmulate/index'], resolve),
        name: 'ScenesEmulate',
        meta: { title: '应用场景', activeMenu: '/dataSet/dataMake' }
      },
    ]
  },
  {
    path: '/wisdomSchool',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'learn',
        component: (resolve) => require(['@/views/wisdomSchool/wisdomSchool'], resolve),
        name: 'Learn',
        meta: { title: '学习', activeMenu: '/explorationCenter7/wisdomSchool' }
      },
      {
        path: 'presentation',
        component: (resolve) => require(['@/views/wisdomSchool/presentationIndex'], resolve),
        name: 'Presentation',
        meta: { title: '课件列表', activeMenu: '/explorationCenter7/presentationIndex' }
      },
      {
        path: 'reviewDataSet',
        component: (resolve) => require(['@/views/wisdomSchool/reviewDataSet'], resolve),
        name: 'reviewDataSet',
        meta: { title: '知识点', activeMenu: '/explorationCenter7/reviewDataSet' }
      },
    ]
  },
  {
    path: '/teachingMaterials',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addMaterials',
        component: (resolve) => require(['@/views/teachingMaterials/addTeachingMaterials.vue'], resolve),
        name: 'AddMaterials',
        meta: { title: '创建', activeMenu: '/explorationCenter6/teachingMaterials' }
      },
      {
        path: 'updateMaterials',
        component: (resolve) => require(['@/views/teachingMaterials/updateTeachingMaterials.vue'], resolve),
        name: 'UpdateMaterials',
        meta: { title: '修改', activeMenu: '/explorationCenter6/teachingMaterials' }
      },
      {
        path: 'reviewMaterials',
        component: (resolve) => require(['@/views/teachingMaterials/reviewTeachingMaterials.vue'], resolve),
        name: 'ReviewMaterials',
        meta: { title: '查看', activeMenu: '/explorationCenter6/teachingMaterials' }
      },
      {
        path: 'coursewareManagement',
        component: (resolve) => require(['@/views/teachingMaterials/coursewareManagement.vue'], resolve),
        name: 'CoursewareManagement',
        meta: { title: '课件管理', activeMenu: '/explorationCenter6/teachingMaterials' }
      },
      {
        path: 'presentationPreview',
        component: (resolve) => require(['@/views/teachingMaterials/presentationPreview.vue'], resolve),
        name: 'PresentationPreview',
        meta: { title: '课件预览', activeMenu: '/explorationCenter6/teachingMaterials' }
      },
    ]
  },

  {
    path: '/applicationScenario',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'applicationScenario',
        component: (resolve) => require(['@/views/applicationScenario/index.vue'], resolve),
        name: 'ApplicationScenario',
        meta: { title: 'applicationScenario', activeMenu: '/applicationScenario' }
      },
      {
        path: 'scenario2',
        component: (resolve) => require(['@/views/applicationScenario/scenario2.vue'], resolve),
        name: 'Scenario2',
        meta: { title: 'applicationScenario', activeMenu: '/applicationScenario/scenario2' }
      },
      {
        path: 'scenario',
        component: (resolve) => require(['@/views/applicationScenario/scenario.vue'], resolve),
        name: 'Scenario',
        meta: { title: 'applicationScenario', activeMenu: '/applicationScenario/scenario' }
      },
      {
        path: 'execl',
        component: (resolve) => require(['@/views/applicationScenario/execl.vue'], resolve),
        name: 'execl',
        meta: { title: 'execl', activeMenu: '/applicationScenario/execl' }
      },

       {
        path: 'putzsk',
        component: (resolve) => require(['@/views/applicationScenario/putzsk.vue'], resolve),
        name: 'putzsk',
        meta: { title: '知识库上传', activeMenu: '/applicationScenario/putzsk' }
      },
    ]
  },

  // {
  //   path: '',
  //   component: Layout,
  //   redirect: 'VideoPlayer',
  //   children: [
  //     {
  //       path: 'VideoPlayer',
  //       component: () => import('@/views/VideoPlayer'),
  //       name: 'VideoPlayer',
  //       meta: { title: '首页',  affix: true }
  //     }
  //   ]
  // },
  {
    path: '/VideoPlayer',
    name: 'VideoPlayer',
    component: () => import('@/views/VideoPlayer'),
  },
  {
    path: '/plat',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'motion',
        component: (resolve) => require(['@/views/plat/figure/preview'], resolve),
        name: 'Motion',
        meta: { title: '详情', activeMenu: '/plat/figure' }
      },
    ]
  },
  {
    // 智能提升部分
    path: '/intelligencePromotion',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'analyticalPrediction/index',
        component: (resolve) => require(['@/views/intelligencePromotion/analyticalPrediction/index.vue'], resolve),
        name: 'AnalyticalPrediction',
        meta: { title: '预测分析', activeMenu: '/intelligencePromotion/AnalyticalPrediction' }
      },
      {
        path: 'myPortrait/index',
        component: (resolve) => require(['@/views/intelligencePromotion/myPortrait/index.vue'], resolve),
        name: 'StudentProfile',
        meta: { title: '我的画像', activeMenu: '/intelligencePromotion/StudentProfile' }
      },
    ]
  },
  {
    path: '/intellectSmartPpt',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/intellectSmartPpt/index'], resolve),
        name: 'IntellectPPT',
        meta: { title: '智能ppt生成', activeMenu: '/explorationCenter6/smartPPT' }
      },
    ]
  },
  {
    path: '/courseManagement',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addCourse',
        component: (resolve) => require(['@/views/courseManagement/addCourse'], resolve),
        name: 'AddCourse',
        meta: { title: '创建课程', activeMenu: '/explorationCenter6/courseManagement' }
      },
      {
        path: 'reviewCourseInfo',
        component: (resolve) => require(['@/views/courseManagement/reviewCourseInfo'], resolve),
        name: 'ReviewCourseInfo',
        meta: { title: '课程详情', activeMenu: '/explorationCenter6/courseManagement' }
      },
      {
        path: 'reviewCourseDetails',
        component: (resolve) => require(['@/views/courseManagement/reviewCourseDetails'], resolve),
        name: 'reviewCourseDetails',
        meta: { title: '课程详情', activeMenu: '/explorationCenter6/courseManagement' }
      },
      {
        path: 'courseAnalysis',
        component: (resolve) => require(['@/views/courseAnalysis/index'], resolve),
        name: 'CourseAnalysis',
        meta: { title: '课程分析', activeMenu: '/explorationCenter6/courseManagement' }
      },
      {
        path: 'discussion',
        component: (resolve) => require(['@/views/myCourse/discussion'], resolve),
        name: 'Discussion',
        meta: { title: '讨论', activeMenu: '/explorationCenter6/courseManagement' }
      },
    ]
  },
  {
    path: '/formula',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'formula',
        component: (resolve) => require(['@/views/formula/index'], resolve),
        name: 'formula',
        meta: { title: '学习', activeMenu: '/formula/index' }
      },
    ]
  },
  {
    path: '/unionCourse',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'mapping',
        component: (resolve) => require(['@/views/unionCourses/mapping'], resolve),
        name: 'Mapping',
        meta: { title: '图谱', activeMenu: '/unionCourses' }
      },
      {
        path: 'courseInfo',
        component: (resolve) => require(['@/views/unionCourses/courseInfo'], resolve),
        name: 'CourseInfo',
        meta: { title: '课程详情', activeMenu: '/unionCourses' }
      },
    ]
  },
  {
    path: '/myCourse',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'reviewMyCourse',
        component: (resolve) => require(['@/views/myCourse/reviewMyCourse'], resolve),
        name: 'ReviewMyCourse',
        meta: { title: '课程详情', activeMenu: '/explorationCenter7/myCourse' }
      },
      {
        path: 'discussion',
        component: (resolve) => require(['@/views/myCourse/discussion'], resolve),
        name: 'Discussion',
        meta: { title: '讨论', activeMenu: '/explorationCenter7/myCourse' }
      },
    ]
  },
  {
    path: '/sceneManagement',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'addSceneManagement',
        component: (resolve) => require(['@/views/sceneManagement/addSceneManagement'], resolve),
        name: 'AddSceneManagement',
        meta: { title: '创建场景', activeMenu: '/sceneManagement/addSceneManagement' }
      },
      {
        path: 'reviewSceneManagement',
        component: (resolve) => require(['@/views/sceneManagement/reviewSceneManagement'], resolve),
        name: 'ReviewSceneManagement',
        meta: { title: '场景详情', activeMenu: '/sceneManagement/reviewSceneManagement' }
      },
      {
        path: 'updateSceneManagement',
        component: (resolve) => require(['@/views/sceneManagement/updateSceneManagement'], resolve),
        name: 'UpdateSceneManagement',
        meta: { title: '场景修改', activeMenu: '/sceneManagement/updateSceneManagement' }
      },
      {
        path: 'index',
        component: (resolve) => require(['@/views/sceneManagement/index'], resolve),
        name: 'SceneManagement',
        meta: { title: '应用场景', activeMenu: '/sceneManagement/index' }
      },
    ]
  },
]
export default businessRouter
