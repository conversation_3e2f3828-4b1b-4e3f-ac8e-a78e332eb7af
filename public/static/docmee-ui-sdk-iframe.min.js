var N = Object.defineProperty;
var R = (e, r, t) => r in e ? N(e, r, { enumerable: !0, configurable: !0, writable: !0, value: t }) : e[r] = t;
var d = (e, r, t) => R(e, typeof r != "symbol" ? r + "" : r, t);
const S = "%[a-f0-9]{2}", p = new RegExp("(" + S + ")|([^%]+?)", "gi"), F = new RegExp("(" + S + ")+", "gi");
function l(e, r) {
  try {
    return [decodeURIComponent(e.join(""))];
  } catch {
  }
  if (e.length === 1)
    return e;
  r = r || 1;
  const t = e.slice(0, r), n = e.slice(r);
  return Array.prototype.concat.call([], l(t), l(n));
}
function q(e) {
  try {
    return decodeURIComponent(e);
  } catch {
    let r = e.match(p) || [];
    for (let t = 1; t < r.length; t++)
      e = l(r, t).join(""), r = e.match(p) || [];
    return e;
  }
}
function D(e) {
  const r = {
    "%FE%FF": "��",
    "%FF%FE": "��"
  };
  let t = F.exec(e);
  for (; t; ) {
    try {
      r[t[0]] = decodeURIComponent(t[0]);
    } catch {
      const a = q(t[0]);
      a !== t[0] && (r[t[0]] = a);
    }
    t = F.exec(e);
  }
  r["%C2"] = "�";
  const n = Object.keys(r);
  for (const a of n)
    e = e.replace(new RegExp(a, "g"), r[a]);
  return e;
}
function L(e) {
  if (typeof e != "string")
    throw new TypeError("Expected `encodedURI` to be of type `string`, got `" + typeof e + "`");
  try {
    return decodeURIComponent(e);
  } catch {
    return D(e);
  }
}
function I(e, r) {
  if (!(typeof e == "string" && typeof r == "string"))
    throw new TypeError("Expected the arguments to be of type `string`");
  if (e === "" || r === "")
    return [];
  const t = e.indexOf(r);
  return t === -1 ? [] : [
    e.slice(0, t),
    e.slice(t + r.length)
  ];
}
function P(e, r) {
  const t = {};
  if (Array.isArray(r))
    for (const n of r) {
      const a = Object.getOwnPropertyDescriptor(e, n);
      a != null && a.enumerable && Object.defineProperty(t, n, a);
    }
  else
    for (const n of Reflect.ownKeys(e)) {
      const a = Object.getOwnPropertyDescriptor(e, n);
      if (a.enumerable) {
        const o = e[n];
        r(n, o, e) && Object.defineProperty(t, n, a);
      }
    }
  return t;
}
const k = (e) => e == null, H = (e) => encodeURIComponent(e).replaceAll(/[!'()*]/g, (r) => `%${r.charCodeAt(0).toString(16).toUpperCase()}`), m = Symbol("encodeFragmentIdentifier");
function T(e) {
  switch (e.arrayFormat) {
    case "index":
      return (r) => (t, n) => {
        const a = t.length;
        return n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
          ...t,
          [f(r, e), "[", a, "]"].join("")
        ] : [
          ...t,
          [f(r, e), "[", f(a, e), "]=", f(n, e)].join("")
        ];
      };
    case "bracket":
      return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
        ...t,
        [f(r, e), "[]"].join("")
      ] : [
        ...t,
        [f(r, e), "[]=", f(n, e)].join("")
      ];
    case "colon-list-separator":
      return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
        ...t,
        [f(r, e), ":list="].join("")
      ] : [
        ...t,
        [f(r, e), ":list=", f(n, e)].join("")
      ];
    case "comma":
    case "separator":
    case "bracket-separator": {
      const r = e.arrayFormat === "bracket-separator" ? "[]=" : "=";
      return (t) => (n, a) => a === void 0 || e.skipNull && a === null || e.skipEmptyString && a === "" ? n : (a = a === null ? "" : a, n.length === 0 ? [[f(t, e), r, f(a, e)].join("")] : [[n, f(a, e)].join(e.arrayFormatSeparator)]);
    }
    default:
      return (r) => (t, n) => n === void 0 || e.skipNull && n === null || e.skipEmptyString && n === "" ? t : n === null ? [
        ...t,
        f(r, e)
      ] : [
        ...t,
        [f(r, e), "=", f(n, e)].join("")
      ];
  }
}
function B(e) {
  let r;
  switch (e.arrayFormat) {
    case "index":
      return (t, n, a) => {
        if (r = /\[(\d*)]$/.exec(t), t = t.replace(/\[\d*]$/, ""), !r) {
          a[t] = n;
          return;
        }
        a[t] === void 0 && (a[t] = {}), a[t][r[1]] = n;
      };
    case "bracket":
      return (t, n, a) => {
        if (r = /(\[])$/.exec(t), t = t.replace(/\[]$/, ""), !r) {
          a[t] = n;
          return;
        }
        if (a[t] === void 0) {
          a[t] = [n];
          return;
        }
        a[t] = [...a[t], n];
      };
    case "colon-list-separator":
      return (t, n, a) => {
        if (r = /(:list)$/.exec(t), t = t.replace(/:list$/, ""), !r) {
          a[t] = n;
          return;
        }
        if (a[t] === void 0) {
          a[t] = [n];
          return;
        }
        a[t] = [...a[t], n];
      };
    case "comma":
    case "separator":
      return (t, n, a) => {
        const o = typeof n == "string" && n.includes(e.arrayFormatSeparator), s = typeof n == "string" && !o && c(n, e).includes(e.arrayFormatSeparator);
        n = s ? c(n, e) : n;
        const i = o || s ? n.split(e.arrayFormatSeparator).map((u) => c(u, e)) : n === null ? n : c(n, e);
        a[t] = i;
      };
    case "bracket-separator":
      return (t, n, a) => {
        const o = /(\[])$/.test(t);
        if (t = t.replace(/\[]$/, ""), !o) {
          a[t] = n && c(n, e);
          return;
        }
        const s = n === null ? [] : n.split(e.arrayFormatSeparator).map((i) => c(i, e));
        if (a[t] === void 0) {
          a[t] = s;
          return;
        }
        a[t] = [...a[t], ...s];
      };
    default:
      return (t, n, a) => {
        if (a[t] === void 0) {
          a[t] = n;
          return;
        }
        a[t] = [...[a[t]].flat(), n];
      };
  }
}
function M(e) {
  if (typeof e != "string" || e.length !== 1)
    throw new TypeError("arrayFormatSeparator must be single character string");
}
function f(e, r) {
  return r.encode ? r.strict ? H(e) : encodeURIComponent(e) : e;
}
function c(e, r) {
  return r.decode ? L(e) : e;
}
function E(e) {
  return Array.isArray(e) ? e.sort() : typeof e == "object" ? E(Object.keys(e)).sort((r, t) => Number(r) - Number(t)).map((r) => e[r]) : e;
}
function x(e) {
  const r = e.indexOf("#");
  return r !== -1 && (e = e.slice(0, r)), e;
}
function W(e) {
  let r = "";
  const t = e.indexOf("#");
  return t !== -1 && (r = e.slice(t)), r;
}
function b(e, r) {
  return r.parseNumbers && !Number.isNaN(Number(e)) && typeof e == "string" && e.trim() !== "" ? e = Number(e) : r.parseBooleans && e !== null && (e.toLowerCase() === "true" || e.toLowerCase() === "false") && (e = e.toLowerCase() === "true"), e;
}
function h(e) {
  e = x(e);
  const r = e.indexOf("?");
  return r === -1 ? "" : e.slice(r + 1);
}
function g(e, r) {
  r = {
    decode: !0,
    sort: !0,
    arrayFormat: "none",
    arrayFormatSeparator: ",",
    parseNumbers: !1,
    parseBooleans: !1,
    ...r
  }, M(r.arrayFormatSeparator);
  const t = B(r), n = /* @__PURE__ */ Object.create(null);
  if (typeof e != "string" || (e = e.trim().replace(/^[?#&]/, ""), !e))
    return n;
  for (const a of e.split("&")) {
    if (a === "")
      continue;
    const o = r.decode ? a.replaceAll("+", " ") : a;
    let [s, i] = I(o, "=");
    s === void 0 && (s = o), i = i === void 0 ? null : ["comma", "separator", "bracket-separator"].includes(r.arrayFormat) ? i : c(i, r), t(c(s, r), i, n);
  }
  for (const [a, o] of Object.entries(n))
    if (typeof o == "object" && o !== null)
      for (const [s, i] of Object.entries(o))
        o[s] = b(i, r);
    else
      n[a] = b(o, r);
  return r.sort === !1 ? n : (r.sort === !0 ? Object.keys(n).sort() : Object.keys(n).sort(r.sort)).reduce((a, o) => {
    const s = n[o];
    return a[o] = s && typeof s == "object" && !Array.isArray(s) ? E(s) : s, a;
  }, /* @__PURE__ */ Object.create(null));
}
function A(e, r) {
  if (!e)
    return "";
  r = {
    encode: !0,
    strict: !0,
    arrayFormat: "none",
    arrayFormatSeparator: ",",
    ...r
  }, M(r.arrayFormatSeparator);
  const t = (s) => r.skipNull && k(e[s]) || r.skipEmptyString && e[s] === "", n = T(r), a = {};
  for (const [s, i] of Object.entries(e))
    t(s) || (a[s] = i);
  const o = Object.keys(a);
  return r.sort !== !1 && o.sort(r.sort), o.map((s) => {
    const i = e[s];
    return i === void 0 ? "" : i === null ? f(s, r) : Array.isArray(i) ? i.length === 0 && r.arrayFormat === "bracket-separator" ? f(s, r) + "[]" : i.reduce(n(s), []).join("&") : f(s, r) + "=" + f(i, r);
  }).filter((s) => s.length > 0).join("&");
}
function C(e, r) {
  var a;
  r = {
    decode: !0,
    ...r
  };
  let [t, n] = I(e, "#");
  return t === void 0 && (t = e), {
    url: ((a = t == null ? void 0 : t.split("?")) == null ? void 0 : a[0]) ?? "",
    query: g(h(e), r),
    ...r && r.parseFragmentIdentifier && n ? { fragmentIdentifier: c(n, r) } : {}
  };
}
function O(e, r) {
  r = {
    encode: !0,
    strict: !0,
    [m]: !0,
    ...r
  };
  const t = x(e.url).split("?")[0] || "", n = h(e.url), a = {
    ...g(n, { sort: !1 }),
    ...e.query
  };
  let o = A(a, r);
  o && (o = `?${o}`);
  let s = W(e.url);
  if (typeof e.fragmentIdentifier == "string") {
    const i = new URL(t);
    i.hash = e.fragmentIdentifier, s = r[m] ? i.hash : `#${e.fragmentIdentifier}`;
  }
  return `${t}${o}${s}`;
}
function _(e, r, t) {
  t = {
    parseFragmentIdentifier: !0,
    [m]: !1,
    ...t
  };
  const { url: n, query: a, fragmentIdentifier: o } = C(e, t);
  return O({
    url: n,
    query: P(a, r),
    fragmentIdentifier: o
  }, t);
}
function V(e, r, t) {
  const n = Array.isArray(r) ? (a) => !r.includes(a) : (a, o) => !r(a, o);
  return _(e, n, t);
}
const K = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  exclude: V,
  extract: h,
  parse: g,
  parseUrl: C,
  pick: _,
  stringify: A,
  stringifyUrl: O
}, Symbol.toStringTag, { value: "Module" })), $ = {
  BASE_URL: "https://iframe.docmee.cn"
}, z = () => $.BASE_URL, w = (e) => {
  const r = U[e], t = z();
  return t.endsWith("/") ? `${t}${r}` : `${t}/${r}`;
}, U = {
  dashboard: "sdk-ui/dashboard",
  editor: "sdk-ui/editor",
  creator: "sdk-ui/creator/0",
  customTemplate: "sdk-ui/custom-template",
  templateCreator: "sdk-ui/custom-template-creator",
  templateMarker: "sdk-ui/marker"
};
class G {
  /**
   *
   * @param {DocmeeUIConstructorOptions} options
   */
  constructor({
    token: r,
    page: t = "dashboard",
    container: n,
    pptId: a,
    onMessage: o,
    DOMAIN: s,
    ...i
  }) {
    d(this, "docmeeHref", w("dashboard"));
    d(this, "query", { iframe: "1" });
    d(this, "iframe", null);
    d(this, "onMessage", () => Promise.resolve(!0));
    d(this, "iframeMounted", !1);
    d(this, "initInterval", null);
    this.onMessage = o, this.container = n, location.protocol.startsWith("file") && console.log(
      "%c %s",
      "color: red; background-color: #f7c600",
      "🔴 不能在file协议下运行，请启动一个http服务来运行！ 🔴 "
    ), r || console.log(
      "%c 初始化时，token不能为空！",
      "color: #d7514f; background-color: #2e2e2e"
    ), s && ($.BASE_URL = s), this.init({ token: r, page: t, pptId: a, ...i });
  }
  _postMessage(r) {
    var t, n;
    this.iframe.contentWindow || (console.log(this.iframe.contentWindow), console.error("iframe未挂载！")), (n = (t = this.iframe) == null ? void 0 : t.contentWindow) == null || n.postMessage(r, this.docmeeHref);
  }
  init({ token: r, page: t = "dashboard", ...n }) {
    if (t === "editor" && !n.pptId)
      throw new Error("初始化editor页面时，必须传入pptId");
    this.query = Object.assign({}, this.query, n), this.docmeeHref = w(t), this.updateToken(r), this._initIframe(!0);
  }
  // 初始化iframe
  _initIframe(r) {
    const t = this.container, n = document.createElement("iframe"), a = `${location.protocol}//${location.host}`, o = K.stringifyUrl({
      url: this.docmeeHref,
      query: r ? { iframe: 1, targetOrigin: a } : this.query
    });
    n.src = o, n.style.width = "100%", n.style.height = "100%", n.style.border = "0", n.style.outline = "none", n.style.padding = "0px", n.setAttribute("allowfullscreen", "true"), this.iframe = n, this.iframeMounted = !1, t.innerHTML = "", t.appendChild(n), window.addEventListener("message", async (s) => {
      var u, y;
      if (s.source !== this.iframe.contentWindow) return;
      const i = s.data;
      if (i.type)
        if (r && (i.type === "mounted" || i.type === "invalid-token") && (this.iframeMounted = !0, i.type === "mounted" && this._postMessage({
          type: "transParams",
          data: this.query
        })), i.type === "user-info" && (this.iframeMounted = !0), i.type.startsWith("before")) {
          const j = await ((u = this.onMessage) == null ? void 0 : u.call(this, i));
          this._postMessage({ data: j, type: `recover_${i.type}` });
        } else
          (y = this.onMessage) == null || y.call(this, i);
    }), this.iframe.addEventListener("load", () => {
      let s = 0;
      setTimeout(() => {
        this.iframeMounted || (this.initInterval = setInterval(() => {
          if (this.iframeMounted || s >= 5)
            return s = 0, clearInterval(this.initInterval);
          r && this._postMessage({
            type: "transParams",
            data: this.query
          }), s++;
        }, 200));
      }, 300);
    });
  }
  /**
   * 更新用户token
   * @param {string} latestToken 新的token
   */
  updateToken(r) {
    /(a|s)k_.+/.test(r) || console.error("token 错误！"), this.token = r, this.query.token = r, this.iframeMounted && this._postMessage({
      type: "transParams",
      data: {
        token: r
      }
    });
  }
  /**
   * 卸载iframe
   */
  destroy() {
    this.container.innerHTML = "";
  }
  /**
   * 发送消息
   * @param {{type: 'warning' | 'success' | 'error' | 'info', content: string}}
   */
  sendMessage(r) {
    this._postMessage({ type: "message", data: r });
  }
  getInfo() {
    this._postMessage({ type: "getInfo" });
  }
  navigate({ page: r, pptId: t, templateId: n }) {
    if (!U[r]) throw new Error(`页面${r} 不存在`);
    this._postMessage({
      type: "nav",
      data: {
        page: r,
        token: this.token,
        pptId: t,
        templateId: n
      }
    });
  }
  changeCreatorData(r, t = !1) {
    this._postMessage({
      type: "transParams",
      data: { creatorData: { ...r, createNow: t } }
    });
  }
  updateTemplate(r) {
    this._postMessage({
      type: "changeTemplateById",
      data: { templateId: r }
    });
  }
  showTemplateDialog(r = "system") {
    this._postMessage({
      type: "showTemplateDialog",
      data: { type: r }
    });
  }
  getCurrentPptInfo() {
    this._postMessage({
      type: "getCurrentPptInfo"
    });
  }
  importCSS(r) {
    this._postMessage({ type: "importCSS", data: { css: r } });
  }
}
window.DocmeeUI = G;
