<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>PPTXjs - Meshesha</title>

    <link rel="stylesheet" href="./css/pptxjs.css">
    <link rel="stylesheet" href="./css/nv.d3.min.css">

<script type="text/javascript" src="./js/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="./js/jszip.min.js"></script>
<script type="text/javascript" src="./js/filereader.js"></script>
<script type="text/javascript" src="./js/d3.min.js"></script>
<script type="text/javascript" src="./js/nv.d3.min.js"></script>
<script type="text/javascript" src="./js/pptxjs.js"></script>
<script type="text/javascript" src="./js/divs2slides.js"></script>
<style>
	html, body { margin: 0; padding: 0; transform: scale(0.82);
    }
	#warper {width: 100%; }
	/* transform-origin: top left; /* 从左上角开始缩放 */ */
</style>
</head>
<body>
	<div id="warper">
		<!-- <input id="uploadFileInput" type="file" />
		<br><br> -->
		<div id="container">
			<div  id="result"></div>
		</div>
	</div>
<script>
	// $("#result").pptxToHtml({
	// 		pptxFileUrl: "./pptfile/城市管理学：理论与实践1.pptx",
	// 		fileInputId: "uploadFileInput",
	// 		slidesScale: "50%",
	// 		slideMode: false,
	// 		keyBoardShortCut: false

      // 	})
      //模拟后端获取文件///使用input获取
      $('#uploadFileInput').on('change', function(evt) {
        //获取文件
        var file = evt.target.files[0]
        //console.log(file);
        $('#result').pptxToHtml({
          file: file,
          fileInputId: '',
          slidesScale: '50%',
          slideMode: false,
          keyBoardShortCut: false
        })
      })

      window.addEventListener('message', function(event) {
        var data = event.data
        // console.log("从vue中获得的数据", data);
        // 定义一个变量去接收,然后就可以获得vue传过来的数据
        //var obj = JSON.parse(data);
        console.log(data.params.pptIndex)
        // console.log(data.params.realSumbitUrl)
        // JavaScript代码
        // var realSumbitUrl = document.getElementById("realSumbitUrl");
        // realSumbitUrl.value = data.params.realSumbitUrl
        // var epccGwMsg = document.getElementById("epccGwMsg");
        // epccGwMsg.value = data.params.epccGwMsg
        // document.getElementById("form").submit();
      }, '*')

      function cresssated(ppts) {
        //  const pptIndex = (pptInfo.currentPage &&pptInfo.currentPage!=pptInfo.presentationAllpage)?pptInfo.currentPage:1
        // 	var pptUrl = pptInfo.presentationHttp + pptInfo.prefix + pptIndex +pptInfo.suffix;
        //   var urls = 'http://************:8012/onlinePreview?url='+ encodeuRIcomponent(Base64.encode(pptUrl))
        console.log(ppts, 'pps')
        $('#result').pptxToHtml({
          file: ppts,
          fileInputId: '',
          slidesScale: '50%',
          slideMode: false,
          keyBoardShortCut: false
        })
        setTimeout(function() {
          checkForVideos()
        }, 1000)
      }

      // 检查转换后的HTML中是否有video标签并添加监听
      function checkForVideos() {
        // console.log(document.getElementById('result').innerHTML)

        // 查找#result中所有的视频元素
        var videos = document.querySelectorAll('#result video')
        console.log('videos')
        console.log('videos', videos)
        // 为每个视频元素添加播放监听
        videos.forEach(function(video) {
          video.addEventListener('play', function() {
            // 当视频开始播放时，弹出提示
            // alert('视频开始播放!')
            // 发送消息到父页面（假设父页面在iframe或同域中）
            window.parent.postMessage({
              type: 'video-play',
              message: 'video-start-play',
              videoSrc: video.src
            }, '*');  // '*' 表示消息可以发送到任何域，确保父页面能接收
          })

          // 如果视频已经在播放（例如自动播放），立即弹出提示
          if (video.paused) {
            // alert('视频暂停了!')
            // 发送消息到父页面（假设父页面在iframe或同域中）
            window.parent.postMessage({
              type: 'video-paused',
              message: 'video-start-paused',
                videoSrc: video.src
            }, '*');  // '*' 表示消息可以发送到任何域，确保父页面能接收
          }
        })
      }

      function clickFun(index) {
        // 假设动态生成的元素有一个共同的class为.dynamic-element
        var htmlContent = $('#all_slides_warpper').html() // 获取第3个子元素的HTML内容
        //console.log(htmlContent);
        // 向vue中发送数据
        window.parent.postMessage({
          params: htmlContent
        }, '*')
      }

      var pptList = ['城市管理学：理论与实践.pptx',
        'Sample_12.pptx']
      let pptIndex = 1

      function change(ppts) {
        $('#result').html('')
        $('#result').pptxToHtml({
          file: ppts,
          fileInputId: '',
          slidesScale: '50%',
          slideMode: false,
          keyBoardShortCut: false
        })
      }

      // setInterval(function() {
      // 	change(pptIndex);
      // 	pptIndex++
      // },3000);
    </script>

  </body>
</html>
