<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ai才专有大模型 第三方信息共享清单</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h2{
          text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        p{
            margin: 10px 0;
        }
        ul{
          margin:0
        }
        span{
          font-size: 14px;
        }
        .highlight{
          text-decoration: underline;
        }
        
    </style>
</head>
<body>
    <div class="container">
      <h2>第三方信息共享清单</h2>
      <ul>
        <li><span>为保障Ai才专有大模型 相关功能的实现与应用安全稳定的运行，我们可能会接入由合作方提供的软件开发包（SDK）、应用程序接口（API）、应用程序插件等代码或通过其他合作方式，以实现相关目的。</span></li>
        <li><span>我们会对获取信息的合作方的代码进行严格的安全监测，以保护数据安全。</span></li>
        <li><span>我们对接入的相关合作方在目录中列明，合作方具体的处理情况请参见合作方的隐私政策或服务协议。</span></li>
        <li><span>请注意，合作方的代码可能因为其版本升级、策略调整等原因导致数据处理类型存在一定变化，请以其公示的官方说明为准。</span></li>
        <li><span>以下共同适用于Ai才专有大模型的最新版本，如有单独适用的目的将在下列列表说明。由于产品迭代升级，部分历史版本可能与当前版本情况存在差异，以实际情况为准。</span></li>
      </ul>
      <p><span><strong>微信OpenSDK</strong></span></p>
      <ul>
        <li><span><strong>涉及的个人信息类型：</strong>授权参数信息（如AppID、code、access_token）</span></li>
        <li><span><strong>使用目的：</strong>支持微信小程序登录</span></li>
        <li><span><strong>使用场景：</strong>在用户使用微信登录时使用</span></li>
        <li><span><strong>第三方主体：</strong>深圳市腾讯计算机系统有限公司</span></li>
        <li><span><strong>收集方式：</strong>SDK采集</span></li>
        <li><span><strong>官网链接：</strong><a href="https://open.weixin.qq.com/" target="_blank" rel="noopener noreferrer">https://open.weixin.qq.com/</a></span></li>
        <li><span><strong>官网链接：</strong><a href="https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8" target="_blank" rel="noopener noreferrer">https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</a></span></li>
      </ul>
      <p><span><strong>百度OpenAPI</strong></span></p>
      <ul>
        <li><span><strong>涉及的个人信息类型：</strong>授权参数信息（如AppID、code、access_token）</span></li>
        <li><span><strong>使用目的：</strong>模型及功能调用</span></li>
        <li><span><strong>使用场景：</strong>文生图、图片解析、语音</span></li>
        <li><span><strong>第三方主体：</strong>北京百度网讯科技有限公司</span></li>
        <li><span><strong>收集方式：</strong>api传递</span></li>
        <li><span><strong>官网链接：</strong><a href="https://console.bce.baidu.com/" target="_blank" rel="noopener noreferrer">https://console.bce.baidu.com/</a></span></li>
        <li><span><strong>官网链接：</strong><a href="https://cloud.baidu.com/doc/Agreements/s/Plr0fi68q" target="_blank" rel="noopener noreferrer">隐私政策 - 协议 | 百度智能云文档</a></span></li>
      </ul>
      <p><span><strong>阿里OpenAPI</strong></span></p>
      <ul>
        <li><span><strong>涉及的个人信息类型：</strong>授权参数信息（如手机号）</span></li>
        <li><span><strong>使用目的：</strong>短信认证</span></li>
        <li><span><strong>使用场景：</strong>忘记密码、修改密码</span></li>
        <li><span><strong>第三方主体：</strong>阿里云计算有限公司</span></li>
        <li><span><strong>收集方式：</strong>SDK采集</span></li>
        <li><span><strong>官网链接：</strong><a href="https://www.aliyun.com/?spm=5176.21213303.J_4VYgf18xNlTAyFFbOuOQe.d_logo.25432f3dD9VrzK" target="_blank" rel="noopener noreferrer">阿里云-计算，为了无法计算的价值</a></span></li>
        <li><span><strong>官网链接：</strong><a href="https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202107091605_49213_5_4_26149.html?spm=5176.25163407.console-base_help.13.65c92ec8kuagWR" target="_blank" rel="noopener noreferrer">阿里云法律声明及隐私权政策</a></span></li>
      </ul>
    </div>
</body>
</html>